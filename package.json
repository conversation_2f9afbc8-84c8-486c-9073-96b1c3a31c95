{"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "max dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "prepare": "husky install", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit", "new": "plop"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions"], "dependencies": {"@ant-design/icons": "^4.8.1", "@ant-design/pro-components": "^2.6.13", "@ant-design/use-emotion-css": "1.0.4", "@tencent/eslint-config-tencent": "^1.0.4", "@umijs/route-utils": "^2.2.2", "antd": "^5.10.0", "classnames": "^2.3.2", "cos-js-sdk-v5": "^1.4.20", "echarts": "^5.3.2", "echarts-for-react": "^3.0.2", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^7.0.4", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "omit.js": "^2.0.2", "prettier": "^3.0.3", "query-string": "^8.1.0", "rc-menu": "^9.11.1", "rc-util": "^5.37.0", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dev-inspector": "^1.9.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.1.2", "react-final-form": "^6.5.9", "react-helmet-async": "^1.0.4", "react-router": "^6.15.0", "react-use": "^17.4.0", "umi-presets-pro": "^2.0.3", "umi-request": "^1.3.5", "use-merge-value": "^1.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.5", "@testing-library/react": "^13.4.0", "@types/express": "^4.17.17", "@types/history": "^4.7.11", "@types/jest": "^29.5.4", "@types/lodash": "^4.14.197", "@types/react": "^18.2.21", "@types/react-copy-to-clipboard": "^5.0.3", "@types/react-dom": "^18.2.7", "@types/react-helmet": "^6.1.6", "@umijs/fabric": "^2.14.1", "@umijs/max": "^4.0.78", "cross-env": "^7.0.3", "eslint": "^8.48.0", "express": "^4.18.2", "gh-pages": "^3.2.3", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "plop": "^4.0.1", "stylelint": "^13.0.0", "typescript": "~5.2.2", "uglifyjs-webpack-plugin": "^2.2.0", "umi-presets-pro": "^2.0.3"}, "engines": {"node": ">=12.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}