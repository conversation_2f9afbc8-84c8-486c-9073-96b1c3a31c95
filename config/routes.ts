export const isChina = globalThis.location?.hostname === 'sms-config.woa.com';
export const isIntl = ['sms-config-singpore.woa.com', 'sms-config-germany.woa.com'].includes(globalThis.location?.hostname);
export default [
  {
    path: '/',
    redirect: '/index',
  },
  {
    path: '/index',
    name: 'index',
    component: './sms/index',
  },
  {
    path: '/sms',
    name: 'sms',
    routes: [
      {
        path: '/sms',
        redirect: '/sms/send-query',
      },
      {
        name: 'sms-send-query',
        path: '/sms/send-query',
        component: './sms/sendQuery',
        authority: ['admin', 'user'],
      },
      {
        name: 'sms-replay-query',
        path: '/sms/replay-query',
        component: './sms/replayQuery',
      },
      {
        name: 'sms-daily-query',
        path: '/sms/daily-query',
        component: './sms/dailyQuery',
      },
      {
        name: 'sms-payment-query',
        path: '/sms/payment-query',
        component: './sms/paymentQuery',
      },
      {
        name: 'sms-sdkappid-query',
        path: '/sms/sdkappid-query',
        component: './sms/sdkappidQuery',
      },
      {
        name: 'sms-sdkappid-detail',
        path: '/sms/sdkappid-detail',
        component: './sms/sdkappidDetail',
        hideInMenu: true,
        parentKeys: ['/sms/sdkappid-query'],
      },
      {
        name: 'sms-send-task',
        path: '/sms/send-task',
        component: './sms/smsSend',
        hideInMenu: true,
      },
      // {
      //   name: 'sms-tci-query',
      //   path: '/sms/tci-query',
      //   component: './sms/tciLogQuery',
      // },
      {
        name: 'sms-fail-reason',
        path: '/sms/fail-reason',
        component: './sms/failReason',
      },
      {
        name: 'sms-intl-query',
        path: '/sms/intl-query',
        component: './sms/ismsQuery',
      },
      {
        name: 'sms-unbackfill',
        path: '/sms/unbackfill',
        component: './sms/unbackfill',
      },
      {
        name: 'sms-undelivery',
        path: '/sms/undelivery',
        component: './sms/undelivery',
      },
      {
        name: 'sms-schedule-info',
        path: '/sms/schedule-info',
        component: './sms/querySchedulerInfo',
      },
      {
        name: 'sms-direct-port',
        path: '/sms/direct-port',
        component: './sms/directPortQuery',
        hideInMenu: isIntl,
      },
      {
        name: 'sms-company-info',
        path: '/sms/company-info',
        component: './sms/companyInfoManage',
        hideInMenu: isIntl,
      },
      {
        name: 'sms-up-app-search',
        path: '/sms/up-app-search',
        component: './sms/upAppSearch',
      },
    ],
  },
  {
    path: '/vms',
    name: 'vms',
    // redirect: 'vms/send-query',
    routes: [
      {
        path: '/vms',
        redirect: '/vms/send-query',
      },
      {
        name: 'vms-send-query',
        path: '/vms/send-query',
        component: './vms/sendQuery',
      },
      {
        name: 'vms-send-new-query',
        path: '/vms/send-new-query',
        component: './vms/sendNewQuery',
      },
      {
        name: 'vms-number-query',
        path: '/vms/number-query',
        component: './vms/numberQuery',
      },
    ],
  },
  {
    path: '/monitor',
    name: 'monitor',
    routes: [
      {
        path: '/monitor',
        redirect: '/monitor/console-log',
      },
      {
        name: 'monitor-console-log',
        path: '/monitor/console-log',
        component: './monitor/consoleLog',
      },
      {
        name: 'monitor-config-log',
        path: '/monitor/config-log',
        component: './monitor/configLog',
      },
      {
        name: 'monitor-modify-dispatch',
        path: '/monitor/modify-dispatch',
        component: './monitor/addNomalMonitor',
      },
      {
        name: 'monitor-query-dispatch',
        path: '/monitor/query-dispatch',
        component: './monitor/queryNormalMonitor',
      },
      {
        name: 'monitor-modify-market',
        path: '/monitor/modify-market',
        component: './monitor/addNomalMonitor',
      },
      {
        name: 'monitor-query-market',
        path: '/monitor/query-market',
        component: './monitor/queryNormalMonitor',
      },
      {
        name: 'monitor-reply-check',
        path: '/monitor/reply-check',
        component: './monitor/replyCheck',
      },
      {
        name: 'monitor-white-list',
        path: '/monitor/white-list',
        component: './monitor/whiteList',
        hideInMenu: isIntl,
      },
      {
        name: 'monitor-query-db',
        path: '/monitor/query-db',
        component: './monitor/queryDatabase',
      },
      {
        name: 'monitor-vip-config',
        path: '/monitor/vip-config',
        component: './monitor/vipConfig',
      },
      {
        name: 'monitor-provider-quality',
        path: '/monitor/provider-quality',
        component: './monitor/providerQuality',
      },
    ],
  },
  {
    path: '/cmpp',
    name: 'cmpp',
    hideInMenu: isIntl,
    routes: [
      {
        path: '/cmpp',
        redirect: '/cmpp/list',
      },
      {
        name: 'cmpp-list',
        path: '/cmpp/list',
        component: './cmpp/accountList',
      },
      {
        name: 'cmpp-account',
        path: '/cmpp/account',
        component: './cmpp/addAccount',
      },
      {
        name: 'cmpp-apList',
        path: '/cmpp/apList',
        component: './cmpp/apList',
      },
    ],
  },
  {
    path: '/group',
    name: 'group',
    hideInMenu: isIntl,
    routes: [
      {
        path: '/group',
        redirect: '/group/direct-customer',
      },
      {
        name: 'group-direct-customer',
        path: '/group/direct-customer',
        component: './group/directChannelManage',
      },
      {
        name: 'group-direct-customer-appList',
        path: '/group/direct-customer-appList',
        component: './group/directChannelAppList',
        hideInMenu: true,
        parentKeys: ['/group/direct-customer'],
      },
      {
        name: 'group-direct-customer-detail',
        path: '/group/direct-customer-detail',
        component: './group/directChannelDetail',
        hideInMenu: true,
        parentKeys: ['/group/direct-customer'],
      },
      {
        name: 'group-channel-customer',
        path: '/group/channel-customer',
        component: './group/customChannelManage',
      },
      {
        name: 'group-channel-customer-appList',
        path: '/group/channel-customer-appList',
        component: './group/customChannelAppList',
        hideInMenu: true,
        parentKeys: ['/group/channel-customer'],
      },
      {
        name: 'group-channel-customer-detail',
        path: '/group/channel-customer-detail',
        component: './group/customChannelDetail',
        hideInMenu: true,
        parentKeys: ['/group/channel-customer'],
      },
    ],
  },
  {
    path: '/dispatch',
    name: 'dispatch',
    routes: [
      {
        name: 'dispatch-channel-recommend-query',
        path: '/dispatch/channel-recommend-query',
        component: './dispatch/recommendQuery',
      },
      {
        name: 'dispatch-label-charge',
        path: '/dispatch/label-charge',
        component: './dispatch/labelCharge',
      },
      {
        name: 'dispatch-providerManage',
        path: '/dispatch/providerManage',
        component: './dispatch/providerManage',
        hideInMenu: isIntl,
      },
      {
        name: 'dispatch-providerAccount',
        path: '/dispatch/providerAccount',
        component: './dispatch/providerAccountInfo',
        hideInMenu: isIntl,
      },
      {
        name: 'dispatch-csms-schedule',
        path: '/dispatch/csms-schedule',
        component: './dispatch/providerAccountList',
        hideInMenu: isIntl,
      },
    ],
  },
  {
    path: '/console',
    name: 'console',
    routes: [
      {
        path: '/console',
        redirect: '/console/limitSetting',
      },
      {
        name: 'console-limitSetting',
        path: '/console/limitSetting',
        component: './console/limitSetting',
      },
      {
        name: 'console-sendSetting',
        path: '/console/sendSetting',
        component: './console/sendSetting',
      },
    ],
  },
  {
    path: '/label',
    name: 'label',
    // redirect: 'sms/send-query',
    routes: [
      {
        path: '/label',
        redirect: '/label/list',
      },
      {
        name: 'label-list',
        path: '/label/list',
        component: './label/labelList',
        authority: ['admin', 'user'],
      },
      {
        name: 'label-bind',
        path: '/label/bind',
        component: './label/bindList',
        authority: ['admin', 'user'],
      },
      {
        name: 'label-group',
        path: '/label/group',
        component: './label/labelGroup',
        authority: ['admin', 'user'],
      },
    ],
  },
  {
    path: '/enterprise',
    name: 'enterprise',
    routes: [
      {
        path: '/enterprise',
        redirect: '/enterprise/audit',
      },
      {
        name: 'enterprise-audit',
        path: '/enterprise/audit',
        component: './enterprise/auditList',
      },
    ],
  },
  {
    path: '/sign',
    name: 'sign',
    routes: [
      {
        path: '/sign',
        redirect: '/sign/weight',
      },
      {
        name: 'sign-weight',
        path: '/sign/weight',
        component: './sign/weightManage',
      },
      {
        name: 'sign-force',
        path: '/sign/force',
        component: './sign/forceDispatch',
      },
      {
        name: 'sign-finance',
        path: '/sign/finance',
        component: './sign/financeWeight',
      },
      {
        name: 'sign-restock',
        path: '/sign/restock',
        component: './sign/restockList',
      },
      {
        name: 'sign-code',
        path: '/sign/code',
        component: './sign/code',
      },
      {
        name: 'sign-manage',
        path: '/sign/manage',
        component: './sign/manage',
      },
      {
        name: 'sign-audit',
        path: '/sign/audit',
        component: './sign/signAudit',
      },
      {
        name: 'sign-port-query-old',
        path: '/sign/port-query-old',
        component: './sign/selfPortQuery',
        hideInMenu: isIntl,
      },
      {
        name: 'sign-port-query',
        path: '/sign/port-query',
        component: './sign/getSelfPort',
        hideInMenu: isIntl,
      },
      {
        name: 'sign-direct-port',
        path: '/sign/direct-port',
        component: './sign/directPortQuery',
      },
      {
        name: 'sign-account-migration-report',
        path: '/sign/account-migration-report',
        component: './sign/accountMigrationReport',
      },
      {
        name: 'sign-sdkappid-provider-report',
        path: '/sign/sdkappid-provider-report',
        component: './sign/sdkappidProviderReport',
      },
      {
        name: 'sign-fixed-sign',
        path: '/sign/fixed-sign',
        component: './sign/fixedSign',
      },
      {
        name: 'sign-dev-fixed-sign',
        path: '/sign/dev-fixed-sign',
        component: './sign/devFixedSign',
      },
      {
        name: 'sign-credential-report',
        path: '/sign/credentialReport',
        component: './sign/credentialReport',
      },
    ],
  },
  {
    path: '/app',
    name: 'app',
    routes: [
      {
        path: '/app',
        redirect: '/app/ipManage',
      },
      {
        name: 'app-ipManage',
        path: '/app/ipManage',
        component: './app/ipManage',
      },
      {
        name: 'app-uplinkPort',
        path: '/app/uplinkPort',
        component: './app/uplinkPort',
      },
      {
        name: 'app-weight',
        path: '/app/weight',
        component: './app/weight',
      },
      {
        name: 'app-batchSchedule',
        path: '/app/batchSchedule',
        component: './app/batchSchedule',
      },
      {
        name: 'app-scheduleConfig',
        path: '/app/scheduleConfig',
        component: './app/scheduleConfig',
        hideInMenu: true,
        parentKeys: ['/app/batchSchedule'],
      },
      {
        name: 'app-freqRules',
        path: '/app/freqRules',
        component: './app/freqRules',
      },
      {
        name: 'app-intlfreq',
        path: '/app/intlfreq',
        component: './app/IntlAreaFreqLimitSetting',
      },
      {
        name: 'app-freqWhite',
        path: '/app/freqWhite',
        component: './app/freqWhite',
      },
      {
        name: 'app-timeControl',
        path: '/app/timeControl',
        component: './app/timeControl',
      },
      {
        name: 'app-intrcptCache',
        path: '/app/intrcptCache',
        component: './app/intrcptCache',
      },
      {
        name: 'app-dealRisk',
        path: '/app/dealRisk',
        component: './app/dealRisk',
      },
      {
        name: 'app-customRemark',
        path: '/app/customRemark',
        component: './app/customRemark',
      },
      {
        name: 'app-largeLog',
        path: '/app/largeLog',
        component: './app/largeLog',
      },
      {
        name: 'app-disableMobile',
        path: '/app/disableMobile',
        component: './app/disableMobile',
      },
      {
        name: 'app-mqRateCtl',
        path: '/app/mqRateCtl',
        component: './app/mqRateCtl',
      },
      {
        name: 'app-cmppReturnCode',
        path: '/app/cmppReturnCode',
        component: './app/cmppReturnCode',
        hideInMenu: isIntl,
      },
      {
        name: 'app-company-info',
        path: '/app/company-info',
        component: './app/companyInfoManage',
      },
      {
        name: 'app-bit-manage',
        path: '/app/bit-manage',
        component: './app/bitManage',
      },
    ],
  },
  {
    path: '/template',
    name: 'template',
    routes: [
      {
        path: '/template',
        redirect: '/template/typeReplace',
      },
      {
        name: 'template-typeReplace',
        path: '/template/typeReplace',
        component: './template/typeReplace',
      },
      {
        name: 'template-filterByTmp',
        path: '/template/filterByTmp',
        component: './template/filterByTmp',
      },
      {
        name: 'template-manage',
        path: '/template/manage',
        component: './template/manage',
      },
      {
        name: 'template-audit',
        path: '/template/audit',
        component: './template/tplAudit',
      },
    ],
  },
  {
    path: '/global',
    name: 'global',
    routes: [
      {
        path: '/global',
        redirect: '/global/forbidProvider',
      },
      {
        name: 'global-forbidProvider',
        path: '/global/forbidProvider',
        component: './global/forbidProvider',
      },
      {
        name: 'global-dispatchIntrvntn',
        path: '/global/dispatchIntrvntn',
        component: './global/dispatchIntrvntn',
      },
      {
        name: 'global-defaultWeight',
        path: '/global/defaultWeight',
        component: './global/defaultWeight',
      },
      {
        name: 'global-dirtyWords',
        path: '/global/dirtyWords',
        component: './global/dirtyWords',
      },
      {
        name: 'global-deliveryStatus',
        path: '/global/deliveryStatus',
        component: './global/deliveryStatus',
      },
      {
        name: 'global-errorStatusCode',
        path: '/global/errorStatusCode',
        component: './global/errorStatusCode',
      },
      {
        name: 'global-forbiddenAppid',
        path: '/global/forbiddenAppid',
        component: './global/forbiddenAppid',
      },
      {
        name: 'global-sendAudit',
        path: '/global/sendAudit',
        component: './global/sendAudit',
      },
      {
        name: 'global-smsPackage',
        path: '/global/smsPackage',
        component: './global/smsPackage',
      },
      {
        name: 'global-abroadPackage',
        path: '/global/abroadPackage',
        component: './global/smsPackage',
      },
      {
        name: 'global-packageConfig',
        path: '/global/packageConfig',
        component: './global/packageConfig',
        hideInMenu: isIntl,
      },
      {
        name: 'global-keeponServing',
        path: '/global/keeponServing',
        component: './global/keeponServing',
      },
      {
        name: 'global-debit',
        path: '/global/debit',
        component: './global/debitList',
      },
      {
        name: 'global-allowReport',
        path: '/global/allowReport',
        component: './global/allowReport',
      },
      {
        name: 'global-urlReg',
        path: '/global/urlReg',
        component: './global/urlReg',
      },
      {
        name: 'global-bindingAppid',
        path: '/global/bindingAppid',
        component: './global/bindingAppid',
      },
      {
        name: 'global-ignoreLog',
        path: '/global/ignoreLog',
        component: './global/ignoreLog',
      },
    ],
  },
  {
    path: '/eb-report',
    name: 'eb-report',
    hideInMenu: isIntl,
    routes: [
      {
        path: '/eb-report',
        redirect: '/eb-report/events',
      },
      {
        name: 'eb-report-events',
        path: '/eb-report/events',
        component: './ebReport/eventsOps',
      },
      {
        name: 'eb-report-error',
        path: '/eb-report/error',
        component: './ebReport/errorEvents',
      },
    ],
  },
  {
    path: '/sender',
    name: 'sender',
    routes: [
      {
        path: '/sender',
        redirect: '/sender/default/manage',
      },
      {
        name: 'sender-default-manage',
        path: '/sender/default/manage',
        component: './sender/defaultSenderManage',
      },
      {
        name: 'sender-mandatory-manage',
        path: '/sender/mandatory/manage',
        component: './sender/mandatorySenderManage',
      },
      {
        name: 'sender-global-manage',
        path: '/sender/global/manage',
        component: './sender/globalSenderManage',
      },
      {
        name: 'sender-userApply-manage',
        path: '/sender/userApply/manage',
        component: './sender/userApplySenderManage',
      },
      {
        name: 'sender-transparent-manage',
        path: '/sender/transparent/manage',
        component: './sender/transparentSenderManage',
      },
      {
        name: 'sender-transparent-country',
        path: '/sender/transparent/country',
        component: './sender/transparentSenderCountry',
      },
      {
        name: 'sender-cover-search',
        path: '/sender/cover/search',
        component: './sender/senderCoverSearch',
      },
      {
        name: 'sender-attachment',
        path: '/sender/attachment',
        component: './sender/senderAttachment',
      },
      {
        name: 'sender-id-resource',
        path: '/sender/sender-id-resourcet',
        component: './sender/senderIdResource/index',
      },
      {
        name: 'sender-up-app-config',
        path: '/sender/up-app-config',
        component: './sender/UpAppConfig',
      },
      {
        name: 'sender-user-transparent-whitelist',
        path: '/sender/user-transparent-whitelist',
        component: './sender/userTransparentWhiteList',
      },
    ],
  },
  {
    path: '/channel',
    name: 'channel',
    routes: [
      {
        path: '/channel',
        redirect: '/channel/sdkappid',
      },
      {
        name: 'channel-sdkappid',
        path: '/channel/sdkappid',
        component: './channel/SdkappidCountryList',
      },
      {
        name: 'channel-sdkappid-config',
        path: '/channel/sdkappid/config',
        component: './channel/SdkappidConfigList',
        hideInMenu: true,
        parentKeys: ['/channel/sdkappid'],
      },
      {
        name: 'channel-uin',
        path: '/channel/uin',
        component: './channel/UinCountryList',
      },
      {
        name: 'channel-uin-config',
        path: '/channel/uin/config',
        component: './channel/UinConfigList',
        hideInMenu: true,
        parentKeys: ['/channel/uin'],
      },
      {
        name: 'channel-uin-ressiue',
        path: '/channel/uin/ressiue',
        component: './channelRessiue/RessiueContainer',
        hideInMenu: true,
        parentKeys: ['/channel/uin/ressiue'],
      },
      {
        name: 'channel-uin-ressiue-view',
        path: '/channel/uin/ressiue-view',
        component: './channelRessiue/ViewSupplyConfigContainer',
        hideInMenu: true,
        parentKeys: ['/channel/uin/ressiue'],
      },
      {
        name: 'channel-sdkappid-ressiue',
        path: '/channel/sdkappid/ressiue',
        component: './channelRessiue/RessiueContainer',
        hideInMenu: true,
        parentKeys: ['/channel/sdkappid'],
      },
      {
        name: 'channel-sdkappid-ressiue-view',
        path: '/channel/sdkappid/ressiue-view',
        component: './channelRessiue/ViewSupplyConfigContainer',
        hideInMenu: true,
        parentKeys: ['/channel/sdkappid/ressiue'],
      },
      {
        name: 'channel-global',
        path: '/channel/global',
        component: './channel/GlobalCountryList',
      },
      {
        name: 'channel-global-config',
        path: '/channel/global/config',
        component: './channel/GlobalConfigList',
        hideInMenu: true,
        parentKeys: ['/channel/global'],
      },
      {
        name: 'channel-force',
        path: '/channel/force',
        component: './channel/ForceCountryList',
      },
      {
        name: 'channel-force-config',
        path: '/channel/force/config',
        component: './channel/ForceConfigList',
        hideInMenu: true,
        parentKeys: ['/channel/force'],
      },
      {
        name: 'channel-group-list',
        path: '/channel/group/list',
        component: './channel/ChannelGroupList',
      },
      {
        name: 'channel-country-strategy-list',
        path: '/channel/country/strategy',
        component: './channel/MutipleCountryBindChannel',
      },
      {
        name: 'channel-group-bind-list',
        path: '/channel/group/bind',
        hideInMenu: true,
        parentKeys: ['/channel/group/list'],
        component: './channel/ChannelGroupBindList',
      },
      {
        name: 'channel-strategy-list',
        path: '/channel/strategy/list',
        component: './channel/ChannelStrategyList',
      },
      {
        name: 'channel-strategy-bind-list',
        path: '/channel/strategy/bind',
        hideInMenu: true,
        parentKeys: ['/channel/strategy/list'],
        component: './channel/ChannelStrategyBindList',
      },
      {
        name: 'channel-offline',
        path: '/channel/offline',
        component: './channel/ChannelOffline',
      },
      {
        name: 'channel-dispatch-info',
        path: '/channel/dispatch/info',
        component: './channel/ChannelDispatchInfo',
      },
      {
        name: 'channel-detection-task',
        path: '/channel/detection/task',
        component: './channel/DetectionTaskManage',
      },
      {
        name: 'channel-detection-detail',
        path: '/channel/detection/detail',
        component: './channel/DetectionTaskDetailAndStatistics',
        hideInMenu: true,
        parentKeys: ['/channel/detection/task'],
      },
      {
        name: 'channel-tactic-cpq',
        path: '/channel/tactic/cpq',
        component: './channel/CpqTacticScheduler',
      },
      {
        name: 'channel-downgrade-policy',
        path: '/channel/downgrade-policy',
        component: './channel/DowngradePolicy',
      },
      {
        name: 'channel-provider-whitelist',
        path: '/channel/provider-whitelist',
        component: './channel/ProviderWhitelist',
      },
    ],
  },
  {
    path: '/isms-permission',
    name: 'isms-permission',
    routes: [
      {
        name: 'isms-permission-sendable-area-control',
        path: '/isms-permission/sendable-area-control',
        routes: [
          {
            name: 'isms-permission-rick-control-region',
            path: '/isms-permission/sendable-area-control/rick-control/region',
            component: './isms-permission/RickControlRegion',
          },
          {
            name: 'isms-permission-rick-control-uin',
            path: '/isms-permission/sendable-area-control/rick-control/uin',
            component: './isms-permission/RickControlUin',
          },
        ],
      },
    ],
  },
  {
    path: '/tactic-resources',
    name: 'tactic-resources',
    hideInMenu: isChina,
    routes: [
      {
        path: '/tactic-resources',
        redirect: '/tactic-resources/resource',
      },
      {
        name: 'tactic-resources-resource',
        path: '/tactic-resources/resource',
        component: './tactic-resources/resourceType',
      },
      {
        name: 'tactic-resources-product',
        path: '/tactic-resources/product',
        component: './tactic-resources/productType',
      },
      {
        name: 'tactic-resources-tactic-conf',
        path: '/tactic-resources/tactic-conf',
        component: './tactic-resources/tacticConf',
      },
      {
        name: 'tactic-resources-lowest-cr',
        path: '/tactic-resources/lowest-conf',
        component: './tactic-resources/tacticLowestCr',
      },
      {
        name: 'tactic-resources-testing-weight',
        path: '/tactic-resources/testing-weight',
        component: './tactic-resources/probeTraficTestingWeightConf',
      },
      {
        name: 'tactic-resources-valid-point-conf',
        path: '/tactic-resources/valid-point-conf',
        component: './tactic-resources/validPointConf',
      },
      {
        name: 'tactic-resources-data-source-conf',
        path: '/tactic-resources/data-source-conf',
        component: './tactic-resources/dataSourceConf',
      },
      {
        name: 'tactic-resources-tactic-sale-product',
        path: '/tactic-resources/tactic-sale-product',
        component: './tactic-resources/tacticSaleProduct',
      },
    ],
  },
  {
    path: '/resources',
    name: 'resources',
    routes: [
      {
        path: '/resources',
        redirect: '/resources/group',
      },
      {
        name: 'resources-group',
        path: '/resources/group',
        component: './resources/GroupInfo',
      },
      {
        name: 'resources-tactic',
        path: '/resources/tactic',
        component: './resources/TacticInfo',
      },
      {
        name: 'resources-operator-group',
        path: '/resources/operator-group',
        component: './resources/OperatorGroupInfo',
      },
      {
        name: 'resources-operator-tactic',
        path: '/resources/operator-tactic',
        component: './resources/OperatorTacticInfo',
      },
      {
        name: 'resources-operator-tactic-ressiue',
        path: '/resources/operator-tactic/ressiue',
        component: './channelRessiue/RessiueContainer',
        hideInMenu: true,
        parentKeys: ['/resources/operator-tactic'],
      },
    ],
  },
  {
    path: '/intl-resource',
    name: 'intl-resource',
    routes: [
      {
        path: '/intl-resource',
        redirect: '/intl-resource/manage',
      },
      {
        name: 'intl-resource-smpp-account',
        path: '/intl-resource/smppAccount',
        component: './intl-resource/accountManage',
      },
      {
        name: 'intl-resource-price-diff',
        path: '/intl-resource/priceAttributeLog',
        component: './intl-resource/priceAttributeLog',
      },
    ],
  },
  {
    path: '/segment',
    name: 'segment',
    routes: [
      {
        path: '/segment',
        redirect: '/segment/manage',
      },
      {
        name: 'segment-manage',
        path: '/segment/manage',
        component: './segment/manage',
      },
      {
        name: 'segment-mnc-manage',
        path: '/segment/mncManage',
        component: './segment/MncManage',
      },
      {
        name: 'segment-mnc-segment',
        path: '/segment/mncSegment',
        component: './segment/MncSegment',
      },
    ],
  },
  {
    path: '/quality',
    name: 'quality',
    routes: [
      {
        path: '/quality',
        redirect: '/quality/query',
      },
      {
        name: 'quality-query',
        path: '/quality/query',
        component: './quality/queryEnableStrategy',
      },
      {
        name: 'quality-update',
        path: '/quality/update',
        component: './quality/updateEnableStrategy',
      },
    ],
  },
  {
    path: '/yeheAlarm',
    name: 'yehe-alarm',
    component: './yeheAlarm/index',
    hideInMenu: isIntl,
  },
  {
    path: '/issue',
    name: 'issue',
    component: './issue/index',
  },
  {
    path: '/system',
    name: 'system',
    routes: [
      {
        path: '/system',
        redirect: '/system/userSetting',
      },
      {
        name: 'system-userSetting',
        path: '/system/userSetting',
        component: './system/userSetting',
      },
      {
        name: 'system-userSetting-authList',
        path: '/system/userSetting-authList',
        component: './system/userSetting/userAuthList',
        hideInMenu: true,
        parentKeys: ['/system/userSetting'],
      },
      {
        name: 'system-roleSetting',
        path: '/system/roleSetting',
        component: './system/roleSetting',
      },
      {
        name: 'system-roleSetting-authList',
        path: '/system/roleSetting-authList',
        component: './system/roleSetting/roleAuthList',
        hideInMenu: true,
        parentKeys: ['/system/roleSetting'],
      },
      {
        name: 'system-apiSetting',
        path: '/system/apiSetting',
        component: './system/apiSetting',
      },
    ],
    // component: './system/authority'
  },
  {
    path: '/audit',
    name: 'audit',
    routes: [
      {
        path: '/audit',
        redirect: '/audit/list',
      },
      {
        name: 'audit-list',
        path: '/audit/list',
        component: './audit/auditList',
      },
    ],
  },
  {
    path: '/profit-loss',
    name: 'profit-loss',
    hideInMenu: isIntl,
    routes: [
      {
        path: '/profit-loss',
        redirect: '/profit-loss/subsidy',
      },
      {
        name: 'profit-loss-subsidy',
        path: '/profit-loss/subsidy',
        component: './profit-loss/subsidy',
      },
    ],
  },
  // {
  //   path: '/smsAudit',
  //   name: 'smsAudit',
  //   routes: [
  //     {
  //       path: '/',
  //       redirect: '/smsAudit/sign',
  //     },
  //     {
  //       name: 'smsAudit-sign',
  //       path: '/smsAudit/sign',
  //       component: './smsAudit/sign'
  //     },
  //     {
  //       name: 'smsAudit-template',
  //       path: '/smsAudit/template',
  //       component: './smsAudit/template'
  //     },
  //   ]
  // },
  {
    name: 'exception',
    icon: 'warning',
    hideInMenu: true,
    path: '/exception',
    routes: [
      // {
      //   path: '',
      //   redirect: 'exception/403',
      // },
      {
        name: '403',
        icon: 'smile',
        path: '/exception/403',
        component: './exception/403',
      },
      {
        name: '404',
        icon: 'smile',
        path: '/exception/404',
        component: './exception/404',
      },
      {
        name: '500',
        icon: 'smile',
        path: '/exception/500',
        component: './exception/500',
      },
    ],
  },
  { path: '/*', component: '@/layouts/BasicLayout' },
];
