export default {
  index: ['GET:/admin/user/get-config-list'],
  // 基础接口
  // 'user-config': ['/admin/user/get-config-list'],
  'sms-send-query': ['GET:/sms/detail/get-down-info'],
  'sms-replay-query': ['GET:/sms/detail/get-up-info'],
  'sms-payment-query': ['GET:/sms/paid/get-list'],
  'sms-sdkappid-query': ['GET:/sms/app/get-app-info'],
  'sms-sdkappid-detail': ['GET:/sms/app/get-app-detail'],
  // 'sms-send-task': [], // 待定
  'sms-daily-query': ['GET:/sms/amount/get-daily-amount'],
  'sms-tci-query': ['GET:/sms/tci-log/query'],
  'sms-fail-reason': ['GET:/sms/err-anaylysis/query'],
  'sms-intl-query': ['GET:/sms/abroad-sms/query'],
  'sms-unbackfill': ['GET:/sms/unfilled-phone/query'],
  'sms-undelivery': ['GET:/sms/undelivery-phone/query'],
  'sms-schedule-info': ['GET:/sms/channel-scheduling/query'],

  'sms-up-app-search': ['GET:/abroad-scheduler/up-app/get-record-list'],

  'vms-send-query': ['GET:/voice/detail/get-down-info'],
  'vms-send-new-query': ['GET:/voice/detail/get-new-info'],
  'vms-number-query': ['GET:/voice/number/get-number-info'],
  'monitor-console-log': ['GET:/admin/record/get-console-list'],
  'monitor-config-log': ['GET:/admin/record/get-list'],
  'monitor-modify-dispatch': [
    'GET:/monitor/supplier/get-list',
    'GET:/monitor/monitor-dispatch/get-default-conf',
  ],
  'monitor-query-dispatch': [
    'GET:/monitor/supplier/get-list',
    'GET:/monitor/monitor-dispatch/get-sup-conf',
    'GET:/monitor/monitor-dispatch/get-sup-tel-conf',
    'GET:/monitor/monitor-dispatch/get-sup-app-conf',
    'GET:/monitor/monitor-dispatch/get-sup-app-tel-conf',
    'GET:/monitor/monitor-dispatch/get-sup-qapp-conf',
    'GET:/monitor/monitor-dispatch/get-sup-qapp-tel-conf',
  ],
  'monitor-modify-market': [
    'GET:/monitor/supplier/get-list',
    'GET:/monitor/market-monitor-dispatch/get-default-conf',
  ],
  'monitor-query-market': [
    'GET:/monitor/supplier/get-list',
    'GET:/monitor/market-monitor-dispatch/get-sup-conf',
    'GET:/monitor/market-monitor-dispatch/get-sup-tel-conf',
    'GET:/monitor/market-monitor-dispatch/get-sup-app-conf',
    'GET:/monitor/market-monitor-dispatch/get-sup-qapp-conf',
  ],
  'monitor-reply-check': ['GET:/monitor/check-reply-extend/get-list'],
  'monitor-white-list': ['GET:/monitor/sdkappid-white/search'],
  'monitor-query-db': [
    'GET:/monitor/sdkappid-white/search-smslog',
    'GET:/monitor/sdkappid-white/search-antibomb',
  ],
  'monitor-vip-config': ['GET:/monitor/vip-alarm/search'],
  'monitor-provider-quality': ['GET:/monitor/provider-quality/get-list'],
  'cmpp-list': ['GET:/cmpp/account/query'],
  'cmpp-account': ['POST:/cmpp/account/add'],
  'cmpp-apList': ['GET:/cmpp/access-point/query'],
  'group-direct-customer': ['GET:/channal-group/group/list'],
  'group-direct-customer-detail': ['GET:/channal-group/group/group-info'],
  'group-direct-customer-appList': ['GET:/channal-group/group/app-list'],
  'group-channel-customer': ['GET:/canal-channal-group/group/list'],
  'group-channel-customer-appList': ['GET:/canal-channal-group/group/app-list'],
  'group-channel-customer-detail': [
    'GET:/canal-channal-group/group/provider-list',
    'GET:/canal-channal-group/group/get-unassigned-provider',
    'POST:/canal-channal-group/group/delete-provider',
    'POST:/canal-channal-group/group/edit',
    'POST:/canal-channal-group/group/add-provider',
    'POST:/canal-channal-group/group/edit-provider',
    'POST:/canal-channal-group/group/group-info',
  ],
  'dispatch-channel-recommend-query': ['GET:/channel/recommend/query'],
  'dispatch-label-charge': ['GET:/global-setting/billing-type/get-list'],
  'dispatch-providerManage': ['GET:/csms-scheduler/provider-primary/get-list'],
  'dispatch-providerAccount': ['GET:/csms-scheduler/provider-inner-account/get-list'],
  'dispatch-csms-schedule': ['GET:/csms-scheduler/provider-account/get-list'],
  'console-limitSetting': ['GET:/sms/qappid-max-number/get'],
  'console-sendSetting': ['GET:/sms/app/get-app-freq-limit'],
  'label-list': ['GET:/label/label-info/query'],
  'label-group': ['GET:/label/label-group/query'],
  'label-bind': [
    'GET:/label/bind-conf/query',
    'GET:/label/label-info/query',
    'GET:/label/object/get-client-list',
    'GET:/label/object/get-scheduler-list',
    'GET:/label/object/get-sign-listy',
  ],
  'label-charge': ['GET:/global-setting/billing-type/get-list'],
  'segment-manage': ['GET:/number-segment/valid-prefix/get-list'],
  'segment-mnc-segment': ['GEt:/number-segment/phone-prefix-operator/get-list'],
  'segment-mnc-manage': ['GET:/number-segment/operator-network/get-list'],
  'quality-query': ['GET:/schedule-strategy/supplier/get-list'],
  'quality-update': [
    'POST:/schedule-strategy/supplier/save',
    'POST:/schedule-strategy/supplier-country/save',
    'POST:/schedule-strategy/supplier-uin/save',
    'POST:/schedule-strategy/supplier-uin-country/save',
    'POST:/schedule-strategy/default/save',
  ],
  'sign-weight': ['GET:/sign/weight/weight-list'],
  'sign-force': ['GET:/sign/force/get-list'],
  'sign-finance': ['GET:/sign/weight/market-weight-list'],
  'sign-restock': ['GET:/sign/unsubscribe/query'],
  'sign-code': ['GET:/sign/appsign/get-list'],
  'sign-manage': ['GET:/sign/sign/get-list'],
  'sign-audit': ['POST:/sign/reset-sign/find-sign'],
  'sign-port-query-old': ['GET:/sms/self-sign-port/query'],
  'sign-port-query': ['GET:/sms/self-sign-port/get-list'],
  'app-ipManage': ['GET:/application/ip/get-list'],
  'app-uplinkPort': ['GET:/application/port/get-list'],
  'app-weight': ['GET:/sms/weight/get-list'],
  'app-batchSchedule': ['GET:/sms/weight/batch-query'],
  'app-scheduleConfig': ['POST:/sms/weight/batch-insert'],
  'app-freqRules': ['GET:/application/frq/get-list'],
  'app-freqWhite': ['GET:/application/frqwhite/get-list'],
  'app-intlfreq': ['GET:/sms/app/get-country-freq-rule'],
  'app-timeControl': [
    'GET:/application/bizsms/get-allow-time',
    'GET:/application/bizsms/allow-app-list',
  ],
  'app-intrcptCache': ['GET:/application/bizsms/hold-app-list'],
  'app-dealRisk': ['GET:/application/deal-risky-phone/get-list'],
  'app-customRemark': ['GET:/application/qapp-remark/get-list'],
  'app-largeLog': ['GET:/application/app-large-log/get-list'],
  'app-disableMobile': ['GET:/application/app-disable-mobile-query/get-list'],
  'app-mqRateCtl': ['GET:/application/mq-rate-ctl/get-list'],
  'app-cmppReturnCode': ['GET:/application/sign-hardcode-srcid/get-list'],
  'app-bit-manage': ['GET:/sms/app/get-common-bit-define'],
  'template-typeReplace': ['GET:/template/sms-type-replace/get-replace-list'],
  'template-filterByTmp': ['GET:/template/sms-template-filter/get-filter-list'],
  'template-manage': ['GET:/template/template/get-template-list'],
  'template-audit': ['POST:/template/reset-template/find-template'],
  'global-forbidProvider': ['GET:/global-setting/forbid-provider/get-list'],
  'global-dispatchIntrvntn': ['GET:/global-setting/dispatch-intervention/get'],
  'global-defaultWeight': ['GET:/global-setting/default-weight/get-list'],
  'global-dirtyWords': ['GET:/global-setting/dirty-word/get-list'],
  'global-deliveryStatus': ['GET:/global-setting/delivery-status/get-list'],
  'global-forbiddenAppid': ['GET:/global-setting/forbidden-appid/get-list'],
  'global-sendAudit': ['GET:/global-setting/send-task-audit/get-list'],
  'global-smsPackage': ['GET:/global-setting/sms-package/get-list'],
  'global-abroadPackage': ['GET:/global-setting/abroad-package/get-list'],
  'global-packageConfig': ['GET:/global-setting/sms-package-config/get-list'],
  'global-keeponServing': ['GET:/global-setting/keepon-serving/get-list'],
  'global-debit': ['GET:/global-setting/need-report-appid-items/get-list'],
  'global-allowReport': ['GET:/global-setting/allow-report/get-list'],
  'global-urlReg': ['GET:/global-setting/url-regex/get-list'],
  'global-bindingAppid': ['GET:/global-setting/provider-bind-app/get-list'],
  'global-ignoreLog': ['GET:/global-setting/ignore-log-appid/get-list'],
  'global-errorStatusCode': ['GET:/global-setting/error-status-code/get'],

  'eb-report-events': ['GET:/eb/events-ops/get-list'],
  'eb-report-error': ['GET:/eb/breakdown-events/get-list'],
  'sender-default-manage': ['GET:/sender/default-sender/get-list'],
  'sender-mandatory-manage': ['GET:/sender/mandatory-sender/get-list'],
  'sender-global-manage': ['GET:/sender/global-sender/get-list'],
  'sender-userApply-manage': ['GET:/sender/user-apply-sender/get-list'],
  'sender-transparent-manage': ['GET:/sender/transparent-sender/get-list'],
  'sender-transparent-country': ['GET:/sender/transparent-country/get-list'],
  'sender-cover-search': ['GET:/sender/check-sender-resource/query'],
  'sender-attachment': ['GET:/sender/register-fields/get-list'],
  'sender-id-resource': ['GET:/sender/resource/get-list'],
  'sender-up-app-config': ['GET:/abroad-scheduler/up-app/get-list'],
  'channel-sdkappid': ['GET:/abroad-scheduler/sdk-channel/get-list'],
  'channel-sdkappid-config': [
    'POST:/abroad-scheduler/sdk-channel/add',
    'POST:/abroad-scheduler/sdk-channel/edit',
    'POST:/abroad-scheduler/sdk-channel/delete',
  ],
  'channel-uin': ['GET:/abroad-scheduler/uin-channel/get-list'],
  'channel-uin-config': [
    'POST:/abroad-scheduler/uin-channel/add',
    'POST:/abroad-scheduler/uin-channel/edit',
    'POST:/abroad-scheduler/uin-channel/delete',
  ],
  'channel-global': ['GET:/abroad-scheduler/overall-channel/get-country-list'],
  'channel-global-config': [
    'POST:/abroad-scheduler/global-channel/add',
    'POST:/abroad-scheduler/global-channel/edit',
    'POST:/abroad-scheduler/global-channel/delete',
  ],
  'channel-force': ['GET:/abroad-scheduler/force-scheduler/get-country-list'],
  'channel-force-config': [
    'POST:/abroad-scheduler/force-channel/add',
    'POST:/abroad-scheduler/force-channel/edit',
    'POST:/abroad-scheduler/force-channel/delete',
  ],
  'channel-group-list': ['GET:/abroad-scheduler/provider-group/get-list'],
  'channel-group-bind-list': ['GET:/abroad-scheduler/provider-group/get-bind-list'],
  'channel-country-strategy-list': [
    'POST:/abroad-scheduler/provider-group-tactic/batch-channel-conf',
  ],
  'channel-strategy-list': ['GET:/abroad-scheduler/provider-group-tactic/get-list'],
  'channel-strategy-bind-list': ['GET:/abroad-scheduler/provider-group-tactic/get-bind-list'],
  'channel-offline': ['GET:/abroad-scheduler/channel-offline/get-list'],
  'channel-dispatch-info': ['GET:/abroad-scheduler/channel-offline/get-scheduler-info'],
  'channel-tactic-cpq': ['GET:/abroad-scheduler/tactic-cpq-record/get-list'],

  'channel-downgrade-policy': ['GET:/abroad-scheduler/downgrade-policy/get-list'],
  'channel-provider-whitelist': ['GET:/abroad-scheduler/provider-whitelist/get-list'],

  'tactic-resources-data-source-conf': ['GET:/abroad-scheduler/data-source-conf/get-list'],
  'tactic-resources-valid-point-conf': ['GET:/abroad-scheduler/valid-point-conf/get-list'],
  'tactic-resources-testing-weight': [
    'GET:/abroad-scheduler/probe-trafic-testing-weight-conf/get-list',
  ],
  'tactic-resources-lowest-cr': ['GET:/abroad-scheduler/country-operator-min-cr-conf/get-list'],
  'tactic-resources-tactic-conf': ['GET:/abroad-scheduler/tactic-conf/get-list'],
  'tactic-resources-product': ['GET:/abroad-scheduler/product-type/get-list'],
  'tactic-resources-resource': ['GET:/abroad-scheduler/resource-type/get-list'],
  'tactic-resources-tactic-sale-product': ['GET:/abroad-scheduler/tactic-sale-product/get-list'],
  'resources-group': ['GET:/abroad-scheduler/provider-resources/get-group-resources'],
  'resources-tactic': ['GET:/abroad-scheduler/provider-resources/get-tactic-resources'],
  'resources-operator-group': [
    'GET:/abroad-scheduler/provider-operator-resources/get-group-resources',
  ],
  'resources-operator-tactic': [
    'GET:/abroad-scheduler/provider-operator-resources/get-tactic-resources',
  ],
  'intl-resource-smpp-account': ['GET:/purchase/account/get-list'],
  'intl-resource-price-diff': ['GET:/abroad-scheduler/utils/get-price-attribute-change-log'],
  'yehe-alarm': ['GET:/yehe/send-msg/get-list'],
  'system-userSetting': ['GET:/admin/user/get-user-list'],
  'system-userSetting-authList': ['GET:/admin/auth/get-conf-list'],
  'system-roleSetting': ['GET:/admin/role/get-list'],
  'system-roleSetting-authList': ['GET:/admin/auth/get-conf-list'],
  'system-apiSetting': ['GET:/admin/api/add'],
  'audit-list': ['GET:/admin/audit/get-list'],
  'profit-loss-subsidy': ['GET:/profit-loss/cost-subsidy/get-list'],
  issue: ['GET:/sms/issue/get-list'],
  'app-company-info': ['GET:/application/company-info/get-list'],
  'sign-direct-port': ['GET:/sms/direct-port/get-list'],
  'sign-account-migration-report': ['POST:/sign/sign-reporting/account-migration-report'],
  'sign-sdkappid-provider-report': ['POST:/sign/sign-reporting/sdkappid-provider-report'],

  'sign-fixed-sign': ['GET:/csms-scheduler/fixed-signature/get-list'],
  'sign-dev-fixed-sign': ['GET:/csms-scheduler/fixed-signature/get-list'],

  'isms-permission-rick-control-region': [
    'GET:/isms-rick-control/region/get-list',
    'GET:/isms-rick-control/region-uin/get-list',
  ],
  'isms-permission-rick-control-uin': ['GET:/isms-rick-control/uin-region/get-list'],
  'enterprise-audit': ['GET:/credential/auth/get-list'],
  'sign-credential-report': ["GET:/sign/sign-reporting/get-list"],
  'sender-user-transparent-whitelist': ['GET:/sender/white-user-sender/get-list']

// append plop config
};
