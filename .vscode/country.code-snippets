{"国家数据": {"prefix": "country-data", "body": ["const { regionOptions } = useFetchCountryInfo();", ""], "description": "国家数据"}, "国家数据选择或输入": {"prefix": "country-select-with-input", "body": ["<SelectOptionsByInput", "  mode=\"multiple\"", "  showSearch", "  allowClear", "  placeholder=\"国家/地区\"", "  filterOption={(val: string, opt: any) =>", "    opt.label.toLowerCase().includes(val.toLowerCase())", "  }", "  options={regionOptions}", "  maxTagCount={30}", "></SelectOptionsByInput>"], "description": "国家数据选择或输入"}, "国家数据选择": {"prefix": "country-select", "body": ["<Select", "  showSearch", "  allowClear", "  placeholder=\"国家/地区\"", "  options={regionOptions}", "  filterOption={(input, option) => {", "    return (", "      option?.label.includes(input) ||", "      option?.value.includes(input.toLocaleUpperCase()) ||", "      false", "    );", "  }}", "  style={{ width: 250 }}", "/>"], "description": "国家数据选择"}}