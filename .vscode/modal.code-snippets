{"antd基础弹窗": {"prefix": "modal-simple", "body": ["import { useDialog } from '@/utils/react-use/useDialog';", "import { But<PERSON>, message, Modal } from 'antd';", "import React, { useMemo, useState } from 'react';", "", "export function Dialog(props: { dialogRef: any; onSuccess?: () => void }) {", "  const { dialogRef, onSuccess } = props;", "", "  const [visible, setVisible, defaultValue] = useDialog(dialogRef);", "  const [loading, setLoading] = useState(false);", "", "  return (", "    <Modal", "      title=\"title\"", "      open={visible}", "      width={1000}", "      onCancel={() => setVisible(false)}", "      footer={[]}", "    >", "      <></>", "    </Modal>", "  );", "}", ""], "description": "antd基础弹窗"}}