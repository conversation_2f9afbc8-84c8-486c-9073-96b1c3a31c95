server {
  listen 80;
  listen [::]:80;

  # china server name
  server_name _;

  #charset koi8-r;
  access_log logs/host.access.log;
  error_log logs/error.log;

  location ^~ /apis/ {
    proxy_pass http://************;
    rewrite /apis/(.*) /$1 break;
  }


  location / {
    root /usr/share/nginx/html;
    try_files $uri /index.html;
    if ($request_filename ~* .*\.(?:htm|html)$) {
      add_header Cache-Control 'no-store, no-cache';
    }
    if ($request_filename ~* .*\.(?:js|css)$) {
      expires 7d;
    }
    if ($request_filename ~* .*\.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm)$) {
      expires 7d;
    }
    index index.html index.htm;
  }

  #error_page  404              /404.html;

  # redirect server error pages to the static page /50x.html
  #
  error_page 500 502 503 504 /50x.html;
  location = /50x.html {
    root /usr/share/nginx/html;
  }
}
