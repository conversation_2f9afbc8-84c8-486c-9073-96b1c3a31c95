import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  deleteProductType,
  getProductType,
  addProductType,
  editProductType,
} from '@/services/tacticResources';

const {{name}} = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>();
  const [type, setType] = useState<'create' | 'edit'>('create');

  function handleEdit(row: any) {
    addFormRef.current?.setFieldsValue({
      ...row,
    });
    setInitialValues({
      ...row,
    });
    setOpen(true);
    setType('edit');
  }

  async function handleDelete(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除该条数据吗?`,
        onOk: async () => {
          const res = await deleteProductType({
            id: row.id,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: 'id',
        dataIndex: 'id',
        key: 'id',
        hideInSearch: true,
        // 只读
        proFieldProps: {
          mode: 'read',
        },
      },
      {
        title: 'name',
        dataIndex: 'name',
        key: 'name',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        hideInSearch: true,
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        hideInSearch: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleEdit(row)}>
                编辑
              </Button>
              <Button type="link" onClick={() => handleDelete(row)}>
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, []);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getProductType({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'create'
          ? await addProductType({
              ...vals,
            })
          : await editProductType({
              ...vals,
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const keys = type === 'create' ? ['name'] : ['name', 'id'];
    return columns.filter((el) => keys.includes(el.key));
  }, [columns, type]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      name: '',
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
  <PageContainer>
      <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol=\{{ span: 4 }}
        onFinish={onFinish}
        modalProps=\{{ maskClosable: false }}
        columns={formItems}
        width={450}
        initialValues=\{{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setOpen(true);
        }}
      >
        新增
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination=\{{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search=\{{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default {{name}};
