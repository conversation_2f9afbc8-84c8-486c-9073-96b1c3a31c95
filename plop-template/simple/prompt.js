const handlebars = require('handlebars');
const { toComponentName } = require('../utils');
const _ = require('lodash');

handlebars.registerHelper('split', (string) => {
  return string.split();
});

// 辅助函数1：判断是否相等
handlebars.registerHelper('eq', (a, b) => {
  return a === b;
});

// 辅助函数3：将 URL 转换为函数名（例如 "/api/user" → "getUser"）
handlebars.registerHelper('toApiName', (url) => {
  // 示例逻辑：移除斜杠和"api"，转换为驼峰命名
  return url
    .replace(/^\/api\//, '')
    .split('/')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')
    .replace(/^./, (match) => match.toLowerCase());
});

module.exports = {
  description: 'generate a s simple page',
  prompts: [
    {
      type: 'input',
      name: 'path',
      message: '文件路径',
      validate: (v) => {
        return !v || v.trim() === '' ? `path is required` : true;
      },
    },
    // {
    //   type: 'input',
    //   name: 'componentName',
    //   message: '组件名',
    // },
    {
      type: 'input',
      name: 'api',
      message: 'api',
    },
    {
      type: 'input',
      name: 'menu',
      message: '菜单名',
    },
    {
      type: 'input',
      name: 'permission',
      message: 'permission api',
    },
  ],

  actions: (data) => {
    const { path, api, menu, permission } = data;
    const componentName = toComponentName(`${path.split('/').join('-')}`);
    const apiPath = path.split('/');
    const apiList = api.split(' ').map((item) => {
      const [method, url] = item.split(':');
      return { method, url };
    });
    const [parentRouteName, routeName, menuName] = menu.split(' ');
    const permissionList = permission.split(' ').map((item) => {
      const [method, url] = item.split(':');
      return `${_.toUpper(method)}:${url}`;
    });

    const actions = [
      {
        type: 'add',
        path: `src/pages/${path}.tsx`,
        templateFile: 'plop-template/simple/index.hbs',
        data: {
          name: componentName,
        },
      },
      {
        type: 'add',
        path: `src/services/${apiPath[apiPath.length - 1]}.ts`,
        templateFile: 'plop-template/create-api/index.hbs',
        data: {
          apiList,
        },
      },
      {
        type: 'modify',
        path: 'src/locales/zh-CN/menu.ts',
        pattern: /\/\/ append plop config/,
        templateFile: 'plop-template/modify-menu/index.hbs',
        data: {
          parentRouteName,
          routeName,
          menuName,
        },
      },
      {
        type: 'modify',
        path: 'config/permission.ts',
        pattern: /\/\/ append plop config/,
        templateFile: 'plop-template/modify-permission/index.hbs',
        data: {
          routeName,
          permissionList,
        },
      },
    ];

    return actions;
  },
};
