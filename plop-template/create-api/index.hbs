import request from '@/utils/request';
import _ from 'lodash';

{{#each apiList as |item index|}}
  {{#if (eq item.method "get")}}
    export async function {{toApiName item.url}}(params?: any): Promise<any> {
      return request('{{item.url}}', {
      params: _.pickBy(params, (v) => v !== ''),
      });
      }
  {{else if (eq item.method "post")}}
    export async function {{toApiName item.url}}(data: any): Promise<any> {
      return request('{{item.url}}', {
      method: 'post',
      data: { ...data },
      });
      }
  {{/if}}
{{/each}}