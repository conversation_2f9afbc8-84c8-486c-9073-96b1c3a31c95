const toApiName = (name) => {
  const nameArray = name.split('-');
  return nameArray
    .map(
      (item, index) =>
        `${index === 0 ? item.charAt(0) : item.charAt(0).toUpperCase()}${item.substr(1)}`,
    )
    .join('');
};

const toComponentName = (name) => {
  const nameArray = name.split('-');
  return nameArray.map((item) => `${item.charAt(0).toUpperCase()}${item.substr(1)}`).join('');
};

module.exports = {
  toApiName,
  toComponentName,
};
