import request from '@/utils/request';
import _ from 'lodash';
import qs from 'query-string';

export async function getFixedSignature(params?: any): Promise<any> {
  return request('/csms-scheduler/fixed-signature/get-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function deleteFixedSignature(data: any): Promise<any> {
  return request('/csms-scheduler/fixed-signature/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function addFixedSignature(data: any): Promise<any> {
  return request('/csms-scheduler/fixed-signature/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function getAllSignature(params?: any): Promise<any> {
  return request(
    `/csms-scheduler/fixed-signature/get-all-signature?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

export async function judgeSignature(params?: any): Promise<any> {
  return request(
    `/csms-scheduler/fixed-signature/judge-signature?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

export async function getProviderChannelList(params?: any): Promise<any> {
  return request('/csms-scheduler/fixed-signature/provider-channel-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function getSchedulerApiConfigList(params?: any): Promise<any> {
  return request('/csms-scheduler/fixed-signature/scheduler-apiconfig-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function disabledFixedSignature(data: any): Promise<any> {
  return request('/csms-scheduler/fixed-signature/disabled', {
    method: 'post',
    data: { ...data },
  });
}
