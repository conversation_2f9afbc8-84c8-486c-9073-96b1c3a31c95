import request from '@/utils/request';
import qs from 'qs';
// 查询默认sender列表
export async function getDefaultSenderList(params?: any): Promise<any> {
  return request('/sender/default-sender/get-list', { params: { ...params } });
}

// 新增默认sender
export async function addDefaultSender(data: any): Promise<any> {
  return request('/sender/default-sender/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑默认sender
export async function editDefaultSender(data: any): Promise<any> {
  return request('/sender/default-sender/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 删除默认sender
export async function deleteDefaultSender(data: any): Promise<any> {
  return request('/sender/default-sender/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 查询全局sender列表
export async function getGlobalSenderList(params?: any): Promise<any> {
  return request('/sender/global-sender/get-list', { params: { ...params } });
}

// 新增全局sender
export async function addGlobalSender(data: any): Promise<any> {
  return request('/sender/global-sender/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑全局sender
export async function editGlobalSender(data: any): Promise<any> {
  return request('/sender/global-sender/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 删除全局sender
export async function deleteGlobalSender(data: any): Promise<any> {
  return request('/sender/global-sender/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 查询强制sender列表
export async function getMandatorySenderList(params?: any): Promise<any> {
  return request(
    `/sender/mandatory-sender/get-list?${qs.stringify(params, { arrayFormat: 'brackets' })}`,
  );
}

// 新增强制sender
export async function addMandatorySender(data: any): Promise<any> {
  return request('/sender/mandatory-sender/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑强制sender
export async function editMandatorySender(data: any): Promise<any> {
  return request('/sender/mandatory-sender/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 删除强制sender
export async function deleteMandatorySender(data: any): Promise<any> {
  return request('/sender/mandatory-sender/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 查询透传sender列表
export async function getTransparentSenderList(params?: any): Promise<any> {
  return request('/sender/transparent-sender/get-list', { params: { ...params } });
}

// 新增透传sender
export async function addTransparentSender(data: any): Promise<any> {
  return request('/sender/transparent-sender/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑透传sender
export async function editTransparentSender(data: any): Promise<any> {
  return request('/sender/transparent-sender/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 删除透传sender
export async function deleteTransparentSender(data: any): Promise<any> {
  return request('/sender/transparent-sender/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 查询客户可用sender列表
export async function getUserApplySenderList(params?: any): Promise<any> {
  return request('/sender/user-apply-sender/get-list', { params: { ...params } });
}

// 新增客户可用sender
export async function addUserApplySender(data: any): Promise<any> {
  return request('/sender/user-apply-sender/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑客户可用sender
export async function editUserApplySender(data: any): Promise<any> {
  return request('/sender/user-apply-sender/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 删除客户可用sender
export async function deleteUserApplySender(data: any): Promise<any> {
  return request('/sender/user-apply-sender/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 查询客户可透传sender列表
export async function getUserTransportSenderList(params?: any): Promise<any> {
  return request('/sender/user-transport-sender/get-list', { params: { ...params } });
}

// 新增客户可透传sender
export async function addUserTransportSender(data: any): Promise<any> {
  return request('/sender/user-transport-sender/add', {
    method: 'post',
    data: { ...data },
  });
}

// 删除客户可透传sender
export async function deleteUserTransportSender(data: any): Promise<any> {
  return request('/sender/user-transport-sender/delete', {
    method: 'post',
    data: { ...data },
  });
}

// sender可透传国家/运营商
export async function getTransparentCountryList(params?: any): Promise<any> {
  return request('/sender/transparent-country/get-list', { params: { ...params } });
}

export async function addTransparentCountry(data: any): Promise<any> {
  return request('/sender/transparent-country/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteTransparentCountry(data: any): Promise<any> {
  return request('/sender/transparent-country/delete', {
    method: 'post',
    data: { ...data },
  });
}

// sender未覆盖查询
export async function queryCoverSender(params?: any): Promise<any> {
  return request('/sender/check-sender-resource/query', { params: { ...params } });
}

// 获取sender支持的附件类型
export async function querySenderRegisterFields(params?: any): Promise<any> {
  return request('/sender/register-fields/get-field-list', { params: { ...params } });
}

// 获取sender资料配置
export async function querySenderAttachment(params?: any): Promise<any> {
  return request('/sender/register-fields/get-list', { params: { ...params } });
}

// 新增sender资料配置
export async function addSenderAttachment(data: any): Promise<any> {
  return request('/sender/register-fields/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑sender资料配置
export async function editSenderAttachment(data: any): Promise<any> {
  return request('/sender/register-fields/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 删除sender资料配置
export async function deleteSenderAttachment(data: any): Promise<any> {
  return request('/sender/register-fields/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 获取senderid资源
export async function querySenderIdResource(params?: any): Promise<any> {
  return request(`/sender/resource/get-list?${qs.stringify(params, { arrayFormat: 'brackets' })}`);
}

// 新增senderid资源
export async function addSenderIdResource(data: any): Promise<any> {
  return request('/sender/resource/add', {
    method: 'post',
    data: { ...data },
  });
}
// 删除senderid资源
export async function deleteSenderIdResource(data: any): Promise<any> {
  return request('/sender/resource/delete', {
    method: 'post',
    data: { ...data },
  });
}
// 编辑senderid资源
export async function editSenderIdResource(data: any): Promise<any> {
  return request('/sender/resource/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 添加客户透传白名单
export async function addWhiteUserSender(data: any): Promise<any> {
  return request('/sender/white-user-sender/add', {
    method: 'post',
    data: { ...data },
  });
}
// 删除客户透传白名单
export async function deleteWhiteUserSender(data: any): Promise<any> {
  return request('/sender/white-user-sender/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 查询客户透传白名单
export async function getWhiteUserSender(params?: any): Promise<any> {
  return request('/sender/white-user-sender/get-list', { params: { ...params } });
}
