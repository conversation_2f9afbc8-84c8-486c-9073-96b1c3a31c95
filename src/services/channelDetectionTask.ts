import request from '@/utils/request';
import _ from 'lodash';

export async function getDetectionTaskList(params?: any): Promise<any> {
  return request('/abroad-scheduler/ismschannel-utils/get-task-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function addDetectionTask(data: any): Promise<any> {
  return request('/abroad-scheduler/ismschannel-utils/add-task', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteDetectionTask(data: any): Promise<any> {
  return request('/abroad-scheduler/ismschannel-utils/del-task', {
    method: 'post',
    data: { ...data },
  });
}

export async function setDetectionTask(data: any): Promise<any> {
  return request('/abroad-scheduler/ismschannel-utils/set-task', {
    method: 'post',
    data: { ...data },
  });
}

export async function getDetectionTaskDetails(params?: any): Promise<any> {
  return request('/abroad-scheduler/ismschannel-utils/query-details', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function getDetectionTaskStatistics(params?: any): Promise<any> {
  return request('/abroad-scheduler/ismschannel-utils/query-statistics', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function getTelqMccMncList(params?: any): Promise<any> {
  return request('/abroad-scheduler/ismschannel-utils/get-telq', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}
