import request from '@/utils/request';
import qs from 'qs';
/** sdkappid防刷导白 */
// 查询防刷sdkappid导白短信数据
export async function getSmsLog(params?: any): Promise<any> {
  return request('/monitor/sdkappid-white/search-smslog', { params: { ...params } });
}
export async function getAntiBomb(params?: any): Promise<any> {
  return request('/monitor/sdkappid-white/search-antibomb', { params: { ...params } });
}
// 防刷应用导白
export async function getSdkappidWhite(params?: any): Promise<any> {
  return request('/monitor/sdkappid-white/search', { params: { ...params } });
}

export async function addSdkappidWhite(data: any): Promise<any> {
  return request('/monitor/sdkappid-white/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function delSdkappidWhite(data: any): Promise<any> {
  return request('/monitor/sdkappid-white/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 监控告警vip配置
export async function getVipAlarm(params?: any): Promise<any> {
  return request('/monitor/vip-alarm/search', { params: { ...params } });
}

export async function addVipAlarm(data: any): Promise<any> {
  return request('/monitor/vip-alarm/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function delVipAlarm(data: any): Promise<any> {
  return request('/monitor/vip-alarm/delete', {
    method: 'post',
    data: { ...data },
  });
}
// 供应商质量干预调度上报配置
export async function getProviderQuality(params?: any): Promise<any> {
  return request('/monitor/provider-quality/get-list', { params: { ...params } });
}

export async function addProviderQuality(data: any): Promise<any> {
  return request('/monitor/provider-quality/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editProviderQuality(data: any): Promise<any> {
  return request('/monitor/provider-quality/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function delProviderQuality(data: any): Promise<any> {
  return request('/monitor/provider-quality/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 透传通道绑定上行短信
export async function getAvailableProvider(): Promise<any> {
  return request('/global-setting/provider-bind-app/get-available-provider');
}
export async function getBindingProvider(params: any): Promise<any> {
  return request('/global-setting/provider-bind-app/get-list', { params: { ...params } });
}
export async function editBindingProvider(data: any): Promise<any> {
  return request('/global-setting/provider-bind-app/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function delBindingProvider(data: any): Promise<any> {
  return request('/global-setting/provider-bind-app/delete', {
    method: 'post',
    data: { ...data },
  });
}
export async function addBindingProvider(data: any): Promise<any> {
  return request('/global-setting/provider-bind-app/add', {
    method: 'post',
    data: { ...data },
  });
}

// 应用忽略日志入库管理
export async function getIgnoreLog(params: any): Promise<any> {
  return request('/global-setting/ignore-log-appid/get-list', { params: { ...params } });
}
export async function addIgnoreLog(data: any): Promise<any> {
  return request('/global-setting/ignore-log-appid/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function delIgnoreLog(data: any): Promise<any> {
  return request('/global-setting/ignore-log-appid/delete', {
    method: 'post',
    data: { ...data },
  });
}

// tci
export async function queryTciLog(params: any): Promise<any> {
  return request('/sms/tci-log/query', { params: { ...params } });
}

export async function queryRecommend(params: any): Promise<any> {
  return request('/channel/recommend/query', { params: { ...params } });
}

// 计费业务标签
export async function getChargeLabel(params: any): Promise<any> {
  return request('/global-setting/billing-type/get-list', { params: { ...params } });
}
export async function addChargeLabel(data: any): Promise<any> {
  return request('/global-setting/billing-type/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function editChargeLabel(data: any): Promise<any> {
  return request('/global-setting/billing-type/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function deleteChargeLabel(data: any): Promise<any> {
  return request('/global-setting/billing-type/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 失败原因分析
export async function queryErrAnaylysis(params: any): Promise<any> {
  return request('/sms/err-anaylysis/query', { params: { ...params } });
}
export async function queryErrAnaylysisMobile(params: any): Promise<any> {
  return request('/sms/err-anaylysis/get-errcode-phones', { params: { ...params } });
}

export async function getTypeStr(params?: any): Promise<any> {
  return request('/global-setting/billing-type/get-type-str', { params: { ...params } });
}
export async function addTypeStr(data: any): Promise<any> {
  return request('/global-setting/billing-type/add-type-str', {
    method: 'post',
    data: { ...data },
  });
}
// 国际短信发送记录查询
export async function queryIsmsRecord(params: any): Promise<any> {
  return request(`/sms/abroad-sms/query?${qs.stringify(params, { arrayFormat: 'bracket' })}`);
}

// 国际短信补发记录查询
export async function queryIsmsReissue(params: any): Promise<any> {
  return request(`/sms/abroad-sms/get-reissue-info`, { params: { ...params } });
}

// 获取cos临时key
export async function getCosKey(params: any): Promise<any> {
  return request('/application/cos/get-cos-key', { params: { ...params } });
}

// 新增国内短信通道调度
export async function dispatchProviderAccount(data: any): Promise<any> {
  return request('/csms-scheduler/provider-account/dispatch', {
    method: 'post',
    data: { ...data },
  });
}

// 获取通道列表
export async function getProviderAccountList(params: any): Promise<any> {
  return request('/csms-scheduler/provider-account/get-list', { params: { ...params } });
}

// 编辑通道
export async function editProviderAccount(data: any): Promise<any> {
  return request('/csms-scheduler/provider-account/update', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑接入状态
export async function changeStatusProviderAccount(data: any): Promise<any> {
  return request('/csms-scheduler/provider-account/change-status', {
    method: 'post',
    data: { ...data },
  });
}
