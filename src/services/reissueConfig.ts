import request from '@/utils/request';

export async function getReissueInfo(params?: any): Promise<any> {
  return request('/sms/reissue-app/info', { params: { ...params } });
}

export async function getReissueGroup(params?: any): Promise<any> {
  return request('/sms/reissue-group/get-group-list', { params: { ...params } });
}

export async function getReissueChannelList(params?: any): Promise<any> {
  return request('/sms/reissue-group/get-list', { params: { ...params } });
}

export async function setReissueApp(data: any): Promise<any> {
  return request('/sms/reissue-app/set', {
    method: 'post',
    data: { ...data },
  });
}

export async function editReissueChannel(data: any): Promise<any> {
  return request('/sms/reissue-group/edit', {
    method: 'post',
    data: { ...data },
  });
}
export async function addReissueChannel(data: any): Promise<any> {
  return request('/sms/reissue-group/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function deleteReissueChannel(data: any): Promise<any> {
  return request('/sms/reissue-group/delete', {
    method: 'post',
    data: { ...data },
  });
}
