import request from '@/utils/request';

export async function getPackageConfig(params?: any): Promise<any> {
  return request('/global-setting/sms-package-config/get-list', { params: { ...params } });
}

export async function addPackageConfig(data: any): Promise<any> {
  return request('/global-setting/sms-package-config/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function updatePackageConfig(data: any): Promise<any> {
  return request('/global-setting/sms-package-config/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function delPackageConfig(data: any): Promise<any> {
  return request('/global-setting/sms-package-config/delete', {
    method: 'post',
    data: { ...data },
  });
}
