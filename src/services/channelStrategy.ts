// 国际短信通道调度
import request from '@/utils/request';
import _ from 'lodash';
import qs from 'qs';

// 查询通道策略列表
export async function getProviderStrategyList(params?: any): Promise<any> {
  const _params = _.pickBy(params, (v) => !_.isNil(v) && v !== '');
  return request(
    `/abroad-scheduler/provider-group-tactic/get-list?${qs.stringify(_params, {
      arrayFormat: 'brackets',
    })}`,
  );
}

// 查询策略绑定列表
export async function getProviderStrategyBindList(params?: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/get-bind-list', {
    params: { ...params },
  });
}
// 刷新策略列表价格
export async function refreshStrategyPrice(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/refresh-price', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑通道策略
export async function editProviderGroupStrategy(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 新增通道策略
export async function addProviderGroupStrategy(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/add', {
    method: 'post',
    data: { ...data },
  });
}

// 删除通道策略
export async function deleteProviderStrategy(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 绑定通道策略
export async function bindProviderGroupStrategy(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/bind', {
    method: 'post',
    data: { ...data },
  });
}

// 解绑通道策略
export async function unbindProviderGroupStrategy(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/unbind', {
    method: 'post',
    data: { ...data },
  });
}

// 通道权重策略调整
export async function editProviderStrategyWeight(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/edit-weight', {
    method: 'post',
    data: { ...data },
  });
}
