import request from '@/utils/request';
import _ from 'lodash';

// 供应商调度策略
export async function getSupplierStrategy(params?: any): Promise<any> {
  return request('/schedule-strategy/supplier/get-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function updateSupplierStrategy(data: any): Promise<any> {
  return request('/schedule-strategy/supplier/save', {
    method: 'post',
    data: { ...data },
  });
}

// 供应商地区策略
export async function getSupplierCountryStrategy(params?: any): Promise<any> {
  return request('/schedule-strategy/supplier-country/get-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function updateSupplierCountryStrategy(data: any): Promise<any> {
  return request('/schedule-strategy/supplier-country/save', {
    method: 'post',
    data: { ...data },
  });
}

// Uin供应商策略
export async function getSupplierUinStrategy(params?: any): Promise<any> {
  return request('/schedule-strategy/supplier-uin/get-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function updateSupplierUinStrategy(data: any): Promise<any> {
  return request('/schedule-strategy/supplier-uin/save', {
    method: 'post',
    data: { ...data },
  });
}

// Uin供应商地区策略
export async function getSupplierUinCountryStrategy(params?: any): Promise<any> {
  return request('/schedule-strategy/supplier-uin-country/get-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function updateSupplierUinCountryStrategy(data: any): Promise<any> {
  return request('/schedule-strategy/supplier-uin-country/save', {
    method: 'post',
    data: { ...data },
  });
}

// 默认调度策略
export async function getDefaultStrategy(params?: any): Promise<any> {
  return request('/schedule-strategy/default/get-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function updateDefaultStrategy(data: any): Promise<any> {
  return request('/schedule-strategy/default/save', {
    method: 'post',
    data: { ...data },
  });
}

// uin分运营商策略
export async function getSupplierUinCountryOperatorStrategy(params?: any): Promise<any> {
  return request('/schedule-strategy/supplier-uin-country-mnc/get-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

// 供应商分运营商策略
export async function getSupplierCountryOperatorStrategy(params?: any): Promise<any> {
  return request('/schedule-strategy/supplier-country-mnc/get-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function updateSupplierCountryOperatorStrategy(data: any): Promise<any> {
  return request('/schedule-strategy/supplier-country-mnc/save', {
    method: 'post',
    data: { ...data },
  });
}

export async function updateSupplierUinCountryOperatorStrategy(data: any): Promise<any> {
  return request('/schedule-strategy/supplier-uin-country-mnc/save', {
    method: 'post',
    data: { ...data },
  });
}
