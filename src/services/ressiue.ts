import request from '@/utils/request';

// 更新补发配置&通道
export async function updateRessiueConfiguration(data: any): Promise<any> {
  return request('/abroad-scheduler/reissue/update-configuration', {
    method: 'post',
    data: { ...data },
  });
}
// 获取补发配置
export async function getRessiueConfiguration(params?: any): Promise<any> {
  return request('/abroad-scheduler/reissue/get-configuration', { params: { ...params } });
}

// 获取补发通道
export async function getRessiueChannel(params?: any): Promise<any> {
  return request('/abroad-scheduler/reissue/get-channel', { params: { ...params } });
}

// 获取补发状态列表
export async function getRessiueStatus(params?: any): Promise<any> {
  return request('/abroad-scheduler/reissue/get-status', { params: { ...params } });
}

// 删除补发配置
export async function deleteRessiueConfiguration(data?: any): Promise<any> {
  return request('/abroad-scheduler/reissue/delete-configuration', {
    method: 'post',
    data: { ...data },
  });
}
