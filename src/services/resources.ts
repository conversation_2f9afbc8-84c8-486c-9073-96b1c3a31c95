import request from '@/utils/request';
import _ from 'lodash';
import qs from 'query-string';

const paramsSerializer = (v: any) => {
  return qs.stringify(
    _.mapValues(v, (v: any) => {
      if (Array.isArray(v)) {
        return v.map((o) => (typeof o === 'object' ? JSON.stringify(o) : o));
      }
      return v;
    }),
    {
      arrayFormat: 'bracket',
    },
  );
};

export async function getGroupResources(params?: any): Promise<any> {
  return request('/abroad-scheduler/provider-resources/get-group-resources', {
    params: _.pickBy(params, (v) => v !== ''),
    paramsSerializer,
  });
}

export async function getTacticResources(params?: any): Promise<any> {
  return request('/abroad-scheduler/provider-resources/get-tactic-resources', {
    params: _.pickBy(params, (v) => v !== ''),
    paramsSerializer,
  });
}

export async function getOperatorGroupResources(params?: any): Promise<any> {
  return request('/abroad-scheduler/provider-operator-resources/get-group-resources', {
    params: _.pickBy(params, (v) => v !== ''),
    paramsSerializer,
  });
}

export async function getOperatorTacticResources(params?: any): Promise<any> {
  return request('/abroad-scheduler/provider-operator-resources/get-tactic-resources', {
    params: _.pickBy(params, (v) => v !== ''),
    paramsSerializer,
  });
}
