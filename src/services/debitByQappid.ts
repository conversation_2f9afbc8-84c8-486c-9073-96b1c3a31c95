import request from '@/utils/request';

export async function getDebitList(params?: any): Promise<any> {
  return request('/global-setting/need-report-appid-items/get-list', { params: { ...params } });
}

export async function addDebitList(data: any): Promise<any> {
  return request('/global-setting/need-report-appid-items/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteDebitList(data: any): Promise<any> {
  return request('/global-setting/need-report-appid-items/del', {
    method: 'post',
    data: { ...data },
  });
}
