import request from '@/utils/request';
import qs from 'query-string';

// 获取用户配置
export async function queryUserConfigList(params: any): Promise<any> {
  return request('/admin/user/get-config-list', { params: { ...params } });
}

// 获取用户列表
export async function queryUserList(params: any): Promise<any> {
  return request('/admin/user/get-user-list', { params: { ...params } });
}

// 编辑用户信息
export async function editUserInfo(data: any): Promise<any> {
  return request('/admin/user/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 新增用户
export async function addUser(data: any): Promise<any> {
  return request('/admin/user/add', {
    method: 'post',
    data: { ...data },
  });
}

// 获取权限配置列表
export async function queryAuthList(params: any): Promise<any> {
  return request('/admin/auth/get-conf-list', { params: { ...params } });
}

// 删除用户
export async function deleteUser(data: any): Promise<any> {
  return request('/admin/user/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 获取角色列表
export async function queryRoleList(params?: any): Promise<any> {
  return request('/admin/role/get-list', { params: { ...params } });
}

// 新增角色
export async function addRole(data: any): Promise<any> {
  return request('/admin/role/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑角色状态
export async function editRoleInfo(data: any): Promise<any> {
  return request('/admin/role/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 获取接口列表
export async function queryApiList(params?: any): Promise<any> {
  return request('/admin/api/get-list', { params: { ...params } });
}

// 新增接口
export async function addApi(data: any): Promise<any> {
  return request('/admin/api/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑接口
export async function editApi(data: any): Promise<any> {
  return request('/admin/api/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 新增权限配置
export async function addAuth(data: any): Promise<any> {
  return request('/admin/auth/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑权限配置
export async function editAuth(data: any): Promise<any> {
  return request('/admin/auth/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 获取控制台操作日志
export async function queryConsoleLog(params?: any): Promise<any> {
  return request('/admin/record/get-console-list', { params: { ...params } });
}

// 获取smsConfig操作日志
export async function querySmsConfigLog(params?: any): Promise<any> {
  return request('/admin/record/get-list', { params: { ...params } });
}

// 获取审核列表
export async function queryAuditList(params?: any): Promise<any> {
  return request('/admin/audit/get-list', { params: { ...params } });
}

// 创建审核单
export async function addAudit(data: any): Promise<any> {
  return request('/admin/audit/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑审核单
export async function editAudit(data: any): Promise<any> {
  return request('/admin/audit/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 执行审核操作
export async function operateAudit(data: any): Promise<any> {
  return request('/admin/audit/operate', {
    method: 'post',
    data: { ...data },
  });
}

// 下行短信查询
export async function querySmsSend(params?: any): Promise<any> {
  return request('/sms/detail/get-down-info', { params: { ...params } });
}
export async function queryReissueInfo(params?: any): Promise<any> {
  return request('/sms/detail/get-reissue-info', { params: { ...params } });
}

export async function exportSmsSendQuery(data: any): Promise<any> {
  return request('/sms/sms/export', {
    method: 'post',
    data: { ...data },
  });
}

// 上行短信查询
export async function querySmsUp(params?: any): Promise<any> {
  return request('/sms/detail/get-up-info', { params: { ...params } });
}

// 下行语音查询
export async function queryVmsSend(params?: any): Promise<any> {
  return request('/voice/detail/get-down-info', { params: { ...params } });
}

// 下行新语音查询
export async function queryVmsNewSend(params?: any): Promise<any> {
  return request('/voice/detail/get-new-info', { params: { ...params } });
}

// 语音号码查询
export async function queryVmsNumber(params?: any): Promise<any> {
  return request('/voice/number/get-number-info', { params: { ...params } });
}

// 查询cmpp列表
export async function queryCmppList(params?: any): Promise<any> {
  return request('/cmpp/account/query', { params: { ...params } });
}

// 查询接cmpp接入点列表
export async function queryAP(params?: any): Promise<any> {
  return request('/cmpp/access-point/query', { params: { ...params } });
}

// 创建cmpp账号
export async function createCmppAcount(data: any): Promise<any> {
  return request('/cmpp/account/add', {
    method: 'post',
    data: { ...data },
  });
}
// 编辑cmpp账号
export async function modifyCmppAcount(data: any): Promise<any> {
  return request('/cmpp/account/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑接入点
export async function modifyAccessPoint(data: any): Promise<any> {
  return request('/cmpp/access-point/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 创建接入点
export async function createAccessPoint(data: any): Promise<any> {
  return request('/cmpp/access-point/add', {
    method: 'post',
    data: { ...data },
  });
}

// 发布现网账号
export async function releaseAccount(data: any): Promise<any> {
  return request('/cmpp/account/release-account-conf', {
    method: 'post',
    data: { ...data },
  });
}

// 推送邮件
export async function sendEmail(data: any): Promise<any> {
  return request('/cmpp/account/send-email', {
    method: 'post',
    data: { ...data },
  });
}

// 签名子码获取通道组列表
export async function getDirectGroup(params: any): Promise<any> {
  return request('/channal-group/group/list', { params: { ...params } });
}
// 应用子码获取通道组列表
export async function getChannelGroup(params: any): Promise<any> {
  return request('/canal-channal-group/group/list', { params: { ...params } });
}

// 获取指定通道组信息
export async function getDirectGroupInfo(params: any): Promise<any> {
  return request('/channal-group/group/group-info', { params: { ...params } });
}
export async function getChannelGroupInfo(params: any): Promise<any> {
  return request('/canal-channal-group/group/group-info', { params: { ...params } });
}

// 签名子码创建通道组
export async function createDirectGroup(data: any): Promise<any> {
  return request('/channal-group/group/create', {
    method: 'post',
    data: { ...data },
  });
}
// 应用子码 创建通道组
export async function createChannelGroup(data: any): Promise<any> {
  return request('/canal-channal-group/group/create', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑通道组
export async function editDirectGroup(data: any): Promise<any> {
  return request('/channal-group/group/edit', {
    method: 'post',
    data: { ...data },
  });
}
export async function editChannelGroup(data: any): Promise<any> {
  return request('/canal-channal-group/group/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 删除通道组
export async function deleteDirectGroup(data: any): Promise<any> {
  return request('/channal-group/group/delete', {
    method: 'post',
    data: { ...data },
  });
}
export async function deleteChannelGroup(data: any): Promise<any> {
  return request('/canal-channal-group/group/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 获取通道列表
export async function getProviderList(): Promise<any> {
  return request('/channal-group/provider/list');
}
export async function getCanalProviders(): Promise<any> {
  return request('/canal-channal-group/group/get-unassigned-provider');
}

// 获取通道组app列表
export async function getDirectAppList(params: any): Promise<any> {
  return request(
    `/channal-group/group/app-list?${qs.stringify(params, { arrayFormat: 'bracket' })}`,
  );
}
export async function getChannelAppList(params: any): Promise<any> {
  return request(
    `/canal-channal-group/group/app-list?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

// 编辑组内app
export async function editDirectAppList(data: any): Promise<any> {
  return request('/channal-group/group/edit-app', {
    method: 'post',
    data: { ...data },
  });
}
export async function editChannelAppList(data: any): Promise<any> {
  return request('/canal-channal-group/group/edit-app', {
    method: 'post',
    data: { ...data },
  });
}

// 添加组内app
export async function addDirectAppList(data: any): Promise<any> {
  return request('/channal-group/group/add-app', {
    method: 'post',
    data: { ...data },
  });
}
export async function addChannelAppList(data: any): Promise<any> {
  return request('/canal-channal-group/group/add-apps', {
    method: 'post',
    data: { ...data },
  });
}

// 删除组内app
export async function delDirectAppList(data: any): Promise<any> {
  return request('/channal-group/group/delete-app', {
    method: 'post',
    data: { ...data },
  });
}
export async function delChannelAppList(data: any): Promise<any> {
  return request('/canal-channal-group/group/delete-apps', {
    method: 'post',
    data: { ...data },
  });
}

// 获取组内通道商列表
export async function getDirectProviderList(params: any): Promise<any> {
  return request('/channal-group/group/provider-list', { params: { ...params } });
}
export async function getChannelProviderList(params: any): Promise<any> {
  return request('/canal-channal-group/group/provider-list', { params: { ...params } });
}

// 编辑组内通道商
export async function editDirectProvider(data: any): Promise<any> {
  return request('/channal-group/group/edit-provider', {
    method: 'post',
    data: { ...data },
  });
}
export async function editChannelProvider(data: any): Promise<any> {
  return request('/canal-channal-group/group/edit-provider', {
    method: 'post',
    data: { ...data },
  });
}

// 删除组内通道商
export async function deleteDirectProvider(data: any): Promise<any> {
  return request('/channal-group/group/delete-provider', {
    method: 'post',
    data: { ...data },
  });
}
export async function deleteChannelProvider(data: any): Promise<any> {
  return request('/canal-channal-group/group/delete-provider', {
    method: 'post',
    data: { ...data },
  });
}

// 添加组内通道商
export async function addDirectProvider(data: any): Promise<any> {
  return request('/channal-group/group/add-provider', {
    method: 'post',
    data: { ...data },
  });
}
export async function addChannelProvider(data: any): Promise<any> {
  return request('/canal-channal-group/group/add-provider', {
    method: 'post',
    data: { ...data },
  });
}

// 获取应用频率限制信息
export async function querySendFreqList(params: any): Promise<any> {
  return request('/sms/app/get-app-freq-limit', { params: { ...params } });
}

// 设置应用频率限制
export async function setAppFreqLimit(data: any): Promise<any> {
  return request('/sms/app/set-app-freq-limit', {
    method: 'post',
    data: { ...data },
  });
}

// 设置国家地区频率限制
export async function setCountryLimit(data: any): Promise<any> {
  return request('/sms/app/set-app-country-freq-limit', {
    method: 'post',
    data: { ...data },
  });
}

// 获取qappid-max-number
export async function getMaxNumber(params: any): Promise<any> {
  return request('/sms/qappid-max-number/get', { params: { ...params } });
}

// 设置qappid-max-number
export async function setMaxNumber(data: any): Promise<any> {
  return request('/sms/qappid-max-number/set', {
    method: 'post',
    data: { ...data },
  });
}

// 拉标签信息
export async function getLabelInfo(params: any): Promise<any> {
  return request('/label/label-info/query', {
    params: { ...params },
  });
}

// 增加标签信息
export async function addLabel(data: any): Promise<any> {
  return request('/label/label-info/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑标签信息
export async function setLabelInfo(params: any): Promise<any> {
  return request('/label/label-info/edit', {
    params: { ...params },
  });
}

// 拉标签绑定绑定数
export async function getLabelBindSum(params: any): Promise<any> {
  return request('/label/label-info/get-bind-sum', {
    params: { ...params },
  });
}

// 拉标签组列表
export async function getLabelGroup(params: any): Promise<any> {
  return request('/label/label-group/query', {
    params: { ...params },
  });
}

// 创建标签组
export async function addLabelGroup(data: any): Promise<any> {
  return request('/label/label-group/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑标签组
export async function setLabelGroup(data: any): Promise<any> {
  return request('/label/label-group/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 删除标签组
export async function delLabelGroup(data: any): Promise<any> {
  return request('/label/label-group/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 查询绑定信息
export async function getLabelBind(params: any): Promise<any> {
  return request('/label/bind-conf/query', {
    params: { ...params },
  });
}

// 获取客户列表
export async function getCustomerList(params: any): Promise<any> {
  return request('/label/object/get-client-list', {
    params: { ...params },
  });
}

// 获取通道列表
export async function getSchedulerList(params: any): Promise<any> {
  return request('/label/object/get-scheduler-list', {
    params: { ...params },
  });
}

// 获取签名列表
export async function getLabelSignList(params: any): Promise<any> {
  return request('/label/object/get-sign-list', {
    params: { ...params },
  });
}

// 绑定标签
export async function setLabelBind(data: any): Promise<any> {
  return request('/label/bind-conf/bind', {
    data: { ...data },
    method: 'post',
  });
}

// 解绑标签
export async function setLabelUnbind(data: any): Promise<any> {
  return request('/label/bind-conf/unbind', {
    data: { ...data },
    method: 'post',
  });
}

// 删除标签
export async function delLabel(data: any): Promise<any> {
  return request('/label/label-info/delete', {
    data: { ...data },
    method: 'post',
  });
}

// 通用获取供应商列表
export async function getGloablProviders(): Promise<any> {
  return request('/global-setting/provider/list');
}

// 拉国家列表
export async function getCountryList(): Promise<any> {
  return request('/sms/nation/get-nations');
}
