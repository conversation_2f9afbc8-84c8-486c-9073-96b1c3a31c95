import request from '@/utils/request';

export async function getCompanyInfoList(params?: any): Promise<any> {
  return request('/application/company-info/get-list', { params: { ...params } });
}

export async function addCompanyInfo(data: any): Promise<any> {
  return request('/application/company-info/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteCompanyInfo(data: any): Promise<any> {
  return request('/application/company-info/delete', {
    method: 'post',
    data: { ...data },
  });
}
