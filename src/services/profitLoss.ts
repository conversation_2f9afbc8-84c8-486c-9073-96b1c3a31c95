import request from '@/utils/request';

// 删除配置
export async function deleteProfitLossSubsidy(data: any): Promise<any> {
  return request('/profit-loss/cost-subsidy/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 获取配置列表
export async function getProfitLossSubsidyList(params?: any): Promise<any> {
  return request('/profit-loss/cost-subsidy/get-list', { params: { ...params } });
}

// 新增配置
export async function addProfitLossSubsidy(data: any): Promise<any> {
  return request('/profit-loss/cost-subsidy/add', {
    method: 'post',
    data: { ...data },
  });
}
