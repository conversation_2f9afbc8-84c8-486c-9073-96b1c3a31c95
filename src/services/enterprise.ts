import request from '@/utils/request';
import _ from 'lodash';

export async function updateCredentialAuthStatus(data: any): Promise<any> {
  return request('/credential/auth/update-status', {
    method: 'post',
    data: { ...data },
  });
}

export async function updateCredentialAuth(data: any): Promise<any> {
  return request('/credential/auth/update', {
    method: 'post',
    data: { ...data },
  });
}

export async function getCredentialAuthList(params?: any): Promise<any> {
  return request('/credential/auth/get-list', { params: _.pickBy(params, (v) => v !== '') });
}
