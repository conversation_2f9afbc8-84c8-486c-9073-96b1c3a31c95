import request from '@/utils/request';
import qs from 'qs';
// 查询sdk配置列表
export async function getErrorCodeList(params?: any): Promise<any> {
  return request(
    `/global-setting/error-status-code/get?${qs.stringify(params, { arrayFormat: 'brackets' })}`,
  );
}

export async function getStandardCodeList(): Promise<any> {
  return request('/global-setting/error-status-code/get-standard');
}

export async function addErrorCodeList(params: any): Promise<any> {
  return request(`/global-setting/error-status-code/add`, {
    method: 'post',
    data: params,
  });
}

export async function updateErrorCodeList(params: any): Promise<any> {
  return request('/global-setting/error-status-code/update', { data: params, method: 'post' });
}

export async function deleteErrorCodeList(params: any): Promise<any> {
  return request(`/global-setting/error-status-code/delete`, {
    data: params,
    method: 'post',
  });
}
