import request from '@/utils/request';
import _ from 'lodash';
import qs from 'query-string';

export async function getAccountList(params?: any): Promise<any> {
  return request(`/purchase/account/get-list?${qs.stringify(params, { arrayFormat: 'bracket' })}`);
}

export async function getAccountAgents(params?: any): Promise<any> {
  return request('/purchase/account/get-agents', { params: { ...params } });
}

export async function updateAccountStatus(data: any): Promise<any> {
  return request('/purchase/account/change-status', {
    method: 'post',
    data: { ...data },
  });
}

export async function editAccount(data: any): Promise<any> {
  return request('/purchase/account/edit', {
    method: 'post',
    data: { ..._.pickBy(data, (v) => !_.isNil(v) && v !== '') },
  });
}

export async function getConnectionNum(params?: any): Promise<any> {
  return request('/purchase/account/get-connection-num', { params: { ...params } });
}

export async function setConnectionNum(data: any): Promise<any> {
  return request('/purchase/account/set-connection-num', {
    method: 'post',
    data: { ...data },
  });
}
