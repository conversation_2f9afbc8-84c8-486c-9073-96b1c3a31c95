import request from '@/utils/request';
import _ from 'lodash';

export async function getChannleOfflineList(params?: any): Promise<any> {
  return request('/abroad-scheduler/channel-offline/get-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function quickOfflineConfig(data: any): Promise<any> {
  return request('/abroad-scheduler/channel-offline/one-click-offline', {
    method: 'post',
    data: { ...data },
  });
}

export async function addOfflineForTestFalied(data: any): Promise<any> {
  return request('/abroad-scheduler/channel-offline/offline-for-test-failed', {
    method: 'post',
    data: { ...data },
  });
}

export async function restoreOffline(data: any): Promise<any> {
  return request('/abroad-scheduler/channel-offline/restore-offline', {
    method: 'post',
    data: { ...data },
  });
}

export async function getPriceSubStatus(params: any): Promise<any> {
  return request('/abroad-scheduler/utils/get-sub-status-confs', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}
