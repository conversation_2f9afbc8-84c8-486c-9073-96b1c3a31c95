import request from '@/utils/request';

export async function addResourceType(data: any): Promise<any> {
  return request('/abroad-scheduler/resource-type/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editResourceType(data: any): Promise<any> {
  return request('/abroad-scheduler/resource-type/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteResourceType(data: any): Promise<any> {
  return request('/abroad-scheduler/resource-type/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getResourceType(params?: any): Promise<any> {
  return request('/abroad-scheduler/resource-type/get-list', { params: { ...params } });
}

export async function addProductType(data: any): Promise<any> {
  return request('/abroad-scheduler/product-type/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editProductType(data: any): Promise<any> {
  return request('/abroad-scheduler/product-type/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteProductType(data: any): Promise<any> {
  return request('/abroad-scheduler/product-type/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getProductType(params?: any): Promise<any> {
  return request('/abroad-scheduler/product-type/get-list', { params: { ...params } });
}

export async function addTacticConf(data: any): Promise<any> {
  return request('/abroad-scheduler/tactic-conf/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editTacticConf(data: any): Promise<any> {
  return request('/abroad-scheduler/tactic-conf/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteTacticConf(data: any): Promise<any> {
  return request('/abroad-scheduler/tactic-conf/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getTacticConf(params?: any): Promise<any> {
  return request('/abroad-scheduler/tactic-conf/get-list', { params: { ...params } });
}

export async function addCountryOperatorMinCr(data: any): Promise<any> {
  return request('/abroad-scheduler/country-operator-min-cr-conf/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editCountryOperatorMinCr(data: any): Promise<any> {
  return request('/abroad-scheduler/country-operator-min-cr-conf/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteCountryOperatorMinCr(data: any): Promise<any> {
  return request('/abroad-scheduler/country-operator-min-cr-conf/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getCountryOperatorMinCr(params?: any): Promise<any> {
  return request('/abroad-scheduler/country-operator-min-cr-conf/get-list', {
    params: { ...params },
  });
}

export async function addValidPointConf(data: any): Promise<any> {
  return request('/abroad-scheduler/valid-point-conf/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editValidPointConf(data: any): Promise<any> {
  return request('/abroad-scheduler/valid-point-conf/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteValidPointConf(data: any): Promise<any> {
  return request('/abroad-scheduler/valid-point-conf/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getValidPointConf(params?: any): Promise<any> {
  return request('/abroad-scheduler/valid-point-conf/get-list', {
    params: { ...params },
  });
}

export async function addProbeTraficTestingWeightConf(data: any): Promise<any> {
  return request('/abroad-scheduler/probe-trafic-testing-weight-conf/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editProbeTraficTestingWeightConf(data: any): Promise<any> {
  return request('/abroad-scheduler/probe-trafic-testing-weight-conf/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteProbeTraficTestingWeightConf(data: any): Promise<any> {
  return request('/abroad-scheduler/probe-trafic-testing-weight-conf/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getProbeTraficTestingWeightConf(params?: any): Promise<any> {
  return request('/abroad-scheduler/probe-trafic-testing-weight-conf/get-list', {
    params: { ...params },
  });
}

export async function addDataSourceConf(data: any): Promise<any> {
  return request('/abroad-scheduler/data-source-conf/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editDataSourceConf(data: any): Promise<any> {
  return request('/abroad-scheduler/data-source-conf/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteDataSourceConf(data: any): Promise<any> {
  return request('/abroad-scheduler/data-source-conf/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getDataSourceConf(params?: any): Promise<any> {
  return request('/abroad-scheduler/data-source-conf/get-list', {
    params: { ...params },
  });
}

export async function addTacticSaleProduct(data: any): Promise<any> {
  return request('/abroad-scheduler/tactic-sale-product/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editTacticSaleProduct(data: any): Promise<any> {
  return request('/abroad-scheduler/tactic-sale-product/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteTacticSaleProduct(data: any): Promise<any> {
  return request('/abroad-scheduler/tactic-sale-product/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getTacticSaleProduct(params?: any): Promise<any> {
  return request('/abroad-scheduler/tactic-sale-product/get-list', {
    params: { ...params },
  });
}
