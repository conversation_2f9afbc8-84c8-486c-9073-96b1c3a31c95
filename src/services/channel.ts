// 国际短信通道调度
import request from '@/utils/request';
import _ from 'lodash';
import qs from 'qs';

// 查询sdk配置列表
export async function getSdkChannelList(params?: any): Promise<any> {
  return request('/abroad-scheduler/sdk-channel/get-list', { params: { ...params } });
}

// 编辑sdk配置
export async function editSdkChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/sdk-channel/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 新增sdk配置
export async function addSdkChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/sdk-channel/add', {
    method: 'post',
    data: { ...data },
  });
}

// 删除sdk配置
export async function deleteSdkChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/sdk-channel/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 删除sdk主通道+补发通道配置
export async function deleteSdkRessiueChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/reissue/delete-sdk', {
    method: 'post',
    data: { ...data },
  });
}

// 查询强制配置列表
export async function getForceChannelList(params?: any): Promise<any> {
  return request('/abroad-scheduler/force-scheduler/get-list', { params: { ...params } });
}

// 编辑强制配置
export async function editForceChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/force-scheduler/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 新增强制配置
export async function addForceChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/force-scheduler/add', {
    method: 'post',
    data: { ...data },
  });
}

// 删除强制配置
export async function deleteForceChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/force-scheduler/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 查询uin配置列表
export async function getUinChannelList(params?: any): Promise<any> {
  return request('/abroad-scheduler/uin-channel/get-list', { params: { ...params } });
}

// 编辑uin配置
export async function editUinChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/uin-channel/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 新增uin配置
export async function addUinChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/uin-channel/add', {
    method: 'post',
    data: { ...data },
  });
}

// 删除uin配置
export async function deleteUinChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/uin-channel/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 删除uin主通道和补发
export async function deleteUinRessiueChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/reissue/delete-uin', {
    method: 'post',
    data: { ...data },
  });
}

// 查询全局配置列表
export async function getGlobalChannelList(params?: any): Promise<any> {
  return request('/abroad-scheduler/overall-channel/get-list', { params: { ...params } });
}

// 编辑全局配置
export async function editGlobalChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/overall-channel/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 新增全局配置
export async function addGlobalChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/overall-channel/add', {
    method: 'post',
    data: { ...data },
  });
}

// 删除全局配置
export async function deleteGlobalChannel(data: any): Promise<any> {
  return request('/abroad-scheduler/overall-channel/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 查询通道组列表
export async function getProviderGroupList(params?: any): Promise<any> {
  const _params = _.pickBy(params, (v) => !_.isNil(v) && v !== '');
  return request(
    `/abroad-scheduler/provider-group/get-list?${qs.stringify(_params, {
      arrayFormat: 'brackets',
    })}`,
  );
}

// 查询绑定列表
export async function getProviderGroupBindList(params?: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/get-bind-list', { params: { ...params } });
}

// 编辑通道组
export async function editProviderGroup(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 新增通道组
export async function addProviderGroup(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/add', {
    method: 'post',
    data: { ...data },
  });
}

// 删除通道组
export async function deleteProviderGroup(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 绑定通道
export async function bindProviderGroup(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/bind', {
    method: 'post',
    data: { ...data },
  });
}

// 解绑通道
export async function unbindProviderGroup(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/unbind', {
    method: 'post',
    data: { ...data },
  });
}

// 通道权重调整
export async function editProviderGroupWeight(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/edit-weight', {
    method: 'post',
    data: { ...data },
  });
}

// 冻结通道
export async function freezeProviderGroup(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/freeze', {
    method: 'post',
    data: { ...data },
  });
}

// 解冻通道
export async function unfreezeProviderGroup(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/unfreeze', {
    method: 'post',
    data: { ...data },
  });
}

export async function getProviderAccountList(params?: any): Promise<any> {
  return request('/abroad-scheduler/utils/get-account-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function getMccMncList(params?: any): Promise<any> {
  return request('/abroad-scheduler/utils/get-mcc-mnc-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function getSdkConfigCountry(params?: any): Promise<any> {
  return request('/abroad-scheduler/sdk-channel/get-country-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function getUinConfigCountry(params?: any): Promise<any> {
  return request('/abroad-scheduler/uin-channel/get-country-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function getForceConfigCountry(params?: any): Promise<any> {
  return request('/abroad-scheduler/force-scheduler/get-country-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function getOverallConfigCountry(params?: any): Promise<any> {
  return request('/abroad-scheduler/overall-channel/get-country-list', {
    params: _.pickBy(params, (v) => v !== ''),
  });
}

export async function checkAccountPrice(params?: any): Promise<any> {
  return request('/abroad-scheduler/utils/check-account-price', {
    method: 'post',
    data: _.pickBy(params, (v) => v !== ''),
  });
}

export async function checkIncomeCostPrice(params?: any): Promise<any> {
  return request('/abroad-scheduler/utils/check-income-cost-price', {
    method: 'post',
    data: _.pickBy(params, (v) => v !== ''),
  });
}

// check通道组报价
export async function checkGroupPrice(data?: any): Promise<any> {
  return request('/abroad-scheduler/utils/check-group-account', {
    method: 'post',
    data: { ...data },
  });
}

// 多国家策略绑定
export async function batchChannelConf(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/batch-channel-conf', {
    method: 'post',
    data: { ...data },
  });
}

// 多国家策略绑定
export async function checkExistsTactic(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group-tactic/check-exists-tactic', {
    method: 'post',
    data: { ...data },
  });
}

// 刷新通道组列表价格
export async function refreshGroupPrice(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-group/refresh-price', {
    method: 'post',
    data: { ...data },
  });
}

// cpq生效标品自动调度
export async function getTacticCpqRecord(params?: any): Promise<any> {
  return request('/abroad-scheduler/tactic-cpq-record/get-list', {
    params: { ...params },
  });
}

export async function bindTacticCpqRecord(data: any): Promise<any> {
  return request('/abroad-scheduler/tactic-cpq-record/bind', {
    method: 'post',
    data: { ...data },
  });
}

export async function changeStatusTacticCpqRecord(data: any): Promise<any> {
  return request('/abroad-scheduler/tactic-cpq-record/change-status', {
    method: 'post',
    data: { ...data },
  });
}
