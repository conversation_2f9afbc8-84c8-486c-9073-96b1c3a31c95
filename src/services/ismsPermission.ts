import request from '@/utils/request';

export async function getRickControlRegionList(params?: any): Promise<any> {
  return request('/isms-rick-control/region/get-list', { params: { ...params } });
}

export async function addRickControlRegion(data: any): Promise<any> {
  return request('/isms-rick-control/region/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function delRickControlRegion(data: any): Promise<any> {
  return request('/isms-rick-control/region/delete', {
    method: 'post',
    data: { ...data },
  });
}
export async function getRickControlRegionUinList(params?: any): Promise<any> {
  return request('/isms-rick-control/region-uin/get-list', { params: { ...params } });
}

export async function addRickControlRegionUin(data: any): Promise<any> {
  return request('/isms-rick-control/region-uin/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function delRickControlRegionUin(data: any): Promise<any> {
  return request('/isms-rick-control/region-uin/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getRickControlUinRegionList(params?: any): Promise<any> {
  return request('/isms-rick-control/uin-region/get-list', { params: { ...params } });
}

export async function addRickControlUinRegion(data: any): Promise<any> {
  return request('/isms-rick-control/uin-region/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function delRickControlUinRegion(data: any): Promise<any> {
  return request('/isms-rick-control/uin-region/delete', {
    method: 'post',
    data: { ...data },
  });
}
