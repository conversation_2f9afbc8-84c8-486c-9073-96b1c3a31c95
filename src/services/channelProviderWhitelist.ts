import request from '@/utils/request';
//查询加白号码列表
export async function getProviderWhitelistList(params?: any): Promise<any> {
  return request('/abroad-scheduler/provider-whitelist/get-list', {
    params: { ...params },
  });
}

//
export async function addProviderWhitelist(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-whitelist/add', {
    method: 'post',
    data: { params: { ...data } },
  });
}
//
export async function editProviderWhitelist(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-whitelist/edit', {
    method: 'post',
    data: { ...data },
  });
}
//
export async function deletetProviderWhitelist(data: any): Promise<any> {
  return request('/abroad-scheduler/provider-whitelist/delete', {
    method: 'post',
    data: { ...data },
  });
}
