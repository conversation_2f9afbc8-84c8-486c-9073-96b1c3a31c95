import request from '@/utils/request';

export async function getProviderAccount(params?: any): Promise<any> {
  return request('/csms-scheduler/provider-inner-account/get-list', { params: { ...params } });
}

export async function addProviderAccount(data: any): Promise<any> {
  return request('/csms-scheduler/provider-inner-account/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteProviderAccount(data: any): Promise<any> {
  return request('/csms-scheduler/provider-inner-account/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getProviderInfo(params?: any): Promise<any> {
  return request('/csms-scheduler/provider-primary/get-list', { params: { ...params } });
}

export async function addProviderInfo(data: any): Promise<any> {
  return request('/csms-scheduler/provider-primary/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editProviderInfo(data: any): Promise<any> {
  return request('/csms-scheduler/provider-primary/edit', {
    method: 'post',
    data: { ...data },
  });
}
