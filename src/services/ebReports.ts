import request from '@/utils/request';

// 投递平台运维事件列表
export async function getReportEvents(params?: any): Promise<any> {
  return request('/eb/events-ops/get-list', { params: { ...params } });
}

export async function addReportEvents(data: any): Promise<any> {
  return request('/eb/events-ops/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editReportEvents(data: any): Promise<any> {
  return request('/eb/events-ops/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 接入上报故障事件
export async function getErrorEvents(params?: any): Promise<any> {
  return request('/eb/breakdown-events/get-list', { params: { ...params } });
}

export async function addErrorEvents(data: any): Promise<any> {
  return request('/eb/breakdown-events/put', {
    method: 'post',
    data: { ...data },
  });
}
