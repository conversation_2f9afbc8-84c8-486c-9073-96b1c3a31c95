import request from '@/utils/request';

export async function getNumberSegment(params?: any): Promise<any> {
  return request('/number-segment/valid-prefix/get-list', { params: { ...params } });
}

export async function addNumberSegment(data: any): Promise<any> {
  return request('/number-segment/valid-prefix/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function editNumberSegment(data: any): Promise<any> {
  return request('/number-segment/valid-prefix/edit', {
    method: 'post',
    data: { ...data },
  });
}
export async function delNumberSegment(data: any): Promise<any> {
  return request('/number-segment/valid-prefix/delete', {
    method: 'post',
    data: { ...data },
  });
}

/** mnc 管理 **/
export async function getOperatorNetwork(params?: any): Promise<any> {
  return request('/number-segment/operator-network/get-list', { params: { ...params } });
}

export async function editOperatorNetwork(data: any): Promise<any> {
  return request('/number-segment/operator-network/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function addOperatorNetwork(data: any): Promise<any> {
  return request('/number-segment/operator-network/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function deleteOperatorNetwork(data: any): Promise<any> {
  return request('/number-segment/operator-network/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function getDiapatchMnc(params?: any): Promise<any> {
  return request('/number-segment/operator-network/get-dispatch-mnc', { params: { ...params } });
}

/** 运营商号段管理 **/
export async function deletePhonePrefixOperator(data: any): Promise<any> {
  return request('/number-segment/phone-prefix-operator/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function compareGooglePhonePrefixOperator(params?: any): Promise<any> {
  return request('/number-segment/phone-prefix-operator/compare-google', {
    params: { ...params },
  });
}

export async function addPhonePrefixOperator(data: any): Promise<any> {
  return request('/number-segment/phone-prefix-operator/add', {
    method: 'post',
    data: { ...data },
  });
}

export async function editPhonePrefixOperator(data: any): Promise<any> {
  return request('/number-segment/phone-prefix-operator/edit', {
    method: 'post',
    data: { ...data },
  });
}

export async function getPhonePrefixOperator(params?: any): Promise<any> {
  return request('/number-segment/phone-prefix-operator/get-list', { params: { ...params } });
}
