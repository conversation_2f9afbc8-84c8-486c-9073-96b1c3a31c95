import request from '@/utils/request';

//上行短信记录查询
export async function getUpAppSearch(params?: any): Promise<any> {
  return request('/abroad-scheduler/up-app/get-record-list', { params: { ...params } });
}

// 查询上行应用配置列表
export async function getUpAppConfigList(params?: any): Promise<any> {
  return request('/abroad-scheduler/up-app/get-list', { params: { ...params } });
}

// 新增上行应用配置
export async function addUpAppConfig(data: any): Promise<any> {
  return request('/abroad-scheduler/up-app/add', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑上行应用配置
export async function editUpAppConfig(data: any): Promise<any> {
  return request('/abroad-scheduler/up-app/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 删除上行应用配置
export async function deleteUpAppConfig(data: any): Promise<any> {
  return request('/abroad-scheduler/up-app/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 获取上行码号对应对应归属国家
export async function querySenderIdResource(params?: any): Promise<any> {
  return request('/abroad-scheduler/up-app/get-sender-country-code', {
    params: { ...params },
  });
}
