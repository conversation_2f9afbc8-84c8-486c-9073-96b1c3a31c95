import request from '@/utils/request';
import _ from 'lodash';
import qs from 'query-string';

/* 签名管理 */
// 签名权重管理
export async function getWeightInfo(params?: any): Promise<any> {
  return request('/sign/weight/weight-list', { params: { ...params } });
}

export async function updateWeightInfo(data: any): Promise<any> {
  return request('/sign/weight/update-weight', {
    method: 'post',
    data: { ...data },
  });
}

export async function insertWeightInfo(data: any): Promise<any> {
  return request('/sign/weight/insert-weight', {
    method: 'post',
    data: { ...data },
  });
}
// 客户备注
export async function getRemarkList(params?: any): Promise<any> {
  return request('/application/qapp-remark/get-list', { params: { ...params } });
}
export async function addRemark(data: any): Promise<any> {
  return request('/application/qapp-remark/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function editRemark(data: any): Promise<any> {
  return request('/application/qapp-remark/edit', {
    method: 'post',
    data: { ...data },
  });
}
export async function delRemark(data: any): Promise<any> {
  return request('/application/qapp-remark/delete', {
    method: 'post',
    data: { ...data },
  });
}

export async function findAuditSign(data: any): Promise<any> {
  return request('/sign/reset-sign/find-sign', {
    method: 'post',
    data: { ...data },
  });
}
export async function editSignStatus(data: any): Promise<any> {
  return request('/sign/reset-sign/update-sign', {
    method: 'post',
    data: { ...data },
  });
}

/* 应用管理--IP管理 */
// 添加ip信息
export async function insertIpInfo(data: any): Promise<any> {
  return request('/application/ip/insert', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑ip信息
export async function updateIpInfo(data: any): Promise<any> {
  return request('/application/ip/update', {
    method: 'post',
    data: { ...data },
  });
}

// 获取ip信息
export async function getIpInfo(params?: any): Promise<any> {
  return request(`/application/ip/get-list?${qs.stringify(params, { arrayFormat: 'bracket' })}`);
}

// 删除ip信息
export async function deleteIpInfo(data: any): Promise<any> {
  return request('/application/ip/delete', {
    method: 'post',
    data: { ...data },
  });
}

/* 应用管理--上行端口管理 */
// 获取上行端口信息
export async function getPortInfo(params?: any): Promise<any> {
  return request('/application/port/get-list', { params: { ...params } });
}

// 编辑上行端口信息
export async function updatePortInfo(data: any): Promise<any> {
  return request('/application/port/update', {
    method: 'post',
    data: { ...data },
  });
}

// 添加上行端口信息
export async function insertPortInfo(data: any): Promise<any> {
  return request('/application/port/insert', {
    method: 'post',
    data: { ...data },
  });
}

// 删除上行端口信息
export async function deletePortInfo(data: any): Promise<any> {
  return request('/application/port/delete', {
    method: 'post',
    data: { ...data },
  });
}

/* 应用管理--权重管理 */
export async function getWeight(params?: any): Promise<any> {
  return request('/sms/weight/get-list', { params: { ...params } });
}

// 获取应用未分配的应用子码通道组
export async function getUnanssignedGroup(params?: any): Promise<any> {
  return request('/canal-channal-group/group/get-unanssigned-group', { params: { ...params } });
}

export async function insertWeight(data: any): Promise<any> {
  return request('/sms/weight/insert', {
    method: 'post',
    data: { ...data },
  });
}

export async function updateWeight(data: any): Promise<any> {
  return request('/sms/weight/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function getFrqWhite(params?: any): Promise<any> {
  return request('/application/frqwhite/get-list', { params: { ...params } });
}

export async function insertFrqWhite(data: any): Promise<any> {
  return request('/application/frqwhite/insert', {
    method: 'post',
    data: { ...data },
  });
}

export async function updateFrqWhite(data: any): Promise<any> {
  return request('/application/frqwhite/update', {
    method: 'post',
    data: { ...data },
  });
}

// 退订名单管理
export async function operateSignUnsub(data: any): Promise<any> {
  return request('/sign/unsubscribe/exec', {
    method: 'post',
    data: _.pickBy(data, (v) => !_.isNil(v) && v !== ''),
  });
}

export async function querySignUnsub(params?: any): Promise<any> {
  return request(`/sign/unsubscribe/query?${qs.stringify(params, { arrayFormat: 'bracket' })}`);
}

// 应用签名子码管理
export async function getAppSign(params?: any): Promise<any> {
  return request('/sign/appsign/get-list', { params: { ...params } });
}
export async function insertAppSign(data: any): Promise<any> {
  return request('/sign/appsign/insert', {
    method: 'post',
    data: { ...data },
  });
}
export async function updateAppSign(data: any): Promise<any> {
  return request('/sign/appsign/update', {
    method: 'post',
    data: { ...data },
  });
}

// 后付费
export async function getPayList(params?: any): Promise<any> {
  return request('/sms/paid/get-list', { params: { ...params } });
}
export async function addPayList(data: any): Promise<any> {
  return request('/sms/paid/insert', {
    method: 'post',
    data: { ...data },
  });
}
export async function delPayList(data: any): Promise<any> {
  return request('/sms/paid/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 签名强制调度
export async function getSignForce(params?: any): Promise<any> {
  return request('/sign/force/get-list', { params: { ...params } });
}

export async function insertSignForce(data: any): Promise<any> {
  return request('/sign/force/insert', {
    method: 'post',
    data: { ...data },
  });
}
export async function delSignForce(data: any): Promise<any> {
  return request('/sign/force/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 金融游戏营销签名权重管理
export async function getMarketWeight(params?: any): Promise<any> {
  return request('/sign/weight/market-weight-list', { params: { ...params } });
}

export async function insertMarketWeight(data: any): Promise<any> {
  return request('/sign/weight/insert-market-weight', {
    method: 'post',
    data: { ...data },
  });
}

export async function delMarketWeight(data: any): Promise<any> {
  return request('/sign/weight/delete-market-weight', {
    method: 'post',
    data: { ...data },
  });
}

export async function updateMarketWeight(data: any): Promise<any> {
  return request('/sign/weight/update-market-weight', {
    method: 'post',
    data: { ...data },
  });
}

// 签名列表
export async function getSignList(params?: any): Promise<any> {
  return request('/sign/sign/get-list', { params: { ...params } });
}

export async function insertSignList(data: any): Promise<any> {
  return request('/sign/sign/insert', {
    method: 'post',
    data: { ...data },
  });
}

export async function editSignList(data: any): Promise<any> {
  return request('/sign/sign/update', {
    method: 'post',
    data: { ...data },
  });
}

/* 应用管理--频率规则管理 */
// 获取频率规则
export async function getFrqInfo(params?: any): Promise<any> {
  return request('/application/frq/get-list', { params: { ...params } });
}

// 添加频率规则
export async function insertFrqInfo(data: any): Promise<any> {
  return request('/application/frq/insert', {
    method: 'post',
    data: { ...data },
  });
}

// 编辑频率规则
export async function updateFrqInfo(data: any): Promise<any> {
  return request('/application/frq/update', {
    method: 'post',
    data: { ...data },
  });
}

// 营销短信时间控制
export async function setAllowTime(data: any): Promise<any> {
  return request('/application/bizsms/set-allow-time', {
    method: 'post',
    data: { ...data },
  });
}
export async function addWhiteList(data: any): Promise<any> {
  return request('/application/bizsms/add-allow-app', {
    method: 'post',
    data: { ...data },
  });
}
export async function delWhiteList(data: any): Promise<any> {
  return request('/application/bizsms/delete-allow-app', {
    method: 'post',
    data: { ...data },
  });
}
export async function getAllowTime(params?: any): Promise<any> {
  return request('/application/bizsms/get-allow-time', { params: { ...params } });
}
export async function getWhiteList(params?: any): Promise<any> {
  return request('/application/bizsms/allow-app-list', { params: { ...params } });
}

// 营销短信拦截缓存
export async function getCacheList(params?: any): Promise<any> {
  return request('/application/bizsms/hold-app-list', { params: { ...params } });
}
export async function addCacheList(data: any): Promise<any> {
  return request('/application/bizsms/add-hold-app', {
    method: 'post',
    data: { ...data },
  });
}
export async function editCacheList(data: any): Promise<any> {
  return request('/application/bizsms/edit-hold-app', {
    method: 'post',
    data: { ...data },
  });
}
export async function delCacheList(data: any): Promise<any> {
  return request('/application/bizsms/delete-hold-app', {
    method: 'post',
    data: { ...data },
  });
}

// cmpp返回码号
export async function getCmppReturnList(params?: any): Promise<any> {
  return request('/application/sign-hardcode-srcid/get-list', { params: { ...params } });
}
export async function addCmppReturn(data: any): Promise<any> {
  return request('/application/sign-hardcode-srcid/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function editCmppReturn(data: any): Promise<any> {
  return request('/application/sign-hardcode-srcid/edit', {
    method: 'post',
    data: { ...data },
  });
}
export async function delCmppReturn(data: any): Promise<any> {
  return request('/application/sign-hardcode-srcid/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 日志独立通道管理
export async function getLargeLog(params?: any): Promise<any> {
  return request('/application/app-large-log/get-list', { params: { ...params } });
}
export async function addLargeLog(data: any): Promise<any> {
  return request('/application/app-large-log/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function delLargeLog(data: any): Promise<any> {
  return request('/application/app-large-log/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 禁用归属地查询
export async function getDisableMobile(params?: any): Promise<any> {
  return request('/application/app-disable-mobile-query/get-list', { params: { ...params } });
}
export async function addDisableMobile(data: any): Promise<any> {
  return request('/application/app-disable-mobile-query/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function delDisableMobile(data: any): Promise<any> {
  return request('/application/app-disable-mobile-query/delete', {
    method: 'post',
    data: { ...data },
  });
}

// 消息队列速率控制
export async function getRateCtl(params?: any): Promise<any> {
  return request('/application/mq-rate-ctl/get-list', { params: { ...params } });
}
export async function addRateCtl(data: any): Promise<any> {
  return request('/application/mq-rate-ctl/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function editRateCtl(data: any): Promise<any> {
  return request('/application/mq-rate-ctl/edit', {
    method: 'post',
    data: { ...data },
  });
}

// 短信应用信息查询
export async function getAppInfo(params?: any): Promise<any> {
  return request('/sms/app/get-app-info', { params: { ...params } });
}
export async function setPullApp(data: any): Promise<any> {
  return request('/sms/app/set-pull-app', {
    method: 'post',
    data: { ...data },
  });
}
export async function getAppDetail(params?: any): Promise<any> {
  return request('/sms/app/get-app-detail', { params: { ...params } });
}
export async function setAppDetail(data: any): Promise<any> {
  return request('/sms/app/update-app-info', {
    method: 'post',
    data: { ...data },
  });
}
// 获取告警联系人
export async function getAppWarnContactor(params?: any): Promise<any> {
  return request('/sms/app/get-news-receivers', { params: { ...params } });
}

//
export async function getDirtyWhite(params?: any): Promise<any> {
  return request('/sms/app/get-dirty-word-white', { params: { ...params } });
}
export async function getAppCmq(params?: any): Promise<any> {
  return request('/sms/app/get-app-cmq', { params: { ...params } });
}
export async function getDirtyBlack(params?: any): Promise<any> {
  return request('/sms/app/get-dirty-word', { params: { ...params } });
}
export async function addDirtyWhite(data: any): Promise<any> {
  return request('/sms/app/add-dirty-word-white', {
    method: 'post',
    data: { ...data },
  });
}
export async function setAppCmq(data: any): Promise<any> {
  return request('/sms/app/set-app-cmq', {
    method: 'post',
    data: { ...data },
  });
}
export async function addDirtyBlack(data: any): Promise<any> {
  return request('/sms/app/add-dirty-word', {
    method: 'post',
    data: { ...data },
  });
}
export async function delDirtyBlack(data: any): Promise<any> {
  return request('/sms/app/delete-dirty-word', {
    method: 'post',
    data: { ...data },
  });
}
export async function delAppCmq(data: any): Promise<any> {
  return request('/sms/app/del-app-cmq', {
    method: 'post',
    data: { ...data },
  });
}
export async function getAppGroupIDs(params?: any): Promise<any> {
  return request('/canal-channal-group/group/get-unanssigned-group', { params: { ...params } });
}
export async function getAppGroup(params?: any): Promise<any> {
  return request('/canal-channal-group/group/get-app-group', { params: { ...params } });
}
export async function addAppGroup(data: any): Promise<any> {
  return request('/canal-channal-group/group/add-app', {
    method: 'post',
    data: { ...data },
  });
}
export async function editAppGroup(data: any): Promise<any> {
  return request('/canal-channal-group/group/update-app-group', {
    method: 'post',
    data: { ...data },
  });
}
export async function delAppGroup(data: any): Promise<any> {
  return request('/canal-channal-group/group/delete-app-group', {
    method: 'post',
    data: { ...data },
  });
}
export async function getIotsmsOperator(params?: any): Promise<any> {
  return request('/sms/app/get-iotsms-operator', { params: { ...params } });
}
export async function editIotsmsOperator(data: any): Promise<any> {
  return request('/sms/app/update-iotsms-operator', {
    method: 'post',
    data: { ...data },
  });
}
export async function addIotsmsOperator(data: any): Promise<any> {
  return request('/sms/app/add-iotsms-operator', {
    method: 'post',
    data: { ...data },
  });
}
// bit位管理
export async function getCommonBitDefine(params?: any): Promise<any> {
  return request('/sms/app/get-common-bit-define', { params: { ...params } });
}
export async function editCommonBitDefine(data: any): Promise<any> {
  return request('/sms/app/edit-common-bit-define', {
    method: 'post',
    data: { ...data },
  });
}
export async function addCommonBitDefine(data: any): Promise<any> {
  return request('/sms/app/add-common-bit-define', {
    method: 'post',
    data: { ...data },
  });
}

// 恶意号码处理
export async function getRiskyList(params?: any): Promise<any> {
  return request('/application/deal-risky-phone/get-list', { params: { ...params } });
}
export async function addRiskyInfo(data: any): Promise<any> {
  return request('/application/deal-risky-phone/add', {
    method: 'post',
    data: { ...data },
  });
}
export async function editRiskyInfo(data: any): Promise<any> {
  return request('/application/deal-risky-phone/edit', {
    method: 'post',
    data: { ...data },
  });
}
export async function delRiskyInfo(data: any): Promise<any> {
  return request('/application/deal-risky-phone/delete', {
    method: 'post',
    data: { ...data },
  });
}

/** 全局管理 */
// 一键禁用
export async function getForbidProv(params?: any): Promise<any> {
  return request('/global-setting/forbid-provider/get-list', { params: { ...params } });
}
export async function addForbidProv(data: any): Promise<any> {
  return request('/global-setting/forbid-provider/add-forbid', {
    method: 'post',
    data: { ...data },
  });
}
export async function delForbidProv(data: any): Promise<any> {
  return request('/global-setting/forbid-provider/delete-forbid', {
    method: 'post',
    data: { ...data },
  });
}
// 调度干预设置
export async function getDispatchIntrvntn(params?: any): Promise<any> {
  return request('/global-setting/dispatch-intervention/get', { params: { ...params } });
}
export async function setDispatchIntrvntn(data: any): Promise<any> {
  return request('/global-setting/dispatch-intervention/update', {
    method: 'post',
    data: { ...data },
  });
}

// 默认调度管理
export async function getDefaultWeight(params?: any): Promise<any> {
  return request('/global-setting/default-weight/get-list', { params: { ...params } });
}
export async function editDefaultWeight(data: any): Promise<any> {
  return request('/global-setting/default-weight/update-weight', {
    method: 'post',
    data: { ...data },
  });
}
export async function addDefaultWeight(data: any): Promise<any> {
  return request('/global-setting/default-weight/add-weight', {
    method: 'post',
    data: { ...data },
  });
}

// 全局脏字管理
export async function getDirtyWords(params?: any): Promise<any> {
  return request('/global-setting/dirty-word/get-list', { params: { ...params } });
}
export async function addDirtyWord(data: any): Promise<any> {
  return request('/global-setting/dirty-word/add', {
    method: 'post',
    data: { ...data },
  });
}

// 错误码映射管理
export async function getDeliveryStatus(params?: any): Promise<any> {
  return request('/global-setting/delivery-status/get-list', { params: { ...params } });
}
export async function editDeliveryStatus(data: any): Promise<any> {
  return request('/global-setting/delivery-status/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function addDeliveryStatus(data: any): Promise<any> {
  return request('/global-setting/delivery-status/add', {
    method: 'post',
    data: { ...data },
  });
}

// 错误码映射管理
export async function getForbiddenAppid(params?: any): Promise<any> {
  return request('/global-setting/forbidden-appid/get-list', { params: { ...params } });
}
export async function delForbiddenAppid(data: any): Promise<any> {
  return request('/global-setting/forbidden-appid/delete', {
    method: 'post',
    data: { ...data },
  });
}
export async function addForbiddenAppid(data: any): Promise<any> {
  return request('/global-setting/forbidden-appid/add', {
    method: 'post',
    data: { ...data },
  });
}

// 发送任务强制审核
export async function getSendTask(params?: any): Promise<any> {
  return request('/global-setting/send-task-audit/get-list', { params: { ...params } });
}
export async function delSendTask(data: any): Promise<any> {
  return request('/global-setting/send-task-audit/delete', {
    method: 'post',
    data: { ...data },
  });
}
export async function addSendTask(data: any): Promise<any> {
  return request('/global-setting/send-task-audit/add', {
    method: 'post',
    data: { ...data },
  });
}

// 国内套餐包管理
export async function getSmsPackage(params?: any): Promise<any> {
  return request('/global-setting/sms-package/get-list', { params: { ...params } });
}
export async function editSmsPackage(data: any): Promise<any> {
  return request('/global-setting/sms-package/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function delSmsPackage(data: any): Promise<any> {
  return request('/global-setting/sms-package/delete', {
    method: 'post',
    data: { ...data },
  });
}
export async function addSmsPackage(data: any): Promise<any> {
  return request('/global-setting/sms-package/add', {
    method: 'post',
    data: { ...data },
  });
}

// 海外套餐包管理
export async function getAbroadPackage(params?: any): Promise<any> {
  return request('/global-setting/abroad-package/get-list', { params: { ...params } });
}
export async function editAbroadPackage(data: any): Promise<any> {
  return request('/global-setting/abroad-package/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function delAbroadPackage(data: any): Promise<any> {
  return request('/global-setting/abroad-package/delete', {
    method: 'post',
    data: { ...data },
  });
}
export async function addAbroadPackage(data: any): Promise<any> {
  return request('/global-setting/abroad-package/add', {
    method: 'post',
    data: { ...data },
  });
}

// 欠费不停服管理
export async function getKeeponServing(params?: any): Promise<any> {
  return request('/global-setting/keepon-serving/get-list', { params: { ...params } });
}
export async function delKeeponServing(data: any): Promise<any> {
  return request('/global-setting/keepon-serving/delete', {
    method: 'post',
    data: { ...data },
  });
}
export async function addKeeponServing(data: any): Promise<any> {
  return request('/global-setting/keepon-serving/add', {
    method: 'post',
    data: { ...data },
  });
}

// 上报导白
export async function getAllowReport(params?: any): Promise<any> {
  return request('/global-setting/allow-report/get-list', { params: { ...params } });
}
export async function editAllowReport(data: any): Promise<any> {
  return request('/global-setting/allow-report/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function addAllowReport(data: any): Promise<any> {
  return request('/global-setting/allow-report/add', {
    method: 'post',
    data: { ...data },
  });
}

// URL检测正则配置
export async function getUrlReg(params?: any): Promise<any> {
  return request('/global-setting/url-regex/get-list', { params: { ...params } });
}
export async function editUrlReg(data: any): Promise<any> {
  return request('/global-setting/url-regex/update', {
    method: 'post',
    data: { ...data },
  });
}
export async function delUrlReg(data: any): Promise<any> {
  return request('/global-setting/url-regex/delete', {
    method: 'post',
    data: { ...data },
  });
}
export async function addUrlReg(data: any): Promise<any> {
  return request('/global-setting/url-regex/add', {
    method: 'post',
    data: { ...data },
  });
}

// 短信日用量查询
export async function getDailyAmount(params?: any): Promise<any> {
  return request('/sms/amount/get-daily-amount', { params: { ...params } });
}

/** 模板管理 */
// 模板管理
export async function getTplList(params?: any): Promise<any> {
  return request('/template/template/get-template-list', { params: { ...params } });
}
export async function addTplList(data: any): Promise<any> {
  return request('/template/template/insert-template', {
    method: 'post',
    data: { ...data },
  });
}
export async function editTplList(data: any): Promise<any> {
  return request('/template/template/update-template', {
    method: 'post',
    data: { ...data },
  });
}
// 模板过滤
export async function getFilterList(params?: any): Promise<any> {
  return request('/template/sms-template-filter/get-filter-list', { params: { ...params } });
}
export async function addFilterList(data: any): Promise<any> {
  return request('/template/sms-template-filter/insert-filter', {
    method: 'post',
    data: { ...data },
  });
}
export async function editFilterList(data: any): Promise<any> {
  return request('/template/sms-template-filter/update-filter', {
    method: 'post',
    data: { ...data },
  });
}
export async function delFilterList(data: any): Promise<any> {
  return request('/template/sms-template-filter/delete-filter', {
    method: 'post',
    data: { ...data },
  });
}
// 模板强制转换
export async function getReplaceList(params?: any): Promise<any> {
  return request('/template/sms-type-replace/get-replace-list', { params: { ...params } });
}
export async function addReplaceList(data: any): Promise<any> {
  return request('/template/sms-type-replace/insert-replace', {
    method: 'post',
    data: { ...data },
  });
}
export async function editReplaceList(data: any): Promise<any> {
  return request('/template/sms-type-replace/update-replace', {
    method: 'post',
    data: { ...data },
  });
}
export async function delReplaceList(data: any): Promise<any> {
  return request('/template/sms-type-replace/delete-replace', {
    method: 'post',
    data: { ...data },
  });
}

// 模版审核
export async function findAuditTpl(data: any): Promise<any> {
  return request('/template/reset-template/find-template', {
    method: 'post',
    data: { ...data },
  });
}
export async function editTplStatus(data: any): Promise<any> {
  return request('/template/reset-template/update-template', {
    method: 'post',
    data: { ...data },
  });
}
/* 监控调度配置 */
// 获取供应商列表
export async function getSupplierList(params?: any): Promise<any> {
  return request('/monitor/supplier/get-list', { params: { ...params } });
}

// 供应商配置
export async function editSupConf(data: any): Promise<any> {
  return request('/monitor/monitor-dispatch/set-sup-conf', {
    method: 'post',
    data: { ...data },
  });
}
export async function getSupConf(params?: any): Promise<any> {
  return request(
    `/monitor/monitor-dispatch/get-sup-conf?${qs.stringify(params, { arrayFormat: 'bracket' })}`,
  );
}

// 供应商运营商配置
export async function editSupTelConf(data: any): Promise<any> {
  return request('/monitor/monitor-dispatch/set-sup-tel-conf', {
    method: 'post',
    data: { ...data },
  });
}
export async function getSupTelConf(params?: any): Promise<any> {
  return request(
    `/monitor/monitor-dispatch/get-sup-tel-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

// 应用供应商配置
export async function getSupAppConf(params?: any): Promise<any> {
  return request(
    `/monitor/monitor-dispatch/get-sup-app-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}
export async function editSupAppConf(data: any): Promise<any> {
  return request('/monitor/monitor-dispatch/set-sup-app-conf', {
    method: 'post',
    data: { ...data },
  });
}

// 应用供应商运营商配置
export async function editSupAppTelConf(data: any): Promise<any> {
  return request('/monitor/monitor-dispatch/set-sup-app-tel-conf', {
    method: 'post',
    data: { ...data },
  });
}
export async function getSupAppTelConf(params?: any): Promise<any> {
  return request(
    `/monitor/monitor-dispatch/get-sup-app-tel-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

// 添加账号供应商配置
export async function editSupQappConf(data: any): Promise<any> {
  return request('/monitor/monitor-dispatch/set-sup-qapp-conf', {
    method: 'post',
    data: { ...data },
  });
}
export async function getSupQappConf(params?: any): Promise<any> {
  return request(
    `/monitor/monitor-dispatch/get-sup-qapp-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

// 添加账号供应商运营商配置
export async function editSupQappTelConf(data: any): Promise<any> {
  return request('/monitor/monitor-dispatch/set-sup-qapp-tel-conf', {
    method: 'post',
    data: { ...data },
  });
}
export async function getSupQappTelConf(params?: any): Promise<any> {
  return request(
    `/monitor/monitor-dispatch/get-sup-qapp-tel-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

// 默认调度配置
export async function getDefaultConf(params?: any): Promise<any> {
  return request('/monitor/monitor-dispatch/get-default-conf', { params: { ...params } });
}
export async function setDefaultConf(data: any): Promise<any> {
  return request('/monitor/monitor-dispatch/update-default-conf', {
    method: 'post',
    data: { ...data },
  });
}
/* 营销调度配置 */
// 供应商配置
export async function editMktSupConf(data: any): Promise<any> {
  return request('/monitor/market-monitor-dispatch/set-sup-conf', {
    method: 'post',
    data: { ...data },
  });
}
export async function getMktSupConf(params?: any): Promise<any> {
  return request(
    `/monitor/market-monitor-dispatch/get-sup-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

// 供应商运营商配置
export async function editMktSupTelConf(data: any): Promise<any> {
  return request('/monitor/market-monitor-dispatch/set-sup-tel-conf', {
    method: 'post',
    data: { ...data },
  });
}
export async function getMktSupTelConf(params?: any): Promise<any> {
  return request(
    `/monitor/market-monitor-dispatch/get-sup-tel-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

// 客户供应商配置
export async function editMktSupQappConf(data: any): Promise<any> {
  return request('/monitor/market-monitor-dispatch/set-sup-qapp-conf', {
    method: 'post',
    data: { ...data },
  });
}

// 应用供应商配置
export async function getMktSupAppConf(params?: any): Promise<any> {
  return request(
    `/monitor/market-monitor-dispatch/get-sup-app-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}
export async function editMktSupAppConf(data: any): Promise<any> {
  return request('/monitor/market-monitor-dispatch/set-sup-app-conf', {
    method: 'post',
    data: { ...data },
  });
}

// 应用供应商运营商配置
export async function getMktSupAppTelConf(params?: any): Promise<any> {
  return request(
    `/monitor/market-monitor-dispatch/get-sup-app-tel-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

// 默认调度配置
export async function getMktDefaultConf(params?: any): Promise<any> {
  return request('/monitor/market-monitor-dispatch/get-default-conf', {
    params: { ...params },
  });
}
export async function setMktDefaultConf(data: any): Promise<any> {
  return request('/monitor/market-monitor-dispatch/update-default-conf', {
    method: 'post',
    data: { ...data },
  });
}
export async function getMktSupQappConf(params?: any): Promise<any> {
  return request(
    `/monitor/market-monitor-dispatch/get-sup-qapp-conf?${qs.stringify(params, {
      arrayFormat: 'bracket',
    })}`,
  );
}

// 短信回复检查
export async function getReplyCheckList(params?: any): Promise<any> {
  return request('/monitor/check-reply-extend/get-list', { params: { ...params } });
}
