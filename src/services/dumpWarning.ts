import { findName, getLabel } from '@/components/GlobalHeader/RightContent';
import request from '@/utils/request';
import routes from '../../config/routes';
import _ from 'lodash';

function findTitle(p: string, menu: any) {
  const { name } = findName(p, menu);
  const res = getLabel(name);
  return res || '';
}

// 导出告警
export async function dumpwarning({
  route,
  params,
}: {
  route: string;
  params?: Record<string, any>;
}): Promise<any> {
  const route_name = findTitle(window.location.pathname, routes);
  return request('/global-setting/dump/warning', {
    params: {
      route,
      route_name,
      params: _.omit(
        _.pickBy(params, (v) => v !== '' && !_.isNil(v)),
        ['page_index', 'page_size'],
      ),
    },
  });
}
