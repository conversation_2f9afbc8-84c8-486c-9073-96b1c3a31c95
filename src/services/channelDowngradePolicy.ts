import request from '@/utils/request';
//查询补发策略列表
export async function getDowngradePolicyList(params?: any): Promise<any> {
  return request('/abroad-scheduler/downgrade-policy/get-list', {
    params: { ...params },
  });
}

//
export async function addDowngradePolicy(data: any): Promise<any> {
  return request('/abroad-scheduler/downgrade-policy/add', {
    method: 'post',
    data: { ...data },
  });
}
//
export async function editDowngradePolicy(data: any): Promise<any> {
  return request('/abroad-scheduler/downgrade-policy/edit', {
    method: 'post',
    data: { ...data },
  });
}
//
export async function deletetDowngradePolicy(data: any): Promise<any> {
  return request('/abroad-scheduler/downgrade-policy/delete', {
    method: 'post',
    data: { ...data },
  });
}
