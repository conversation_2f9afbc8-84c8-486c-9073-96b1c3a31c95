export const smsType = [
  {
    label: 'OTP验证码',
    value: 1,
  },
  {
    label: 'NOTICE通知',
    value: 2,
  },
  {
    label: 'MKT营销',
    value: 4,
  },
];

export const smsTypeValueMap = [
  {
    text: 'OTP验证码',
    label: 'OTP验证码',
    value: 1,
    valueMap: [1],
  },
  {
    text: 'NOTICE通知',
    label: 'NOTICE通知',
    value: 2,
    valueMap: [2],
  },
  {
    text: 'MKT营销',
    label: 'MKT营销',
    value: 4,
    valueMap: [4],
  },
  {
    text: 'OTP验证码，NOTICE通知',
    label: 'OTP验证码，NOTICE通知',
    value: 3,
    valueMap: [1, 2],
  },
  {
    text: 'OTP验证码，MKT营销',
    label: 'OTP验证码，MKT营销',
    value: 5,
    valueMap: [1, 4],
  },
  {
    text: 'NOTICE通知，MKT营销',
    label: 'NOTICE通知，MKT营销',
    value: 6,
    valueMap: [2, 4],
  },
  {
    text: 'OTP验证码，NOTICE通知，MKT营销',
    label: 'OTP验证码，NOTICE通知，MKT营销',
    value: 7,
    valueMap: [1, 2, 4],
  },
];

export const yesOrNo = [
  {
    label: '是',
    value: 1,
  },
  {
    label: '否',
    value: 0,
  },
];

export const strategyTypes = [
  { label: '直连', value: 0 },
  { label: '高混70(不含语音/OTT)', value: 1 },
  { label: '高混65(不含语音/OTT)', value: 2 },
  { label: '高混60(不含语音/OTT)', value: 3 },
  { label: '高混70(任意混合)', value: 4 },
  { label: '高混65(任意混合)', value: 5 },
  { label: '高混60(任意混合)', value: 6 },
];

export const resourceTypes = [
  { label: '直连', value: 1 },
  { label: '卡发', value: 2 },
  { label: '本地发国际', value: 3 },
  { label: '语音', value: 4 },
  { label: 'OTT', value: 5 },
  { label: '混合', value: 6 },
];

export const accountTypes = [
  { value: 0, label: '普通' },
  { value: 1, label: '营销' },
];

export const smsRegion = [
  {
    title: '广州',
    value: 'ap-guangzhou',
    children: [
      { value: 'ap-guangzhou-3', title: 'ap-guangzhou-3' },
      { value: 'ap-guangzhou-4', title: 'ap-guangzhou-4' },
    ],
  },
  {
    title: '上海',
    value: 'ap-shanghai',
    children: [{ value: 'ap-shanghai-2', title: 'ap-shanghai-2' }],
  },
  {
    title: '南京',
    value: 'ap-nanjing',
    children: [
      { value: 'ap-nanjing-1', title: 'ap-nanjing-1' },
      { value: 'ap-nanjing-2', title: 'ap-nanjing-2' },
    ],
  },
  {
    title: '北京',
    value: 'ap-beijing',
    children: [
      { value: 'ap-beijing-5', title: 'ap-beijing-5' },
      { value: 'ap-beijing-6', title: 'ap-beijing-6' },
      { value: 'ap-beijing-7', title: 'ap-beijing-7' },
    ],
  },
  {
    title: '天津',
    value: 'ap-tianjin',
    children: [
      { value: 'ap-tianjin-1', title: 'ap-tianjin-1' },
      { value: 'ap-tianjin-2', title: 'ap-tianjin-2' },
    ],
  },
  {
    title: '新加坡',
    value: 'ap-singapore',
    children: [
      { value: 'ap-singapore-1', title: 'ap-singapore-1' },
      { value: 'ap-singapore-2', title: 'ap-singapore-2' },
    ],
  },
  {
    title: '法兰克福',
    value: 'ap-frankfurt',
    children: [
      { value: 'ap-frankfurt-1', title: 'ap-frankfurt-1' },
      { value: 'ap-frankfurt-2', title: 'ap-frankfurt-2' },
    ],
  },
];

export const site = {
  singapore: 'sms-config-singpore.woa.com',
  china: 'sms-config.woa.com',
  germany: 'sms-config-germany.woa.com',
  test: 'test.sms-config.woa.com',
};

export const Region = (() => {
  if (window.location.hostname === 'sms-config-singpore.woa.com') {
    return 'ap-singapore';
  }
  if (window.location.hostname === 'sms-config-germany.woa.com') {
    return 'eu-frankfurt';
  }
  return 'ap-guangzhou';
})();

export const isChina = window.location.hostname === 'sms-config.woa.com';
