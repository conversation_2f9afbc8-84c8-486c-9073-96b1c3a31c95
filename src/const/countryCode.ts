import _ from 'lodash';

export const countryCode = [
  ['阿森松岛', 'AC', 'Ascension Island', '247'],
  ['安道尔', 'AD', 'Andorra', '376'],
  ['阿拉伯联合酋长国', 'AE', 'United Arab Emirates', '971'],
  ['阿富汗', 'AF', 'Afghanistan', '93'],
  ['安提瓜和巴布达', 'AG', 'Antigua and Barbuda', '1268'],
  ['安圭拉', 'AI', 'Anguilla', '1264'],
  ['阿尔巴尼亚', 'AL', 'Albania', '355'],
  ['亚美尼亚', 'AM', 'Armenia', '374'],
  ['荷属安的列斯', 'AN', 'Netherlands Antilles', '599'],
  ['安哥拉', 'AO', 'Angola', '244'],
  ['阿根廷', 'AR', 'Argentina', '54'],
  ['美属萨摩亚', 'AS', 'American Samoa', '1684'],
  ['奥地利', 'AT', 'Austria', '43'],
  ['澳大利亚', 'AU', 'Australia', '61'],
  ['阿鲁巴', 'AW', 'Aruba', '297'],
  ['阿塞拜疆', 'AZ', 'Azerbaijan', '994'],
  ['波斯尼亚和黑塞哥维那', 'BA', 'Bosnia and Herzegovina', '387'],
  ['巴巴多斯', 'BB', 'Barbados', '1246'],
  ['孟加拉国', 'BD', 'Bangladesh', '880'],
  ['比利时', 'BE', 'Belgium', '32'],
  ['布基纳法索', 'BF', 'Burkina Faso', '226'],
  ['保加利亚', 'BG', 'Bulgaria', '359'],
  ['巴林', 'BH', 'Bahrain', '973'],
  ['布隆迪', 'BI', 'Burundi', '257'],
  ['贝宁', 'BJ', 'Benin', '229'],
  ['百慕大群岛', 'BM', 'Bermuda', '1441'],
  ['文莱', 'BN', 'Brunei', '673'],
  ['玻利维亚', 'BO', 'Bolivia', '591'],
  ['荷兰加勒比', 'BQ', 'Caribisch Nederland', '599'],
  ['巴西', 'BR', 'Brazil', '55'],
  ['巴哈马', 'BS', 'Bahamas', '1242'],
  ['不丹', 'BT', 'Bhutan', '975'],
  ['博茨瓦纳', 'BW', 'Botswana', '267'],
  ['白俄罗斯', 'BY', 'Belarus', '375'],
  ['伯利兹', 'BZ', 'Belize', '501'],
  ['加拿大', 'CA', 'Canada', '1'],
  ['刚果民主共和国', 'CD', 'Democratic Republic of the Congo', '243'],
  ['中非共和国', 'CF', 'Central African Republic', '236'],
  ['刚果共和国', 'CG', 'Republic Of The Congo', '242'],
  ['瑞士', 'CH', 'Switzerland', '41'],
  ['象牙海岸', 'CI', 'Ivory Coast', '225'],
  ['库克群岛', 'CK', 'Cook Islands', '682'],
  ['智利', 'CL', 'Chile', '56'],
  ['喀麦隆', 'CM', 'Cameroon', '237'],
  ['哥伦比亚', 'CO', 'Colombia', '57'],
  ['哥斯达黎加', 'CR', 'Costa Rica', '506'],
  ['佛得角', 'CV', 'Cape Verde', '238'],
  ['库拉索', 'CW', 'Curacao', '599'],
  ['塞浦路斯', 'CY', 'Cyprus', '357'],
  ['捷克共和国', 'CZ', 'Czech', '420'],
  ['德国', 'DE', 'Germany', '49'],
  ['吉布提', 'DJ', 'Djibouti', '253'],
  ['丹麦', 'DK', 'Denmark', '45'],
  ['多米尼加', 'DM', 'Dominica', '1767'],
  ['多米尼加共和国', 'DO', 'Dominican Republic', '1809'],
  ['阿尔及利亚', 'DZ', 'Algeria', '213'],
  ['厄瓜多尔', 'EC', 'Ecuador', '593'],
  ['爱沙尼亚', 'EE', 'Estonia', '372'],
  ['埃及', 'EG', 'Egypt', '20'],
  ['西撒哈拉', 'EH', 'Western Sahara', '212'],
  ['厄立特里亚', 'ER', 'Eritrea', '291'],
  ['西班牙', 'ES', 'Spain', '34'],
  ['埃塞俄比亚', 'ET', 'Ethiopia', '251'],
  ['芬兰', 'FI', 'Finland', '358'],
  ['斐济', 'FJ', 'Fiji', '679'],
  ['密克罗尼西亚', 'FM', 'Micronesia', '691'],
  ['法罗群岛', 'FO', 'Faroe Islands', '298'],
  ['法国', 'FR', 'France', '33'],
  ['加蓬', 'GA', 'Gabon', '241'],
  ['英国', 'GB', 'United Kingdom', '44'],
  ['格林纳达', 'GD', 'Grenada', '1473'],
  ['格鲁吉亚', 'GE', 'Georgia', '995'],
  ['法属圭亚那', 'GF', 'French Guiana', '594'],
  ['根西岛', 'GG', 'Guernsey', '826'],
  ['加纳', 'GH', 'Ghana', '233'],
  ['直布罗陀', 'GI', 'Gibraltar', '350'],
  ['格陵兰岛', 'GL', 'Greenland', '299'],
  ['冈比亚', 'GM', 'Gambia', '220'],
  ['几内亚', 'GN', 'Guinea', '224'],
  ['瓜德罗普岛', 'GP', 'Guadeloupe', '590'],
  ['赤道几内亚', 'GQ', 'Equatorial Guinea', '240'],
  ['希腊', 'GR', 'Greece', '30'],
  ['瓜地马拉', 'GT', 'Guatemala', '502'],
  ['关岛', 'GU', 'Guam', '1671'],
  ['几内亚比绍共和国', 'GW', 'Guinea-Bissau', '245'],
  ['圭亚那', 'GY', 'Guyana', '592'],
  ['中国香港', 'HK', 'Hong Kong, China', '852'],
  ['洪都拉斯', 'HN', 'Honduras', '504'],
  ['克罗地亚', 'HR', 'Croatia', '385'],
  ['海地', 'HT', 'Haiti', '509'],
  ['匈牙利', 'HU', 'Hungary', '36'],
  ['印度尼西亚', 'ID', 'Indonesia', '62'],
  ['爱尔兰', 'IE', 'Ireland', '353'],
  ['以色列', 'IL', 'Israel', '972'],
  ['马恩岛', 'IM', 'Isle of Man', '833'],
  ['印度', 'IN', 'India', '91'],
  ['伊拉克', 'IQ', 'Iraq', '964'],
  ['冰岛', 'IS', 'Iceland', '354'],
  ['意大利', 'IT', 'Italy', '39'],
  ['泽西岛', 'JE', 'Jersey', '832'],
  ['牙买加', 'JM', 'Jamaica', '1876'],
  ['约旦', 'JO', 'Jordan', '962'],
  ['日本', 'JP', 'Japan', '81'],
  ['肯尼亚', 'KE', 'Kenya', '254'],
  ['吉尔吉斯斯坦', 'KG', 'Kyrgyzstan', '996'],
  ['柬埔寨', 'KH', 'Cambodia', '855'],
  ['基里巴斯', 'KI', 'Kiribati', '686'],
  ['科摩罗', 'KM', 'Comoros', '269'],
  ['圣基茨和尼维斯', 'KN', 'Saint Kitts and Nevis', '1869'],
  ['韩国', 'KR', 'South Korea', '82'],
  ['科威特', 'KW', 'Kuwait', '965'],
  ['开曼群岛', 'KY', 'Cayman Islands', '1345'],
  ['哈萨克斯坦', 'KZ', 'Kazakhstan', '7'],
  ['老挝', 'LA', 'Laos', '856'],
  ['黎巴嫩', 'LB', 'Lebanon', '961'],
  ['圣露西亚', 'LC', 'Saint Lucia', '1758'],
  ['列支敦士登', 'LI', 'Liechtenstein', '423'],
  ['斯里兰卡', 'LK', 'Sri Lanka', '94'],
  ['利比里亚', 'LR', 'Liberia', '231'],
  ['莱索托', 'LS', 'Lesotho', '266'],
  ['立陶宛', 'LT', 'Lithuania', '370'],
  ['卢森堡', 'LU', 'Luxembourg', '352'],
  ['拉脱维亚', 'LV', 'Latvia', '371'],
  ['利比亚', 'LY', 'Libya', '218'],
  ['摩洛哥', 'MA', 'Morocco', '212'],
  ['摩纳哥', 'MC', 'Monaco', '377'],
  ['摩尔多瓦', 'MD', 'Moldova', '373'],
  ['黑山', 'ME', 'Montenegro', '382'],
  ['马达加斯加', 'MG', 'Madagascar', '261'],
  ['马绍尔群岛', 'MH', 'Marshall Islands', '692'],
  ['马其顿', 'MK', 'Macedonia', '389'],
  ['马里', 'ML', 'Mali', '223'],
  ['缅甸', 'MM', 'Myanmar', '95'],
  ['蒙古', 'MN', 'Mongolia', '976'],
  ['中国澳门', 'MO', 'Macau, China', '853'],
  ['北马利安纳群岛', 'MP', 'Northern Mariana Islands', '1670'],
  ['马丁尼克', 'MQ', 'Martinique', '596'],
  ['毛里塔尼亚', 'MR', 'Mauritania', '222'],
  ['蒙特塞拉特岛', 'MS', 'Montserrat', '1664'],
  ['马耳他', 'MT', 'Malta', '356'],
  ['毛里求斯', 'MU', 'Mauritius', '230'],
  ['马尔代夫', 'MV', 'Maldives', '960'],
  ['马拉维', 'MW', 'Malawi', '265'],
  ['墨西哥', 'MX', 'Mexico', '52'],
  ['马来西亚', 'MY', 'Malaysia', '60'],
  ['莫桑比克', 'MZ', 'Mozambique', '258'],
  ['纳米比亚', 'NA', 'Namibia', '264'],
  ['新喀里多尼亚', 'NC', 'New Caledonia', '687'],
  ['尼日尔', 'NE', 'Niger', '227'],
  ['诺福克岛', 'NF', 'Norfolk Island', '574'],
  ['尼日利亚', 'NG', 'Nigeria', '234'],
  ['尼加拉瓜', 'NI', 'Nicaragua', '505'],
  ['荷兰', 'NL', 'Netherlands', '31'],
  ['挪威', 'NO', 'Norway', '47'],
  ['尼泊尔', 'NP', 'Nepal', '977'],
  ['瑙鲁', 'NR', 'Nauru', '674'],
  ['纽埃', 'NU', 'Niue', '683'],
  ['新西兰', 'NZ', 'New Zealand', '64'],
  ['阿曼', 'OM', 'Oman', '968'],
  ['巴拿马', 'PA', 'Panama', '507'],
  ['秘鲁', 'PE', 'Peru', '51'],
  ['法属波利尼西亚', 'PF', 'French Polynesia', '689'],
  ['巴布亚新几内亚', 'PG', 'Papua New Guinea', '675'],
  ['菲律宾', 'PH', 'Philippines', '63'],
  ['巴基斯坦', 'PK', 'Pakistan', '92'],
  ['波兰', 'PL', 'Poland', '48'],
  ['圣彼埃尔和密克隆岛', 'PM', 'Saint Pierre and Miquelon', '508'],
  ['波多黎各', 'PR', 'Puerto Rico', '1787'],
  ['巴勒斯坦', 'PS', 'Palestinian Territory', '970'],
  ['葡萄牙', 'PT', 'Portugal', '351'],
  ['帕劳', 'PW', 'Palau', '680'],
  ['巴拉圭', 'PY', 'Paraguay', '595'],
  ['卡塔尔', 'QA', 'Qatar', '974'],
  ['留尼汪', 'RE', 'Réunion Island', '262'],
  ['罗马尼亚', 'RO', 'Romania', '40'],
  ['塞尔维亚', 'RS', 'Serbia', '381'],
  ['俄罗斯', 'RU', 'Russia', '7'],
  ['卢旺达', 'RW', 'Rwanda', '250'],
  ['沙特阿拉伯', 'SA', 'Saudi Arabia', '966'],
  ['所罗门群岛', 'SB', 'Solomon Islands', '677'],
  ['塞舌尔', 'SC', 'Seychelles', '248'],
  ['苏丹', 'SD', 'Sudan', '249'],
  ['瑞典', 'SE', 'Sweden', '46'],
  ['新加坡', 'SG', 'Singapore', '65'],
  ['斯洛文尼亚', 'SI', 'Slovenia', '386'],
  ['斯洛伐克', 'SK', 'Slovakia', '421'],
  ['塞拉利昂', 'SL', 'Sierra Leone', '232'],
  ['圣马力诺', 'SM', 'San Marino', '378'],
  ['塞内加尔', 'SN', 'Senegal', '221'],
  ['索马里', 'SO', 'Somalia', '252'],
  ['苏里南', 'SR', 'Suriname', '597'],
  ['南苏丹', 'SS', 'South Sudan', '211'],
  ['圣多美和普林西比', 'ST', 'Sao Tome and Principe', '239'],
  ['萨尔瓦多', 'SV', 'El Salvador', '503'],
  ['荷属圣马丁', 'SX', 'Sint Maarten (Dutch Part)', '1721'],
  ['斯威士兰', 'SZ', 'Swaziland', '268'],
  ['特克斯和凯科斯群岛', 'TC', 'Turks and Caicos Islands', '1649'],
  ['乍得', 'TD', 'Chad', '235'],
  ['多哥', 'TG', 'Togo', '228'],
  ['泰国', 'TH', 'Thailand', '66'],
  ['塔吉克斯坦', 'TJ', 'Tajikistan', '992'],
  ['托克劳', 'TK', 'Tokelau', '690'],
  ['东帝汶', 'TL', 'East Timor', '670'],
  ['土库曼斯坦', 'TM', 'Turkmenistan', '993'],
  ['突尼斯', 'TN', 'Tunisia', '216'],
  ['汤加', 'TO', 'Tonga', '676'],
  ['土耳其', 'TR', 'Türkiye', '90'],
  ['特立尼达和多巴哥', 'TT', 'Trinidad and Tobago', '1868'],
  ['图瓦卢', 'TV', 'Tuvalu', '688'],
  ['中国台湾', 'TW', 'Taiwan, China', '886'],
  ['坦桑尼亚', 'TZ', 'Tanzania', '255'],
  ['乌克兰', 'UA', 'Ukraine', '380'],
  ['乌干达', 'UG', 'Uganda', '256'],
  ['美国', 'US', 'United States', '1'],
  ['乌拉圭', 'UY', 'Uruguay', '598'],
  ['乌兹别克斯坦', 'UZ', 'Uzbekistan', '998'],
  ['圣文森特和格林纳丁斯', 'VC', 'Saint Vincent and The Grenadines', '1784'],
  ['委内瑞拉', 'VE', 'Venezuela', '58'],
  ['英属维尔京群岛', 'VG', 'Virgin Islands, British', '1284'],
  ['美属维尔京群岛', 'VI', 'Virgin Islands, US', '1340'],
  ['越南', 'VN', 'Vietnam', '84'],
  ['瓦努阿图', 'VU', 'Vanuatu', '678'],
  ['萨摩亚', 'WS', 'Samoa', '685'],
  ['科索沃共和国', 'XK', 'Kosovo', '383'],
  ['也门', 'YE', 'Yemen', '967'],
  ['马约特', 'YT', 'Mayotte', '269'],
  ['南非', 'ZA', 'South Africa', '27'],
  ['赞比亚', 'ZM', 'Zambia', '260'],
  ['津巴布韦', 'ZW', 'Zimbabwe', '263'],
];

export const regionOptions = _.reduce(
  countryCode,
  (res: { label: string; value: string }[], item) => {
    res.push({
      label: `${item[0]}_${item[1]}`,
      value: item[1],
    });
    return res;
  },
  [],
);

export const regionOptionsByName = _.reduce(
  countryCode,
  (res: { label: string; value: string }[], item) => {
    res.push({
      label: item[0],
      value: item[1],
    });
    return res;
  },
  [],
);

export const regionOptionsByNationCode = _.reduce(
  countryCode,
  (res: { label: string; value: number }[], item) => {
    res.push({
      label: `${item[0]}_${item[3]}`,
      value: +item[3],
    });
    return res;
  },
  [],
);
