import { Checkbox, Divider, Select } from 'antd';
import React, { useEffect, useState } from 'react';

export default function SelectAll({
  value,
  onChange,
  showAll = true,
  options,
  mode = 'multiple',
  ...restProps
}: {
  value?: any;
  onChange?: (value: any) => void;
  showAll?: boolean;
  mode?: 'multiple' | 'tags' | undefined;
  options: any;

  [key: string]: any;
}) {
  const [selectAllState, setSelectAllState] = useState(false);

  useEffect(() => {
    setSelectAllState(value?.length === options?.length);
  }, [options?.length, value]);

  return (
    <Select
      value={value}
      onChange={onChange}
      mode={mode}
      options={options}
      allowClear
      showSearch
      placeholder="请选择"
      filterOption={(inputValue, option: any) =>
        !!option?.label.toLowerCase().includes(inputValue.toLocaleLowerCase())
      }
      dropdownRender={
        showAll
          ? (allSelectValue: any) => (
              <div>
                <div style={{ padding: '4px 8px 8px 8px', cursor: 'pointer' }}>
                  <Checkbox
                    checked={selectAllState}
                    onChange={(e) => {
                      // 判断 是否 选中
                      if (e.target.checked === true) {
                        setSelectAllState(true);
                        onChange?.(options?.map((item: any) => item.value));
                      } else {
                        setSelectAllState(false);
                        onChange?.([]);
                      }
                    }}
                  >
                    全选
                  </Checkbox>
                </div>
                <Divider style={{ margin: '0' }} />
                {allSelectValue}
              </div>
            )
          : undefined
      }
      {...restProps}
    />
  );
}
