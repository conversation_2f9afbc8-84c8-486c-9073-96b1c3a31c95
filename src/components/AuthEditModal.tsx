import React, { useState, useEffect } from 'react';
import { Input, Form, Modal, Radio, Alert } from 'antd';
import { editAuth } from '@/services/api';

interface Props {
  dialogRef: any;
  reload: () => void;
}

const AuthEditModal = (props: Props) => {
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  // const [initData, setInitData] = useState<any>({})
  const [configIds, setConfigIds] = useState<string>('');
  const { dialogRef, reload } = props;

  useEffect(() => {
    dialogRef.current = {
      toggleModalVisible: (row?: any) => {
        if (!Array.isArray(row)) {
          form.setFieldsValue({
            appids: row.appids,
            sdkappids: row.sdkappids,
            status: row.auth_status,
          });
          setConfigIds(`${row.conf_id}`);
        } else {
          setConfigIds(row.join(','));
        }
        setIsModalVisible(true);
      },
    };
    !isModalVisible && form.resetFields();
  }, [isModalVisible]);

  async function onSubmit(vals: any) {
    const data = { ...vals };
    data.appids === 'null' && (data.appids = 0);
    data.sdkappids === 'null' && (data.sdkappids = 0);
    await editAuth({
      ...data,
      conf_ids: configIds,
    });
    setIsModalVisible(false);
    reload();
  }
  return (
    <Modal
      title="编辑配置"
      open={isModalVisible}
      onOk={() => form.submit()}
      onCancel={() => setIsModalVisible(false)}
    >
      <Alert
        message="如果需要清空appids, sdkappids, 请填入null"
        type="info"
        showIcon
        style={{ marginBottom: 20 }}
      />
      <Form
        labelCol={{ span: 6 }}
        form={form}
        labelAlign="right"
        onFinish={(vals) => {
          onSubmit(vals);
        }}
      >
        <Form.Item name="appids" label="appid白名单">
          <Input placeholder="腾讯云appid白名单，用,隔开" style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="sdkappids" label="sdkappid白名单">
          <Input placeholder="腾讯云sdkappid白名单，用,隔开" style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="status" label="状态">
          <Radio.Group>
            <Radio value={0}>启用</Radio>
            <Radio value={1}>禁用</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default AuthEditModal;
