// 执行审核操作时，通道绑定定制化弹窗
import React, { useMemo } from 'react';
import { Table, Typography } from 'antd';
import { channelType } from '@/pages/channel/component/const';
import { smsType } from '@/const/const';
import _ from 'lodash';
import useMccMncInfo from '../Hooks/UseFetchMccMncInfo';
import useFetchCountryInfo from '../Hooks/UseFetchCountryInfo';

const { Text } = Typography;

type ErrorType = {
  line: number;
  param: any;
  code: number;
  msg: string;
  data: any[];
}[];

const ChannelBindErrorModal = (props: { errorInfo: ErrorType }) => {
  const { regionOptions } = useFetchCountryInfo();
  const [mccMncInfo] = useMccMncInfo();
  const { errorInfo } = props;
  const list = useMemo(() => {
    return errorInfo.map((item) => {
      const obj = {
        ...item.param,
        code: item.code,
        msg: item.msg,
        children: Array.isArray(item.data) ? item.data : [],
      };
      return obj;
    });
  }, [errorInfo]);

  const expandedRowRender = (record: any) => {
    return (
      <>
        <Table
          columns={[
            { title: '通道id', dataIndex: 'account_id', key: 'account_id' },
            {
              title: '国家',
              key: 'country_code',
              render: (row: any) => `${row.country_code}(${row.mcc})`,
            },
            {
              title: '运营商',
              key: 'operator_name',
              render: (row: any) => `${row.operator_name}(${row.mnc})`,
            },
          ]}
          dataSource={record.children}
          pagination={false}
          rowKey="account_id"
        />
      </>
    );
  };

  const columns = useMemo(() => {
    const type = errorInfo[0]?.param.type;
    const allCols = [
      {
        title: '通道类型',
        dataIndex: 'type',
        key: 'type',
        width: 80,
        render: (val: number) => channelType.find((el) => el.value === val)?.label,
      },
      {
        title: `${channelType.find((el) => el.value?.toString() === type?.toString())?.label}id`,
        dataIndex: 'channel_id',
        key: 'channel_id',
        width: 80,
      },
      {
        title: '通道id',
        dataIndex: 'account_id',
        key: 'account_id',
        width: 80,
      },
      {
        title: '国家',
        key: 'mcc_mnc',
        width: 80,
        render: (row: any) => {
          const countryCode = mccMncInfo?.find(
            (el) => el.mcc.toString() === row.mcc_mnc.split('_')[0],
          )?.country_code;
          const name = (regionOptions ?? []).find((el) => el.value === countryCode)?.label;
          return `${name}(${row.mcc_mnc.split('_')[0]})`;
        },
      },
      {
        title: '运营商',
        key: 'mcc_mnc',
        width: 80,
        render: (row: any) => {
          const operatorName = mccMncInfo?.find(
            (el) => el.mnc.toString() === row.mcc_mnc.split('_')[1],
          )?.operator_name;
          return `${operatorName}(${row.mcc_mnc.split('_')[1]})`;
        },
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        key: 'sms_type',
        width: 80,
        render: (val: number) =>
          smsType.find((el) => el.value?.toString() === val?.toString())?.label,
      },
      {
        title: 'code',
        key: 'code',
        dataIndex: 'code',
        width: 80,
        render: (val: string) => <Text type="danger">{val}</Text>,
      },
      {
        title: 'msg',
        key: 'msg',
        dataIndex: 'msg',
        width: 80,
        render: (val: string) => <Text type="danger">{val}</Text>,
      },
    ];
    const col1 = allCols.filter((el) =>
      ['type', 'sms_type', 'mcc_mnc', 'channel_id', 'code', 'msg'].includes(el.key),
    );
    const cols2 = allCols.filter((el) =>
      ['type', 'sms_type', 'mcc_mnc', 'channel_id', 'code', 'msg'].includes(el.key),
    );
    return type === 0 ? col1 : cols2;
  }, [errorInfo, mccMncInfo]);

  return (
    <>
      <Table
        columns={columns}
        dataSource={list}
        rowKey={(row, index) => `${row.channel_id}_${index}`}
        expandable={{
          expandRowByClick: true,
          expandedRowRender: (record) => (record.children ? expandedRowRender(record) : undefined),
          rowExpandable: (record) => !_.isEmpty(record.children),
        }}
        pagination={false}
      />
    </>
  );
};

export default ChannelBindErrorModal;
