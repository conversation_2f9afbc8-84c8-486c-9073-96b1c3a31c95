// 执行审核操作时，通道绑定定制化弹窗
import React, { useMemo } from 'react';
import { Table, Typography } from 'antd';

const { Text } = Typography;

type ErrorType = {
  line: number;
  param: any;
  code: number;
  msg: string;
}[];

const ProviderGroupBindErrorModal = (props: { errorInfo: ErrorType }) => {
  const { errorInfo } = props;
  const list = useMemo(() => {
    return errorInfo.map((el) => ({
      ...el.param,
      ...el,
    }));
  }, [errorInfo]);

  const columns = useMemo(() => {
    const cols = [
      {
        title: '策略id',
        dataIndex: 'tactic_id',
        key: 'tactic_id',
        width: 80,
      },
      {
        title: '通道组id',
        dataIndex: 'group_id',
        key: 'group_id',
        width: 80,
      },
      {
        title: '通道id',
        dataIndex: 'provider_id',
        key: 'provider_id',
        width: 80,
      },
      {
        title: '权重',
        dataIndex: 'weight',
        key: 'weight',
        width: 80,
      },
      {
        title: 'code',
        key: 'code',
        dataIndex: 'code',
        width: 80,
        render: (val: string) => <Text type="danger">{val}</Text>,
      },
      {
        title: 'msg',
        key: 'msg',
        dataIndex: 'msg',
        width: 80,
        render: (val: string) => <Text type="danger">{val}</Text>,
      },
    ];
    const col1 = cols.filter((col) =>
      ['tactic_id', 'provider_id', 'weight', 'code', 'msg'].includes(col.key),
    );
    const col2 = cols.filter((col) =>
      ['group_id', 'provider_id', 'weight', 'code', 'msg'].includes(col.key),
    );
    return list[0].tactic_id ? col1 : col2;
  }, [list]);

  return (
    <>
      <Table
        columns={columns}
        dataSource={list}
        rowKey={(row, index) => `${row.provider_id}_${index}`}
        pagination={false}
      />
    </>
  );
};

export default ProviderGroupBindErrorModal;
