import React from 'react';
import ChannelBindErrorModal from './ChannelBindErrorModal';
import ProviderGroupBindErrorModal from './ProviderGroupBindErrorModal';

export default [
  {
    apis: [
      '/abroad-scheduler/overall-channel/add',
      '/abroad-scheduler/overall-channel/edit',
      '/abroad-scheduler/uin-channel/add',
      '/abroad-scheduler/uin-channel/edit',
      '/abroad-scheduler/force-scheduler/add',
      '/abroad-scheduler/force-scheduler/edit',
      '/abroad-scheduler/sdk-channel/add',
      '/abroad-scheduler/sdk-channel/edit',
    ],
    component: (errorInfo: any) => <ChannelBindErrorModal errorInfo={errorInfo} />,
  },
  {
    apis: ['/abroad-scheduler/provider-group-tactic/bind', '/abroad-scheduler/provider-group/bind'],
    component: (errorInfo: any) => <ProviderGroupBindErrorModal errorInfo={errorInfo} />,
  },
];
