import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Select, Spin } from 'antd';
import { useUpdateEffect } from 'react-use';

export type SelectOptionWithGroup = {
  value: string;
  text?: React.ReactNode;
  disabled?: boolean;
  groupKey?: string;
};

interface Props {
  mode?: 'multiple' | 'tags' | 'default' | undefined;
  onChange?: (value: string, context: { [key in string]: any }) => any;
  value?: any;
  loadFn: (params: any) => Promise<{
    options: {
      label: string;
      value: string;
    }[];
    total: number;
  }>;
  reloadOn?: string;
  placeholder?: string;
  disabled?: boolean;
  dropdownRender?: any;
  pageSize?: number;
  [key: string]: any;
}

const ScrollLoadMore = (props: Props) => {
  const {
    onChange,
    value,
    loadFn,
    reloadOn,
    mode,
    placeholder,
    disabled,
    dropdownRender,
    pageSize = 100,
    ...restProps
  } = props;
  const [pageNo, setPageNo] = useState(1);
  const [isFirst, setIsFirst] = useState(true);
  const loading = useRef<boolean>(false);
  const [firstOpen, setFirstOpen] = useState(true);
  const [totalPage, setTotalPage] = useState(1);
  const [searchValue, setSearchValue] = useState<any>('');
  const [options, setOptions] = useState<any[]>([]);
  const timerRef = useRef<any>(null);
  const callbackRef = useRef<Props['loadFn']>();

  useEffect(() => {
    callbackRef.current = loadFn;
  }, [loadFn]);

  const fn = useCallback(
    async (isUnmount?: boolean) => {
      try {
        const res = await callbackRef.current?.({
          page_index: pageNo,
          page_size: pageSize || 100,
          search_key: searchValue,
        });
        if (isUnmount) return;
        if (res) {
          if (isFirst) {
            setOptions(res.options);
          } else {
            setOptions((options) => {
              return [...options, ...res.options];
            });
          }
          setTotalPage(Math.ceil(Number(res.total) / pageSize));
        }
        loading.current = false;
      } catch (error) {
        setOptions([]);
        loading.current = false;
      }
    },
    [isFirst, pageNo, searchValue],
  );

  useEffect(() => {
    let isUnmount = false;
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    timerRef.current = setTimeout(() => {
      fn(isUnmount);
    }, 600);

    return () => {
      isUnmount = true;
    };
  }, [fn]);

  useUpdateEffect(() => {
    if (!isFirst || pageNo !== 1 || searchValue) {
      setIsFirst(true);
      setPageNo(1);
      setSearchValue('');
      setFirstOpen(true);
      return;
    }
    fn();
  }, [reloadOn]);

  useEffect(() => {
    setPageNo(1);
    setIsFirst(true);
  }, [firstOpen]);

  return (
    <Select
      showSearch
      allowClear
      disabled={disabled}
      onPopupScroll={() => {
        if (!loading.current && pageNo < totalPage) {
          loading.current = true;
          setIsFirst(false);
          setPageNo((page) => page + 1);
        }
      }}
      filterOption={() => {
        return true;
      }}
      searchValue={searchValue}
      mode={mode}
      options={options}
      placeholder={placeholder || '请选择'}
      value={value}
      onChange={(value, context) => {
        onChange?.(value, context);
      }}
      notFoundContent={loading.current ? <Spin size="small" /> : null}
      autoClearSearchValue={false}
      onSearch={(keyword) => {
        setSearchValue(keyword);
        setPageNo(1);
        setIsFirst(true);
      }}
      onDropdownVisibleChange={() => {
        setFirstOpen(false);
      }}
      dropdownRender={dropdownRender}
      {...restProps}
    />
  );
};
export default ScrollLoadMore;
