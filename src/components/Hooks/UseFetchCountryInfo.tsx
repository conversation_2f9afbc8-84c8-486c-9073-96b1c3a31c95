import { useEffect } from 'react';
import { _useCountryInfo } from '@/pages/global-state';
import { getCountryList } from '@/services/api';
import _ from 'lodash';
type CountryType = {
  mcc: string;
  nation_code: string;
  nation_id: number;
  nation_name: string;
  nation_name_en: string;
};
type OptionsType = { label: string; value: string | number }[];
const useFetchCountryInfo = () => {
  const [countryInfo, setCountryInfo] = _useCountryInfo();

  useEffect(() => {
    if (!countryInfo?.setted) {
      getCountryList().then((res) => {
        const list = _.uniqBy((res?.data ?? []) as CountryType[], 'nation_code');

        const options: OptionsType = [];
        const optionsMcc: OptionsType = [];
        const optionsByNationCode: OptionsType = [];
        const optionsMccCountryCode: OptionsType = [];
        list.map((item: CountryType) => {
          options.push({
            label: `${item.nation_name}_${item.nation_code}(${item.mcc})`,
            value: item.nation_code,
          });
          optionsMcc.push({
            label: `${item.nation_name}_${item.nation_code}(${item.mcc})`,
            value: `${item.mcc}`,
          });
          optionsMccCountryCode.push({
            label: `${item.nation_name}_${item.nation_code}(${item.mcc})`,
            value: `${item.nation_code}_${item.mcc}`,
          });
          optionsByNationCode.push({
            label: `${item.nation_name}_${item.nation_id}`,
            value: +item.nation_id,
          });
        });
        setCountryInfo({
          list,
          setted: true,
          optionsMcc,
          regionOptions: options,
          regionOptionsNationCode: optionsByNationCode,
          regionOptionsMccCountryCode: optionsMccCountryCode,
        });
      });
    }
  }, [countryInfo?.setted, setCountryInfo]);

  return {
    regionOptions: countryInfo?.regionOptions,
    regionOptionsMcc: countryInfo?.optionsMcc,
    regionOptionsMccCountryCode: countryInfo?.regionOptionsMccCountryCode,
    regionOptionsNationCode: countryInfo?.regionOptionsNationCode,
    regionList: countryInfo?.list,
  } as const;
};
export default useFetchCountryInfo;
