import { useEffect } from 'react';
import { _useMccMncInfo } from '@/pages/global-state';
import { getMccMncList } from '@/services/channel';
const useMccMncInfo = () => {
  const [mccMncInfo, setMccMncInfo] = _useMccMncInfo();

  useEffect(() => {
    if (!mccMncInfo?.length) {
      getMccMncList({ page_index: 1, page_size: 10000 }).then((res) => {
        setMccMncInfo(res?.data?.list ?? []);
      });
    }
  }, [mccMncInfo?.length, setMccMncInfo]);

  return [mccMncInfo, setMccMncInfo] as const;
};
export default useMccMncInfo;
