import React, { Fragment, useCallback, useEffect, useState } from 'react';
import { isMobile } from '@/const/jadgeUserAgent';
import labelMenu from '@/locales/zh-CN/menu';
import type { ConnectState } from '@/models/connect';
import { DownOutlined, InfoCircleOutlined, UserOutlined } from '@ant-design/icons';
import type { Settings as ProSettings } from '@ant-design/pro-layout';
import { Avatar, Cascader, Dropdown, Select, Space, Tag } from 'antd';
import type { DefaultOptionType } from 'antd/es/cascader';
import _ from 'lodash';
import type { ConnectProps } from 'umi';
import { NavLink, connect, history } from 'umi';
import style from './index.module.less';
import { site } from '@/const/const';

interface Option {
  value: string;
  label: string;
  children?: Option[];
  disabled?: boolean;
}

export function findName(p: string, routes: any) {
  let r = routes;
  let r2 = [];
  for (;;) {
    // eslint-disable-next-line @typescript-eslint/prefer-for-of
    for (let i = 0; i < r.length; i++) {
      if (r[i].path === p) {
        return r[i];
      }
      if (r[i]?.routes?.length) {
        r2.push(...r[i].routes);
      }
    }
    r = r2;
    r2 = [];
    if (r.length === 0) return;
  }
}

export function getLabel(name: string): string {
  const res = _.find(labelMenu, (item, index) => {
    const labelArr = index.split('.');
    return labelArr[labelArr.length - 1] === name;
  });

  return res || '';
}

function findTitle(p: string, menu: any) {
  const { name } = findName(p, menu);
  const res = getLabel(name);
  return res || '';
}

export function getAllowRoutes(route: any) {
  const arr: any = [];
  for (const item of route) {
    if (item.path.includes('/exception') || item.hideInMenu) {
      continue;
    }
    const obj: Option & { children: Option[] } = {
      value: item.path,
      label: getLabel(item.name),
      children: [],
    };
    if (item.routes) {
      for (const child of item?.routes) {
        if (child.hideInMenu) {
          continue;
        }
        obj!.children.push({ value: child.path, label: getLabel(child.name) });
      }
    }
    arr.push(obj);
  }
  return arr;
}
export interface GlobalHeaderRightProps extends Partial<ConnectProps>, Partial<ProSettings> {
  theme?: ProSettings['navTheme'] | 'realDark';
}

const GlobalHeaderRight: React.FC<GlobalHeaderRightProps> = (props: any) => {
  const isDev = process.env.NODE_ENV === 'development' || window.location.host === site.test;

  console.log('********', isDev, window.location.host);
  const { menu } = props;
  const [options, setOptions] = useState([]);
  const [usedRecords, setUsedRecords] = useState<any>([]);
  const flag = isMobile();

  const getRecords = useCallback(() => {
    try {
      const records = JSON.parse(localStorage.getItem('routeHistory') || '[]');
      const r = _.map(records, (item) => {
        return {
          key: item.path,
          label: (
            <NavLink to={item.path} end>
              {findTitle(item.path, menu)}
            </NavLink>
          ),
        };
      });
      setUsedRecords(r);
    } catch (error) {
      setUsedRecords([]);
    }
  }, [menu]);

  useEffect(() => {
    if (!menu?.length) {
      return;
    }
    setOptions(getAllowRoutes(menu));
    getRecords();
  }, [getRecords, menu]);

  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      (option) => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1,
    );

  return (
    <div className={style.custom_header}>
      <div>
        {!isDev && (
          <>
            <Tag icon={<InfoCircleOutlined />} color="red">
              正式环境，谨慎操作
            </Tag>
            <Select
              defaultValue={location.host.split('.')[0]}
              bordered={false}
              options={[
                {
                  value: 'sms-config',
                  label: '中国区',
                },
                {
                  value: 'sms-config-singpore',
                  label: '新加坡',
                },
                {
                  value: 'sms-config-germany',
                  label: '法兰克福',
                },
              ]}
              onChange={(val) => (location.href = `https://${val}.woa.com`)}
            />
          </>
        )}
      </div>
      {!flag && (
        <Space>
          <div style={{ marginRight: 20 }}>
            {usedRecords.length > 0 && (
              <Dropdown
                menu={{ items: usedRecords }}
                arrow
                onOpenChange={(visible: boolean) => {
                  visible && getRecords();
                }}
              >
                <a onClick={(e) => e.preventDefault()}>
                  <Space>
                    最近使用过
                    <DownOutlined />
                  </Space>
                </a>
              </Dropdown>
            )}
          </div>
          <div>
            <Cascader
              options={options}
              onChange={(value: any, selectedOptions: DefaultOptionType[] = []) => {
                const path: any = selectedOptions?.at(-1)?.value;
                if (!path) {
                  return;
                }
                history.push(path);
              }}
              style={{ width: 250, marginRight: 5 }}
              placeholder="搜索模块名称快速进入"
              showSearch={{ filter }}
            />
            <Tag color={isDev ? 'green' : 'red'}>{isDev ? '测试环境' : '正式环境'}</Tag>
            <Avatar icon={<UserOutlined />} />
            <span style={{ paddingLeft: 10, fontSize: 16 }}>{props.currentUser.staff_name}</span>
          </div>
        </Space>
      )}
    </div>
  );
};

export default connect(({ user }: ConnectState) => ({
  currentUser: user.currentUserConfig || {},
}))(GlobalHeaderRight);
