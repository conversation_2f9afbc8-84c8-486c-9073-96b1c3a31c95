import React, { useEffect, useState } from 'react';
import { TreeSelect } from 'antd';
import permission from '../../config/permission';
import menu from '@/locales/zh-CN/menu';

interface AuthSelectProps {
  // apiList: any[];
  authList: any[];
  authValueChange: (data: any) => void;
}

// @ts-ignore
const AuthPageSelect = (props: AuthSelectProps) => {
  const [treeData, setTreeData] = useState<any[]>([]);
  const [authValue, setAuthValue] = useState<any[]>([]);
  const { authValueChange, authList } = props;
  useEffect(() => {
    if (authList.length) {
      setAuthValue(authList);
    }
    const fmenu = Object.keys(permission);
    const tree: any = [];
    fmenu.forEach((k1) => {
      const treeItem = {
        title: menu[`menu.${k1}`],
        value: `/${k1}`,
        key: `/${k1}`,
        children: [],
      };
      Object.keys(permission[k1])?.forEach((k2) => {
        treeItem.children.push({
          title: menu[`menu.${k1}.${k1}-${k2}`],
          value: `/${k1}/${k2}`,
          key: `/${k1}/${k2}`,
        });
      });
      tree.push(treeItem);
    });
    setTreeData(tree);
  }, [authList]);

  function onChange(val: any) {
    setAuthValue(val);
    authValueChange(val);
  }
  return (
    <TreeSelect
      // defaultExpandAll
      value={authValue}
      treeCheckable
      treeData={treeData}
      maxTagCount={2}
      allowClear
      style={{ width: 300 }}
      placeholder="请选择"
      onChange={(val) => onChange(val)}
    />
  );
};

export default AuthPageSelect;
