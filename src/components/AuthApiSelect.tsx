import React, { useEffect, useState } from 'react';
import { TreeSelect } from 'antd';
import _ from 'lodash';
import type { ConnectState } from '@/models/connect';
import { specialApis } from '../../config/specialApi';
import { connect } from 'dva';

interface AuthSelectProps {
  apiList: any[];
  authList: any[];
  authValueChange: (data: any) => void;
  currentUser: any;
}

// @ts-ignore
const AuthApiSelect = (props: AuthSelectProps) => {
  const [treeData, setTreeData] = useState<any[]>([]);
  const [authValue, setAuthValue] = useState<any[]>([]);
  const { authValueChange, apiList, authList } = props;
  useEffect(() => {
    if (!apiList.length) return;
    const apiListNotAdmin = apiList.filter((api) => {
      return !specialApis.includes(`${api.route}/${api.api_name}`);
    });
    const apiListByRole = props.currentUser.roles.includes('admin') ? apiList : apiListNotAdmin;
    const tree: any[] = [];
    const module: any = [];
    let apiListFilter = [];
    if (authList?.length) {
      apiListFilter = _.differenceBy(apiListByRole, authList, 'api_id');
    } else {
      apiListFilter = apiListByRole;
    }
    apiListFilter.forEach((api: any) => {
      if (!module.includes(api.module_name)) {
        module.push(api.module_name);
        tree.push({
          title: api.module_name,
          value: api.module_name,
          key: api.module_name,
          children: [
            {
              title: `${api.name}(${api.route}/${api.api_name})`,
              key: api.api_id,
              value: api.api_id,
            },
          ],
        });
      } else {
        tree.forEach((el: any) => {
          if (el.title === api.module_name) {
            el.children.push({
              title: `${api.name}(${api.route}/${api.api_name})`,
              key: api.api_id,
              value: api.api_id,
            });
          }
        });
      }
    });
    setTreeData(tree);
  }, [apiList, authList]);
  // useEffect(() => {
  //   apiIds = authList.map((api:any) => api.api_id)
  //   initAuth()
  // }, [authList])
  function onChange(val: any) {
    setAuthValue(val);
    authValueChange(val);
  }
  return (
    <TreeSelect
      // defaultExpandAll
      value={authValue}
      treeNodeFilterProp="title"
      treeCheckable
      treeData={treeData}
      maxTagCount={2}
      allowClear
      style={{ width: 300 }}
      onChange={(val) => onChange(val)}
      placeholder="请选择"
    />
  );
};

export default connect(({ user }: ConnectState) => ({
  currentUser: user.currentUserConfig || {},
}))(AuthApiSelect);
