/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Button, Upload, message } from 'antd';
import type {
  RcFile,
  UploadChangeParam,
  UploadFile,
  UploadFileStatus,
} from 'antd/lib/upload/interface';
import { getCosKey } from '@/services/thrdAPI';
import { getDvaApp } from 'umi';
import COS from 'cos-js-sdk-v5';
import { InboxOutlined, UploadOutlined } from '@ant-design/icons';
import { Region } from '@/const/const';
import _ from 'lodash';

interface FileItem {
  uid: string;
  name: string;
  status?: UploadFileStatus | undefined;
  thumbUrl?: string;
  url?: string;
  lastModifiedTime?: string;
  remotePath: string;
}

interface FileType extends UploadFile {
  remotePath?: string;
}

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

function upload2Item(uploadFile: FileType): FileItem {
  return {
    uid: uploadFile.uid,
    name: uploadFile.name,
    status: uploadFile.status,
    url: uploadFile.response?.url || uploadFile.url,
    thumbUrl: uploadFile.response?.url || uploadFile.url,
    remotePath: uploadFile.response?.remotePath || uploadFile.remotePath,
    lastModifiedTime: uploadFile.lastModifiedDate?.toUTCString(),
  };
}

const ImageUpload: React.FC<{
  value?: any;
  onChange?: (value: string[]) => void;
  type?: 'button' | 'dragger';
  multiple?: boolean;
  bucket?: string;
  scene?: string;
  defaultFileList?: any;
}> = ({ value, onChange, type = 'dragger', bucket, scene, defaultFileList }) => {
  const BucketConfig = {
    'ap-guangzhou': bucket ?? 'sms-tools-1258344699',
    'ap-singapore': bucket ?? 'sms-tools-sg-1258344699',
    'eu-frankfurt': bucket ?? 'sms-tools-gm-1258344699',
  };
  // 自定义文件列表数据，收集文件上传状态，用于List列表展示
  const [fileList, setFileList] = useState<FileItem[]>(
    value?.map((url: string) => ({
      uid: url,
      remotePath: decodeURIComponent(url),
      name: decodeURIComponent(decodeURIComponent(url)).split('/').at(-1),
      status: 'done',
      url,
    })) || [],
  );
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const staffName = getDvaApp()._store.getState().user.currentUserConfig?.staff_name;
  useEffect(() => {
    setFileList(
      defaultFileList?.map((url: string) => ({
        uid: url,
        remotePath: decodeURIComponent(url),
        name: decodeURIComponent(decodeURIComponent(url)).split('/').at(-1),
        status: 'done',
        url,
      })) || [],
    );
  }, [defaultFileList]);

  const handleUpload = async (option: any) => {
    const file = option.file as File;

    try {
      if (!staffName) {
        option.onError('请登录');
      }
      const dt = new Date();
      const month =
        String(dt.getMonth() + 1).length < 2
          ? ['0', dt.getMonth() + 1].join('')
          : dt.getMonth() + 1;
      const bucket = BucketConfig[Region];
      const newFolder = [staffName, '/', String(dt.getFullYear()), '/', month].join('');
      const tname = file.name.split('.');
      tname.splice(tname.length - 1, 0, `${Math.ceil(Math.random() * 100000000)}`);
      tname.splice(tname.length - 1, 0, `${Math.ceil(Math.random() * 100000000)}`);
      const newFileName = tname.join('.');
      const remotePath = `${newFolder}/${newFileName}`;
      const result = await getCosKey({
        file_name: remotePath,
        bucket,
      });
      const cos = new COS({
        Domain: `${BucketConfig[Region]}.cos-internal.${Region}.tencentcos.cn`,
        Protocol: 'https:',
        getAuthorization(options, callback) {
          callback({
            TmpSecretId: result.data?.tmp_sid,
            TmpSecretKey: result.data?.tmp_skey,
            XCosSecurityToken: result.data?.session_token,
            StartTime: result.data?.start_time,
            ExpiredTime: result.data?.expired_time,
          });
        },
        FileParallelLimit: 8, // 控制文件上传并发数
        ChunkParallelLimit: 8, // 控制单个文件下分片上传并发数
        ProgressInterval: 500, // 控制上传的 onProgress 回调的间隔
      });
      cos.putObject(
        {
          Bucket: result?.data.bucket /* 必须 */,
          Region,
          Key: remotePath /* 必须 */,
          Body: file, // 上传文件对象
        },
        (err1) => {
          if (err1) {
            message.error('上传失败，请重试');
            option.onError(err1);
            return;
          }
          cos.getObjectUrl(
            {
              Method: 'get',
              Bucket: result.data?.bucket /* 必须 */,
              Region,
              Key: remotePath,
              Sign: true,
            },
            (err2, data2: any) => {
              if (!err2) {
                option.onSuccess({ url: data2.Url, remotePath });
              } else {
                option.onError(err2);
              }
            },
          );
        },
      );
    } catch (error) {
      option.onError(error);
    }
  };

  const handleUploadChange = (info: UploadChangeParam) => {
    setFileList(info.fileList.map(upload2Item));
    onChange?.(
      info.fileList
        .map(upload2Item)
        .map((v) => v.remotePath)
        .filter((v) => v),
    );
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  const draggerProps = {
    name: 'file',
    // accept: '.png,.jpg,./jpeg',
    multiple: true,
    customRequest(option: any) {
      handleUpload(option);
    },
    onChange(info: UploadChangeParam) {
      handleUploadChange(info);
    },
    onPreview(file: UploadFile) {
      handlePreview(file);
    },
    fileList: fileList?.map((file: FileItem) => ({
      uid: file.uid,
      remotePath: file.remotePath,
      status: file.status,
      name: file.name,
      url: file.url,
    })),
  };

  return (
    <>
      {type === 'dragger' ? (
        <Upload.Dragger {...draggerProps} style={{ width: 300 }}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击上传，支持拖拽</p>
          <p className="ant-upload-hint">支持上传单个或多个</p>
        </Upload.Dragger>
      ) : (
        <Upload {...draggerProps}>
          <Button icon={<UploadOutlined />}>Upload</Button>
        </Upload>
      )}
    </>
  );
};

export default ImageUpload;
