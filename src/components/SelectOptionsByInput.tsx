import { Radio, Input } from 'antd';
import { useEffect, useRef, useState, useCallback } from 'react';
import SelectAll from './SelectAll';

const { TextArea } = Input;

type SelectAllProps = Parameters<typeof SelectAll>[0];
type SelectOptionsByInputProps = Omit<SelectAllProps, 'options'> & {
  showInput?: boolean;
  options?: { label: string; value: string | number }[];
  isMatch?: (item: { label: string; value: string | number }, inputData: string[]) => boolean;
};

const regex = /^(.*?)\_(.*?)\(?(\d+)?\)?$/;

const defaultIsMatchCountry = (item: any, inputData: string[]) => {
  const match = regex.exec(item.label);
  return match && inputData.some((v: string) => match.slice(1, 4).includes(String(v)));
};
const SelectOptionsByInput = (props: SelectOptionsByInputProps) => {
  const { value, onChange, options, showInput = true, ...rest } = props;
  if (props.mode !== 'multiple') return <SelectAll {...(props as SelectAllProps)} />;
  const isMatch = props?.isMatch ? props.isMatch : defaultIsMatchCountry;
  const [transformValue, setTransformValue] = useState<(string | number)[]>([]);
  const inputSelectKeys = useRef<(string | number)[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [type, setType] = useState('select');

  useEffect(() => {
    setTransformValue(value ?? []);
    if (value === void 0) {
      setInputValue('');
      inputSelectKeys.current = [];
      setType('select');
    }
  }, [value]);

  const handleSelectChange = useCallback((val: string | string[]) => {
    const newValues: string[] = typeof val === 'string' ? [val] : val;
    const finallyValues = [...new Set([...inputSelectKeys.current, ...newValues])];
    setTransformValue(finallyValues);
    props?.onChange(finallyValues);
  }, []);
  const handleInputChange = useCallback(
    (e: any) => {
      const isMultiLine = e.target.value.includes('\n');
      const inputData = (e.target.value as string)
        .split(isMultiLine ? '\n' : ' ')
        .map((v) => v.trim())
        .filter(Boolean);

      setInputValue(e.target.value);

      const selectedKeys =
        options?.filter((item) => isMatch(item, inputData)).map((item) => item.value) || [];

      const finallyValues = [
        ...new Set([
          ...transformValue.filter((value) => !inputSelectKeys.current.includes(value)), // 去除上次选中 本次未选中的
          ...selectedKeys, // 添加新选中的
        ]),
      ];
      inputSelectKeys.current = selectedKeys;

      setTransformValue(finallyValues);
      props?.onChange(finallyValues);
    },
    [options, transformValue],
  );
  return (
    <div>
      {showInput ? (
        <Radio.Group
          onChange={(e) => setType(e.target.value)}
          value={type}
          style={{ marginBottom: 7, marginTop: 6 }}
        >
          <Radio value="select">选择</Radio>
          <Radio value="input">输入</Radio>
        </Radio.Group>
      ) : null}
      {type === 'select' || !showInput ? (
        <SelectAll
          {...rest}
          options={options}
          value={transformValue}
          onChange={handleSelectChange}
        />
      ) : (
        <TextArea
          placeholder="请输入需要匹配的选项，多行时以换行匹配，单行时以空格分割"
          value={inputValue}
          required={false}
          rows={4}
          onChange={handleInputChange}
        />
      )}
    </div>
  );
};

export default SelectOptionsByInput;
