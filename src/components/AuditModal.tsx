import React, { useState, useEffect } from 'react';
import { Input, Form, Modal, message, Radio, Select, DatePicker } from 'antd';
import type { Dispatch } from 'umi';
import { connect } from 'umi';
import type { ConnectState } from '@/models/connect';
import { addAudit, queryApiList } from '@/services/api';
import ImageUpload from './ImageUpload';
import dayjs from 'dayjs';
import _ from 'lodash';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';

const { TextArea } = Input;

interface Props {
  auditInfo: any;
  isModalVisible: boolean;
  dispatch: Dispatch;
}

const timeOptions = [
  { value: 0, label: '立即' },
  { value: 3, label: '3分钟' },
  { value: 5, label: '5分钟' },
  { value: 10, label: '10分钟' },
  { value: 20, label: '20分钟' },
];

const AuditModal = (props: Props) => {
  const [form] = Form.useForm();
  const { auditInfo, isModalVisible, dispatch } = props;
  const [auditors, setAuditors] = useState<string>('');
  const [auditorOptions, setAuditorOptions] = useState([]);
  const [lastInfo, setLastInfo] = useState<any>({});

  useEffect(() => {
    const _lastInfo = JSON.parse(sessionStorage.getItem('lastAuditInfo') || '{}');
    if (isModalVisible && !_.isEmpty(_lastInfo)) {
      Modal.confirm({
        title: '回填信息',
        content: '是否回填上一次填写的审核单信息',
        okText: 'YES',
        cancelText: 'NO',
        zIndex: 1600,
        onOk: () => {
          form.setFieldsValue({
            start_time: dayjs(_lastInfo.start_time),
            end_time: dayjs(_lastInfo.end_time),
            remark: _lastInfo.remark,
            urgent: _lastInfo.urgent,
            notice_auditor: _lastInfo.notice_auditor,
            after_mins_notice: _lastInfo.after_mins_notice,
            modify_impact: _lastInfo.modify_impact,
            pictures: _lastInfo.pictures,
          });
          setLastInfo(_lastInfo);
        },
      });
    } else {
      form.resetFields();
      setLastInfo({});
    }
  }, [form, isModalVisible]);

  useEffect(() => {
    if (auditInfo.user_id === undefined) return;
    form.setFieldsValue({
      api_id: auditInfo.api_id,
      user_id: auditInfo.user_id,
      req_ext: JSON.stringify(auditInfo.req_ext),
      start_time: dayjs(),
      end_time: dayjs().add(3, 'day'),
    });
    setAuditors(auditInfo.auditors.map((el: any) => el.staff_name).join(','));
    setAuditorOptions(auditInfo.auditors.map((el: any) => ({ value: el.staff_name })));
  }, [auditInfo, form]);

  const { value: apiInfo } = useAsyncRetryFunc(async () => {
    const res = await queryApiList({ api_id: auditInfo.api_id });
    return res.data?.list[0];
  }, [auditInfo.api_id]);

  function closeModal() {
    dispatch({
      type: 'audit/toggleModal',
      payload: {
        isModalVisible: false,
      },
    });
  }

  async function onSubmit(vals: any) {
    dispatch({
      type: 'audit/resetForm',
      payload: {
        isReset: true,
      },
    });
    const res = await addAudit({
      ...vals,
      start_time: dayjs(vals.start_time).format('YYYY-MM-DD HH:mm:ss'),
      end_time: dayjs(vals.end_time).format('YYYY-MM-DD HH:mm:ss'),
    });
    if (res.code === 0) {
      message.success('提交成功，请等待审核');
      closeModal();
      sessionStorage.setItem(
        'lastAuditInfo',
        JSON.stringify({
          start_time: dayjs(vals.start_time).format('YYYY-MM-DD HH:mm:ss'),
          end_time: dayjs(vals.end_time).format('YYYY-MM-DD HH:mm:ss'),
          remark: vals.remark,
          urgent: vals.urgent,
          notice_auditor: vals.notice_auditor,
          after_mins_notice: vals.after_mins_notice,
          modify_impact: vals.modify_impact,
          pictures: vals.pictures,
        }),
      );
    } else {
      message.error('提交失败');
    }
  }

  return (
    <Modal
      title="创建审核单"
      open={isModalVisible}
      onOk={() => form.submit()}
      onCancel={() => closeModal()}
      width={600}
      destroyOnClose
      zIndex={1500}
    >
      <Form
        labelCol={{ span: 5 }}
        labelAlign="left"
        form={form}
        size="small"
        onFinish={(vals) => {
          onSubmit(vals);
        }}
        initialValues={{ urgent: 0 }}
      >
        <Form.Item name="api_id" label="接口">
          【{apiInfo?.module_name}】{apiInfo?.name}
        </Form.Item>
        <Form.Item name="user_id" label="用户id" hidden>
          <Input placeholder="user_id" bordered={false} disabled />
        </Form.Item>
        <Form.Item name="req_ext" label="请求参数" hidden>
          <TextArea rows={3} bordered={false} disabled style={{ width: 300, color: '#666' }} />
        </Form.Item>
        <Form.Item label="审核人">
          <Input bordered={false} disabled value={auditors} style={{ width: 300, color: '#666' }} />
        </Form.Item>
        <Form.Item name="start_time" rules={[{ required: true }]} label="变更开始时间">
          <DatePicker showTime minDate={dayjs()}></DatePicker>
        </Form.Item>
        <Form.Item name="end_time" rules={[{ required: true }]} label="变更结束时间">
          <DatePicker showTime minDate={dayjs()}></DatePicker>
        </Form.Item>
        <Form.Item name="remark" rules={[{ required: true }]} label="变更背景">
          <TextArea rows={3} style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="modify_impact" rules={[{ required: true }]} label="变更影响">
          <TextArea rows={3} style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="pictures" label="附件">
          <ImageUpload defaultFileList={lastInfo?.pictures} />
        </Form.Item>
        <Form.Item name="urgent" label="是否紧急">
          <Radio.Group>
            <Radio value={0}>否</Radio>
            <Radio value={1}>是</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.urgent !== curValues.urgent}
        >
          {({ getFieldValue }) => {
            const urgent = getFieldValue('urgent');
            return urgent ? (
              <>
                <Form.Item label="紧急审核人" name="notice_auditor" rules={[{ required: true }]}>
                  <Select options={auditorOptions} allowClear style={{ width: 120 }} />
                </Form.Item>
                <Form.Item label="等待时间" name="after_mins_notice" rules={[{ required: true }]}>
                  <Select options={timeOptions} allowClear style={{ width: 100 }} />
                </Form.Item>
              </>
            ) : null;
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default connect(({ audit }: ConnectState) => ({
  auditInfo: audit.auditInfo || {},
  isModalVisible: audit.isModalVisible || false,
}))(AuditModal);
// export default AuditModal
