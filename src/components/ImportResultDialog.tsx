import React, { useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { message, Modal, Table } from 'antd';
import _ from 'lodash';

interface DialogProps {
  dialogRef: DialogRef;
  columns: any[];
  onSubmit: (params: any) => Promise<any>;
  onSuccess?: () => void;
  save?: boolean;
  onError?: (params: any, index: number) => void;
}

export function ImportResultDialog(props: DialogProps) {
  const { dialogRef, columns, onSubmit, onSuccess } = props;
  const [loading, setLoading] = useState(false);
  const [visible, setShowState, defaultVal] = useDialog<{
    list: any;
  }>(dialogRef);
  const { list } = defaultVal;

  async function handlerSubmit() {
    setLoading(true);
    try {
      const res = await onSubmit({ params: list });
      setLoading(false);
      if (res.code === 0) {
        onSuccess?.();
        setShowState(false);
        message.success('提交成功');
      } else {
        message.error('提交失败，请确认表格内容无误');
      }
    } catch (error) {
      setLoading(false);
    }
  }

  return (
    <>
      <Modal
        open={visible}
        title={'导入成功'}
        confirmLoading={loading}
        onCancel={() => setShowState(false)}
        onOk={handlerSubmit}
        width="70%"
        destroyOnClose={true}
        maskClosable={false}
      >
        <Table
          dataSource={list}
          columns={_.concat(
            {
              header: '序号',
              key: 'id',
              width: 80,
              render: (record: any, rowKey: string, recordIndex: number) => recordIndex + 1,
            },
            columns,
          )}
          size="small"
          scroll={{ y: 540, x: '80%' }}
          pagination={false}
        />
      </Modal>
    </>
  );
}
