import { BetaSchemaForm } from '@ant-design/pro-components';
import { useState } from 'react';

type FormFormProps = Parameters<typeof BetaSchemaForm>[0];

type FormSchemaProps = FormFormProps & {
  open: boolean;
};
const initProps = {
  layout: 'horizontal',
  layoutType: 'ModalForm',
  open: false,
  columns: [],
};
type Config<T extends string> = {
  [K in T]:
    | {
        columnsFilters?: string[];
        onFinish: (values: unknown, extraData: any, rest: any) => void;
        [key: string]: any; // 允许其他属性
      }
    | ((rest: any) => any);
};

export const useMultipleConfigWithExtraDataModal = <T extends string>(
  columns: Array<{ key: string }>,
  config: Config<T>,
  rest?: any,
) => {
  const [currentFormProps, setCurrentFormProps] = useState<FormSchemaProps>(initProps as any);

  const changeModal = (visible: boolean) => {
    setCurrentFormProps((prev) => ({
      ...prev,
      open: visible,
    }));
  };

  const openModal = (type: T, extraData: any) => {
    const finallyConfig =
      typeof config[type] === 'function' ? config[type]({ ...rest }) : config[type];

    const filteredColumns = finallyConfig?.columnsFilters
      ? columns.filter((column) => finallyConfig.columnsFilters.includes(column.key))
      : columns;

    setCurrentFormProps({
      ...initProps,
      open: true,
      ...finallyConfig,
      columns: filteredColumns,
      initialValues: extraData,
      onOpenChange: changeModal,
      onFinish: (values: unknown) => finallyConfig.onFinish(values, extraData, { ...rest }),
    });
  };

  return [!!currentFormProps.open, currentFormProps, openModal] as [
    boolean,
    any,
    (type: T, extraData?: unknown) => void,
  ];
};
