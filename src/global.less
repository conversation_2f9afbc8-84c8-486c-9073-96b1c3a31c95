html,
body,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

// 兼容IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

.audit-approve {
  margin-bottom: 8px;
  color: #389e0d;
  background-color: #f6ffed;
  border-color: #b7eb8f;
}

.audit-reject {
  color: #cf1322;
  background-color: #fff1f0;
  border-color: #ffa39e;
}

.expanded-table {
  .ant-table-thead th {
    background-color: #fff !important;
  }
}

.monitor-field {
  display: block;
  margin-top: 10px;
  border-color: threedface;
  border-style: solid;
  border-width: 2px;
  border-image: initial;
  margin-inline-start: 2px;
  margin-inline-end: 2px;
  padding-block-start: 0.35em;
  padding-inline-start: 0.75em;
  padding-inline-end: 0.75em;
  padding-block-end: 0.625em;
  min-inline-size: min-content;
  legend {
    display: block;
    width: auto;
    font-size: 16px;
    border-color: initial;
    border-style: none;
    border-width: initial;
    border-image: initial;
    padding-inline-start: 2px;
    padding-inline-end: 2px;
  }
}

.ant-table-container {
  overflow: auto;
}

.text-ellipsis {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
  word-break: keep-all;
}

.send-query-text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
  word-break: break-all;
}

.sender-search-form {
  .ant-form-item {
    margin-bottom: 10px !important;
  }
}

.failure-reason {
  margin: -16px;
  .failure-reason-row {
    display: flex;
    justify-content: space-around;
    padding-top: 0;
    line-height: 50px;
    .failure-reason-cell {
      min-height: 50px;
      padding: 5px;
      border-right: 1px solid rgba(240, 240, 240, 0.85);
    }
  }
  .failure-reason-row:not(:last-child) {
    border-bottom: 1px solid rgba(240, 240, 240, 0.85);
  }
}
