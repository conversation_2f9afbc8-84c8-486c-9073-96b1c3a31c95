import React from 'react';
import { message, But<PERSON>, Tooltip } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { CopyToClipboard } from 'react-copy-to-clipboard';

const CopyText = ({
  text,
  width,
  render,
}: {
  text: string;
  width: number;
  render?: () => React.ReactNode;
}) => {
  return (
    <div>
      <Tooltip title={text}>
        {render?.()}
        {!render && (
          <div style={{ width: width - 48 }} className="text-ellipsis">
            {text}
          </div>
        )}
      </Tooltip>
      <CopyToClipboard
        text={text}
        onCopy={() => {
          message.success('Copy successfully');
        }}
      >
        <Button
          icon={<CopyOutlined />}
          type="link"
          style={{ width: '16px', height: '16px', verticalAlign: 'top' }}
        />
      </CopyToClipboard>
    </div>
  );
};
export default CopyText;
