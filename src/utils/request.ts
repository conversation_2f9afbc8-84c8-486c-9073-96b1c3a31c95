/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { history } from '@umijs/max';
import { Modal, message, notification } from 'antd';
import { getDvaApp } from 'umi';
import { extend } from 'umi-request';
import apisMapCustomizedDialog from '@/components/CustomizedDialog/apisMapCustomizedDialog';
import { errorsObj } from '@/pages/global-components/BatchErrorModal';
import _ from 'lodash';
import { uuid } from './uuid';

const codeMessage: any = {
  200: '服务器成功返回请求的数据。',
  201: '新建或编辑数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或编辑数据的操作。',
  401: '用户没有权限。',
  403: '访问被禁止的。',
  404: '接口不存在。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

/**
 * 异常处理程序
 */
const errorHandler = (response: Response) => {
  const { status, statusText } = response;
  const { pathname } = new URL(response.url);
  if (status) {
    const errorText = codeMessage[status] || statusText;
    notification.error({
      message: `请求错误【${status}】`,
      description: `${errorText}【${pathname}】`,
      duration: 6,
    });
  } else if (!response) {
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
      duration: 6,
    });
  }
  return response;
};

/**
 * 配置request请求时的默认参数
 */
const { NODE_ENV } = process.env;
const request = extend({
  // @ts-ignore
  // credentials: 'include', // 默认请求是否带上cookie
  timeout: 1000 * 600,
  headers: {
    // StaffName: NODE_ENV === 'development' ? 'miamwu' : undefined,
  },
  credentials: 'include',
});

// const BASE_URL = NODE_ENV === 'development' ? 'https://smsconfig-api.woa.com' : 'https://smsconfig-api.woa.com'
const BASE_URL = NODE_ENV === 'development' ? '/apis' : '/apis';

request.interceptors.request.use(
  (url, options) => {
    return {
      url: `${BASE_URL}${url}`,
      options: {
        ...options,
        params: _.pickBy(options.params, (v) => !_.isNil(v)),
        data:
          options.data instanceof FormData
            ? options.data
            : _.pickBy(options.data, (v) => !_.isNil(v)),
        interceptors: true,
        headers: {
          'request-id': uuid(),
          'X-Requested-With': 'XMLHttpRequest',
        },
      },
    };
  },
  { global: false },
);

request.interceptors.response.use(async (response) => {
  if (response.status === 401) {
    Modal.warning({
      title: '由于长时间挂机导致掉线，需要刷新页面恢复',
      okText: '刷新页面',
      onOk: () => {
        window.location.reload();
      },
    });
    return response;
  }

  if (response.status < 200 || response.status >= 300) {
    return errorHandler(response);
  }
  const res = await response.clone().json();
  const { pathname } = new URL(response.url || window.location.href);
  const { code, msg, data } = res;
  if (code !== 0) {
    if (code === 2001) {
      // 需要审核
      message.warning('该操作需要审核，请提交审核单');
      // eslint-disable-next-line no-underscore-dangle
      getDvaApp()._store.dispatch({
        type: 'audit/toggleModal',
        payload: {
          isModalVisible: true,
        },
      });
      response
        .clone()
        .json()
        .then((result) => {
          // eslint-disable-next-line no-underscore-dangle
          getDvaApp()._store.dispatch({
            type: 'audit/saveAuditParams',
            payload: {
              auditInfo: { ...result.data },
            },
          });
        });
    } else if (code === 1002) {
      message.warning(msg);
      // 无账号信息
      history.push(`/exception/403?nouser=1`);
    } else if (code === 2005) {
      // 已存在审核单
      const staffName = getDvaApp()._store.getState().user.currentUserConfig?.staff_name;
      if (data.applicant !== staffName) {
        message.warning(`${msg}，提单人：${data?.applicant || ''}`);
      } else {
        history.push({
          pathname: '/audit/list',
          query: {
            id: data?.apply_id,
          },
        });
      }
    } else if (code === 1004) {
      message.warning(msg);
      history.push(`/exception/403?api=${response.url.split('/apis')[1].split('?')[0]}`);
    } else {
      let errorMsg = msg;
      if (typeof msg === 'object') {
        errorMsg = Object.keys(msg)
          .map((key) => msg[key][0])
          .slice(0, 1);
      }
      notification.error({
        message: `请求错误【code:${code}】`,
        description: `${errorMsg}【${pathname}】`,
        duration: 6,
      });
    }
  } else {
    const _pathname = pathname?.split('/apis')[1];
    if (data?.errors?.length) {
      const ModalContent =
        apisMapCustomizedDialog.find((item) => item.apis.includes(_pathname))?.component ?? null;
      if (ModalContent) {
        Modal.error({
          title: '执行错误',
          width: 800,
          content: ModalContent?.(data?.errors),
        });
      } else {
        errorsObj.setVisible(true, data?.errors);
      }
    }
    if (msg?.errors?.length) {
      errorsObj.setVisible(true, msg?.errors);
    }
  }
  return res;
});

export default request;
