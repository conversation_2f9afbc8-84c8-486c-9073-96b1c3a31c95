import React from 'react';
import { message, Tag, Typography } from 'antd';
import _ from 'lodash';
import { BaseType } from 'antd/es/typography/Base';
const { Text } = Typography;

export function findText(options: { value: any; text: string }[], keyValue: any) {
  return (
    _.find(options, (item: any) => item?.value?.toString() === keyValue?.toString())?.text || ''
  );
}

export function findLabel(options: { value: any; label: string }[], keyValue: any) {
  return (
    _.find(options, (item: any) => item?.value?.toString() === keyValue?.toString())?.label ||
    keyValue
  );
}

export function findLabelWithType(
  options: { value: any; label: string; type: BaseType }[],
  keyValue: any,
) {
  const item = _.find(options, (item: any) => item?.value?.toString() === keyValue?.toString());
  return <Text type={item?.type}>{item?.label}</Text>;
}

export const filterStringSpace = (str: string) => {
  return str?.replace(/(\s*)/g, '');
};

export const getPathByUrl = (url: string) => {
  try {
    const path = new URL(url).pathname?.slice(1);
    const decode = decodeURIComponent(decodeURIComponent(path));
    return {
      path: decode,
      name: decode.split('/').at(-1),
    };
  } catch (err) {
    console.log(err);
  }
};

export const downloadCsvFile = (href: string, fileName?: string) => {
  const link = document.createElement('a');
  link.href = href;
  link.download = fileName || '';
  link.click();
};

// 获取16位随机字符串
export function generateRandomString() {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let randomString = '';
  for (let i = 0; i < 16; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomString += characters.charAt(randomIndex);
  }
  return randomString;
}

export function getOperatorName(mccMncInfo: any, mnc: string, vals: any) {
  return (
    _.find(mccMncInfo, (item) => {
      return (
        item.mnc?.toString() === mnc?.toString() &&
        (item.mcc?.toString() === vals.mcc?.toString() || item?.country_code === vals?.country_code)
      );
    })?.operator_name ?? '-'
  );
}

export function renderYesOrNO(val: boolean | number) {
  return !!val ? <Tag color="green">是</Tag> : <Tag color="volcano">否</Tag>;
}

function fallbackCopy(text: string) {
  // 创建一个临时的 textarea 元素
  const textarea = document.createElement('textarea');
  textarea.value = text; // 设置要复制的文本
  document.body.appendChild(textarea); // 将 textarea 添加到文档中
  textarea.select(); // 选中 textarea 中的文本

  try {
    const successful = document.execCommand('copy'); // 执行复制命令
    if (successful) {
      console.log('文本已复制到剪贴板 (使用 execCommand)');
    } else {
      console.error('复制失败 (execCommand)');
    }
  } catch (err) {
    console.error('复制失败 (execCommand):', err);
  }

  document.body.removeChild(textarea); // 移除临时的 textarea
}

export function copy(text: string) {
  // 尝试使用 Clipboard API
  if (navigator.clipboard?.writeText) {
    return navigator.clipboard
      .writeText(text)
      .then(() => {
        message.success('复制成功');
        console.log('文本已复制到剪贴板 (使用 Clipboard API)');
      })
      .catch((err) => {
        message.error('复制失败');
        console.error('复制失败 (Clipboard API):', err);
        fallbackCopy(text); // 如果失败，使用回退方法
      });
  }
  // 如果 Clipboard API 不可用，使用回退方法
  fallbackCopy(text);
}
