import { DependencyList, useEffect } from 'react';
import { useAsyncFn } from 'react-use';

type FunctionReturningPromise = (...args: any[]) => Promise<any>;

export default function useAsyncRetryFunc<T extends FunctionReturningPromise>(
  fn: T,
  deps: DependencyList = [],
) {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const [state, fetchData] = useAsyncFn(fn, deps, {
    loading: true,
  });

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    ...state,
    retry: fetchData,
  };
}
