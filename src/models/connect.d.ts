import type { MenuDataItem, Settings as ProSettings } from '@ant-design/pro-layout';
import { GlobalModelState } from './global';
import { UserModelState } from './user';
import type { AuditModelState } from './audit';
import type { RoutesModelState } from './routes';

export { GlobalModelState, UserModelState };

export interface Loading {
  global: boolean;
  effects: Record<string, boolean | undefined>;
  models: {
    global?: boolean;
    setting?: boolean;
    user?: boolean;
    audit?: boolean;
  };
}

export interface ConnectState {
  global: GlobalModelState;
  loading: Loading;
  settings: ProSettings;
  user: UserModelState;
  audit: AuditModelState;
  routes: RoutesModelState;
}

export interface Route extends MenuDataItem {
  routes?: Route[];
}
