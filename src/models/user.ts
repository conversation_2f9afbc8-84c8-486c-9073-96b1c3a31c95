import type { Effect, Reducer } from 'umi';

import { queryUserConfigList } from '@/services/api';

export interface CurrentUserConfig {
  user_id?: number;
  staff_name?: string;
  roles?: string;
  api?: any[];
}

export interface UserModelState {
  currentUserConfig?: CurrentUserConfig;
}

export interface UserModelType {
  namespace: 'user';
  state: UserModelState;
  effects: {
    fetchCurrent: Effect;
  };
  reducers: {
    saveCurrentUserConfig: Reducer<UserModelState>;
  };
}

const UserModel: UserModelType = {
  namespace: 'user',

  state: {
    currentUserConfig: {},
  },

  effects: {
    *fetchCurrent(_, { call, put }) {
      const response = yield call(queryUserConfigList);
      yield put({
        type: 'saveCurrentUserConfig',
        payload: response.data,
      });
    },
  },

  reducers: {
    saveCurrentUserConfig(state, action) {
      return {
        ...state,
        currentUserConfig: action.payload || {},
      };
    },
  },
};

export default UserModel;
