import type { Effect, Reducer } from 'umi';

export interface RoutesModelState {
  routes?: any[];
  isModalVisible?: boolean;
  isReset?: boolean;
  cmppInfo?: any;
}

export interface RoutesModelType {
  namespace: 'routes';
  state: RoutesModelState;
  effects: {
    getAuthRoutes: Effect;
  };
  reducers: {
    saveAuthRoutes: Reducer<RoutesModelState>;
  };
}

const RoutesModel: RoutesModelType = {
  namespace: 'routes',

  state: {
    routes: [],
  },

  effects: {
    *getAuthRoutes({ payload }, { put }) {
      yield put({
        type: 'saveAuthRoutes',
        payload: { ...payload },
      });
    },
  },

  reducers: {
    saveAuthRoutes(state, action) {
      return {
        ...state,
        routes: action.payload.routes,
      };
    },
  },
};

export default RoutesModel;
