import type { Effect, Reducer } from 'umi';

import { queryApiList, queryRoleList } from '@/services/api';

export interface GlobalModelState {
  ApiList?: any[];
  RoleList?: any[];
}

export interface GlobalModelType {
  namespace: 'global';
  state: GlobalModelState;
  effects: {
    fetchApiList: Effect;
    fetchRoleList: Effect;
  };
  reducers: {
    saveApiList: Reducer<GlobalModelState>;
    saveRoleList: Reducer<GlobalModelState>;
  };
}

const GlobalModel: GlobalModelType = {
  namespace: 'global',

  state: {
    ApiList: [],
    RoleList: [],
  },

  effects: {
    *fetchApiList({ payload }, { call, put }) {
      const response = yield call(queryApiList, payload);
      yield put({
        type: 'saveApiList',
        payload: { ...response.data },
      });
    },
    *fetchRoleList({ payload }, { call, put }) {
      const response = yield call(queryRoleList, payload);
      yield put({
        type: 'saveRoleList',
        payload: { ...response.data },
      });
    },
  },

  reducers: {
    saveApiList(state, action) {
      return {
        ...state,
        ApiList: action.payload.list,
      };
    },
    saveRoleList(state, action) {
      return {
        ...state,
        RoleList: action.payload.list,
      };
    },
  },
};

export default GlobalModel;
