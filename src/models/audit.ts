import type { Effect, Reducer } from 'umi';

export interface AuditModelState {
  auditInfo?: AuditInfoModalState;
  isModalVisible?: boolean;
  isReset?: boolean;
  cmppInfo?: any;
}

interface AuditInfoModalState {
  api_id?: number;
  auditors?: any[];
  req_ext?: any;
  user_id?: number;
}

export interface AuditModelType {
  namespace: 'audit';
  state: AuditModelState;
  effects: {
    createAudit: Effect;
    toggleModal: Effect;
    resetForm: Effect;
    editCmppForm: Effect;
  };
  reducers: {
    saveAuditParams: Reducer<AuditModelState>;
    saveVisible: Reducer<AuditModelState>;
    saveReset: Reducer<AuditModelState>;
    saveCmppInfo: Reducer<AuditModelState>;
  };
}

const AuditModel: AuditModelType = {
  namespace: 'audit',

  state: {
    auditInfo: {},
    isModalVisible: false,
    isReset: false,
    cmppInfo: {
      customer_name: '',
      customer_email: '',
      sdkappid: '',
      sms_type: 0,
      sp_type: 0,
      business_type: 0,
      account_attribute: [],
      use_description: '',
      remark: '',
      customer_type: 0,
    },
  },

  effects: {
    *createAudit({ payload }, { put }) {
      yield put({
        type: 'saveAuditParams',
        payload: { ...payload },
      });
    },
    *toggleModal({ payload }, { put }) {
      yield put({
        type: 'saveVisible',
        payload: { ...payload },
      });
    },
    *resetForm({ payload }, { put }) {
      yield put({
        type: 'saveReset',
        payload: { ...payload },
      });
    },
    *editCmppForm({ payload }, { put }) {
      yield put({
        type: 'saveCmppInfo',
        payload: { ...payload },
      });
    },
  },

  reducers: {
    saveAuditParams(state, action) {
      return {
        ...state,
        auditInfo: { ...action.payload.auditInfo },
      };
    },
    saveVisible(state, action) {
      return {
        ...state,
        isModalVisible: action.payload.isModalVisible,
      };
    },
    saveReset(state, action) {
      return {
        ...state,
        isReset: action.payload.isReset,
      };
    },
    saveCmppInfo(state, action) {
      return {
        ...state,
        cmppInfo: action.payload.cmppInfo,
      };
    },
  },
};

export default AuditModel;
