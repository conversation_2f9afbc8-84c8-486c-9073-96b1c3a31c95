import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Outlet, connect } from 'umi';
import AuditModal from '@/components/AuditModal';
import RightContent from '@/components/GlobalHeader/RightContent';
import type { ConnectState } from '@/models/connect';
import { BatchErrorModal } from '@/pages/global-components/BatchErrorModal';
import Authorized from '@/utils/Authorized';
import type { MenuDataItem, ProLayoutProps, Settings } from '@ant-design/pro-layout';
import ProLayout from '@ant-design/pro-layout';
import { Link, history, useIntl, useLocation } from '@umijs/max';
import { getMatchMenu } from '@umijs/route-utils';
import { Button, Result, Watermark } from 'antd';
import _ from 'lodash';
import proSettings from '../../config/defaultSettings';
import permission from '../../config/permission';
import routes from '../../config/routes';
import TagsView from './TagsView';
import style from './index.module.less';
// @ts-ignore
import logo from '../assets/logo.ico';
// import { ErrorBoundary } from 'react-error-boundary';
// import ErrorBoundaryRender from '@/components/ErrorBoundaryRender';

const noMatch = (
  <Result
    status={403}
    title="403"
    subTitle="Sorry, you are not authorized to access this page..."
    extra={
      <Button type="primary">
        <Link to="/user/login">Go Login</Link>
      </Button>
    }
  />
);
export interface BasicLayoutProps extends ProLayoutProps {
  breadcrumbNameMap: Record<string, MenuDataItem>;
  route: ProLayoutProps['route'] & {
    authority: string[];
  };
  settings: Settings;
  dispatch: any;
  currentUser: any;
}
export type BasicLayoutContext = { [K in 'location']: BasicLayoutProps[K] } & {
  breadcrumbNameMap: Record<string, MenuDataItem>;
};

function hasRoute(node: any, names: any, parent: any) {
  if (names.includes(node.name)) {
    delete parent.routes;
    return true;
  }
  if (!node.routes) {
    return false;
  }
  let res = false;
  for (const child of node.routes) {
    const { routes, ...rest } = child;
    const pn = { ...rest, routes: [] };
    if (hasRoute(child, names, pn)) {
      parent.routes.push(pn);
      res = true;
    }
  }
  return res;
}

function getRouteFromRoutesTree(names: any): any {
  const root = { routes };
  const result = { routes: [] };
  if (hasRoute(root, names, result)) {
    return result.routes;
  }
}

function hasPermission(authRoutes: any): { pass: boolean; noPermissionPath: string } {
  let pass = false;
  let noPermissionPath = '';
  for (const el of authRoutes) {
    if (el.path === history.location.pathname) {
      pass = true;
      break;
    }
    if (!el.routes?.length) {
      noPermissionPath = location.pathname ?? '';
    } else {
      const { pass: ps, noPermissionPath: npp } = hasPermission(el.routes);
      if (ps) {
        return { pass: ps, noPermissionPath: npp };
      }
    }
  }
  return { pass, noPermissionPath };
}

const BasicLayout: React.FC<BasicLayoutProps> = (props) => {
  const { dispatch, currentUser, settings = proSettings } = props;

  const [isLoadingMenu, setLoading] = useState(false);
  const menuDataRef = useRef<MenuDataItem[]>([]);
  const [authRoutes, setAuthRoutes] = useState<any>([]);
  const [authRoutesNames, setAuthRoutesNames] = useState<any>([]);
  const route = useLocation();

  useEffect(() => {
    if (route.pathname === '/') return;
    try {
      let historyRecordsStr = localStorage.getItem('routeHistory');
      if (!historyRecordsStr) {
        localStorage.setItem('routeHistory', JSON.stringify([]));
        historyRecordsStr = localStorage.getItem('routeHistory');
      }

      let isInRecords = false;
      let historyRecords = JSON.parse(historyRecordsStr || '[]')?.map(
        (v: { path: string; updated_At: number }) => {
          if (v.path === route.pathname) {
            v.updated_At = Date.now();
            isInRecords = true;
          }
          return v;
        },
      );
      if (!isInRecords) {
        historyRecords.unshift({
          path: route.pathname,
          updated_At: Date.now(),
        });
      }
      historyRecords = _.sortBy(historyRecords, (item) => -item.updated_At);
      localStorage.setItem('routeHistory', JSON.stringify(historyRecords.slice(0, 5)));
    } catch (error) {
      localStorage.setItem('routeHistory', JSON.stringify([]));
    }
  }, [route.pathname]);

  useEffect(() => {
    if (dispatch) {
      dispatch({
        type: 'user/fetchCurrent',
      });
    }
  }, [dispatch]);

  useEffect(() => {
    function checkPermission() {
      if (location.pathname?.includes('/exception')) return;
      if (!hasPermission(routes).pass) {
        return history.replace('/exception/404');
      }
      const result = hasPermission(authRoutes);
      if (!result.pass) {
        return history.replace(`/exception/403?page=${result.noPermissionPath}`);
      }
    }

    if (!authRoutes.length) return;
    if (location.pathname === '/') {
      history.replace('/index');
    } else {
      checkPermission();
    }
  }, [authRoutes, authRoutes.length, authRoutesNames.length]);

  useEffect(() => {
    setLoading(true);
    if (!currentUser.user_id) {
      setLoading(false);
      return;
    }
    const userApis = currentUser.api?.map((el: any) => `${el.route}/${el.action}`);
    const authRoutesNames: any = [];
    Object.keys(permission).forEach((k) => {
      // @ts-ignore
      const apiAll = permission[k]?.map((el: any) => el.split(':')[1]);
      if (_.intersection(apiAll, userApis).length) {
        authRoutesNames.push(k);
      }
    });
    const intersection = currentUser.page
      ? _.intersection(authRoutesNames, currentUser.page)
      : authRoutesNames;
    setAuthRoutesNames(intersection);
    // 管理员不做校验
    setAuthRoutes(
      currentUser.roles.includes('admin') ? routes : getRouteFromRoutesTree(intersection) ?? [],
    );
    dispatch({
      type: 'routes/getAuthRoutes',
      payload: {
        routes: currentUser.roles.includes('admin')
          ? routes
          : getRouteFromRoutesTree(intersection) ?? [],
      },
    });
    setLoading(false);
  }, [currentUser, dispatch]);

  const handleMenuCollapse = (payload: boolean): void => {
    setLoading(payload);
  }; // get children authority
  const authorized = useMemo(
    () =>
      getMatchMenu(location.pathname || '/', menuDataRef.current).pop() || {
        authority: undefined,
      },
    [location.pathname],
  );
  const { formatMessage } = useIntl();

  return (
    <div className={style.scrollBar}>
      <ProLayout
        logo={logo}
        fixSiderbar
        siderWidth={208}
        token={{
          sider: {
            paddingInlineLayoutMenu: 0,
          },
          pageContainer: {
            paddingInlinePageContainerContent: 24,
            paddingBlockPageContainerContent: 16,
            colorBgPageContainer: '#fff',
          },
        }}
        fixedHeader
        formatMessage={formatMessage}
        onCollapse={handleMenuCollapse}
        onMenuHeaderClick={() => history.push('/')}
        menuItemRender={(menuItemProps, defaultDom) => {
          if (menuItemProps.isUrl || !menuItemProps.path) {
            return defaultDom;
          }

          return <Link to={menuItemProps.path}>{defaultDom}</Link>;
        }}
        breadcrumbRender={(routers = []) => [
          {
            path: '/',
            breadcrumbName: formatMessage({
              id: 'menu.home',
            }),
          },
          ...routers,
        ]}
        itemRender={(route, params, routes, paths) => {
          const first = routes.indexOf(route) === 0;
          return first ? (
            <Link to={paths.join('/')}>{route.breadcrumbName}</Link>
          ) : (
            <span>{route.breadcrumbName}</span>
          );
        }}
        menu={{ loading: isLoadingMenu }}
        menuDataRender={() => {
          return authRoutes;
        }}
        {...props}
        {...settings}
      >
        {/* <Watermark
          content={[props.currentUser.staff_name, window.location.host]}
          font={{
            fontSize: 18,
            color: 'rgba(0,0,0,0.15)',
            fontFamily: 'microsoft yahei',
            fontWeight: 200,
          }}
          gap={[200, 200]}
          rotate={-30}
        > */}
        {/* <ErrorBoundary FallbackComponent={ErrorBoundaryRender}> */}
        <Authorized authority={authorized!.authority} noMatch={noMatch}>
          <RightContent menu={authRoutes} />
          <TagsView menu={authRoutes} />
          <Outlet />
        </Authorized>
        {/* </ErrorBoundary> */}
        {/* </Watermark> */}
      </ProLayout>

      <AuditModal />
      <BatchErrorModal />
    </div>
  );
};

export default connect(({ user }: ConnectState) => ({
  currentUser: user.currentUserConfig,
}))(BasicLayout);
