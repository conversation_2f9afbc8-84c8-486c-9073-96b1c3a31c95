import React, { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import style from './index.module.less';
import { CloseCircleOutlined } from '@ant-design/icons';
import _ from 'lodash';
import { findName, getLabel } from '@/components/GlobalHeader/RightContent';
import { history } from 'umi';
import type { MenuDataItem } from '@ant-design/pro-layout';
import classNames from 'classnames';

const TagsView = ({ menu }: { menu: MenuDataItem[] }) => {
  const path = history.location.pathname;
  const [visitedRoutes, setVisitedRoutes] = useState([
    {
      path: '/index',
      name: 'index',
      title: '概览',
    },
  ]);

  function removeRoute(index: number) {
    visitedRoutes.splice(index, 1);
    history.replace(visitedRoutes[index]?.path || visitedRoutes[index - 1]?.path);
  }

  function scrollIntoView(path: string) {
    try {
      const dom = document.querySelector(`span[data-router="${path}"]`);
      dom?.scrollIntoView({
        behavior: 'smooth',
        inline: 'center',
      });
    } catch (error) {
      console.log(error);
    }
  }

  useEffect(() => {
    if (!menu.length) return;
    const parentPath = findName(path, menu)?.parentKeys?.[0];
    let name;
    if (parentPath) {
      name = findName(parentPath, menu)?.name;
    } else {
      name = findName(path, menu)?.name;
    }
    const newRouteConfig = {
      path: parentPath || path,
      title: getLabel(name),
      name,
    };
    function addTags(routeConfig: { path: string; title: string; name: string }) {
      if (
        location.pathname === '/' ||
        location.pathname.includes('/exception') ||
        !routeConfig.name
      )
        return;
      const routes = _.cloneDeep(visitedRoutes);
      const currentIndex = routes.findIndex(({ name }) => {
        return name === routeConfig.name;
      });

      const currentConfig = {
        path: routeConfig.path,
        name: routeConfig.name,
        title: routeConfig.title,
      };

      if (currentIndex === -1) {
        routes.push(currentConfig);
        setVisitedRoutes(routes);
      }
      setTimeout(() => {
        scrollIntoView(currentConfig.path);
      }, 20);
    }

    addTags(newRouteConfig);
  }, [menu, path, visitedRoutes]);

  return (
    <div className={style.tagsViewContainer}>
      <div className={style.tagList}>
        {visitedRoutes.map((visitedRoute, index) => (
          <NavLink key={visitedRoute.path} to={visitedRoute.path} end>
            <span
              data-router={visitedRoute.path}
              className={classNames([
                style.tag,
                {
                  [style.isExactActive]: path === visitedRoute.path,
                },
              ])}
            >
              {visitedRoute.title}
              {path === visitedRoute.path && index !== 0 && (
                <CloseCircleOutlined
                  style={{ marginLeft: 10 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    removeRoute(index);
                  }}
                />
              )}
            </span>
          </NavLink>
        ))}
      </div>
    </div>
  );
};
export default TagsView;
