import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  deleteCountryOperatorMinCr,
  getCountryOperatorMinCr,
  addCountryOperatorMinCr,
  editCountryOperatorMinCr,
} from '@/services/tacticResources';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { getOperatorName, findLabel } from '@/utils/utils';
import MncSelect from '../channel/commonComponent/MncSelect';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const TacticLowestCr = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const { regionOptionsMcc = [] } = useFetchCountryInfo();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>();
  const [type, setType] = useState<'create' | 'edit'>('create');
  const [addType, setAddType] = useState<'mcc' | 'mnc'>('mcc');
  const [mccMncInfo] = useMccMncInfo();

  function handleEdit(row: any) {
    if (!row.mnc) {
      setAddType('mcc');
    } else {
      setAddType('mnc');
    }
    addFormRef.current?.setFieldsValue({
      ...row,
      mnc: `${row.mcc}_${row.mnc}`,
    });
    setInitialValues({
      ...row,
      mnc: `${row.mcc}_${row.mnc}`,
    });
    setOpen(true);
    setType('edit');
  }

  async function handleDelete(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除该条数据吗?`,
        onOk: async () => {
          const res = await deleteCountryOperatorMinCr({
            mcc: row.mcc,
            mnc: row.mnc || undefined,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: '国家',
        dataIndex: 'mcc',
        key: 'mcc',
        valueType: 'select',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          // mode: type === 'create' ? 'multiple' : 'default',
          options: regionOptionsMcc,
          showSearch: true,
          onChange: (value) => {
            formRef.current?.setFieldsValue({
              mnc: undefined,
            });
          },
        },
        render: (text, row: any) => findLabel(regionOptionsMcc, row.mcc),
      },
      {
        title: '运营商',
        dataIndex: 'mnc',
        key: 'mnc',
        hideInSearch: true,
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        renderFormItem: () => (
          <MncSelect
            initialValues={{
              mcc: formRef.current?.getFieldValue('mcc')?.split('_')[0],
            }}
            allowClear
          ></MncSelect>
        ),
        render: (text, row: any) => {
          const operatorName = getOperatorName(mccMncInfo, row.mnc, { mcc: row.mcc });
          return row.mnc ? `${operatorName}(${row.mnc})` : '-';
        },
      },
      {
        title: '最低cr',
        dataIndex: 'lowestcr',
        key: 'lowestcr',
        hideInSearch: true,
        valueType: 'digit',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          min: 0,
          max: 100,
          style: {
            width: 150,
          },
        },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        hideInSearch: true,
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        hideInSearch: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleEdit(row)}>
                编辑
              </Button>
              <Button type="link" onClick={() => handleDelete(row)}>
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, [mccMncInfo]);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getCountryOperatorMinCr({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'create'
          ? await addCountryOperatorMinCr({
              mccs: _.isArray(vals.mcc) ? vals.mcc : [vals.mcc],
              mncs: vals.mnc?.map((el) => el.split('_')[1]),
              lowestcr: vals.lowestcr,
            })
          : await editCountryOperatorMinCr({
              ...vals,
              mnc: _.isArray(vals.mnc)
                ? vals.mnc?.map((el) => el.split('_')[1])
                : vals.mnc?.split('_')[1],
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const keys = ['mcc', 'mnc', 'lowestcr'];
    const _columns = _.cloneDeep(columns);
    if (addType === 'mcc') {
      _columns.splice(1, 1);
    }
    return _columns
      .filter((el: any) => keys.includes(el.key))
      .map((el: any) => {
        if (el.key === 'mcc') {
          return {
            ...el,
            fieldProps: {
              mode: type === 'create' && addType === 'mcc' ? 'multiple' : 'default',
              ...el.fieldProps,
              disabled: type === 'edit',
              onChange: (value) => {
                addFormRef.current?.setFieldsValue({
                  mnc: undefined,
                });
              },
            },
          };
        }
        if (el.key === 'mnc') {
          return {
            ...el,
            fieldProps: {
              disabled: type === 'edit',
            },
            renderFormItem: () => (
              <MncSelect
                mode={type === 'create' && addType === 'mnc' ? 'multiple' : undefined}
                initialValues={{
                  mcc: addFormRef.current?.getFieldValue('mcc')?.split('_')[0],
                }}
              ></MncSelect>
            ),
          };
        }
        return el;
      });
  }, [addType, columns, type]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      mcc: undefined,
      mnc: undefined,
      lowestcr: undefined,
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
    <PageContainer>
      <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 4 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={450}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setAddType('mcc');
          setOpen(true);
        }}
      >
        按国家新增
      </Button>
      <Button
        type="primary"
        style={{ marginLeft: 10 }}
        onClick={() => {
          setType('create');
          setAddType('mnc');
          setOpen(true);
        }}
      >
        按运营商新增
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey={(row) => `${row.mcc}_${row.mnc}`}
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default TacticLowestCr;
