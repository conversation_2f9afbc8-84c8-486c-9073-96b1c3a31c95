import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  getTacticSaleProduct,
  addTacticSaleProduct,
  editTacticSaleProduct,
  getProductType,
} from '@/services/tacticResources';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { smsType } from '@/const/const';
import SelectAll from '@/components/SelectAll';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

export const saleType = [
  { label: '高质量', value: 0 },
  { label: '纯直连', value: 1 },
  { label: '其它', value: 2 },
];

const TacticSaleProduct = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const { regionOptions = [] } = useFetchCountryInfo();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>();
  const [type, setType] = useState<'create' | 'edit'>('create');
  const [mccMncInfo] = useMccMncInfo();

  const { value: productType } = useAsyncRetryFunc(async () => {
    const res = await getProductType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);

  function handleEdit(row: any) {
    addFormRef.current?.setFieldsValue({
      ...row,
    });
    setInitialValues({
      ...row,
    });
    setOpen(true);
    setType('edit');
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: '国家/地区名',
        dataIndex: 'country_name',
        key: 'country_name',
        hideInSearch: true,
        render: (text, row: any) => {
          const countryName = _.find(regionOptions, (v) => v.value === row.country_code)?.label;
          return `${countryName}`;
        },
      },
      {
        title: '国家/地区码',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        renderFormItem: () => <SelectAll options={regionOptions}></SelectAll>,
        fieldProps: {
          showSearch: true,
          maxTagCount: 20,
        },
        formItemProps: {
          label: '国家/地区',
          rules: [
            {
              required: true,
            },
          ],
        },
        render: (text, row: any) => row.country_code,
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        key: 'sms_type',
        valueType: 'select',
        fieldProps: {
          options: smsType,
          showSearch: true,
        },
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        title: '售卖类型',
        dataIndex: 'sale_type',
        key: 'sale_type',
        valueType: 'select',
        fieldProps: {
          options: saleType,
          showSearch: true,
        },
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        title: '标品类型',
        dataIndex: 'product_type',
        key: 'product_type',
        valueType: 'select',
        fieldProps: {
          options: productType,
          showSearch: true,
        },
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        hideInSearch: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleEdit(row)}>
                编辑
              </Button>
            </>
          );
        },
      },
    ];
  }, [mccMncInfo, productType]);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getTacticSaleProduct({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current', 'country_name'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      Modal.confirm({
        title: '确认提交吗?',
        content:
          '售卖类型的配置会直接影响线上客户测算单数据及标品通道配置，请确认合理配置，谨慎操作！',
        onOk: async () => {
          const res =
            type === 'create'
              ? await addTacticSaleProduct({
                  ..._.omit(vals, ['country_code', 'sms_type']),
                  country_codes: vals.country_code,
                  sms_types: vals.sms_type,
                })
              : await editTacticSaleProduct({
                  id: initialValues?.id,
                  ...vals,
                });
          if (res.code === 0) {
            setOpen(false);
            actionRef.current?.reload();
            return true;
          }
          return false;
        },
      });
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const keys = ['country_code', 'product_type', 'sale_type', 'sms_type'];
    const _columns = _.cloneDeep(columns);
    const countryCode = _columns.find((el) => el.key === 'country_code');
    const saleType = _columns.find((el) => el.key === 'sale_type');
    const smsType = _columns.find((el) => el.key === 'sms_type');
    countryCode.fieldProps.disabled = type === 'edit';
    countryCode.fieldProps.mode = type === 'create' ? 'multiple' : undefined;
    saleType.fieldProps.disabled = type === 'edit';
    smsType.fieldProps.disabled = type === 'edit';
    smsType.fieldProps.mode = type === 'create' ? 'multiple' : undefined;
    return _columns.filter((el) => keys.includes(el.key));
  }, [columns, type]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      country_code: undefined,
      product_type: undefined,
      sale_type: undefined,
      sms_type: undefined,
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
    <PageContainer>
      <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 5 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={550}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setOpen(true);
        }}
      >
        批量编辑
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default TacticSaleProduct;
