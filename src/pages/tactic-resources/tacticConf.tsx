import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  deleteTacticConf,
  getTacticConf,
  addTacticConf,
  editTacticConf,
  getResourceType,
  getProductType,
} from '@/services/tacticResources';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const TacticConf = () => {
  const { regionOptions } = useFetchCountryInfo();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>();
  const [type, setType] = useState<'create' | 'edit'>('create');
  const [mccMncInfo] = useMccMncInfo();

  const { value: resourceType } = useAsyncRetryFunc(async () => {
    const res = await getResourceType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);
  const { value: productType } = useAsyncRetryFunc(async () => {
    const res = await getProductType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);

  function handleEdit(row: any) {
    addFormRef.current?.setFieldsValue({
      ...row,
    });
    setInitialValues({
      ...row,
    });
    setOpen(true);
    setType('edit');
  }

  async function handleDelete(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除该条数据吗?`,
        onOk: async () => {
          const res = await deleteTacticConf({
            id: row.id,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: '国家',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        width: 280,
        fieldProps: {
          options: regionOptions,
          showSearch: true,
        },
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        render: (text, row: any) => {
          const countryName = _.find(regionOptions, (v) => v.value === row.country_code)?.label;
          return `${countryName}(${row.mcc})`;
        },
      },
      {
        title: '产品类型',
        dataIndex: 'product_type',
        key: 'product_type',
        valueType: 'select',
        width: 280,
        fieldProps: {
          options: productType,
          showSearch: true,
        },
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        title: '资源类型',
        dataIndex: 'resource_type',
        key: 'resource_type',
        valueType: 'select',
        width: 280,
        fieldProps: {
          options: resourceType,
          showSearch: true,
        },
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        hideInSearch: true,
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        hideInSearch: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleEdit(row)}>
                编辑
              </Button>
              <Button type="link" onClick={() => handleDelete(row)}>
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, [mccMncInfo, productType, resourceType]);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getTacticConf({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'create'
          ? await addTacticConf({
              country_codes: vals.country_code,
              resource_types: vals.resource_type,
              product_type: vals.product_type,
            })
          : await editTacticConf({
              id: initialValues?.id,
              ...vals,
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const keys =
      type === 'create'
        ? ['country_code', 'resource_type', 'product_type']
        : ['id', 'country_code', 'resource_type', 'product_type'];
    return columns
      .filter((el: any) => keys.includes(el.key))
      .map((el: any) => {
        if (el.key === 'country_code') {
          return {
            ...el,
            fieldProps: {
              ...el.fieldProps,
              mode: type === 'create' ? 'multiple' : 'default',
              disabled: type === 'edit',
            },
          };
        }
        if (el.key === 'resource_type') {
          return {
            ...el,
            fieldProps: {
              ...el.fieldProps,
              mode: type === 'create' ? 'multiple' : 'default',
            },
          };
        }
        return el;
      });
  }, [columns, type]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      country_code: [],
      resource_type: [],
      product_type: [],
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
    <PageContainer>
      <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 5 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={450}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setOpen(true);
        }}
      >
        新增
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default TacticConf;
