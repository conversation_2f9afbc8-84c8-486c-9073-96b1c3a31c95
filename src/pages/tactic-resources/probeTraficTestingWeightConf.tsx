import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  deleteProbeTraficTestingWeightConf,
  getProbeTraficTestingWeightConf,
  addProbeTraficTestingWeightConf,
  editProbeTraficTestingWeightConf,
} from '@/services/tacticResources';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { findLabel, getOperatorName } from '@/utils/utils';
import MncSelect from '../channel/commonComponent/MncSelect';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getProviderAccountList } from '@/services/channel';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

export const probeConfTypeOptions = [
  { label: 'UIN默认权重', value: 0 },
  { label: 'UIN+ProviderId权重', value: 1 },
];

const uinOptions = [
  { label: '************', value: ************ },
  { label: '************', value: ************ },
];

const ProbeTraficTestingWeightConf = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const { regionOptionsMcc = [] } = useFetchCountryInfo();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>({ type: 0 });
  const [type, setType] = useState<'create' | 'edit'>('create');
  const [addType, setAddType] = useState<'mcc' | 'mnc'>('mcc');
  const [mccMncInfo] = useMccMncInfo();

  const { value: providerIdList } = useAsyncRetryFunc(async () => {
    const res = await getProviderAccountList({
      page_index: 1,
      page_size: 1000,
      status: 7,
    });
    return res?.data?.list?.map(
      (item: {
        provider_id: number;
        supplier_id: number;
        provider_name: string;
        supplier_name: string;
      }) => {
        return {
          value: item.provider_id,
          label: `${item.provider_name}_${item.supplier_name}(${item.provider_id})`,
        };
      },
    );
  }, []);

  function handleEdit(row: any) {
    if (!row.mnc) {
      setAddType('mcc');
    } else {
      setAddType('mnc');
    }
    addFormRef.current?.setFieldsValue({
      ...row,
      mnc: `${row.mcc}_${row.mnc}`,
      type: row.provider_id ? 1 : 0,
    });
    setInitialValues({
      ...row,
      mnc: `${row.mcc}_${row.mnc}`,
      type: row.provider_id ? 1 : 0,
    });
    setOpen(true);
    setType('edit');
  }

  async function handleDelete(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除该条数据吗?`,
        onOk: async () => {
          const res = await deleteProbeTraficTestingWeightConf({
            mcc: row.mcc,
            mnc: row.mnc || undefined,
            provider_id: row.provider_id,
            uin: row.uin,
            type: row.provider_id ? 1 : 0,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: '国家',
        dataIndex: 'mcc',
        key: 'mcc',
        valueType: 'select',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          // mode: type === 'create' ? 'multiple' : 'default',
          options: regionOptionsMcc,
          showSearch: true,
          onChange: (value) => {
            formRef.current?.setFieldsValue({
              mnc: undefined,
            });
          },
        },
        render: (text, row: any) => findLabel(regionOptionsMcc, row.mcc),
      },
      {
        title: '运营商',
        dataIndex: 'mnc',
        key: 'mnc',
        hideInSearch: true,
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        renderFormItem: () => (
          <MncSelect
            initialValues={{
              mcc: formRef.current?.getFieldValue('mcc')?.split('_')[0],
            }}
            allowClear
          ></MncSelect>
        ),
        render: (text, row: any) => {
          const operatorName = getOperatorName(mccMncInfo, row.mnc, {
            mcc: row.mcc,
          });
          return row.mnc ? `${operatorName}(${row.mnc})` : '-';
        },
      },

      {
        title: 'uin',
        dataIndex: 'uin',
        key: 'uin',
        hideInSearch: true,
        valueType: 'select',
        fieldProps: {
          options: uinOptions,
        },
      },
      {
        title: '供应商账号',
        dataIndex: 'provider_id',
        key: 'provider_id',
        valueType: 'select',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          options: providerIdList,
          showSearch: true,
        },
        render: (text, row) =>
          _.find(providerIdList, (v) => v.value === row.provider_id)?.label ??
          (row.provider_id || '-'),
      },

      {
        title: '测试权重',
        dataIndex: 'testing_weight',
        key: 'testing_weight',
        hideInSearch: true,
        valueType: 'digit',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          min: 0,
          max: 100,
          style: {
            width: 150,
          },
        },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        hideInSearch: true,
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        hideInSearch: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleEdit(row)}>
                编辑
              </Button>
              <Button type="link" onClick={() => handleDelete(row)}>
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, [mccMncInfo, providerIdList]);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getProbeTraficTestingWeightConf({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'create'
          ? await addProbeTraficTestingWeightConf({
              mccs: _.isArray(vals.mcc) ? vals.mcc : [vals.mcc],
              mncs: vals.mnc?.map((el) => el.split('_')[1]),
              provider_id: vals.provider_id,
              uin: vals.uin,
              type: vals.type,
              testing_weight: vals.testing_weight,
            })
          : await editProbeTraficTestingWeightConf({
              ...vals,
              mnc: _.isArray(vals.mnc)
                ? vals.mnc.map((el) => el.split('_')[1])
                : vals.mnc?.split('_')[1],
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const keys = ['mcc', 'mnc', 'testing_weight', 'provider_id', 'uin', 'type'];
    const _columns = _.cloneDeep(columns);
    if (addType === 'mcc') {
      _columns.splice(1, 1);
    }
    _columns.splice(2, 0, {
      title: '配置类型',
      dataIndex: 'type',
      key: 'type',
      valueType: 'radio',
      hideInSearch: true,
      formItemProps: {
        rules: [
          {
            required: true,
          },
        ],
      },
      fieldProps: {
        options: probeConfTypeOptions,
      },
    });
    return _columns
      .filter((el: any) => keys.includes(el.key))
      .map((el: any) => {
        const mccField = _columns.find((el) => el.key === 'mcc');
        const mncField = _columns.find((el) => el.key === 'mnc');
        const typeField = _columns.find((el) => el.key === 'type');
        const uinField = _columns.find((el) => el.key === 'uin');

        mccField.fieldProps = {
          mode: type === 'create' && addType === 'mcc' ? 'multiple' : 'default',
          ...mccField.fieldProps,
          disabled: type === 'edit',
          onChange: (value) => {
            addFormRef.current?.setFieldsValue({
              mnc: undefined,
            });
          },
        };
        if (mncField) {
          mncField.fieldProps = {
            disabled: type === 'edit',
          };
          mncField.renderFormItem = () => (
            <MncSelect
              mode={type === 'create' && addType === 'mnc' ? 'multiple' : undefined}
              initialValues={{
                mcc: addFormRef.current?.getFieldValue('mcc')?.split('_')[0],
              }}
            ></MncSelect>
          );
        }
        typeField.fieldProps = {
          ...typeField.fieldProps,
          disabled: type === 'edit',
        };
        uinField.fieldProps = {
          ...uinField.fieldProps,
          disabled: type === 'edit',
        };
        if (el.key === 'provider_id') {
          return {
            valueType: 'dependency',
            name: ['type'],
            columns: ({ type: _type }: any) => {
              const field = columns.find((el) => el.key === 'provider_id');
              field.fieldProps.disabled = type === 'edit';
              return _type === 1 ? [field] : [];
            },
          };
        }
        return el;
      });
  }, [addType, columns, type]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      mcc: undefined,
      mnc: undefined,
      provider_id: undefined,
      uin: undefined,
      testing_weight: undefined,
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
    <PageContainer>
      <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 6 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={500}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setAddType('mcc');
          setOpen(true);
        }}
      >
        按国家新增
      </Button>
      <Button
        type="primary"
        style={{ marginLeft: 10 }}
        onClick={() => {
          setType('create');
          setAddType('mnc');
          setOpen(true);
        }}
      >
        按运营商新增
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey={(row, i) => `${i}`}
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default ProbeTraficTestingWeightConf;
