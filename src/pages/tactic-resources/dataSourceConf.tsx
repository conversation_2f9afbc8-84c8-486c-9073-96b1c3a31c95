import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  deleteDataSourceConf,
  getDataSourceConf,
  addDataSourceConf,
  editDataSourceConf,
} from '@/services/tacticResources';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { findLabel, getOperatorName } from '@/utils/utils';
import MncSelect from '../channel/commonComponent/MncSelect';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const siteOptions = [
  { label: '新加坡', value: 0 },
  { label: '香港', value: 1 },
  { label: '法兰克福', value: 2 },
];

const DataSourceConf = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const { regionOptions = [] } = useFetchCountryInfo();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>({ type: 0 });
  const [type, setType] = useState<'create' | 'edit'>('create');
  const [addType, setAddType] = useState<'mcc' | 'mnc'>('mcc');
  const [mccMncInfo] = useMccMncInfo();

  function handleEdit(row: any) {
    if (!row.mnc) {
      setAddType('mcc');
    } else {
      setAddType('mnc');
    }
    addFormRef.current?.setFieldsValue({
      ...row,
      mnc: `${row.mcc}_${row.mnc}`,
    });
    setInitialValues({
      ...row,
      mnc: `${row.mcc}_${row.mnc}`,
    });
    setOpen(true);
    setType('edit');
  }

  async function handleDelete(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除该条数据吗?`,
        onOk: async () => {
          const res = await deleteDataSourceConf({
            country_code: row.country_code,
            mnc: row.mnc || undefined,
            site: row.site,
            uin: row.uin,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: '国家',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          // mode: type === 'create' ? 'multiple' : 'default',
          options: regionOptions,
          showSearch: true,
          onChange: (value) => {
            formRef.current?.setFieldsValue({
              mnc: undefined,
            });
          },
        },
        render: (text, row: any) => findLabel(regionOptions, row.country_code),
      },
      {
        title: '运营商',
        dataIndex: 'mnc',
        key: 'mnc',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        renderFormItem: () => (
          <MncSelect
            initialValues={{
              country_code: formRef.current?.getFieldValue('country_code'),
            }}
            allowClear
          ></MncSelect>
        ),
        render: (text, row: any) => {
          const operatorName = getOperatorName(mccMncInfo, row.mnc, {
            country_code: row?.country_code,
          });
          return row.mnc ? `${operatorName}(${row.mnc})` : '-';
        },
      },
      {
        title: '站点',
        dataIndex: 'site',
        key: 'site',
        width: 180,
        hideInSearch: true,
        valueType: 'select',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          options: siteOptions,
        },
      },
      {
        title: 'uin',
        dataIndex: 'uin',
        key: 'uin',
        hideInSearch: true,
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        title: '权重',
        dataIndex: 'weight',
        key: 'weight',
        hideInSearch: true,
        valueType: 'digit',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          min: 0,
          max: 100,
          style: { width: 150 },
        },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        hideInSearch: true,
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        hideInSearch: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleEdit(row)}>
                编辑
              </Button>
              <Button type="link" onClick={() => handleDelete(row)}>
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, [mccMncInfo]);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getDataSourceConf({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'create'
          ? await addDataSourceConf({
              country_codes: _.isArray(vals.country_code) ? vals.country_code : [vals.country_code],
              mncs: vals.mnc?.map((el) => el.split('_')[1]),
              weight: vals.weight,
              uin: vals.uin,
              site: vals.site,
            })
          : await editDataSourceConf({
              ...vals,
              mnc: _.isArray(vals.mnc)
                ? vals.mnc.map((el) => el.split('_')[1])
                : vals.mnc?.split('_')[1],
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const keys = ['country_code', 'mnc', 'uin', 'weight', 'site'];
    const _columns = _.cloneDeep(columns);
    if (addType === 'mcc') {
      _columns.splice(1, 1);
    }
    return _columns
      .filter((el: any) => keys.includes(el.key))
      .map((el: any) => {
        const countryCodeField = _columns.find((el) => el.key === 'country_code');
        const mncField = _columns.find((el) => el.key === 'mnc');
        const uinField = _columns.find((el) => el.key === 'uin');

        countryCodeField.fieldProps = {
          mode: type === 'create' && addType === 'mcc' ? 'multiple' : 'default',
          ...countryCodeField.fieldProps,
          disabled: type === 'edit',
          onChange: (value) => {
            addFormRef.current?.setFieldsValue({
              mnc: undefined,
            });
          },
        };
        if (mncField) {
          mncField.fieldProps = {
            disabled: type === 'edit',
          };
          mncField.renderFormItem = () => (
            <MncSelect
              mode={type === 'create' && addType === 'mnc' ? 'multiple' : undefined}
              initialValues={{
                country_code: addFormRef.current?.getFieldValue('country_code')?.split('_')[0],
              }}
            ></MncSelect>
          );
        }
        uinField.fieldProps = {
          ...uinField.fieldProps,
          disabled: type === 'edit',
        };
        return el;
      });
  }, [addType, columns, type]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      country_code: undefined,
      mnc: undefined,
      uin: undefined,
      weight: undefined,
      site: undefined,
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
    <PageContainer>
      <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 6 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={500}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setAddType('mcc');
          setOpen(true);
        }}
      >
        按国家新增
      </Button>
      <Button
        type="primary"
        style={{ marginLeft: 10 }}
        onClick={() => {
          setType('create');
          setAddType('mnc');
          setOpen(true);
        }}
      >
        按运营商新增
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey={(row, i) => `${i}`}
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default DataSourceConf;
