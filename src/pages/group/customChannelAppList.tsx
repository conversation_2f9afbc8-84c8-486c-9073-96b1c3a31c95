import React, { useState, useEffect, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Table, Space, Modal, message, Button, Form, Select, Input } from 'antd';
import {
  getChannelAppList,
  editChannelAppList,
  delChannelAppList,
  addChannelAppList,
  getChannelGroup,
} from '@/services/api';
import { Navigate, history } from 'umi';
import _ from 'lodash';
import { dumpwarning } from '@/services/dumpWarning';

const ChannelAppList = () => {
  const group_id = (history.location?.state as { group_id?: number })?.group_id;
  const [form] = Form.useForm();
  const [form1] = Form.useForm();
  const { TextArea } = Input;
  const [list, setList] = useState<any>([]);
  const [initForm] = useState<any>({
    group_id,
  });
  const [groupList, setGroupList] = useState<any>([]);
  const [searchIds, setSearchIds] = useState<any>([]);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [count, setCount] = useState<number>(0);
  const [isVisible, setIsVisible] = useState(false);
  const [checkApp, setCheckApp] = useState<React.Key[]>([]);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const rowSelection = {
    selectedRowKeys: checkApp,
    onChange: (selectedRowKeys: React.Key[]) => {
      setCheckApp(selectedRowKeys);
    },
  };

  const getList = useCallback(async () => {
    if (!group_id) return;
    setLoading(true);
    setCheckApp([]);
    const appids = searchIds;
    const params = {
      appids,
      group_id,
    };
    if (!appids.length) {
      delete params.appids;
    }
    const res = await getChannelAppList({
      ...params,
      page_index: pageIndex,
      page_size: pageSize,
    });
    setList(res?.data?.list || []);
    setCount(res?.data?.count || 0);
    setLoading(false);
  }, [group_id, pageIndex, pageSize, searchIds]);

  useEffect(() => {
    if (!group_id) return;
    getList();
  }, [getList, group_id]);

  if (!group_id) {
    const url = 'group-channel-customer';
    return <Navigate to={url} />;
  }

  async function doDel(ids: any[]) {
    if (!ids.length) return;
    const params = { group_id, appids: ids };
    const res = await delChannelAppList(params);
    if (res?.code === 0) {
      message.success('操作成功');
    }
    getList();
  }
  async function doEdit(row: any) {
    const { appid, priority } = row;
    const params = { group_id, appid, priority };
    const res = await editChannelAppList({ ...params });
    if (res?.code === 0) {
      message.success('操作成功');
    }
    getList();
  }
  async function onSubmit(val: any) {
    const { appids, group_id, priority } = val;
    let apps = appids
      .trim()
      .replace(/[\s\t\n]+/g, ',')
      .split(',');
    try {
      apps = _.map(apps, (app) => Number(app));
    } catch (e) {
      message.error('请检查应用列表输入');
    }
    const params = { group_id, priority, appids: apps };
    const res = await addChannelAppList(params);
    setIsVisible(false);
    if (res.code === 0) {
      form.resetFields();
      getList();
    }
  }

  function validateApps(value: string, callback: any) {
    if (!value) {
      callback();
      return;
    }
    const appArr = value
      .trim()
      .replace(/[\s\t\n]+/g, ',')
      .split(',');
    const isValid = _.every(appArr, (app: string) => {
      return app && /^[0-9]*$/.test(app);
    });
    isValid ? callback() : callback('应用id格式不正确，请重新输入');
  }

  async function queryAppList() {
    const res = await getChannelGroup({
      nopage: 1, // 0分页，1不分页 ，默认分页
    });
    setGroupList(res?.data?.list || []);
  }

  function handleSearchIds(value: string) {
    const inputStr = value;
    if (inputStr === '') {
      setSearchIds([]);
      return;
    }
    try {
      let strArr = value.split(',');
      strArr = _.map(strArr, (item) => item.trim());
      setSearchIds(strArr);
    } catch (error) {
      setSearchIds([]);
    }
  }

  return (
    <>
      <PageContainer>
        <div style={{ marginBottom: 15 }}>
          <span>通道组id： {group_id}</span>
        </div>
        <Space>
          <Button
            type="primary"
            onClick={() => {
              setIsVisible(true);
              queryAppList();
            }}
          >
            添加应用
          </Button>
          <Button
            type="primary"
            onClick={() => {
              doDel(checkApp);
            }}
          >
            批量删除
          </Button>
          <Form
            layout="inline"
            form={form1}
            labelAlign="right"
            onFinish={(vals) => handleSearchIds(vals.ids)}
          >
            <Form.Item name="ids">
              <Input placeholder="请输入应用id，可使用英文逗号分隔多个" style={{ width: 250 }} />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
            </Form.Item>
          </Form>
        </Space>
        <Button
          style={{ float: 'right' }}
          type="primary"
          onClick={() => {
            const a = document.createElement('a');
            a.href = `/apis/canal-channal-group/group/export-group-app?group_id=${group_id}`;
            a.download = '';
            a.click();
            dumpwarning({
              route: '/canal-channal-group/group/export-group-app',
              params: { group_id },
            });
          }}
        >
          导出全部
        </Button>
        <Table
          rowKey="appid"
          rowSelection={{
            type: 'checkbox',
            ...rowSelection,
          }}
          dataSource={list}
          columns={[
            {
              title: '应用id',
              dataIndex: 'appid',
              key: 'appid',
              align: 'center',
            },
            {
              title: '扩展码',
              align: 'center',
              dataIndex: 'pre_extend',
              key: 'pre_extend',
            },
            {
              title: '应用名称',
              dataIndex: 'app_name',
              key: 'app_name',
              align: 'center',
            },
            {
              title: '优先级',
              align: 'center',
              render: (row: any) => {
                return (
                  <Select
                    style={{ width: 150 }}
                    defaultValue={row.priority}
                    onChange={(value) => (row.priority = value)}
                  >
                    <Select.Option value={0}>0级(低)</Select.Option>
                    <Select.Option value={1}>1级(中)</Select.Option>
                    <Select.Option value={2}>2级(高)</Select.Option>
                  </Select>
                );
              },
            },
            {
              title: '操作',
              render: (row: any) => {
                return (
                  <Space>
                    <a
                      onClick={() => {
                        const arr = [];
                        arr.push(row.appid);
                        doDel(arr);
                      }}
                    >
                      删除
                    </a>
                    <a
                      onClick={() => {
                        doEdit(row);
                      }}
                    >
                      编辑
                    </a>
                  </Space>
                );
              },
              align: 'center',
            },
          ]}
          loading={isLoading}
          pagination={{
            current: pageIndex,
            total: count,
            showSizeChanger: true,
            onShowSizeChange: (current, page_size) => setPageSize(page_size),
            onChange: (page) => {
              setPageIndex(page);
            },
          }}
          style={{ marginTop: 20 }}
        />
      </PageContainer>
      <Modal
        open={isVisible}
        destroyOnClose
        onOk={() => form.submit()}
        onCancel={() => {
          setIsVisible(false);
          form.resetFields();
        }}
      >
        <Form
          form={form}
          labelAlign="right"
          onFinish={(vals) => onSubmit(vals)}
          initialValues={{ ...initForm }}
        >
          <Form.Item name="group_id" label="通道组 " rules={[{ required: true }]}>
            <Select
              placeholder="请选择通道组"
              style={{ width: 250 }}
              filterOption={(input, option) =>
                (option!.children as unknown as string).toLowerCase().includes(input.toLowerCase())
              }
              allowClear
              showSearch
            >
              {(groupList ?? []).map((group: { group_id: number; desc: string }) => {
                return (
                  <Select.Option key={group.group_id} value={group.group_id}>
                    {group.desc}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="priority" label="优先级 " rules={[{ required: true }]}>
            <Select style={{ width: 250 }} placeholder="请选择优先级">
              <Select.Option value={0}>0级(低)</Select.Option>
              <Select.Option value={1}>1级(中)</Select.Option>
              <Select.Option value={2}>2级(高)</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="appids"
            label="应用列表"
            rules={[
              { required: true },
              {
                validator: (rule, value, callback) => {
                  validateApps(value, callback);
                },
              },
            ]}
          >
            <TextArea rows={4} placeholder="请输入应用id，可使用空格或换行分隔多个" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ChannelAppList;
