import React, { useState, useEffect, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Table, Space, Modal, message, Button, Form, Input, Switch, Popconfirm } from 'antd';
import {
  getChannelGroup,
  createChannelGroup,
  deleteChannelGroup,
  editChannelGroup,
} from '@/services/api';
import { history } from 'umi';

const ChannelManage = () => {
  const [form] = Form.useForm();
  const [form1] = Form.useForm();
  const [list, setList] = useState<any>([]);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [count, setCount] = useState<number>(0);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchKey, setSearchKey] = useState<string>('');
  const [isVisible, setIsVisible] = useState(false);
  const [checkProvider, setCheckProvider] = useState<React.Key[]>([]);

  const rowSelection = {
    selectedRowKeys: checkProvider,
    onChange: (selectedRowKeys: React.Key[]) => {
      setCheckProvider(selectedRowKeys);
    },
  };

  const getList = useCallback(
    async (isInitPage?: boolean) => {
      setLoading(true);
      const params = {
        desc: searchKey,
        page_index: isInitPage ? 1 : pageIndex,
        page_size: pageSize,
      };
      const res = await getChannelGroup(params);
      isInitPage && setPageIndex(1);
      setList(res?.data?.list || []);
      setCount(res?.data?.count || 0);
      setLoading(false);
    },
    [pageIndex, pageSize, searchKey],
  );

  useEffect(() => {
    getList();
  }, [getList]);

  function onSuccess(code: number) {
    if (code === 0) {
      message.success('操作成功');
    }
    getList();
  }

  async function doDel(ids: any[]) {
    if (!ids.length) return;
    const res = await deleteChannelGroup({ group_ids: ids });

    onSuccess(res.code);
  }

  async function onSubmit(vals: any) {
    const res = await createChannelGroup({ ...vals });
    onSuccess(res?.code);
    res?.code === 0 && form.resetFields();
    setIsVisible(false);
  }

  async function changeStatus(row: any) {
    const params = {
      group_id: row.group_id,
      enable: row.enable === 1 ? 0 : 1,
    };
    const res = await editChannelGroup(params);
    onSuccess(res.code);
  }

  return (
    <>
      <PageContainer>
        <Space>
          <Button
            type="primary"
            onClick={() => {
              setIsVisible(true);
            }}
          >
            + 新建通道组
          </Button>
          <Button
            type="primary"
            onClick={() => {
              doDel(checkProvider);
            }}
          >
            批量删除
          </Button>
        </Space>
        <div style={{ float: 'right' }}>
          <Form
            layout="inline"
            form={form1}
            labelAlign="right"
            onFinish={(vals) => setSearchKey(vals.desc)}
          >
            <Form.Item name="desc">
              <Input placeholder="请输入通道组描述信息" style={{ width: 250 }} />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
            </Form.Item>
          </Form>
        </div>
        <Table
          rowKey="group_id"
          rowSelection={{
            type: 'checkbox',
            ...rowSelection,
          }}
          dataSource={list}
          columns={[
            {
              title: '通道组id',
              dataIndex: 'group_id',
              key: 'group_id',
              align: 'center',
            },
            {
              title: '扩展码长度',
              dataIndex: 'pre_extend_len',
              key: 'pre_extend_len',
              align: 'center',
            },
            {
              title: '表述信息',
              dataIndex: 'desc',
              key: 'desc',
              align: 'center',
            },
            {
              title: '编辑时间',
              dataIndex: 'modify_time',
              key: 'modify_time',
              align: 'center',
            },
            {
              title: '创建时间',
              dataIndex: 'create_time',
              key: 'create_time',
              align: 'center',
            },
            {
              title: '状态',
              align: 'center',
              render: (row: any) => {
                let isActive = row.enable === 1;
                return (
                  <Space>
                    <span>{row.enable === 1 ? '启用' : '禁用'}</span>
                    <Popconfirm
                      title="Are you sure to change the status of this task?"
                      onConfirm={() => changeStatus(row)}
                      onCancel={() => {
                        isActive = row.enable === 1;
                      }}
                      okText="Yes"
                      cancelText="No"
                    >
                      <Switch checked={isActive} />
                    </Popconfirm>
                  </Space>
                );
              },
            },
            {
              title: '应用列表',
              align: 'center',
              render: (row: any) => {
                return (
                  <a
                    onClick={() => {
                      history.push('/group/channel-customer-appList', {
                        group_id: row.group_id,
                      });
                    }}
                  >
                    查看
                  </a>
                );
              },
            },
            {
              title: '设置',
              render: (row: any) => {
                // return <Link to={`/direct/channel-detail/1`}>详情</Link>;
                return (
                  <a
                    onClick={() => {
                      history.push('/group/channel-customer-detail', {
                        group_id: row.group_id,
                      });
                    }}
                  >
                    详情
                  </a>
                );
              },
              align: 'center',
            },
            {
              title: '删除',
              render: (row: any) => {
                return (
                  <a
                    onClick={() => {
                      const arr = [];
                      arr.push(row.group_id);
                      doDel(arr);
                    }}
                  >
                    删除
                  </a>
                );
              },
              align: 'center',
            },
          ]}
          loading={isLoading}
          pagination={{
            defaultCurrent: 1,
            total: count,
            showSizeChanger: true,
            onShowSizeChange: (current, page_size) => setPageSize(page_size),
            onChange: (page) => {
              setPageIndex(page);
            },
          }}
          style={{ marginTop: 20 }}
        />
      </PageContainer>
      <Modal
        open={isVisible}
        destroyOnClose
        onOk={() => {
          form.submit();
        }}
        onCancel={() => {
          setIsVisible(false);
        }}
      >
        <Form form={form} labelAlign="right" onFinish={(vals) => onSubmit(vals)}>
          <Form.Item name="desc" rules={[{ required: true, message: '请输入通道组描述信息' }]}>
            <Input placeholder="请添加通道组描述信息" style={{ width: 250 }} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ChannelManage;
