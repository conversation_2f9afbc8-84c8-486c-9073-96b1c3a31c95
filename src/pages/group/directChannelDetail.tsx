import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Table, Space, message, Button, Select, Input, InputNumber } from 'antd';
import {
  getDirectProviderList,
  getProviderList, // sepecial 签名子码
  deleteDirectProvider,
  editDirectGroup,
  addDirectProvider,
  editDirectProvider,
  getDirectGroupInfo,
} from '@/services/api';
import type { ColumnsType } from 'antd/lib/table';
import { Navigate, history } from 'umi';
import _ from 'lodash';
import { priorityStatus } from './const';

const ChannelDetail = () => {
  const group_id = (history.location?.state as { group_id?: number })?.group_id;
  const [list, setList] = useState<any>([]);
  const [initList, setInitList] = useState<any>([]);
  const [providerList, setProviderList] = useState<any>([]);
  const [jumpIdList, setJumpIdList] = useState<any>([]);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [count, setCount] = useState<number>(0);
  const [providerId, setProviderId] = useState<any>();
  const [checkProvider, setCheckProvider] = useState<React.Key[]>([]);
  const [groupDesc, setGroupDesc] = useState('');
  const [selectTag, setSelectTag] = useState(['所有']);
  const [filterValue, setFilterValue] = useState<any>(null);

  const massJumpTo = `http://observe.woa.com:8081/explore?orgId=**********&left=%5B%22now-30m%22,%22now%22,%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%7B%22datasource%22:%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%22groupBy%22:%5B%7B%22type%22:%22tag%22,%22params%22:%5B%22supplier%22%5D%7D,%7B%22params%22:%5B%221m%22%5D,%22type%22:%22time%22%7D%5D,%22measurement%22:%22sms%22,%22namespace%22:%22%22,%22orderByTime%22:%22ASC%22,%22policy%22:%22default%22,%22region%22:%22gz_new%22,%22resultFormat%22:%22table%22,%22select%22:%5B%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(req_success_count)%2Fsum(req_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_count)%2Fsum(req_success_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_success_count)%2Fsum(cb_count)%22%5D%7D%5D%5D,%22slimit%22:%2210%22,%22tags%22:%5B%7B%22condition%22:%22AND%22,%22key%22:%22supplier%22,%22operator%22:%22IN%22,%22value%22:%22${jumpIdList.join(
    ',',
  )}%22%7D%5D,%22timeShift%22:%22%22%7D%5D`;

  const getList = useCallback(async () => {
    setLoading(true);
    const params = {
      priority: filterValue,
    };
    params.priority === null && delete params.priority;
    const res = await getDirectProviderList({ ...params, group_id });
    setList(res?.data || []);
    setInitList(res?.data || []);
    setLoading(false);
  }, [filterValue, group_id]);

  const onSuccess = useCallback(
    (code: number) => {
      if (code === 0) {
        message.success('操作成功');
        getList();
      }
    },
    [getList],
  );

  const doDel = useCallback(
    async (ids: any[]) => {
      if (!ids.length) return;
      const params = { provider_ids: ids, group_id };
      const res = await deleteDirectProvider(params);
      onSuccess(res?.code);
    },
    [group_id, onSuccess],
  );

  const doEdit = useCallback(
    async (row: any) => {
      const res = await editDirectProvider({ ...row });
      onSuccess(res?.code);
    },
    [onSuccess],
  );

  const columns: ColumnsType<any> = useMemo(
    () => [
      {
        title: '通道',
        dataIndex: 'provider_name',
        key: 'provider_name',
        align: 'left',
      },
      {
        title: '单发普通',
        align: 'center',
        render: (row: any) => {
          return (
            <InputNumber
              min={0}
              defaultValue={row.single}
              onChange={(value) => {
                if (value === null) {
                  row.single = 0;
                  return;
                }
                row.single = value;
              }}
            />
          );
        },
      },
      {
        title: '单发营销',
        render: (row: any) => {
          return (
            <InputNumber
              min={0}
              defaultValue={row.single_b}
              onChange={(value) => {
                if (value === null) {
                  row.single_b = 0;
                  return;
                }
                row.single_b = value;
              }}
            />
          );
        },
      },
      {
        title: '群发2普通',
        align: 'center',
        render: (row: any) => {
          return (
            <InputNumber
              min={0}
              defaultValue={row.multi2}
              onChange={(value) => {
                if (value === null) {
                  row.multi2 = 0;
                  return;
                }
                row.multi2 = value;
              }}
            />
          );
        },
      },
      {
        title: '群发2营销',
        align: 'center',
        render: (row: any) => {
          return (
            <InputNumber
              min={0}
              defaultValue={row.multi2_b}
              onChange={(value) => {
                if (value === null) {
                  row.multi2_b = 0;
                  return;
                }
                row.multi2_b = value;
              }}
            />
          );
        },
      },
      {
        title: '优先级',
        align: 'center',
        filters: priorityStatus,
        filterMultiple: false,
        filteredValue: filterValue,
        render: (row: any) => {
          return (
            <Select defaultValue={row.priority} onChange={(value) => (row.priority = value)}>
              {(priorityStatus ?? []).map((item) => {
                return (
                  <Select.Option value={item.value} key={item.value}>
                    {item.text}
                  </Select.Option>
                );
              })}
            </Select>
          );
        },
      },
      {
        title: '操作',
        render: (row: any) => {
          const jumpTo = `http://observe.woa.com:8081/explore?orgId=**********&left=%5B%22now-30m%22,%22now%22,%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%7B%22datasource%22:%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%22groupBy%22:%5B%7B%22type%22:%22tag%22,%22params%22:%5B%22supplier%22%5D%7D,%7B%22params%22:%5B%221m%22%5D,%22type%22:%22time%22%7D%5D,%22measurement%22:%22sms%22,%22namespace%22:%22%22,%22orderByTime%22:%22ASC%22,%22policy%22:%22default%22,%22region%22:%22gz_new%22,%22resultFormat%22:%22table%22,%22select%22:%5B%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(req_success_count)%2Fsum(req_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_count)%2Fsum(req_success_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_success_count)%2Fsum(cb_count)%22%5D%7D%5D%5D,%22slimit%22:%2210%22,%22tags%22:%5B%7B%22key%22:%22supplier%22,%22operator%22:%22%3D%22,%22value%22:%22${row.provider_id}%22%7D%5D,%22timeShift%22:%22%22%7D%5D`;
          return (
            <Space>
              <a
                onClick={() => {
                  const arr = [];
                  arr.push(row.provider_id);
                  doDel(arr);
                }}
              >
                删除
              </a>
              <a
                onClick={() => {
                  doEdit(row);
                }}
              >
                编辑
              </a>
              <a href={jumpTo} target="_blank" rel="noreferrer">
                监控
              </a>
            </Space>
          );
        },
        align: 'center',
      },
    ],
    [doDel, doEdit, filterValue],
  );

  const providerOptions = [
    {
      key: '所有',
      value: '所有',
    },
    {
      key: '全国三网',
      value: '全国三网',
    },
    {
      key: '全国移动',
      value: '全国移动',
    },
    {
      key: '全国联通',
      value: '全国联通',
    },
    {
      key: '全国电信',
      value: '全国电信',
    },
  ];
  const rowSelection = {
    selectedRowKeys: checkProvider,
    onChange: (selectedRowKeys: React.Key[]) => {
      setCheckProvider(selectedRowKeys);
    },
  };

  const getProviderInfos = useCallback(async () => {
    const res = await getProviderList();
    setProviderList(res?.data || []);
  }, []);

  const getGroupDesc = useCallback(async () => {
    if (!group_id) return;
    const res = await getDirectGroupInfo({ group_id });
    setGroupDesc(res?.data?.desc);
  }, [group_id]);

  useEffect(() => {
    if (!group_id) return;
    getProviderInfos();
    getGroupDesc();
  }, [getGroupDesc, getProviderInfos, group_id]);

  useEffect(() => {
    if (selectTag[0] === '所有' || !selectTag.length) {
      setList(initList || []);
      setCount(initList.length || 0);
      return;
    }
    const filteredList = _.filter(
      initList,
      (item) => item.provider_name.indexOf(selectTag[0]) >= 0,
    );
    setList(filteredList || []);
    setCount(filteredList.length || 0);
  }, [getList, initList, selectTag]);

  useEffect(() => {
    getList();
  }, [filterValue]);

  useEffect(() => {
    const idArr: number[] = [];
    _.each(list, (item) => {
      idArr.push(item.provider_id);
    });
    setJumpIdList(idArr);
  }, [list]);

  if (!group_id) {
    const url = 'group-direct-customer';
    return <Navigate to={url} />;
  }

  async function editGroupDesc() {
    if (groupDesc === '') return;
    const params = { group_id, desc: groupDesc };
    const res = await editDirectGroup(params);
    if (res.code) {
      message.success('编辑通道组成功');
    }
    getGroupDesc();
  }
  async function add() {
    if (providerId === undefined) {
      return;
    }
    const params = { group_id, provider_id: providerId };
    const res = await addDirectProvider(params);
    onSuccess(res.code);
  }

  function onSelectChange(value: any) {
    setSelectTag(value.length > 1 ? value.slice(value.length - 1) : value);
  }

  function handleTableChange(
    pagination: any,
    filters: any,
    sorter: any,
    action: { action: string },
  ) {
    if (action.action === 'filter') {
      setFilterValue(filters[5]);
    }
  }

  return (
    <>
      <PageContainer>
        <div>
          <span style={{ marginRight: 15 }}>
            通道组id： <Input style={{ width: 250 }} defaultValue={group_id} disabled />
          </span>
          <span style={{ marginRight: 15 }}>
            通道组描述：{' '}
            <Input
              value={groupDesc}
              placeholder="请添加通道组描述信息"
              onChange={(e) => {
                setGroupDesc(e.target.value);
              }}
              style={{ width: 300 }}
            />
          </span>
          <Button
            type="primary"
            onClick={() => {
              editGroupDesc();
            }}
          >
            编辑
          </Button>
        </div>
        <div style={{ marginTop: 10 }}>
          <span style={{ marginRight: 15 }}>
            通道列表：{' '}
            <Select
              placeholder="请选择通道商"
              style={{ width: 350 }}
              filterOption={(input, option) =>
                (option!.children as unknown as string).toLowerCase().includes(input.toLowerCase())
              }
              allowClear
              showSearch
              onChange={(val) => {
                setProviderId(val);
              }}
            >
              {(providerList ?? []).map((prov: any) => {
                return (
                  <Select.Option key={prov.provider_id} value={prov.provider_id}>
                    {prov.provider_name}
                  </Select.Option>
                );
              })}
            </Select>
          </span>
          <Button
            type="primary"
            onClick={() => {
              add();
            }}
          >
            添加
          </Button>
        </div>
        <div style={{ marginTop: 35 }}>
          <Space>
            <Select style={{ width: 250 }} value={selectTag} mode="tags" onChange={onSelectChange}>
              {(providerOptions ?? []).map((prov: any) => {
                return (
                  <Select.Option key={prov.key} value={prov.key}>
                    {prov.value}
                  </Select.Option>
                );
              })}
            </Select>
            <span>
              <a
                href={massJumpTo}
                target="_blank"
                style={{ opacity: !jumpIdList.length ? 0.5 : 1 }}
                onClick={(e) => {
                  if (!jumpIdList.length) {
                    e.preventDefault();
                  }
                }}
                rel="noreferrer"
              >
                监控
              </a>
            </span>
          </Space>
          <Button
            type="primary"
            style={{ float: 'right' }}
            onClick={() => {
              doDel(checkProvider);
            }}
          >
            批量删除
          </Button>
        </div>

        <Table
          rowKey="provider_id"
          rowSelection={{
            type: 'checkbox',
            ...rowSelection,
          }}
          dataSource={list}
          columns={columns}
          loading={isLoading}
          pagination={{
            defaultCurrent: 1,
            total: count,
            showSizeChanger: true,
          }}
          style={{ marginTop: 20 }}
          onChange={handleTableChange}
        />
      </PageContainer>
    </>
  );
};

export default ChannelDetail;
