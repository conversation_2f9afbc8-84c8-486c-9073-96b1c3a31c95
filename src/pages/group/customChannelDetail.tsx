import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Table, Space, message, Button, Select, Input, InputNumber } from 'antd';
import {
  getChannelProviderList,
  getCanalProviders,
  deleteChannelProvider,
  editChannelGroup,
  addChannelProvider,
  editChannelProvider,
  getChannelGroupInfo,
} from '@/services/api';
import type { ColumnsType } from 'antd/lib/table';
import { priorityStatus } from './const';
import { Navigate, history } from 'umi';

const ChannelDetail = () => {
  const group_id = (
    history.location?.state as {
      group_id?: number;
    }
  )?.group_id;
  const [list, setList] = useState<any>([]);
  const [providerList, setProviderList] = useState<any>([]);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [count, setCount] = useState<number>(0);
  const [providerId, setProviderId] = useState<any>();
  const [checkProvider, setCheckProvider] = useState<React.Key[]>([]);
  const [groupDesc, setGroupDesc] = useState('');
  const [pre_extend_len, setExtrendLen] = useState<any>(undefined);
  const [selectTag, setSelectTag] = useState(['所有']);
  const [filterValue, setFilterValue] = useState<any>(null);
  const [page_index, setPageIndex] = useState<number>(1);
  const [page_size, setPageSize] = useState<number>(10);

  const getList = useCallback(async () => {
    setLoading(true);
    const params = {
      priority: filterValue,
    };
    if (params.priority === null) {
      params.priority = undefined;
    }
    const res = await getChannelProviderList({
      ...params,
      group_id,
      page_size,
      page_index,
      provider_name: selectTag[0] === '所有' ? null : selectTag[0],
    });
    setList(res?.data?.list || []);
    setCount(res?.data?.count || 0);
    setLoading(false);
  }, [filterValue, group_id, page_index, page_size, selectTag]);

  const onSuccess = useCallback(
    (code: number) => {
      if (code === 0) {
        message.success('操作成功');
        getList();
      }
    },
    [getList],
  );

  const doDel = useCallback(
    async (ids: any[]) => {
      if (!ids.length) return;
      const params = { provider_ids: ids, group_id };
      const res = await deleteChannelProvider(params);
      onSuccess(res?.code);
    },
    [group_id, onSuccess],
  );

  const doEdit = useCallback(
    async (row: any) => {
      const res = await editChannelProvider({ ...row });
      onSuccess(res?.code);
    },
    [onSuccess],
  );

  const columns: ColumnsType<any> = useMemo(
    () => [
      {
        title: '通道',
        dataIndex: 'provider_name',
        key: 'provider_name',
        align: 'left',
      },
      {
        title: '单发普通',
        align: 'center',
        render: (row: any) => {
          return (
            <InputNumber
              min={0}
              defaultValue={row.single}
              onChange={(value) => {
                if (value === null) {
                  row.single = 0;
                  return;
                }
                row.single = value;
              }}
            />
          );
        },
      },
      {
        title: '单发营销',
        render: (row: any) => {
          return (
            <InputNumber
              min={0}
              defaultValue={row.single_b}
              onChange={(value) => {
                if (value === null) {
                  row.single_b = 0;
                  return;
                }
                row.single_b = value;
              }}
            />
          );
        },
      },
      {
        title: '群发2普通',
        align: 'center',
        render: (row: any) => {
          return (
            <InputNumber
              min={0}
              defaultValue={row.multi2}
              onChange={(value) => {
                if (value === null) {
                  row.multi2 = 0;
                  return;
                }
                row.multi2 = value;
              }}
            />
          );
        },
      },
      {
        title: '群发2营销',
        align: 'center',
        render: (row: any) => {
          return (
            <InputNumber
              min={0}
              defaultValue={row.multi2_b}
              onChange={(value) => {
                if (value === null) {
                  row.multi2_b = 0;
                  return;
                }
                row.multi2_b = value;
              }}
            />
          );
        },
      },
      {
        title: '优先级',
        align: 'center',
        filters: priorityStatus,
        filterMultiple: false,
        filteredValue: filterValue,
        render: (row: any) => {
          return (
            <Select defaultValue={row.priority} onChange={(value) => (row.priority = value)}>
              {(priorityStatus ?? []).map((item) => {
                return (
                  <Select.Option value={item.value} key={item.value}>
                    {item.text}
                  </Select.Option>
                );
              })}
            </Select>
          );
        },
      },
      {
        title: '操作',
        render: (row: any) => {
          return (
            <Space>
              <a
                onClick={() => {
                  const arr = [];
                  arr.push(row.provider_id);
                  doDel(arr);
                }}
              >
                删除
              </a>
              <a
                onClick={() => {
                  doEdit(row);
                }}
              >
                编辑
              </a>
            </Space>
          );
        },
        align: 'center',
      },
    ],
    [doDel, doEdit, filterValue],
  );

  const providerOptions = [
    {
      key: '所有',
      value: '所有',
    },
    {
      key: '全国三网',
      value: '全国三网',
    },
    {
      key: '全国移动',
      value: '全国移动',
    },
    {
      key: '全国联通',
      value: '全国联通',
    },
    {
      key: '全国电信',
      value: '全国电信',
    },
  ];
  const rowSelection = {
    selectedRowKeys: checkProvider,
    onChange: (selectedRowKeys: React.Key[]) => {
      setCheckProvider(selectedRowKeys);
    },
  };

  const getProviderInfos = useCallback(async () => {
    const res = await getCanalProviders();
    setProviderList(res?.data || []);
  }, []);

  const getGroupDesc = useCallback(async () => {
    if (!group_id) return;
    const res = await getChannelGroupInfo({ group_id });
    setGroupDesc(res?.data?.desc);
    setExtrendLen(res?.data?.pre_extend_len);
  }, [group_id]);

  useEffect(() => {
    if (!group_id) return;
    getProviderInfos();
    getGroupDesc();
  }, [getGroupDesc, getProviderInfos, group_id]);

  useEffect(() => {
    getList();
  }, [getList]);

  if (!group_id) {
    const url = 'group-channel-customer';
    return <Navigate to={url} />;
  }

  async function editGroupDesc() {
    if (groupDesc === '') return;
    const params = { group_id, desc: groupDesc };
    const res = await editChannelGroup({ ...params, pre_extend_len });
    if (res?.code === 0) {
      message.success('编辑通道组成功');
    }
    getGroupDesc();
  }
  async function add() {
    if (providerId === undefined) {
      return;
    }
    const params = { group_id, provider_id: providerId };
    const res = await addChannelProvider(params);
    onSuccess(res.code);
  }

  function onSelectChange(value: any) {
    setSelectTag(value.length > 1 ? value.slice(value.length - 1) : value);
    setPageIndex(1);
  }

  function handleTableChange(
    pagination: any,
    filters: any,
    sorter: any,
    action: { action: string },
  ) {
    if (action.action === 'filter') {
      setFilterValue(filters[5]);
    }
  }

  return (
    <>
      <PageContainer>
        <div>
          <span style={{ marginRight: 15 }}>
            通道组id： <Input style={{ width: 250 }} defaultValue={group_id} disabled />
          </span>
          <span style={{ marginRight: 15 }}>
            通道组描述：{' '}
            <Input
              value={groupDesc}
              placeholder="请添加通道组描述信息"
              onChange={(e) => {
                setGroupDesc(e.target.value);
              }}
              style={{ width: 300 }}
            />
          </span>
          <span style={{ marginRight: 15 }}>
            扩展码长度：{' '}
            <InputNumber
              min={1}
              value={pre_extend_len}
              placeholder="请输入扩展码长度"
              onChange={(value) => {
                if (value === null) {
                  setExtrendLen(1);
                  return;
                }
                setExtrendLen(value);
              }}
              style={{ width: 300 }}
            />
          </span>
          <Button
            type="primary"
            onClick={() => {
              editGroupDesc();
            }}
          >
            编辑
          </Button>
        </div>
        <div style={{ marginTop: 10 }}>
          <span style={{ marginRight: 15 }}>
            通道列表：{' '}
            <Select
              placeholder="请选择通道商"
              style={{ width: 350 }}
              filterOption={(input, option) =>
                (option!.children as unknown as string).toLowerCase().includes(input.toLowerCase())
              }
              allowClear
              showSearch
              onChange={(val) => {
                setProviderId(val);
              }}
            >
              {(providerList ?? []).map((prov: any) => {
                return (
                  <Select.Option key={prov.provider_id} value={prov.provider_id}>
                    {prov.provider_name}
                  </Select.Option>
                );
              })}
            </Select>
          </span>
          <Button
            type="primary"
            onClick={() => {
              add();
            }}
          >
            添加
          </Button>
        </div>
        <div style={{ marginTop: 35 }}>
          <Space>
            <Select style={{ width: 250 }} value={selectTag} mode="tags" onChange={onSelectChange}>
              {(providerOptions ?? []).map((prov: any) => {
                return (
                  <Select.Option key={prov.key} value={prov.key}>
                    {prov.value}
                  </Select.Option>
                );
              })}
            </Select>
          </Space>
          <Button
            type="primary"
            style={{ float: 'right' }}
            onClick={() => {
              doDel(checkProvider);
            }}
          >
            批量删除
          </Button>
        </div>

        <Table
          rowKey="provider_id"
          rowSelection={{
            type: 'checkbox',
            ...rowSelection,
          }}
          dataSource={list}
          columns={columns}
          loading={isLoading}
          pagination={{
            current: page_index,
            total: count,
            showSizeChanger: true,
            onShowSizeChange: (current, page_size) => setPageSize(page_size),
            onChange: (page) => {
              setPageIndex(page);
            },
          }}
          style={{ marginTop: 20 }}
          onChange={handleTableChange}
        />
      </PageContainer>
    </>
  );
};

export default ChannelDetail;
