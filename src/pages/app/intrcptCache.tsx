import React, { useState, useEffect } from 'react';
import { Form, Select } from 'antd';
import { getCacheList, editCacheList, addCacheList, delCacheList } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const IntrcptCache = () => {
  const [initVals] = useState<{
    start_hours: number;
    end_hours: number;
    attr_req_hold: number;
    attr_send_hold: number;
  }>({
    start_hours: 0,
    end_hours: 0,
    attr_req_hold: 0,
    attr_send_hold: 0,
  });
  const [timeRange, setTimeRange] = useState<{ label: number; value: number }[]>([]);

  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'app_name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: '开始缓存时间(几点)	',
      dataIndex: 'start_hours',
      key: 'start_hours',
      align: 'center',
    },
    {
      title: '结束缓存时间(几点)',
      dataIndex: 'end_hours',
      key: 'end_hours',
      align: 'center',
    },
    {
      title: '拦截缓存短信量',
      dataIndex: 'attr_req_hold',
      key: 'attr_req_hold',
      align: 'center',
    },
    {
      title: '缓存短信下发量',
      dataIndex: 'attr_send_hold',
      key: 'attr_send_hold',
      align: 'center',
    },
  ];

  useEffect(() => {
    const dateArr = [];
    for (let i = 0; i < 24; i++) {
      dateArr.push({
        label: i,
        value: i,
      });
      setTimeRange(dateArr);
    }
  }, []);

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: false,
      name: 'name',
      label: 'appname',
      placeholder: 'appname',
      isRequired: false,
      disabled: true,
    },
    {
      showOnAdd: true,
      label: '营销短信从',
      isRequired: true,
      disabled: false,
      render: () => {
        return (
          <>
            <Form.Item
              name="start_hours"
              noStyle
              style={{ display: 'inline-block' }}
              rules={[{ required: true }]}
            >
              <Select
                placeholder="start_hours"
                options={timeRange}
                style={{ width: 60, margin: '0 6px' }}
              />
            </Form.Item>
            <span style={{ display: 'inline-flex', height: 32, alignItems: 'center' }}>点到</span>
            <Form.Item
              noStyle
              name="end_hours"
              style={{ display: 'inline-block' }}
              rules={[{ required: true }]}
            >
              <Select
                placeholder="end_hours"
                options={timeRange}
                style={{ width: 60, margin: '0 6px' }}
              />
            </Form.Item>
            <span style={{ display: 'inline-flex', height: 32, alignItems: 'center' }}>
              拦截缓存（第二天再发）
            </span>
          </>
        );
      },
    },
    {
      showOnAdd: true,
      name: 'attr_req_hold',
      label: '拦截缓存短信量monitor属性',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'attr_send_hold',
      label: '缓存短信下发量monitor属性',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
  ];

  async function doEdit(vals: any) {
    return await editCacheList({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addCacheList({ ...vals });
  }
  async function doDel(vals: any) {
    return await delCacheList({ appid: vals.appid });
  }
  async function getList(vals?: any) {
    return await getCacheList({ ...vals });
  }

  return (
    <PatternTable
      rowKey="appid"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      columns={columns}
      searchKeys={[{ label: '', name: 'appid' }]}
      operateForm={operateForm}
      operType={1}
      initialValues={{ ...initVals }}
    />
  );
};

export default IntrcptCache;
