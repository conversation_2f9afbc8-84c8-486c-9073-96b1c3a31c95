import React from 'react';
import { getLargeLog, addLargeLog, delLargeLog } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const LargeLog = () => {
  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: false,
      isRequired: true,
    },
  ];

  async function doAdd(vals: any) {
    return await addLargeLog({ ...vals });
  }
  async function doDel(vals: any) {
    return await delLargeLog({ appid: vals.appid });
  }
  async function getList(vals?: any) {
    return await getLargeLog({ ...vals });
  }

  return (
    <PatternTable
      rowKey="appid"
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[{ label: '', name: 'appid' }]}
      operateForm={operateForm}
      operType={2}
    />
  );
};

export default LargeLog;
