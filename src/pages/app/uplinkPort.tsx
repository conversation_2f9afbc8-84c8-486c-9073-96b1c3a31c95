import React, { useState } from 'react';
import { getPortInfo, updatePortInfo, insertPortInfo, deletePortInfo } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const UplinkPort = () => {
  const [providers] = useState<any>([
    { value: '新联' },
    { value: 'Mig' },
    { value: '未来无线' },
    { value: '云之树' },
    { value: '瑞德魔方' },
    { value: '线上线下' },
    { value: '挖金客' },
    { value: '高信' },
    { value: '创远' },
    { value: '腾讯云' },
  ]);

  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'app_name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: '供应商',
      dataIndex: 'provider',
      key: 'provider',
      align: 'center',
    },
    {
      title: 'port',
      dataIndex: 'port',
      key: 'port',
      align: 'center',
    },
    {
      title: 'sign',
      dataIndex: 'sign',
      key: 'sign',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'port',
      label: 'port',
      isRequired: true,
      disabled: false,
    },
    {
      showOnAdd: true,
      name: 'sign',
      label: 'sign',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'provider',
      label: 'provider',
      disabled: false,
      isRequired: true,
      options: providers,
      renderType: 'select',
    },
  ];

  async function doEdit(vals: any) {
    return await updatePortInfo({ ...vals });
  }
  async function doAdd(vals: any) {
    return await insertPortInfo({ ...vals });
  }

  async function doDel(vals: any) {
    return await deletePortInfo({ id: vals.id });
  }
  async function getList(vals?: any) {
    return await getPortInfo({ ...vals });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        {
          label: 'appid',
          name: 'appid',
        },
        {
          label: 'port',
          name: 'port',
        },
      ]}
      operateForm={operateForm}
      operType={1}
    />
  );
};

export default UplinkPort;
