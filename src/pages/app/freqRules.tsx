import React from 'react';
import { getFrqInfo, updateFrqInfo, insertFrqInfo } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const FreqRules = () => {
  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: 'count',
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
    {
      title: 'max',
      dataIndex: 'max',
      key: 'max',
      align: 'center',
    },
    {
      title: 'interval',
      dataIndex: 'interval',
      key: 'interval',
      align: 'center',
    },
    {
      title: 'remark',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'count',
      label: 'count',
      isRequired: true,
      disabled: false,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'max',
      label: 'max',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'interval',
      label: 'interval',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'remark',
      label: '备注',
      disabled: false,
      isRequired: true,
    },
  ];

  async function doEdit(vals: any) {
    const res = await updateFrqInfo({ ...vals });
    return res;
  }
  async function doAdd(vals: any) {
    const res = await insertFrqInfo({ ...vals });
    return res;
  }
  async function getList(vals?: any) {
    return await getFrqInfo({ ...vals });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'id',
        },
      ]}
      operateForm={operateForm}
      operType={0}
    />
  );
};

export default FreqRules;
