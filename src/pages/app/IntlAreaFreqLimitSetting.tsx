import React, { useRef, useState } from 'react';
import { Button, Form, message, Input, Row, Col, InputNumber } from 'antd';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { PageContainer } from '@ant-design/pro-layout';
import { useSetState } from 'react-use';
import { SetIntlFreqLimitDialog } from './component/SetIntlFreqLimitDialog';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { getIntlFreqLimit, setIntlFreqLimit } from '@/services/intlFreqLimit';
import _ from 'lodash';
import { history } from 'umi';

interface ItemType {
  isocode: string;
  daily_max_limit: number;
}

const IntlAreaFreqLimitSetting: React.FC = () => {
  const [form] = Form.useForm();
  const ref = useRef<ActionType>();
  const sdkappid = (history.location?.state as { sdkappid?: number })?.sdkappid;
  const [searchKeys, setSearchKeys] = useSetState<{
    sdkappid?: number;
  }>({ sdkappid });
  const setDialogRef = useDialogRef();
  const [list, setList] = useState<ItemType[]>([]);

  const request = async () => {
    const res = await getIntlFreqLimit({ sdkappid: searchKeys.sdkappid });
    setList((res.data ?? []).map((v: ItemType) => _.pick(v, ['isocode', 'daily_max_limit'])));
    return {
      data: res.data ?? [],
      success: true,
    };
  };

  async function onFormSub(vals: any) {
    if (!vals.sdkappid) return;
    setSearchKeys({ ...vals });
    ref?.current?.reload();
  }

  async function editLimitFreq(row: any) {
    const { isocode, daily_max_limit } = row;
    const res = await setIntlFreqLimit({
      sdkappid: searchKeys.sdkappid,
      country_freq_rule: [
        {
          isocode,
          daily_max_limit: daily_max_limit === null ? -1 : daily_max_limit,
          type: 'edit',
        },
      ],
    });
    if (res?.code === 0) {
      message.success('编辑成功');
      ref?.current?.reload();
    }
  }

  return (
    <PageContainer>
      <Row className="query-form" justify="space-between">
        <Col>
          <Form
            form={form}
            layout="inline"
            labelWrap={true}
            labelAlign="right"
            onFinish={(vals) => {
              onFormSub(vals);
            }}
            initialValues={{ sdkappid }}
          >
            <Form.Item
              name="sdkappid"
              label="sdkappid"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <Input style={{ width: 180 }} placeholder="sdkappid" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
            </Form.Item>
          </Form>
        </Col>
        <Col>
          <Button
            type="primary"
            disabled={!searchKeys.sdkappid}
            onClick={() => {
              setDialogRef.current.open({
                list,
                sdkappid: searchKeys.sdkappid,
              });
            }}
          >
            设置
          </Button>
        </Col>
      </Row>
      <ProTable
        actionRef={ref}
        rowKey="isocode"
        style={{ margin: '20px -24px 0 -24px' }}
        options={false}
        search={false}
        columns={[
          {
            title: '国家/地区名',
            key: 'country_name',
            dataIndex: 'country_name',
            align: 'center',
            editable: false,
          },
          {
            title: '国家/地区码',
            key: 'isocode',
            dataIndex: 'isocode',
            align: 'center',
            editable: false,
          },
          {
            title: '日请求量预警值（条）',
            key: 'daily_notice_limit',
            align: 'center',
            editable: false,
            dataIndex: 'daily_notice_limit',
            render: (daily_notice_limit) =>
              daily_notice_limit === -1 ? '无限制' : daily_notice_limit,
          },
          {
            title: '日请求量限额值（条）',
            key: 'daily_max_limit',
            align: 'center',
            renderFormItem: () => <InputNumber min={-1} />,
            dataIndex: 'daily_max_limit',
            render: (_, row: ItemType) => {
              return row.daily_max_limit === -1 ? '无限制' : row.daily_max_limit;
            },
          },
          {
            title: '操作',
            valueType: 'option',
            align: 'center',
            render: (text, record: ItemType, _, action) => [
              <a
                key="editable"
                onClick={() => {
                  action?.startEditable?.(record.isocode);
                }}
              >
                设置
              </a>,
            ],
          },
        ]}
        request={request}
        editable={{
          type: 'single',
          actionRender: (row, config, defaultDom) => [defaultDom.save, defaultDom.cancel],
          onSave: async (_, data) => {
            await editLimitFreq(data);
          },
        }}
        pagination={{ pageSize: 10 }}
      />
      <SetIntlFreqLimitDialog
        dialogRef={setDialogRef}
        onSuccess={() => {
          ref.current?.reload();
        }}
      />
    </PageContainer>
  );
};
export default IntlAreaFreqLimitSetting;
