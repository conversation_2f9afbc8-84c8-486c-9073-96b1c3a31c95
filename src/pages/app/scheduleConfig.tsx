import React, { useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Input, Form, Button, Transfer, Select, InputNumber, message } from 'antd';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import _ from 'lodash';
import { useSetState } from 'react-use';
import { getGloablProviders } from '@/services/api';
import { priorityStatus } from './batchSchedule';
import { history } from 'umi';
import { editBatchConfig } from '@/services/scheduleConfig';

const ScheduleConfig = () => {
  const appids = (history.location?.state as { appids?: string[] })?.appids ?? [];
  const [form] = Form.useForm();
  const [configKeys, setConfigKeys] = useSetState<{ targetKeys: string[]; sdkappid: string[] }>({
    sdkappid: appids,
    targetKeys: [],
  });
  const { value: state } = useAsyncRetryFunc(async () => {
    const res = await getGloablProviders();
    return res || {};
  }, []);

  const list = useMemo(() => {
    return (state?.data ?? []).map((v: { provider_id: number }) => ({
      ...v,
      key: `${v.provider_id}`,
    }));
  }, [state]);

  const _handlerSubmit = async (formValue: any) => {
    if (!configKeys.sdkappid.length || !configKeys.targetKeys.length) {
      return message.error('sdkappid或已选通道不能为空');
    }
    const res = await editBatchConfig({
      ...formValue,
      appids: configKeys.sdkappid,
      provider_ids: configKeys.targetKeys,
    });
    if (res.code === 0) {
      message.success('配置成功');
    }
  };

  const onChange = (nextTargetKeys: string[]) => {
    setConfigKeys({ targetKeys: nextTargetKeys });
  };

  const filterOption = (inputValue: string, option: { provider_name: string }) => {
    return option.provider_name.indexOf(inputValue) > -1;
  };

  console.log('appids', appids);
  return (
    <PageContainer>
      <Button
        htmlType="button"
        style={{ marginLeft: 10, float: 'right' }}
        onClick={() => {
          history.push('/app/batchSchedule', { appids: configKeys.sdkappid });
        }}
      >
        返回查询
      </Button>
      <Form
        layout="inline"
        labelAlign="right"
        style={{ marginBottom: 15 }}
        initialValues={{ sdkappid: appids.join('\n') }}
      >
        <Form.Item style={{ marginBottom: 5 }} label="sdkappid" required name="sdkappid">
          <Input.TextArea
            placeholder="换行输入多个"
            rows={6}
            onChange={(e) =>
              setConfigKeys({ sdkappid: _.uniq(_.compact((e.target.value ?? '').split('\n'))) })
            }
          />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} label="providers">
          <Input.TextArea
            cols={26}
            placeholder="换行输入搜索多个provider_id"
            rows={6}
            onChange={(e) => {
              const value = _.uniq(_.compact((e.target.value ?? '').split('\n')));
              const targetKeys = _.filter(value, (v) => {
                return !!_.find(list, (item) => item.key === v);
              });
              setConfigKeys({ targetKeys });
            }}
          />
        </Form.Item>
      </Form>
      <Transfer
        filterOption={filterOption}
        showSearch
        dataSource={list}
        titles={[
          '全部通道',
          <>
            <span style={{ color: '#ff4d4f', marginRight: 4, fontFamily: 'SimSun, sans-serif' }}>
              *
            </span>
            已选通道
          </>,
        ]}
        targetKeys={configKeys.targetKeys}
        onChange={onChange}
        listStyle={{ height: 420, width: 360 }}
        render={(item) => item.provider_name}
        pagination
      />
      <Form
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => _handlerSubmit(vals)}
        initialValues={{ single: 1, single_b: 1, multi2: 1, multi2_b: 1, hot_backup: 0 }}
        style={{ overflow: 'auto', marginTop: 15 }}
      >
        <Form.Item
          style={{ marginBottom: 5 }}
          name="single"
          label="单发普通"
          rules={[{ required: true }]}
        >
          <InputNumber />
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 5 }}
          name="single_b"
          label="单发营销"
          rules={[{ required: true }]}
        >
          <InputNumber />
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 5 }}
          name="multi2"
          label="群发2普通"
          rules={[{ required: true }]}
        >
          <InputNumber />
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 5 }}
          name="multi2_b"
          label="群发2营销"
          rules={[{ required: true }]}
        >
          <InputNumber />
        </Form.Item>
        <Form.Item
          style={{ marginBottom: 5 }}
          name="hot_backup"
          label="优先级"
          rules={[{ required: true }]}
        >
          <Select options={priorityStatus} style={{ width: 120 }} />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary">
            set
          </Button>
        </Form.Item>
      </Form>
    </PageContainer>
  );
};
export default ScheduleConfig;
