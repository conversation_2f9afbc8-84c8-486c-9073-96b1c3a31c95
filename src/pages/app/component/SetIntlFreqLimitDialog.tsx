import React, { useEffect, useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, InputNumber, Select, message, Button } from 'antd';
import _ from 'lodash';
import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { setIntlFreqLimit } from '@/services/intlFreqLimit';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

type ListType = {
  isocode: string[];
  daily_max_limit: number | null;
}[];

function compareCode(v1: any, v2: any) {
  return v1.isocode === v2.isocode;
}

export const SetIntlFreqLimitDialog = (props: DialogProps) => {
  const { regionOptions } = useFetchCountryInfo();
  const { dialogRef, onSuccess } = props;
  const [isConfirmLoading, setConfirmLoading] = useState(false);
  const [visible, setShowState, initVals] = useDialog<{
    list: {
      isocode: string;
      daily_max_limit: number;
    }[];
    sdkappid: number;
  }>(dialogRef);
  const { list = [], sdkappid } = initVals;
  const [regionUpSendEdit, setRegionUpSendEdit] = useState<ListType>([]);
  useEffect(() => {
    visible &&
      setRegionUpSendEdit(
        list.map((v) => ({
          isocode: [v.isocode],
          daily_max_limit: v.daily_max_limit,
        })),
      );
  }, [list, visible]);

  async function onSubmit() {
    const filtered = regionUpSendEdit.filter((x) => x.isocode.length !== 0);
    const flatternRules: {
      isocode: string;
      daily_max_limit: number;
    }[] = [];
    filtered.forEach((item) => {
      item.isocode?.forEach((info) => {
        flatternRules.push({
          isocode: info,
          daily_max_limit: item.daily_max_limit === null ? -1 : item.daily_max_limit,
        });
      });
    });

    // 交集，需要编辑
    const editRules = _.intersectionWith(flatternRules, list, compareCode).map((v) => ({
      ...v,
      type: 'edit',
    }));
    // 需要删除
    const deleteRules = _.differenceWith(list, editRules, compareCode).map((v) => ({
      ...v,
      type: 'unlimited',
    }));
    // 需要增加
    const insertRules = _.differenceWith(flatternRules, list, compareCode).map((v) => ({
      ...v,
      type: 'insert',
    }));
    const params = {
      sdkappid,
      country_freq_rule: _.union(editRules, deleteRules, insertRules),
    };
    if (!params.country_freq_rule.length) {
      return message.error('数据为空');
    }
    setConfirmLoading(true);
    try {
      const res = await setIntlFreqLimit(params);
      setShowState(false);
      if (res.code === 0) {
        message.success('设置成功');
        onSuccess();
      }
    } catch (error) {
    } finally {
      setConfirmLoading(false);
    }
  }

  function getCodeOpts(checkedCode: string[]) {
    if (!regionUpSendEdit?.length) return regionOptions;
    const choosedCountry = _.reduce(
      regionUpSendEdit,
      (result: string[], value) => {
        if (value?.isocode?.length) {
          result = result.concat(value.isocode);
        }
        return result;
      },
      [],
    );
    return _.map(regionOptions, (item) => {
      return {
        ...item,
        disabled: choosedCountry.includes(item.value) && !checkedCode.includes(item.value),
      };
    });
  }

  return (
    <>
      <Modal
        open={visible}
        title={
          <div>
            设置
            <p style={{ color: 'red', display: 'inline-block' }}>（ -1 为无限制 ）</p>
          </div>
        }
        destroyOnClose
        width={600}
        confirmLoading={isConfirmLoading}
        onOk={onSubmit}
        onCancel={() => {
          setShowState(false);
        }}
      >
        {!regionUpSendEdit.length && (
          <Button
            type="primary"
            shape="round"
            onClick={() => {
              setRegionUpSendEdit([
                {
                  isocode: [],
                  daily_max_limit: -1,
                },
              ]);
            }}
          >
            添加
          </Button>
        )}
        {regionUpSendEdit.map((item, index) => {
          return (
            // eslint-disable-next-line react/jsx-key
            <div style={{ marginBottom: 5 }}>
              国家/地区&nbsp;
              <Select
                style={{ width: '180px' }}
                mode="multiple"
                options={getCodeOpts(item.isocode || [])}
                value={item.isocode}
                onChange={(value) => {
                  item.isocode = value;
                  setRegionUpSendEdit([...regionUpSendEdit]);
                }}
              />
              &nbsp;超过&nbsp;
              <InputNumber
                value={item.daily_max_limit}
                style={{ width: '100px' }}
                min={-1}
                onChange={(value) => {
                  item.daily_max_limit = value;
                  setRegionUpSendEdit([...regionUpSendEdit]);
                }}
              />
              &nbsp; 条暂停发送
              <PlusCircleOutlined
                onClick={() => {
                  setRegionUpSendEdit([
                    ...regionUpSendEdit,
                    {
                      isocode: [],
                      daily_max_limit: -1,
                    },
                  ]);
                }}
                style={{ marginLeft: '10px' }}
              />
              {index !== 0 && (
                <MinusCircleOutlined
                  style={{ marginLeft: '15px' }}
                  onClick={() => {
                    setRegionUpSendEdit((prev) => {
                      const newList = _.cloneDeep(prev);
                      newList.splice(index, 1);
                      return newList;
                    });
                  }}
                />
              )}
            </div>
          );
        })}
      </Modal>
    </>
  );
};
