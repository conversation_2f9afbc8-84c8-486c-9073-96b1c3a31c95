import React from 'react';
import { getDisableMobile, addDisableMobile, delDisableMobile } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const DisableMobile = () => {
  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '应用名称',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: '客户名称',
      dataIndex: 'qapp_name',
      key: 'qapp_name',
      align: 'center',
    },
    {
      title: 'created_time',
      dataIndex: 'created_time',
      key: 'created_time',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: false,
      isRequired: true,
    },
  ];

  async function doAdd(vals: any) {
    return await addDisableMobile({ ...vals });
  }
  async function doDel(vals: any) {
    return await delDisableMobile({ appid: vals.appid });
  }
  async function getList(vals?: any) {
    return await getDisableMobile({ ...vals });
  }

  function renderTitle() {
    return (
      <>
        <strong>录入短信应用sdkappid</strong>
        <h4 style={{ color: 'red' }}>添加的应用不会去查询归属地信息（因而也不会启用分省调度）</h4>
      </>
    );
  }

  return (
    <PatternTable
      rowKey="appid"
      upRender={renderTitle}
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[{ label: '', name: 'appid' }]}
      operateForm={operateForm}
      operType={2}
    />
  );
};

export default DisableMobile;
