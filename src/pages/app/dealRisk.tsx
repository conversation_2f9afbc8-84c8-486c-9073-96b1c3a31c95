import React, { useState } from 'react';
import { getRiskyList, editRiskyInfo, addRiskyInfo, delRiskyInfo } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const DealRisk = () => {
  const [initVals] = useState<{
    risk_level3_deal_percent: number;
    risk_level4_deal_percent: number;
    attr_check_risky_total: number;
    attr_deal_risky_total: number;
  }>({
    risk_level3_deal_percent: 0,
    risk_level4_deal_percent: 0,
    attr_check_risky_total: 0,
    attr_deal_risky_total: 0,
  });

  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: '客户名称',
      dataIndex: 'customer',
      key: 'customer',
      align: 'center',
    },
    {
      title: '营销检查放量(%)',
      dataIndex: 'risk_mobile_deal_percent',
      key: 'risk_mobile_deal_percent',
      align: 'center',
    },
    {
      title: '恶意级别-3灰度(%)',
      dataIndex: 'risk_level3_deal_percent',
      key: 'risk_level3_deal_percent',
      align: 'center',
    },
    {
      title: '恶意级别-4灰度(%)',
      dataIndex: 'risk_level4_deal_percent',
      key: 'risk_level4_deal_percent',
      align: 'center',
    },
    {
      title: '恶检查恶意量上报',
      dataIndex: 'attr_check_risky_total',
      key: 'attr_check_risky_total',
      align: 'center',
    },
    {
      title: '处理恶意量上报',
      dataIndex: 'attr_deal_risky_total',
      key: 'attr_deal_risky_total',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'risk_mobile_deal_percent',
      label: '营销检查放量(%)',
      isRequired: true,
      disabled: false,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'risk_level3_deal_percent',
      label: '恶意级别-3灰度(%)',
      isRequired: true,
      disabled: false,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'risk_level4_deal_percent',
      label: '恶意级别-4灰度(%)',
      isRequired: true,
      disabled: false,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'attr_check_risky_total',
      label: '检查恶意量上报',
      isRequired: true,
      disabled: false,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'attr_deal_risky_total',
      label: '处理恶意量上报',
      isRequired: true,
      disabled: false,
      renderType: 'number',
    },
  ];

  async function doEdit(vals: any) {
    return await editRiskyInfo({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addRiskyInfo({ ...vals });
  }
  async function doDel(vals: any) {
    return await delRiskyInfo({ appid: vals.appid });
  }
  async function getList(vals?: any) {
    return await getRiskyList({ ...vals });
  }

  return (
    <PatternTable
      rowKey="appid"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      columns={columns}
      searchKeys={[{ label: '', name: 'appid' }]}
      operateForm={operateForm}
      initialValues={{ ...initVals }}
      operType={1}
    />
  );
};

export default DealRisk;
