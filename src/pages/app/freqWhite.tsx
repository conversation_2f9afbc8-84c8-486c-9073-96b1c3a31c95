import React from 'react';
import { getFrqWhite, updateFrqWhite, insertFrqWhite } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';
import _ from 'lodash';

const FreqWhite = () => {
  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'app_name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: 'nationcode',
      dataIndex: 'nationcode',
      key: 'nationcode',
      align: 'center',
    },
    {
      title: 'mobile',
      dataIndex: 'mobile',
      key: 'mobile',
      align: 'mobile',
    },
  ];

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'appid',
      label: 'sdkappid',
      isRequired: true,
      disabled: true,
      renderType: 'textArea',
      placeholder: '换行输入多个',
    },
    {
      showOnAdd: true,
      name: 'nationcode',
      label: '国家码',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'mobile',
      label: '手机号',
      disabled: false,
      isRequired: true,
      renderType: 'textArea',
      placeholder: '换行输入多个',
    },
  ];

  async function doEdit(vals: any) {
    vals.sdkappid = vals.appid;
    const res = await updateFrqWhite({ ...vals });
    return res;
  }
  async function doAdd(vals: any) {
    const params = {
      sdkappids: _.compact(vals.appid.split('\n')),
      mobiles: _.compact(vals.mobile.split('\n')),
      nationcode: vals.nationcode,
    };

    const res = await insertFrqWhite({ ...params });
    return res;
  }
  async function getList(vals?: any) {
    if (!vals?.sdkappid) return;
    return await getFrqWhite({ ...vals });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'sdkappid',
        },
      ]}
      operateForm={operateForm}
      operType={0}
    />
  );
};

export default FreqWhite;
