import React from 'react';
import { getCmppReturnList, editCmppReturn, addCmppReturn, delCmppReturn } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const CmppReturnCode = () => {
  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: '默认签名',
      dataIndex: 'default_sign',
      key: 'default_sign',
      align: 'center',
    },
    {
      title: '移动码号',
      dataIndex: 'cmcc',
      key: 'cmcc',
      align: 'center',
    },
    {
      title: '联通码号',
      dataIndex: 'unicom',
      key: 'unicom',
      align: 'center',
    },
    {
      title: '电信码号',
      dataIndex: 'telecom',
      key: 'telecom',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'cmcc',
      label: '移动码号',
      isRequired: true,
      disabled: false,
    },
    {
      showOnAdd: true,
      name: 'unicom',
      label: '联通码号',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'telecom',
      label: '电信码号',
      disabled: false,
      isRequired: true,
    },
  ];

  async function doEdit(vals: any) {
    return await editCmppReturn({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addCmppReturn({ ...vals });
  }

  async function doDel(vals: any) {
    return await delCmppReturn({ appid: vals.appid });
  }
  async function getList(vals?: any) {
    return await getCmppReturnList({ ...vals });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      columns={columns}
      searchRender={() => {
        return (
          <span>查询cmpp给客户的码号(与供应商提供的不一定一致，因为有些加了签名区分sdkappid)</span>
        );
      }}
      modalTipRender={() => {
        return (
          <div style={{ color: 'red', margin: '10px 0' }}>
            <strong>
              录入码号时切记不要将签名区分sdkappid的扩展位加进去（给客户时需要加进去），且开启此功能的sdkappid只允许有一个签名
            </strong>
          </div>
        );
      }}
      searchKeys={[
        {
          label: '',
          name: 'appid',
          placeholder: 'appid',
        },
      ]}
      operateForm={operateForm}
      operType={1}
    />
  );
};

export default CmppReturnCode;
