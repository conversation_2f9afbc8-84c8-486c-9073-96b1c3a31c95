import React from 'react';
import { Radio } from 'antd';
import { getRateCtl, editRateCtl, addRateCtl } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';
import _ from 'lodash';

const MqRateCtl = () => {
  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'app_name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: '速率控制时间间隔(单位ms)',
      dataIndex: 'interval',
      key: 'interval',
      align: 'center',
    },
    {
      title: '间隔内能发送的数量',
      dataIndex: 'count',
      key: 'count',
      align: 'center',
    },
    {
      title: '是否使用常规短信模块',
      dataIndex: 'normal_sms_module',
      key: 'normal_sms_module',
      align: 'center',
      render: (normal_sms_module: 0 | 1) => (normal_sms_module ? '是' : '否'),
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: false,
      name: 'app_name',
      label: 'app_name',
      disabled: true,
      isRequired: false,
    },
    {
      showOnAdd: true,
      name: 'interval',
      label: '速率控制时间间隔(单位ms)',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'count',
      label: '间隔内能发送的数量',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'normal_sms_module',
      label: '是否使用常规短信模块',
      isRequired: true,
      render: () => (
        <Radio.Group>
          <Radio value={1}>是</Radio>
          <Radio value={0}>否</Radio>
        </Radio.Group>
      ),
    },
  ];

  async function doAdd(vals: any) {
    const _vals = {
      ..._.omit(vals, ['normal_sms_module']),
      module: vals.normal_sms_module,
    };
    return await addRateCtl({ ..._vals });
  }
  async function doEdit(vals: any) {
    const _vals = {
      ..._.omit(vals, ['normal_sms_module']),
      module: vals.normal_sms_module,
    };
    return await editRateCtl({ ..._vals });
  }
  async function getList(vals?: any) {
    return await getRateCtl({ ...vals });
  }

  return (
    <PatternTable
      rowKey="appid"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[{ label: '', name: 'appid' }]}
      operateForm={operateForm}
      operType={0}
    />
  );
};

export default MqRateCtl;
