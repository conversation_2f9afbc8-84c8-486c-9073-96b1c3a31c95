import React from 'react';
import { getRemarkList, editRemark, addRemark, delRemark } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const CustomRemark = () => {
  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'name',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'name',
      label: 'name',
      isRequired: true,
      disabled: false,
    },
  ];

  async function doEdit(vals: any) {
    return await editRemark({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addRemark({ ...vals });
  }
  async function doDel(vals: any) {
    return await delRemark({ appid: vals.appid });
  }
  async function getList(vals?: any) {
    return await getRemarkList({ ...vals });
  }

  return (
    <PatternTable
      rowKey="appid"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        { label: '', name: 'appid' },
        { label: '', name: 'name' },
      ]}
      operateForm={operateForm}
      operType={1}
    />
  );
};

export default CustomRemark;
