import React, { useState, useEffect } from 'react';
import { Select } from 'antd';
import { getWeight, insertWeight, updateWeight } from '@/services/scdAPI';
import { getGloablProviders } from '@/services/api';
import { priorityStatus } from '../sign/const';
import PatternTable from '@/pages/component/PatternLayout';
import { serialize } from '@/utils/serialize';
import { dumpwarning } from '@/services/dumpWarning';

const Weight = () => {
  const [providers, setProviders] = useState<any>([]);

  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'provider',
      dataIndex: 'provider',
      key: 'provider',
      align: 'center',
    },
    {
      title: '单发普通',
      dataIndex: 'single',
      key: 'single',
      align: 'center',
    },
    {
      title: '单发营销',
      dataIndex: 'single_b',
      key: 'single_b',
      align: 'center',
    },
    {
      title: '群发2普通',
      dataIndex: 'multi2',
      key: 'multi2',
      align: 'center',
    },
    {
      title: '群发2营销',
      dataIndex: 'multi2_b',
      key: 'multi2_b',
      align: 'center',
    },
    {
      title: '优先级',
      key: 'hot_backup',
      align: 'center',
      render: (row: any) => {
        return (
          <span>
            {row.hot_backup === 0 ? '0级(低)' : row.hot_backup === 1 ? '1级(中)' : '2级(高)'}
          </span>
        );
      },
    },
  ];

  useEffect(() => {
    getGloablProviders().then((res) => {
      const providersList = (res?.data ?? []).map(
        (item: { provider_id: number; provider_name: string }) => {
          return {
            value: item.provider_id,
            label: item.provider_name,
          };
        },
      );
      setProviders(providersList);
    });
  }, []);

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'provider_id',
      label: 'provider',
      isRequired: true,
      disabled: true,
      renderType: 'select',
      options: providers,
    },
    {
      showOnAdd: false,
      name: 'single',
      label: '单发普通',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: false,
      name: 'single_b',
      label: '单发营销',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: false,
      name: 'multi2',
      label: '群发2普通',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: false,
      name: 'multi2_b',
      label: '群发2营销',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: false,
      name: 'hot_backup',
      label: '优先级',
      disabled: false,
      isRequired: true,
      render: () => {
        return (
          <Select style={{ width: 120 }}>
            {priorityStatus.map((item) => {
              return (
                <Select.Option value={item.value} key={item.value}>
                  {item.text}
                </Select.Option>
              );
            })}
          </Select>
        );
      },
    },
  ];

  async function doEdit(vals: any) {
    const res = await updateWeight({ ...vals });
    return res;
  }
  async function doAdd(vals: any) {
    const res = await insertWeight({ ...vals });
    return res;
  }
  async function getList(vals?: any) {
    return await getWeight({ ...vals });
  }

  async function onExport(vals: any) {
    const str = serialize(vals);
    const a = document.createElement('a');
    a.href = `/apis/sms/weight/export?${str}`;
    a.download = '';
    a.click();
    dumpwarning({ route: '/sms/weight/export', params: { ...vals } });
  }

  return (
    <PatternTable<{
      appid: number;
      provider_id: number;
    }>
      rowKey={(record) => record.appid + record.provider_id}
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'appid',
        },
        {
          label: '',
          name: 'provider_id',
          renderType: 'select',
          options: providers,
          placeholder: 'provider',
        },
      ]}
      operateForm={operateForm}
      operType={0}
      exportFile={onExport}
    />
  );
};

export default Weight;
