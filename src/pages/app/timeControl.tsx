import React, { useState, useEffect } from 'react';
import { Space, message, Button, Select, Card } from 'antd';
import {
  setAllowTime,
  getAllowTime,
  addWhiteList,
  getWhiteList,
  delWhiteList,
} from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const TimeControl = () => {
  const [timeRange, setTimeRange] = useState<{ label: number; value: number }[]>([]);
  const [start_hours, setStartHours] = useState(8);
  const [end_hours, setEndHours] = useState(22);

  useEffect(() => {
    const dateArr = [];
    for (let i = 0; i < 24; i++) {
      dateArr.push({
        label: i,
        value: i,
      });
      setTimeRange(dateArr);
    }
    getTime();
  }, []);

  const columns: any = [
    {
      title: '应用id',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '应用名称',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
  ];

  async function doAdd(vals: any) {
    return await addWhiteList({ ...vals });
  }
  async function doDel(vals: any) {
    return await delWhiteList({ appid: vals.appid });
  }
  async function getList(vals?: any) {
    return await getWhiteList({ ...vals });
  }

  async function getTime() {
    const res = await getAllowTime();
    if (res?.code === 0) {
      setStartHours(res?.data?.start_hours || '');
      setEndHours(res?.data?.end_hours || '');
    }
  }

  async function setTime() {
    const res = await setAllowTime({
      start_hours,
      end_hours,
    });
    res?.code === 0 && message.success('设置成功');
    getTime();
  }

  function renderUp() {
    return (
      <Card
        style={{
          boxShadow: 'inset 0 0 5px rgba(0, 0, 0, 0.2)',
          marginBottom: '15px',
          padding: '6px 0',
        }}
      >
        <Space>
          <span style={{ marginRight: 10 }}>
            营销短信从：
            <Select
              style={{ margin: '0 6px' }}
              options={timeRange}
              value={start_hours}
              onChange={(value) => {
                setStartHours(value);
              }}
            />
            点到
            <Select
              style={{ margin: '0 6px' }}
              options={timeRange}
              value={end_hours}
              onChange={(value) => {
                setEndHours(value);
              }}
            />
            点允许发送
          </span>
          <Button size="small" type="primary" onClick={() => setTime()}>
            设置
          </Button>
        </Space>
      </Card>
    );
  }

  return (
    <PatternTable
      rowKey="appid"
      upRender={renderUp}
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[{ label: '', name: 'appid' }]}
      operateForm={operateForm}
      operType={2}
    />
  );
};

export default TimeControl;
