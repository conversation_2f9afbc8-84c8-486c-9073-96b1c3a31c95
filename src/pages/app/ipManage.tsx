import React, { useRef, useState } from 'react';
import { deleteIpInfo, getIpInfo, insertIpInfo, updateIpInfo } from '@/services/scdAPI';
import PatternTable, { PatternTableRef } from '@/pages/component/PatternLayout';
import _ from 'lodash';
import { Button, message, Modal, Table } from 'antd';
import { BetaSchemaForm } from '@ant-design/pro-components';

const deleteColumns = [
  {
    title: 'appid',
    dataIndex: 'sdkappid_ids',
    key: 'sdkappid_ids',
    align: 'center',
    fieldProps: {
      placeholder: '换行输入多个',
    },
    formItemProps: {
      rules: [{ required: true, message: '请输入appid' }],
    },
    transform: (value: string) => ({
      sdkappid_ids: _.compact(value.split('\n')).map((el: string) => el.trim()),
    }),
    valueType: 'textarea',
  },
  {
    title: 'ip',
    dataIndex: 'ip',
    key: 'ip',
    align: 'center',
    fieldProps: {
      placeholder: '换行输入多个',
    },
    formItemProps: {
      rules: [{ required: true, message: '请输入ip' }],
    },
    transform: (value: string) => ({
      ips: _.compact(value.split('\n')).map((el: string) => el.trim()),
    }),
    valueType: 'textarea',
  },
];

const IpManage = () => {
  const tableRef = useRef<PatternTableRef>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewDataSource, setPreviewDataSource] = useState([]);
  const deleteParams = useRef({});
  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '应用名称',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: 'ip',
      dataIndex: 'ip',
      key: 'ip',
      align: 'center',
    },
  ];
  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
      renderType: 'textArea',
      placeholder: '换行输入多个',
    },
    {
      showOnAdd: true,
      name: 'ip',
      label: 'ip',
      isRequired: true,
      disabled: false,
      renderType: 'textArea',
      placeholder: '换行输入多个',
    },
  ];

  async function doEdit(vals: any) {
    const res = await updateIpInfo({ ...vals });
    return res;
  }
  async function doAdd(vals: any) {
    const params = {
      appids: _.compact(vals.appid.split('\n')),
      ips: _.compact(vals.ip.split('\n').map((el: string) => el.trim())),
    };
    const res = await insertIpInfo({ ...params });
    return res;
  }
  async function getList(vals?: any) {
    return await getIpInfo({ ...vals });
  }

  async function doDelete({ id }: { id: string }) {
    return await deleteIpInfo({ id });
  }
  async function doPreview(vals: any) {
    deleteParams.current = vals;
    message.loading('获取删除列表中...');
    const res = await getIpInfo({ ...vals, page_size: 10000, page_index: 1 });
    message.destroy();
    if (res.data?.list?.length > 0) {
      setPreviewDataSource(res.data.list);
      setPreviewOpen(true);
    } else {
      message.warning('没有匹配到数据');
    }
    return false;
  }
  async function doBatchDelete() {
    const params = { ...deleteParams.current };
    return deleteIpInfo({ ...params })
      .then(() => {
        message.success('删除成功');
        setPreviewOpen(false);
      })
      .catch((err) => {
        message.error(err.message);
      })
      .finally(() => {
        tableRef.current?.reload();
      });
  }
  const BatchDeleteModal = () => {
    return (
      <BetaSchemaForm
        width={500}
        labelCol={{ span: 4 }}
        title="批量删除"
        onFinish={doPreview}
        columns={deleteColumns}
        layout="horizontal"
        layoutType="ModalForm"
        trigger={<Button style={{ marginBottom: 16 }}>批量删除</Button>}
      />
    );
  };
  return (
    <>
      <Modal
        title="删除列表预览"
        open={previewOpen}
        onCancel={() => setPreviewOpen(false)}
        footer={() => (
          <Button type="primary" onClick={doBatchDelete}>
            批量删除
          </Button>
        )}
      >
        <Table size="small" pagination={false} dataSource={previewDataSource} columns={columns} />
      </Modal>

      <PatternTable
        ref={tableRef}
        rowKey="id"
        getFn={getList}
        addFn={doAdd}
        editFn={doEdit}
        delFn={doDelete}
        columns={columns}
        upRender={() => <BatchDeleteModal />}
        searchKeys={[
          {
            label: '',
            name: 'search_key',
            placeholder: 'appid',
          },
        ]}
        operateForm={operateForm}
        operType={1}
      />
    </>
  );
};

export default IpManage;
