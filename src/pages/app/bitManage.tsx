import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import { getCommonBitDefine, addCommonBitDefine, editCommonBitDefine } from '@/services/scdAPI';

const appScopeOptions = [
  { label: 'sdkappid维度', value: 1 },
  { label: 'qcloudappid维度', value: 2 },
  { label: 'sdkappid和qcloudappid维度', value: 3 },
];

const siteScopeOptions = [
  { label: '国内站', value: 1 },
  { label: '国际站', value: 2 },
  { label: '国内站和国际站', value: 3 },
];

const ResourceTypeManage = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>();
  const [type, setType] = useState<'create' | 'edit'>('create');

  function handleEdit(row: any) {
    addFormRef.current?.setFieldsValue({
      ...row,
    });
    setInitialValues({
      ...row,
    });
    setOpen(true);
    setType('edit');
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: 'id',
        dataIndex: 'id',
        key: 'id',
        width: 150,
        hideInSearch: true,
        // 只读
        proFieldProps: {
          mode: 'read',
        },
      },
      {
        title: 'bit位',
        dataIndex: 'bit_index',
        key: 'bit_index',
        formItemProps: {
          rules: [{ required: true }],
        },
      },
      {
        title: 'bit位功能描述',
        dataIndex: 'bit_desc',
        key: 'bit_desc',
        formItemProps: {
          rules: [{ required: true }],
        },
      },
      {
        title: 'bit位使用范围',
        dataIndex: 'app_scope',
        key: 'app_scope',
        formItemProps: {
          rules: [{ required: true }],
        },
        valueType: 'select',
        fieldProps: {
          options: appScopeOptions,
        },
      },
      {
        title: 'bit位站点范围',
        dataIndex: 'site_scope',
        key: 'site_scope',
        formItemProps: {
          rules: [{ required: true }],
        },
        valueType: 'select',
        fieldProps: {
          options: siteScopeOptions,
        },
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleEdit(row)}>
                编辑
              </Button>
            </>
          );
        },
      },
    ];
  }, []);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getCommonBitDefine({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
    });
    return {
      data,
      success: true,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'create'
          ? await addCommonBitDefine({
              ...vals,
            })
          : await editCommonBitDefine({
              ...vals,
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const keys =
      type === 'create'
        ? ['bit_index', 'bit_desc', 'app_scope', 'site_scope']
        : ['bit_index', 'bit_desc', 'app_scope', 'site_scope', 'id'];
    return columns.filter((el) => keys.includes(el.key));
  }, [columns, type]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      bit_index: '',
      bit_desc: '',
      app_scope: '',
      site_scope: '',
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
    <PageContainer>
      <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 6 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={500}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setOpen(true);
        }}
        style={{ marginBottom: 20 }}
      >
        新增
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={false}
        search={false}
        request={requestFn}
        options={false}
        scroll={{ x: 1200 }}
      />
    </PageContainer>
  );
};
export default ResourceTypeManage;
