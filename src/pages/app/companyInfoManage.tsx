import React, { useRef, useState } from 'react';
import { Button, message, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import { isMobile } from '@/const/jadgeUserAgent';
import _ from 'lodash';
import { addCompanyInfo, deleteCompanyInfo, getCompanyInfoList } from '@/services/companyInfo';
import { findLabel } from '@/utils/utils';
import { cardType } from '../sign/const';

type DataItem = {
  name: string;
  state: string;
};

const CompanyInfoManage = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const [visible, setVisible] = useState(false);

  function handleDelete(row: any) {
    Modal.confirm({
      title: '确认删除吗?',
      onOk: async () => {
        const res = await deleteCompanyInfo({
          id: row.id,
        });
        if (res.code === 0) {
          message.success('删除成功');
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }
      },
    });
  }

  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      hideInSearch: true,
      width: 100,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      hideInForm: true,
    },
    {
      title: 'qappid',
      dataIndex: 'qappid',
      key: 'qappid',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: {
        style: {
          width: 240,
        },
      },
    },
    {
      title: '企业名称',
      dataIndex: 'company_name',
      key: 'company_name',
      hideInSearch: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '企业统一社会信用代码',
      dataIndex: 'company_number',
      key: 'company_number',
      hideInSearch: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '责任人名称',
      dataIndex: 'corp_name',
      key: 'corp_name',
      hideInSearch: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '责任人证件类型',
      dataIndex: 'corp_cr_type',
      key: 'corp_cr_type',
      hideInSearch: true,
      valueType: 'select',
      fieldProps: {
        options: cardType,
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      render: (text, record: Record<string, any>) => findLabel(cardType, record.corp_cr_type),
    },
    {
      title: '责任人证件号',
      dataIndex: 'corp_cr_num',
      key: 'corp_cr_num',
      hideInSearch: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '经办人名称',
      dataIndex: 'transactor_name',
      key: 'transactor_name',
      hideInSearch: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '经办人证件类型',
      dataIndex: 'transactor_cr_type',
      key: 'transactor_cr_type',
      hideInSearch: true,
      valueType: 'select',
      fieldProps: {
        options: cardType,
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      render: (text, record: Record<string, any>) => findLabel(cardType, record.transactor_cr_type),
    },
    {
      title: '经办人证件号',
      dataIndex: 'transactor_cr_num',
      key: 'transactor_cr_num',
      hideInSearch: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '经办人电话',
      dataIndex: 'transactor_phone',
      key: 'transactor_phone',
      hideInSearch: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      width: 130,
      fixed: 'right',
      render: (row: any) => (
        <>
          <Button type="link" onClick={() => handleDelete(row)}>
            删除
          </Button>
        </>
      ),
      hideInSearch: true,
      hideInForm: true,
    },
  ];

  const requestFn = async (params: any) => {
    const { data } = await getCompanyInfoList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_index: params.current,
      page_size: params.pageSize,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  };

  async function onFinish(vals) {
    const res = await addCompanyInfo(vals);
    if (res.code === 0) {
      message.success('新增成功');
      setVisible(false);
      actionRef.current?.reload();
    }
  }

  return (
    <PageContainer>
      <Button type="primary" onClick={() => setVisible(true)}>
        新增
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapseRender: false,
          collapsed: false,
          span: 5,
        }}
        scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 2000 }}
        request={requestFn}
        options={false}
      />
      <BetaSchemaForm<DataItem>
        formRef={addFormRef}
        open={visible}
        layoutType="ModalForm"
        colProps={{
          span: 8,
        }}
        grid={true}
        onFinish={onFinish}
        onOpenChange={(val) => {
          if (!val) {
            addFormRef.current?.resetFields();
          }
          setVisible(val);
        }}
        columns={columns}
      />
    </PageContainer>
  );
};
export default CompanyInfoManage;
