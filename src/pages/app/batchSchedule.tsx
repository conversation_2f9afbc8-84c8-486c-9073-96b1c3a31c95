import React, { useMemo } from 'react';
import { Button, Form, Input, message, Table } from 'antd';
import _ from 'lodash';
import { PageContainer } from '@ant-design/pro-layout';
import { EditOutlined, SearchOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { useSetState } from 'react-use';
import { history } from 'umi';
import { getBatchConfig } from '@/services/scheduleConfig';
import { saveCSV } from '../global-components/saveCsv';
import { isMobile } from '@/const/jadgeUserAgent';

export const priorityStatus = [
  {
    label: '0级(低)',
    value: 0,
  },
  {
    label: '1级(中)',
    value: 1,
  },
  {
    label: '2级(高)',
    value: 2,
  },
];

const yesOrNo = [
  {
    label: '否',
    value: 0,
  },
  {
    label: '是',
    value: 1,
  },
];
function getExportData(columns: any[], list: any[]) {
  const head = _.map(columns, (col) => col.title);
  const keys = _.map(columns, (col) => {
    return {
      key: col.key,
      render: col.render,
      dataIndex: col.dataIndex,
    };
  });
  const data = _.map(list, (item) => {
    return _.map(keys, (o) => {
      const info = o?.render ? o.render(item[o.dataIndex]) : item[o.dataIndex];
      return info;
    });
  });

  return {
    head,
    data,
  };
}

function exportCsv({
  columns,
  list,
  route,
  params,
}: {
  columns: any[];
  list: any[];
  route: string;
  params: Record<string, any>;
}) {
  try {
    const { head, data } = getExportData(columns, list);
    saveCSV('应用配置', head, data, { route, params })
      .then(() => {
        message.success('导出成功');
      })
      .catch(() => {
        message.error('导出失败');
      });
  } catch (error) {
    message.error('导出失败');
  }
}

const BatchSchedule = () => {
  const [form] = Form.useForm();
  const initAppids = (history.location?.state as { appids?: string[] })?.appids ?? [];
  const [searchKeys, setSearchKeys] = useSetState<{
    appids?: string[];
    qappids?: string[];
  }>({
    appids: initAppids,
  });
  const { value: state, loading } = useAsyncRetryFunc(async () => {
    const keys = _.pickBy(searchKeys, (v: number | string) => v !== '');
    const res = await getBatchConfig({ ...keys });
    return res || {};
  }, [searchKeys]);

  const list = useMemo(() => {
    return state?.data ?? [];
  }, [state]);

  const _handlerSubmit = async (formValue: any) => {
    const _values = _.pickBy(formValue, (v) => v);
    if (formValue.appid) {
      _values.appids = _.compact(formValue.appid.split('\n'));
    }
    if (formValue.qappid) {
      _values.qappids = _.compact(formValue.qappid.split('\n'));
    }
    setSearchKeys({
      ..._.pick(_values, ['appids', 'qappids']),
    });
  };

  const columns: any[] = useMemo(
    () => [
      {
        title: '账号ID',
        dataIndex: 'qappid',
        key: 'qappid',
        align: 'center',
      },
      {
        title: '应用ID',
        dataIndex: 'sdkappid',
        key: 'sdkappid',
        align: 'center',
      },
      {
        title: '应用名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
      },
      {
        title: '签名',
        dataIndex: 'default_sign',
        key: 'default_sign',
        align: 'center',
      },
      {
        title: '扩展长度',
        dataIndex: 'self_extend_len',
        key: 'self_extend_len',
        align: 'center',
      },
      {
        title: '是否固签账号',
        dataIndex: 'verify_sign',
        key: 'verify_sign',
        align: 'center',
        render: (pay_type: number) => _.find(yesOrNo, (v) => v.value === pay_type)?.label,
      },
      {
        title: '行业校验',
        dataIndex: 'check_normal_template',
        key: 'check_normal_template',
        align: 'center',
        render: (pay_type: number) => _.find(yesOrNo, (v) => v.value === pay_type)?.label,
      },
      {
        title: '营销校验',
        dataIndex: 'check_business_template',
        key: 'check_business_template',
        align: 'center',
        render: (pay_type: number) => _.find(yesOrNo, (v) => v.value === pay_type)?.label,
      },
      {
        title: '强制调度',
        dataIndex: 'force_route',
        key: 'force_route',
        align: 'center',
        render: (pay_type: number) => _.find(yesOrNo, (v) => v.value === pay_type)?.label,
      },
      {
        title: '是否预付费',
        dataIndex: 'pay_type',
        key: 'pay_type',
        align: 'center',
        render: (pay_type: number) => _.find(yesOrNo, (v) => v.value === pay_type)?.label,
      },
      {
        title: '通道ID',
        dataIndex: 'provider_id',
        key: 'provider_id',
        align: 'center',
      },
      {
        title: '通道名称',
        dataIndex: 'provider',
        key: 'provider',
        align: 'center',
      },
      {
        title: '单发普通',
        dataIndex: 'single',
        key: 'single',
        align: 'center',
      },
      {
        title: '单发营销',
        dataIndex: 'single_b',
        key: 'single_b',
        align: 'center',
      },
      {
        title: '群发普通',
        dataIndex: 'multi2',
        key: 'multi2',
        align: 'center',
      },
      {
        title: '群发营销',
        dataIndex: 'multi2_b',
        key: 'multi2_b',
        align: 'center',
      },
      {
        title: '优先级',
        dataIndex: 'hot_backup',
        key: 'hot_backup',
        align: 'center',
        render: (hot_backup: number) =>
          _.find(priorityStatus, (v) => v.value === hot_backup)?.label || '',
      },
    ],
    [],
  );

  return (
    <PageContainer>
      <Form
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => _handlerSubmit(vals)}
        style={{ maxHeight: 500, overflow: 'auto' }}
        initialValues={{ appid: initAppids.join('\n') }}
      >
        <Form.Item style={{ marginBottom: 5 }} name="appid" label="appid">
          <Input.TextArea placeholder="换行输入多个" autoSize />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="qappid" label="qappid">
          <Input.TextArea placeholder="换行输入多个" autoSize />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading} icon={<SearchOutlined />}>
            查询
          </Button>
        </Form.Item>
        <Form.Item>
          <Button
            htmlType="button"
            type="primary"
            loading={loading}
            icon={<VerticalAlignBottomOutlined />}
            onClick={() => {
              exportCsv({ columns, list, route: '/sms/weight/batch-query', params: searchKeys });
            }}
          >
            导出
          </Button>
        </Form.Item>
        <Form.Item>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => {
              history.push('/app/scheduleConfig', {
                appids: searchKeys?.appids,
              });
            }}
          >
            批量配置
          </Button>
        </Form.Item>
      </Form>
      <Table
        size="middle"
        dataSource={list}
        columns={columns}
        loading={loading}
        pagination={false}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        style={{ marginTop: 20 }}
      />
    </PageContainer>
  );
};

export default BatchSchedule;
