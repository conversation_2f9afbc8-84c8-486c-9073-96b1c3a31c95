import React, { useEffect, useMemo } from 'react';
import { Table, Form, Button, message } from 'antd';
import _ from 'lodash';
import { useSetState } from 'react-use';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';

interface ConfigProps {
  columns: any[];
  legendTitle: string;
  searchKeys: { name: string; label: string; render: () => React.ReactNode }[];
  initialVal?: object;
  getFn: (params: any) => Promise<any>;
  setFn?: (params: any) => Promise<any>;
  type?: string;
}

interface SearchType {
  label: string;
  name: string;
  placeholder: string;
  render?: (form: any) => React.ReactNode;
  dependence?: string[];
}

const StrategyTable = ({
  columns,
  searchKeys,
  initialVal,
  getFn,
  setFn,
  type = 'view',
}: ConfigProps) => {
  const [form] = Form.useForm();
  const [searchVals, setSearchVals] = useSetState<{
    page_index: number;
    page_size: number;
    [key: string]: any;
  }>({
    page_index: 1,
    page_size: 10,
  });

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const result = await getFn({
      ...searchVals,
      mcc: searchVals?.mcc,
      mnc: searchVals?.mnc?.split('_')[1],
    });
    return result?.data ?? {};
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchVals]);

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const newCols = useMemo(() => {
    const newCo = _.cloneDeep(columns);
    if (type === 'modify') {
      newCo.push({
        title: '操作',
        key: 'operate',
        align: 'center',
        render: (row: any) => (
          <Button
            type="primary"
            onClick={async () => {
              const res = await setFn?.({ ...row });
              if (res?.code === 0) {
                message.success('设置成功');
                retry();
              }
            }}
          >
            set
          </Button>
        ),
      });
    }
    return newCo;
  }, [columns, type, setFn, retry]);

  useEffect(() => {
    form.setFieldsValue(initialVal);
  }, [form, initialVal]);

  return (
    <div className="monitor-field">
      <Form
        form={form}
        style={{ marginBottom: 10, marginTop: 10 }}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => {
          setSearchVals({ ...vals, page_index: 1 });
        }}
      >
        {searchKeys.map((item: Partial<SearchType>) => {
          if (!item.dependence) {
            return (
              <Form.Item name={item.name} key={item.name} label={item.label}>
                {item.render?.(form)}
              </Form.Item>
            );
          }
          return (
            <Form.Item
              key={item.name}
              noStyle
              shouldUpdate={(prevValues, curValues) => {
                return (item.dependence ?? []).some((k) => prevValues[k] !== curValues[k]);
              }}
            >
              {() => {
                return (
                  <Form.Item name={item.name} key={item.name} label={item.label}>
                    {item.render?.(form)}
                  </Form.Item>
                );
              }}
            </Form.Item>
          );
        })}
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={newCols}
        dataSource={list}
        rowKey="id"
        loading={loading}
        pagination={{
          current: searchVals.page_index,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchVals({ page_size }),
          onChange: (page_index) => {
            setSearchVals({ page_index });
          },
        }}
      />
    </div>
  );
};
export default StrategyTable;
