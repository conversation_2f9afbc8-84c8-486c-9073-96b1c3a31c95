/* eslint-disable no-param-reassign */
import React, { useEffect, useMemo, useState } from 'react';
import { Table, Select, Button, message, Form, InputNumber } from 'antd';
import { enableOpts } from './const';
import { useSetState } from 'react-use';

interface FieldProps {
  legendTitle: string;
  searchRender?: React.ReactNode;
  addFn: (params: any) => Promise<any>;
  defaultInitConf?: any[];
}

// 请求量阈值（默认100）、回执失败面积阈值（默认800）、回执成功失败面积阈值（默认800）、启用回执（默认不启用）、OTP请求量阈值（默认300）、CR预期值（默认0）、启用转化（默认不启用）
const _initConf = [
  {
    req_threshold: 100,
    cb_fail_area_threshold: 800,
    dr_fail_area_threshold: 800,
    dr_enable: 0,
    otp_req_threshold: 300,
    cr_expected: 0,
    cr_enable: 0,
  },
];

const QualityFieldSet = ({ searchRender, addFn, defaultInitConf }: FieldProps) => {
  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [values, setValues] = useSetState({ ..._initConf[0] });
  const newCols: any = useMemo(() => {
    // 请求量阈值（默认100）、回执失败面积阈值（默认800）、回执成功失败面积阈值（默认800）、启用回执（默认不启用）、OTP请求量阈值（默认300）、CR预期值（默认0）、启用转化（默认不启用）
    return [
      {
        title: '请求量阈值',
        key: 'req_threshold',
        align: 'center',
        render: () => (
          <InputNumber
            value={values.req_threshold}
            onChange={(value) => {
              if (value === null) {
                setValues({ req_threshold: 0 });
                return;
              }
              setValues({ req_threshold: value });
            }}
          />
        ),
      },
      {
        title: '回执失败面积阈值',
        key: 'cb_fail_area_threshold',
        align: 'center',
        render: () => (
          <InputNumber
            value={values.cb_fail_area_threshold}
            onChange={(value) => {
              if (value === null) {
                setValues({ cb_fail_area_threshold: 0 });
                return;
              }
              setValues({ cb_fail_area_threshold: value });
            }}
          />
        ),
      },
      {
        title: '回执成功失败面积阈值',
        key: 'dr_fail_area_threshold',
        align: 'center',
        render: () => (
          <InputNumber
            value={values.dr_fail_area_threshold}
            onChange={(value) => {
              if (value === null) {
                setValues({ dr_fail_area_threshold: 0 });
                return;
              }
              setValues({ dr_fail_area_threshold: value });
            }}
          />
        ),
      },
      {
        title: '启用回执',
        key: 'dr_enable',
        align: 'center',
        render: () => (
          <Select
            value={values.dr_enable}
            options={enableOpts}
            onChange={(value) => {
              setValues({ dr_enable: value });
            }}
            style={{ width: 100 }}
          />
        ),
      },
      {
        title: 'OTP请求量阈值',
        key: 'otp_req_threshold',
        align: 'center',
        render: () => (
          <InputNumber
            value={values.otp_req_threshold}
            onChange={(value) => {
              if (value === null) {
                setValues({ otp_req_threshold: 0 });
                return;
              }
              setValues({ otp_req_threshold: value });
            }}
          />
        ),
      },
      {
        title: 'CR预期值',
        key: 'cr_expected',
        align: 'center',
        render: () => (
          <InputNumber
            value={values.cr_expected}
            onChange={(value) => {
              if (value === null) {
                setValues({ cr_expected: 0 });
                return;
              }
              setValues({ cr_expected: value });
            }}
          />
        ),
      },
      {
        title: '启用转化',
        key: 'cr_enable',
        align: 'center',
        render: () => (
          <Select
            value={values.cr_enable}
            options={enableOpts}
            onChange={(value) => {
              setValues({ cr_enable: value });
            }}
            style={{ width: 100 }}
          />
        ),
      },
    ];
  }, [
    setValues,
    values.cb_fail_area_threshold,
    values.cr_enable,
    values.cr_expected,
    values.dr_enable,
    values.dr_fail_area_threshold,
    values.otp_req_threshold,
    values.req_threshold,
  ]);

  useEffect(() => {
    if (defaultInitConf) {
      setValues({ ...defaultInitConf[0] });
    }
  }, [defaultInitConf]);

  async function addConfig(vals: any) {
    try {
      setLoading(true);
      const res = await addFn({ ...vals, ...values });
      setLoading(false);
      res?.code === 0 && message.success('操作成功');
    } catch (error) {
      setLoading(false);
    }
  }

  return (
    <div className="monitor-field">
      <Form
        form={form}
        layout="inline"
        requiredMark={false}
        labelAlign="right"
        style={{ marginBottom: 10, marginTop: 10 }}
        onFinish={addConfig}
      >
        {searchRender}
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={isLoading}>
            添加/更新
          </Button>
        </Form.Item>
      </Form>
      <Table columns={newCols} dataSource={[values]} pagination={false} />
    </div>
  );
};
export default QualityFieldSet;
