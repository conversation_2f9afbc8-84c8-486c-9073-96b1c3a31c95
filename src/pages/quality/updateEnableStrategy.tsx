import React, { useState, useEffect, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { InputNumber, Select, Form, Tabs } from 'antd';
import _ from 'lodash';
import QualityFieldSet from './component/QualityFieldSet';
import StrategyTable from './component/StrategyTable';
import { enableOpts } from './component/const';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import {
  getDefaultStrategy,
  updateDefaultStrategy,
  updateSupplierCountryOperatorStrategy,
  updateSupplierCountryStrategy,
  updateSupplierStrategy,
  updateSupplierUinCountryOperatorStrategy,
  updateSupplierUinCountryStrategy,
  updateSupplierUinStrategy,
} from '@/services/qualityAnalyze';
import { getAccountList } from '@/services/smppAccount';
import { getOperatorName } from '@/utils/utils';
import MncSelect from '../channel/commonComponent/MncSelect';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { regionOptions } from '@/const/countryCode';

const policyTypes = [
  { value: 'provider', label: '供应商' },
  { value: 'provider_mcc', label: '供应商地区' },
  { value: 'provider_mcc_mnc', label: '供应商地区分运营商' },
  { value: 'uin_provider', label: 'Uin供应商' },
  { value: 'uin_provider_mcc', label: 'Uin供应商地区' },
  { value: 'uin_provider_mcc_mnc', label: 'Uin供应商地区分运营商' },
];

const UpdateEnableStrategy = () => {
  const [providerOptions, setProviderOptions] = useState([]);
  const [mccMncInfo] = useMccMncInfo();
  const [active, setActive] = useState('0');

  const { regionOptionsMcc: mccOptions = [] } = useFetchCountryInfo();

  useEffect(() => {
    getAccountList({ page_index: 1, page_size: 2000 }).then((res) => {
      const list = (res?.data?.list ?? [])
        .filter((v: { status: number }) => [2, 4, 5, 7].includes(v.status))
        .map((item: { account_id: number; account_name: string }) => {
          return { label: `${item.account_name}(${item.account_id})`, value: item.account_id };
        });
      setProviderOptions(list);
    });
  }, []);

  async function editDefaultFn(vals: any) {
    return await updateDefaultStrategy({
      ..._.pick(vals, [
        'policy_type',
        'req_threshold',
        'cb_fail_area_threshold',
        'dr_fail_area_threshold',
        'dr_enable',
        'otp_req_threshold',
        'cr_expected',
        'cr_enable',
      ]),
    });
  }

  const tabsInfo = useMemo(() => {
    const searchChoose = [
      {
        name: 'provider_ids',
        label: '通道ID: ',
        render: (
          <Form.Item label="通道ID" name="provider_ids" rules={[{ required: true }]}>
            <Select
              options={providerOptions}
              style={{ minWidth: 200 }}
              mode="multiple"
              showSearch
              allowClear
              filterOption={(input, option: any) =>
                option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          </Form.Item>
        ),
      },
      {
        name: 'mcc',
        label: 'mcc: ',
        render: (
          <Form.Item label="mcc" name="mcc" rules={[{ required: true }]}>
            <Select
              options={mccOptions}
              style={{ minWidth: 200 }}
              mode={active === '2' || active === '5' ? undefined : 'multiple'}
              allowClear
              showSearch
              filterOption={(input, option: any) =>
                option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          </Form.Item>
        ),
      },
      {
        name: 'mnc',
        label: 'mnc: ',
        render: (
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.mcc !== curValues.mcc}
          >
            {(form) => {
              const mcc = form.getFieldValue('mcc');
              return (
                <Form.Item name="mnc" label="运营商" rules={[{ required: true }]}>
                  <MncSelect
                    mode="multiple"
                    initialValues={{ mcc }}
                    onChange={(mnc: any) => form.setFieldsValue({ mnc })}
                    value={form.getFieldValue('mnc')}
                    style={{ minWidth: 200 }}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
        ),
      },
      {
        name: 'uin',
        label: 'uin: ',
        render: (
          <Form.Item label="uin" name="uin" rules={[{ required: true }]}>
            <InputNumber style={{ width: 200 }} />
          </Form.Item>
        ),
      },
    ];

    function findItems(cols: string[]) {
      return cols.map((v) => <>{_.find(searchChoose, (col) => col.name === v)?.render}</>);
    }
    return [
      {
        key: '0',
        legendTitle: '供应商策略',
        columns: [],
        searchRender: findItems(['provider_ids']),
        addFn: async (vals: any) => await updateSupplierStrategy({ ...vals }),
      },
      {
        key: '1',
        legendTitle: '供应商地区策略',
        searchRender: findItems(['provider_ids', 'mcc']),
        addFn: async (vals: any) =>
          await updateSupplierCountryStrategy({
            ..._.omit(vals, ['mcc']),
            countries: vals.mcc.map((item: string) => ({
              mcc: item,
              country_code: mccOptions
                .find((v) => v.value === item)
                ?.label.match(/_([^()]+)\(/)?.[1],
            })),
          }),
      },
      {
        key: '2',
        legendTitle: '供应商地区分运营商策略',
        searchRender: findItems(['provider_ids', 'mcc', 'mnc']),
        addFn: async (vals: any) =>
          await updateSupplierCountryOperatorStrategy({
            ..._.omit(vals, 'mnc'),
            mcc: vals.mcc,
            country_code: mccOptions
              .find((v) => v.value === vals.mcc)
              ?.label.match(/_([^()]+)\(/)?.[1],
            operators: vals.mnc?.map((v) => {
              console.log(v);
              const mnc = v.split('_')[1];
              const operator = getOperatorName(mccMncInfo, mnc, { mcc: vals.mcc });
              return { mnc, operator };
            }),
          }),
        defaultInitConf: [
          {
            req_threshold: 50,
            cb_fail_area_threshold: 800,
            dr_fail_area_threshold: 800,
            dr_enable: 0,
            otp_req_threshold: 300,
            cr_expected: 0,
            cr_enable: 0,
          },
        ],
      },
      {
        key: '3',
        legendTitle: 'Uin供应商策略',
        searchRender: findItems(['uin', 'provider_ids']),
        addFn: async (vals: any) => await updateSupplierUinStrategy({ ...vals }),
      },
      {
        key: '4',
        legendTitle: 'Uin供应商地区策略',
        searchRender: findItems(['uin', 'provider_ids', 'mcc']),
        addFn: async (vals: any) =>
          await updateSupplierUinCountryStrategy({
            ..._.omit(vals, ['mcc']),
            countries: vals.mcc.map((item: string) => ({
              mcc: item,
              country_code: mccOptions
                .find((v) => v.value === item)
                ?.label.match(/_([^()]+)\(/)?.[1],
            })),
          }),
      },
      {
        key: '5',
        legendTitle: 'Uin供应商地区分运营商策略',
        searchRender: findItems(['uin', 'provider_ids', 'mcc', 'mnc']),
        addFn: async (vals: any) =>
          await updateSupplierUinCountryOperatorStrategy({
            ..._.omit(vals, 'mnc'),
            mcc: vals.mcc,
            country_code: mccOptions
              .find((v) => v.value === vals.mcc)
              ?.label.match(/_([^()]+)\(/)?.[1],
            operators: vals.mnc?.map((v) => {
              console.log(v);
              const mnc = v.split('_')[1];
              const operator = getOperatorName(mccMncInfo, mnc, { mcc: vals.mcc });
              return { mnc, operator };
            }),
          }),
        defaultInitConf: [
          {
            req_threshold: 50,
            cb_fail_area_threshold: 800,
            dr_fail_area_threshold: 800,
            dr_enable: 0,
            otp_req_threshold: 300,
            cr_expected: 0,
            cr_enable: 0,
          },
        ],
      },
    ];
  }, [active, mccMncInfo, mccOptions, providerOptions]);

  function onActive(key: string) {
    setActive(key);
  }

  return (
    <PageContainer>
      <Tabs defaultActiveKey="0" onChange={onActive}>
        {tabsInfo.map((item) => {
          return (
            <Tabs.TabPane tab={item.legendTitle} key={item.key}>
              <QualityFieldSet {...item} />
            </Tabs.TabPane>
          );
        })}
        <Tabs.TabPane tab="默认策略" key="6">
          <StrategyTable
            legendTitle="默认策略"
            columns={[
              {
                title: '策略类型',
                dataIndex: 'policy_type',
                key: 'policy_type',
                align: 'center',
                render: (policy_type: string) =>
                  _.find(policyTypes, (v) => v.value === policy_type)?.label,
              },
              {
                title: '请求量阈值',
                key: 'req_threshold',
                render: (row: { req_threshold: number }) => {
                  return (
                    <InputNumber
                      defaultValue={row.req_threshold}
                      onChange={(value) => {
                        if (value === null) {
                          row.req_threshold = 0;
                          return;
                        }
                        row.req_threshold = value;
                      }}
                    />
                  );
                },
              },
              {
                title: '回执失败面积阈值',
                key: 'cb_fail_area_threshold',
                render: (row: { cb_fail_area_threshold: number }) => {
                  return (
                    <InputNumber
                      defaultValue={row.cb_fail_area_threshold}
                      onChange={(value) => {
                        if (value === null) {
                          row.cb_fail_area_threshold = 0;
                          return;
                        }
                        row.cb_fail_area_threshold = value;
                      }}
                    />
                  );
                },
              },
              {
                title: '回执成功失败面积阈值',
                key: 'dr_fail_area_threshold',
                render: (row: { dr_fail_area_threshold: number }) => {
                  return (
                    <InputNumber
                      defaultValue={row.dr_fail_area_threshold}
                      onChange={(value) => {
                        if (value === null) {
                          row.dr_fail_area_threshold = 0;
                          return;
                        }
                        row.dr_fail_area_threshold = value;
                      }}
                    />
                  );
                },
              },
              {
                title: '启用回执',
                key: 'dr_enable',
                render: (row: { dr_enable: 0 | 1 }) => {
                  return (
                    <Select
                      defaultValue={row.dr_enable}
                      options={enableOpts}
                      onChange={(value) => {
                        row.dr_enable = value;
                      }}
                      style={{ width: 100 }}
                    />
                  );
                },
              },
              {
                title: 'OTP请求量阈值',
                key: 'otp_req_threshold',
                render: (row: { otp_req_threshold: number }) => {
                  return (
                    <InputNumber
                      defaultValue={row.otp_req_threshold}
                      onChange={(value) => {
                        if (value === null) {
                          row.otp_req_threshold = 0;
                          return;
                        }
                        row.otp_req_threshold = value;
                      }}
                    />
                  );
                },
              },
              {
                title: 'CR预期值',
                key: 'cr_expected',
                render: (row: { cr_expected: number }) => {
                  return (
                    <InputNumber
                      defaultValue={row.cr_expected}
                      onChange={(value) => {
                        if (value === null) {
                          row.cr_expected = 0;
                          return;
                        }
                        row.cr_expected = value;
                      }}
                    />
                  );
                },
              },
              {
                title: '启用转化',
                key: 'cr_enable',
                render: (row: { cr_enable: 0 | 1 }) => {
                  return (
                    <Select
                      defaultValue={row.cr_enable}
                      options={enableOpts}
                      onChange={(value) => {
                        row.cr_enable = value;
                      }}
                      style={{ width: 100 }}
                    />
                  );
                },
              },
            ]}
            searchKeys={[
              {
                label: '默认类型',
                name: 'policy_type',
                render: () => {
                  return <Select allowClear options={policyTypes} style={{ width: 200 }} />;
                },
              },
            ]}
            getFn={async (vals: any) => {
              const sortOrder = [1, 2, 5, 3, 4, 6];
              const res = await getDefaultStrategy({ ...vals });
              return {
                ...res,
                data: {
                  ...res.data,
                  list: _.sortBy(res?.data?.list || [], (item) => {
                    return _.indexOf(sortOrder, item.id);
                  }),
                },
              };
            }}
            setFn={editDefaultFn}
            initialVal={{ policy_type: '' }}
            type="modify"
          />
        </Tabs.TabPane>
      </Tabs>
    </PageContainer>
  );
};
export default UpdateEnableStrategy;
