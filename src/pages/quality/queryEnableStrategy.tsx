import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { InputNumber, Select, Tabs } from 'antd';
import StrategyTable from './component/StrategyTable';
import _ from 'lodash';
import { enableOpts } from './component/const';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import {
  getSupplierCountryOperatorStrategy,
  getSupplierCountryStrategy,
  getSupplierStrategy,
  getSupplierUinCountryOperatorStrategy,
  getSupplierUinCountryStrategy,
  getSupplierUinStrategy,
} from '@/services/qualityAnalyze';
import { getAccountList } from '@/services/smppAccount';
import { getMncOptions } from '../channel/component/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const QueryEnableStrategy = () => {
  const [providerOptions, setProviderOptions] = useState([]);
  const [mccMncInfo] = useMccMncInfo();

  const { regionOptionsMcc: mccOptions = [] } = useFetchCountryInfo();

  const searchChoose = [
    {
      // 0
      name: 'provider_id',
      label: '通道ID: ',
      render: () => {
        return (
          <Select
            options={providerOptions}
            style={{ minWidth: 200 }}
            showSearch
            allowClear
            filterOption={(input, option: any) =>
              option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        );
      },
    },
    {
      // 1
      name: 'mcc',
      label: 'mcc: ',
      render: (form: an) => {
        return (
          <Select
            options={mccOptions}
            style={{ width: 200 }}
            allowClear
            showSearch
            filterOption={(input, option: any) =>
              option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            onChange={() => {
              form.setFieldsValue({ mnc: undefined });
            }}
          />
        );
      },
    },
    {
      // 2
      name: 'mnc',
      label: 'mnc: ',
      dependence: ['mcc'],
      render: (form: an) => {
        const mcc = form.getFieldValue('mcc');
        console.log(mcc);
        return (
          <Select
            options={getMncOptions(mccMncInfo, mcc, 'mcc')}
            style={{ width: 200 }}
            allowClear
            showSearch
            filterOption={(input, option: any) =>
              option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        );
      },
    },
    {
      // 3
      name: 'uin',
      label: 'uin: ',
      render: () => {
        return <InputNumber style={{ width: 200 }} />;
      },
    },
  ];

  function getChoosen(indexs: number[]) {
    const opts: { name: string; label: string; render: () => React.ReactNode }[] = [];
    indexs.forEach((item) => {
      opts.push(searchChoose[item]);
    });
    return opts;
  }

  useEffect(() => {
    getAccountList({ page_index: 1, page_size: 2000 }).then((res) => {
      const list = (res?.data?.list ?? [])
        .filter((v: { status: number }) => [2, 4, 5, 7].includes(v.status))
        .map((item: { account_id: number; account_name: string }) => {
          return { label: `${item.account_name}(${item.account_id})`, value: item.account_id };
        });
      setProviderOptions(list);
    });
  }, []);

  const columnsCollect = [
    { title: 'uin', dataIndex: 'uin', key: 'uin' },
    { title: '通道ID', dataIndex: 'provider_id', key: 'provider_id' },
    { title: '供应商名称', dataIndex: 'supplier_name', key: 'supplier_name' },
    { title: '请求量阈值', dataIndex: 'req_threshold', key: 'req_threshold' },
    {
      title: '回执失败面积阈值',
      dataIndex: 'cb_fail_area_threshold',
      key: 'cb_fail_area_threshold',
    },
    {
      title: '回执成功失败面积阈值',
      dataIndex: 'dr_fail_area_threshold',
      key: 'dr_fail_area_threshold',
    },
    {
      title: '启用回执',
      dataIndex: 'dr_enable',
      key: 'dr_enable',
      render: (dr_enable: 0 | 1) => _.find(enableOpts, (v) => v.value === dr_enable)?.label,
    },
    {
      title: 'OTP请求量阈值',
      dataIndex: 'otp_req_threshold',
      key: 'otp_req_threshold',
    },
    {
      title: 'CR预期值',
      dataIndex: 'cr_expected',
      key: 'cr_expected',
    },
    {
      title: '启用转化',
      dataIndex: 'cr_enable',
      key: 'cr_enable',
      render: (cr_enable: 0 | 1) => _.find(enableOpts, (v) => v.value === cr_enable)?.label,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
    },
    {
      title: 'mcc',
      dataIndex: 'mcc',
      key: 'mcc',
      render: (mcc: string) => _.find(mccOptions, (v) => v.value === mcc)?.label || mcc,
    },
    {
      title: 'mnc',
      // dataIndex: 'mnc',
      key: 'mnc',
      render: (row: any) => {
        const item = _.find(mccMncInfo, (v) => v.mcc === row.mcc && v.mnc === row.mnc);
        return item ? `${item?.operator_name}(${item?.mnc})` : row.mnc;
      },
    },
  ];

  function findCols(cols: string[]) {
    return cols.map((v) => _.find(columnsCollect, (col) => col.key === v));
  }

  const tabsConfig = [
    {
      key: '0',
      columns: findCols([
        'provider_id',
        'supplier_name',
        'req_threshold',
        'cb_fail_area_threshold',
        'dr_fail_area_threshold',
        'dr_enable',
        'otp_req_threshold',
        'cr_expected',
        'cr_enable',
      ]),
      legendTitle: '供应商策略',
      getFn: async (vals: any) => await getSupplierStrategy({ ...vals }),
      searchKeys: getChoosen([0]),
      initialVal: {},
    },
    {
      key: '1',
      columns: findCols([
        'provider_id',
        'supplier_name',
        'mcc',
        'req_threshold',
        'cb_fail_area_threshold',
        'dr_fail_area_threshold',
        'dr_enable',
        'otp_req_threshold',
        'cr_expected',
        'cr_enable',
      ]),
      legendTitle: '供应商地区策略',
      getFn: async (vals: any) => await getSupplierCountryStrategy({ ...vals }),
      searchKeys: getChoosen([0, 1]),
      initialVal: { mcc: '' },
    },
    {
      key: '2',
      columns: findCols([
        'provider_id',
        'supplier_name',
        'mcc',
        'mnc',
        'req_threshold',
        'cb_fail_area_threshold',
        'dr_fail_area_threshold',
        'dr_enable',
        'otp_req_threshold',
        'cr_expected',
        'cr_enable',
      ]),
      legendTitle: '供应商地区分运营商策略',
      getFn: async (vals: any) => await getSupplierCountryOperatorStrategy({ ...vals }),
      searchKeys: getChoosen([0, 1, 2]),
      initialVal: { mcc: '' },
    },
    {
      key: '3',
      columns: findCols([
        'uin',
        'provider_id',
        'supplier_name',
        'req_threshold',
        'cb_fail_area_threshold',
        'dr_fail_area_threshold',
        'dr_enable',
        'otp_req_threshold',
        'cr_expected',
        'cr_enable',
      ]),
      legendTitle: 'Uin供应商策略',
      getFn: async (vals: any) => await getSupplierUinStrategy({ ...vals }),
      searchKeys: getChoosen([3, 0]),
      initialVal: { uin: '' },
    },
    {
      key: '4',
      columns: findCols([
        'uin',
        'provider_id',
        'supplier_name',
        'mcc',
        'req_threshold',
        'cb_fail_area_threshold',
        'dr_fail_area_threshold',
        'dr_enable',
        'otp_req_threshold',
        'cr_expected',
        'cr_enable',
      ]),
      legendTitle: 'Uin供应商地区策略',
      getFn: async (vals: any) => await getSupplierUinCountryStrategy({ ...vals }),
      searchKeys: getChoosen([3, 0, 1, 2]),
      initialVal: { uin: '', mcc: '' },
    },
    {
      key: '5',
      columns: findCols([
        'uin',
        'provider_id',
        'supplier_name',
        'mcc',
        'mnc',
        'req_threshold',
        'cb_fail_area_threshold',
        'dr_fail_area_threshold',
        'dr_enable',
        'otp_req_threshold',
        'cr_expected',
        'cr_enable',
      ]),
      legendTitle: 'Uin供应商地区分运营商策略',
      getFn: async (vals: any) => await getSupplierUinCountryOperatorStrategy({ ...vals }),
      searchKeys: getChoosen([3, 0, 1, 2]),
      initialVal: { uin: '', mcc: '' },
    },
  ];

  return (
    <PageContainer>
      <Tabs>
        {tabsConfig.map((item) => {
          return (
            <Tabs.TabPane tab={item.legendTitle} key={item.key}>
              <StrategyTable {...item} />
            </Tabs.TabPane>
          );
        })}
      </Tabs>
    </PageContainer>
  );
};
export default QueryEnableStrategy;
