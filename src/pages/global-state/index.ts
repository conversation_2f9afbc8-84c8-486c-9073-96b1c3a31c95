import { createGlobalState } from 'react-use';

export type InfoType = {
  mcc: string;
  mnc: string;
  country_code: string;
  operator_name: string;
}[];

export type CountryInfoType = {
  regionOptions?: { label: string; value: string | number }[];
  optionsMcc?: { label: string; value: string | number }[];
  regionOptionsNationCode?: { label: string; value: string | number }[];
  regionOptionsMccCountryCode?: { label: string; value: string | number }[];
  list?: any[];
  setted: boolean;
};

export const _useMccMncInfo = createGlobalState<InfoType>([]);
export const _useCountryInfo = createGlobalState<CountryInfoType>({
  setted: false,
  list: [],
  regionOptions: [],
  regionOptionsNationCode: [],
  regionOptionsMccCountryCode: [],
});
