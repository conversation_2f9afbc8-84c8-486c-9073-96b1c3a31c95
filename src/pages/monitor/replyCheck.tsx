import React, { useState, useEffect } from 'react';
import { Table } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import { getReplyCheckList } from '@/services/scdAPI';
const ReplyCheck = () => {
  const [list, setList] = useState([]);
  useEffect(() => {
    getReplyCheckList()
      .then((res) => {
        setList(res?.data || []);
      })
      .catch(() => {
        setList([]);
      });
  }, []);
  return (
    <PageContainer>
      <Table
        dataSource={list}
        columns={[
          { title: 'appid', dataIndex: 'sdkappid', key: 'sdkappid' },
          { title: 'appname', dataIndex: 'name', key: 'name' },
        ]}
      />
    </PageContainer>
  );
};
export default ReplyCheck;
