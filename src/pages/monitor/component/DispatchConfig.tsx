import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Table, Form, Button, InputNumber, message, Select } from 'antd';
import _ from 'lodash';

interface ConfigProps {
  columns: object[];
  legendTitle: string;
  searchKeys: { name: string; label: string; render: () => React.ReactNode }[];
  initialVal?: object;
  showPagination: boolean;
  getFn: Function;
  setFn?: Function;
  type?: string;
  configType: string;
}

interface SearchType {
  label: string;
  name: string;
  placeholder: string;
  render?: () => React.ReactNode;
}

const DispatchConfig = ({
  columns,
  searchKeys,
  initialVal,
  showPagination = true,
  getFn,
  setFn,
  type = 'view',
  configType,
}: ConfigProps) => {
  const [form] = Form.useForm();
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchVals, setSearchVals] = useState({});
  const [showTable, setShowTable] = useState(false);
  const cols: any = useMemo(() => {
    const key = configType === '1' ? 'callback_receipt_rate' : 'callback_receipt_rate_threshold';
    return [
      {
        title: '请求量过滤阈值',
        key: 'amount_threshold',
        align: 'center',
        render: (row: any) => {
          if (type === 'view') {
            return <span>{row.amount_threshold}</span>;
          }
          return (
            <InputNumber
              defaultValue={row.amount_threshold}
              onChange={(value) => {
                if (value === null) {
                  row.amount_threshold = 0;
                  return;
                }
                row.amount_threshold = value;
              }}
            />
          );
        },
      },
      {
        title: '请求成功面积阈值',
        key: 'send_succ_rate_threshold',
        align: 'center',
        render: (row: any) => {
          if (type === 'view') {
            return <span>{row.send_succ_rate_threshold}</span>;
          }
          return (
            <InputNumber
              defaultValue={row.send_succ_rate_threshold}
              onChange={(value) => {
                if (value === null) {
                  row.send_succ_rate_threshold = 0;
                  return;
                }
                row.send_succ_rate_threshold = value;
              }}
            />
          );
        },
      },
      {
        title: '回执面积阈值',
        key: 'callback_rate_threshold',
        align: 'center',
        render: (row: any) => {
          if (type === 'view') {
            return <span>{row.callback_rate_threshold}</span>;
          }
          return (
            <InputNumber
              defaultValue={row.callback_rate_threshold}
              onChange={(value) => {
                if (value === null) {
                  row.callback_rate_threshold = 0;
                  return;
                }
                row.callback_rate_threshold = value;
              }}
            />
          );
        },
      },
      {
        title: '回执失败面积阈值',
        key,
        align: 'center',
        render: (row: any) => {
          if (type === 'view') {
            return <span>{row[key]}</span>;
          }
          return (
            <InputNumber
              defaultValue={row[key]}
              onChange={(value) => {
                if (value === null) {
                  row[key] = 0;
                  return;
                }
                row[key] = value;
              }}
            />
          );
        },
      },
      {
        title: '启用',
        key: 'enable',
        align: 'center',
        render: (row: any) => {
          if (type === 'view') {
            return <span>{row.enable === 1 ? '启用' : '禁用'}</span>;
          }
          return (
            <Select
              defaultValue={row.enable}
              options={[
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 },
              ]}
              onChange={(value) => {
                row.enable = value;
              }}
              style={{ width: 100 }}
            />
          );
        },
      },
      {
        title: '更新时间',
        key: 'update_time',
        dataIndex: 'update_time',
        align: 'center',
      },
    ];
  }, [type, configType]);
  const [newCol, setNewCol] = useState(cols);
  const [isLoading, setLoading] = useState(false);
  const [list, setList] = useState<any[]>([]);
  const [count, setCount] = useState<number>(0);

  const editConf = useCallback(
    async (row: any) => {
      const res = await setFn?.({ ...row });
      if (res?.code === 0) {
        message.success('设置成功');
      }
    },
    [setFn],
  );

  useEffect(() => {
    const newCo = columns.concat(cols);
    if (type === 'modify') {
      newCo.push({
        title: '操作',
        key: 'operate',
        align: 'center',
        render: (row: any) => (
          <Button
            type="primary"
            onClick={() => {
              editConf(row);
            }}
          >
            set
          </Button>
        ),
      });
    }
    setNewCol(newCo);
  }, [editConf, columns]);

  useEffect(() => {
    getList(searchVals);
  }, [pageIndex, pageSize]);

  useEffect(() => {
    form.setFieldsValue(initialVal);
  }, [initialVal]);

  async function getList(vals: any, isInit?: boolean) {
    if (!Object.keys(vals).length) return;
    setLoading(true);
    try {
      const params = _.pickBy(vals, (item) => item !== '');
      if (showPagination) {
        params.page_index = pageIndex;
        params.page_size = pageSize;
      }
      if (isInit && pageIndex !== 1) {
        setPageIndex(1);
        return;
      }
      const res = await getFn({ ...params });
      setList(showPagination ? res?.data?.list : res?.data);
      setCount(showPagination ? res?.data?.count : res?.data?.length);
      setLoading(false);
    } catch (error) {
      setList([]);
      setCount(0);
    }
  }

  return (
    <div className="monitor-field">
      <Form
        form={form}
        style={{ marginBottom: 10, marginTop: 10 }}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => {
          setShowTable(true);
          setSearchVals(vals);
          getList(vals, true);
        }}
      >
        {searchKeys.map((item: Partial<SearchType>) => {
          return (
            <Form.Item name={item.name} key={item.name} label={item.label}>
              {item.render?.()}
            </Form.Item>
          );
        })}
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={isLoading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      {showTable && (
        <Table
          columns={newCol}
          dataSource={list}
          loading={isLoading}
          pagination={
            showPagination
              ? {
                  current: pageIndex,
                  total: count,
                  showSizeChanger: true,
                  onShowSizeChange: (current, page_size) => setPageSize(page_size),
                  onChange: (page) => {
                    setPageIndex(page);
                  },
                }
              : false
          }
        />
      )}
    </div>
  );
};
export default DispatchConfig;
