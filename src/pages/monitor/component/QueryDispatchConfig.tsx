import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { InputNumber, Select, Tabs } from 'antd';
import DispatchConfig from './DispatchConfig';
import { teleoperators, enableOpts, getTelName } from '../const/const';
import {
  getSupplierList,
  getSupConf,
  getSupTelConf,
  getSupAppConf,
  getSupAppTelConf,
  getSupQappConf,
  getSupQappTelConf,
  getMktSupConf,
  getMktSupTelConf,
  getMktSupAppConf,
  getMktSupQappConf,
} from '@/services/scdAPI';

const QueryDispatchConfig = ({ type }: { type: string }) => {
  const [suppliers, setSuppliers] = useState([]);

  const searchChoose = [
    {
      // 0
      name: 'suplier_ids',
      label: '供应商ID: ',
      render: () => {
        return (
          <Select
            options={suppliers}
            style={{ minWidth: 200 }}
            showSearch
            allowClear
            mode="multiple"
            placeholder="不选则默认全部"
            filterOption={(input, option: any) =>
              option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        );
      },
    },
    {
      // 1
      name: 'teleoperator',
      label: '运营商: ',
      render: () => {
        return <Select options={teleoperators} style={{ width: 200 }} />;
      },
    },
    {
      // 2
      name: 'enable',
      label: '是否启用: ',
      render: () => {
        return <Select options={enableOpts} style={{ width: 200 }} />;
      },
    },
    {
      // 3
      name: 'sdkappid',
      label: 'sdkappid: ',
      render: () => {
        return <InputNumber style={{ width: 200 }} />;
      },
    },
    {
      // 4
      name: 'qappid',
      label: 'qcloud_appid: ',
      render: () => {
        return <InputNumber style={{ width: 200 }} />;
      },
    },
  ];

  function getChoosen(indexs: number[]) {
    const opts: { name: string; label: string; render: () => React.ReactNode }[] = [];
    indexs.forEach((item) => {
      opts.push(searchChoose[item]);
    });
    return opts;
  }

  useEffect(() => {
    getSupplierList().then((res) => {
      const list = (res?.data ?? []).map((item: { supplier_id: number; supplier: string }) => {
        return { label: item.supplier, value: item.supplier_id };
      });
      setSuppliers(list);
    });
  }, []);

  const tabsConfig = [
    {
      key: '0',
      tabType: ['1', '2'],
      columns: [
        { title: '供应商id', dataIndex: 'suplier_id', key: 'suplier_id' },
        { title: '供应商名称', dataIndex: 'suplier_name', key: 'suplier_name' },
      ],
      legendTitle: '供应商调度配置',
      getFn: async (vals: any) =>
        type === '1' ? await getSupConf({ ...vals }) : await getMktSupConf({ ...vals }),
      searchKeys: getChoosen([0, 2]),
      initialVal: { suplier_id: '', enable: '' },
      showPagination: true,
      configType: type,
    },
    {
      key: '1',
      tabType: ['1', '2'],
      columns: [
        { title: '供应商id', dataIndex: 'suplier_id', key: 'suplier_id' },
        { title: '供应商名称', dataIndex: 'suplier_name', key: 'suplier_name' },
        {
          title: '运营商',
          dataIndex: 'teleoperator',
          key: 'teleoperator',
          render: (tel: number) => <span>{getTelName(tel)}</span>,
        },
      ],
      legendTitle: '供应商运营商调度配置',
      getFn: async (vals: any) =>
        type === '1' ? await getSupTelConf({ ...vals }) : await getMktSupTelConf({ ...vals }),
      searchKeys: getChoosen([0, 1, 2]),
      initialVal: { suplier_id: '', teleoperator: '', enable: '' },
      showPagination: true,
      configType: type,
    },
    {
      key: '2',
      tabType: ['1', '2'],
      columns: [
        { title: 'sdkappid', dataIndex: 'sdkappid', key: 'sdkappid' },
        { title: '供应商id', dataIndex: 'suplier_id', key: 'suplier_id' },
        { title: '供应商名称', dataIndex: 'suplier_name', key: 'suplier_name' },
      ],
      legendTitle: '应用供应商调度配置',
      getFn: async (vals: any) =>
        type === '1' ? await getSupAppConf({ ...vals }) : await getMktSupAppConf({ ...vals }),
      searchKeys: getChoosen([3, 0, 2]),
      initialVal: { suplier_id: '', enable: '' },
      showPagination: true,
      configType: type,
    },
    {
      key: '3',
      tabType: ['1'],
      columns: [
        { title: 'sdkappid', dataIndex: 'sdkappid', key: 'sdkappid' },
        { title: '供应商id', dataIndex: 'suplier_id', key: 'suplier_id' },
        { title: '供应商名称', dataIndex: 'suplier_name', key: 'suplier_name' },
        {
          title: '运营商',
          dataIndex: 'teleoperator',
          key: 'teleoperator',
          render: (tel: number) => <span>{getTelName(tel)}</span>,
        },
      ],
      legendTitle: '应用供应商运营商调度配置',
      getFn: async (vals: any) => await getSupAppTelConf({ ...vals }),
      searchKeys: getChoosen([3, 0, 1, 2]),
      initialVal: { suplier_id: '', teleoperator: '', enable: '' },
      showPagination: true,
      configType: type,
    },
    {
      key: '4',
      tabType: ['1', '2'],
      columns: [
        {
          title: 'sdkappid',
          dataIndex: type === '1' ? 'sdkappid' : 'qcloud_appid',
          key: 'sdkappid',
        },
        { title: '供应商id', dataIndex: 'suplier_id', key: 'suplier_id' },
        { title: '供应商名称', dataIndex: 'suplier_name', key: 'suplier_name' },
      ],
      legendTitle: type === '1' ? '账号级供应商配置' : '客户供应商调度配置',
      getFn: async (vals: any) =>
        type === '1' ? await getSupQappConf({ ...vals }) : await getMktSupQappConf({ ...vals }),
      searchKeys: getChoosen([4, 0, 2]),
      showPagination: true,
      initialVal: { enable: '' },
      configType: type,
    },
    {
      key: '5',
      tabType: ['1'],
      columns: [
        { title: 'sdkappid', dataIndex: 'sdkappid', key: 'sdkappid' },
        { title: '供应商id', dataIndex: 'suplier_id', key: 'suplier_id' },
        { title: '供应商名称', dataIndex: 'suplier_name', key: 'suplier_name' },
        {
          title: '运营商',
          dataIndex: 'teleoperator',
          key: 'teleoperator',
          render: (tel: number) => <span>{getTelName(tel)}</span>,
        },
      ],
      legendTitle: '账号级供应商运营商配置',
      getFn: async (vals: any) => await getSupQappTelConf({ ...vals }),
      searchKeys: getChoosen([4, 0, 1]),
      showPagination: true,
      configType: type,
    },
  ];

  return (
    <PageContainer>
      <Tabs>
        {tabsConfig.map((item) => {
          if (item.tabType.includes(type)) {
            return (
              <Tabs.TabPane tab={item.legendTitle} key={item.key}>
                <DispatchConfig {...item} />
              </Tabs.TabPane>
            );
          }
          return null;
        })}
      </Tabs>
    </PageContainer>
  );
};
export default QueryDispatchConfig;
