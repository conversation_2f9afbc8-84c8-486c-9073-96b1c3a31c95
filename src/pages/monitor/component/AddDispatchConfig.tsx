import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON>Container } from '@ant-design/pro-layout';
import { InputNumber, Select, Form, Tabs } from 'antd';
import MonitorFieldset from './MonitorFieldSet';
import DispatchConfig from './DispatchConfig';
import { configMktOpts, configOpts, getTelName, getTypeText } from '../const/const';
import {
  getSupplierList,
  editSupConf,
  editSupTelConf,
  editSupAppConf,
  editSupAppTelConf,
  editSupQappConf,
  editSupQappTelConf,
  getDefaultConf,
  setDefaultConf,
  editMktSupConf,
  editMktSupTelConf,
  editMktSupAppConf,
  editMktSupQappConf,
  getMktDefaultConf,
  setMktDefaultConf,
} from '@/services/scdAPI';
import _ from 'lodash';

const operators = [
  {
    title: '运营商',
    dataIndex: 'teleoperator',
    key: 'teleoperator',
    align: 'center',
    render: (teleoperator: number) => {
      return <span>{getTelName(teleoperator)}</span>;
    },
  },
];

const initConf = [
  {
    amount_threshold: 150,
    send_succ_rate_threshold: 150,
    callback_rate_threshold: 300,
    callback_receipt_rate: 300,
    enable: 1,
  },
];

const initTelConf = [
  {
    teleoperator: 1,
    amount_threshold: 150,
    send_succ_rate_threshold: 150,
    callback_rate_threshold: 300,
    callback_receipt_rate: 300,
    enable: 1,
  },
  {
    teleoperator: 2,
    amount_threshold: 150,
    send_succ_rate_threshold: 150,
    callback_rate_threshold: 300,
    callback_receipt_rate: 300,
    enable: 1,
  },
  {
    teleoperator: 3,
    amount_threshold: 150,
    send_succ_rate_threshold: 150,
    callback_rate_threshold: 300,
    callback_receipt_rate: 300,
    enable: 1,
  },
];

const AddDispatchConfig = ({ type }: { type: string }) => {
  const [suppliers, setSuppliers] = useState([]);
  const [supConf] = useState(_.cloneDeep(initConf));
  const [supTelConf] = useState(_.cloneDeep(initTelConf));
  const [supAppConf] = useState(_.cloneDeep(initConf));
  const [supQappConf] = useState(_.cloneDeep(initConf));
  const [supAppTelConf] = useState(_.cloneDeep(initTelConf));
  const [supQappTelConf] = useState(_.cloneDeep(initTelConf));

  useEffect(() => {
    getSupplierList().then((res) => {
      const list = (res?.data ?? []).map((item: { supplier_id: number; supplier: string }) => {
        return { label: item.supplier, value: item.supplier_id };
      });
      setSuppliers(list);
    });
  }, []);

  async function editDefaultFn(vals: any) {
    if (
      vals.callback_receipt_rate_threshold !== undefined &&
      vals.callback_receipt_rate === undefined
    ) {
      vals.callback_receipt_rate = vals.callback_receipt_rate_threshold;
      delete vals.callback_receipt_rate_threshold;
    }
    return type === '1' ? await setDefaultConf({ ...vals }) : await setMktDefaultConf({ ...vals });
  }

  const tabsInfo = useMemo(
    () => [
      {
        key: '0',
        type: ['1', '2'],
        legendTitle: '供应商配置',
        columns: [],
        list: supConf,
        options: suppliers,
        addFn: async (vals: any) =>
          type === '1'
            ? await editSupConf({ ...supConf[0], ...vals })
            : await editMktSupConf({ ...supConf[0], ...vals }),
      },
      {
        key: '1',
        type: ['1', '2'],
        legendTitle: '供应商运营商配置',
        columns: operators,
        list: supTelConf,
        options: suppliers,
        addFn: async (vals: any) =>
          type === '1'
            ? await editSupTelConf({ ...vals, conf_data: supTelConf })
            : await editMktSupTelConf({ ...vals, conf_data: supTelConf }),
      },
      {
        key: '2',
        type: ['1', '2'],
        legendTitle: '应用供应商配置',
        columns: [],
        searchRender: () => {
          return (
            <Form.Item label="sdkappid" name="sdkappid" rules={[{ required: true }]}>
              <InputNumber style={{ width: '180px', marginRight: 5 }} />
            </Form.Item>
          );
        },
        list: supAppConf,
        options: suppliers,
        addFn: async (vals: any) =>
          type === '1'
            ? await editSupAppConf({ ...supAppConf[0], ...vals })
            : await editMktSupAppConf({ ...supAppConf[0], ...vals }),
      },
      {
        key: '3',
        type: ['1'],
        legendTitle: '应用供应商运营商配置',
        columns: operators,
        searchRender: () => {
          return (
            <Form.Item label="sdkappid" name="sdkappid" rules={[{ required: true }]}>
              <InputNumber style={{ width: '180px', marginRight: 5 }} />
            </Form.Item>
          );
        },
        list: supAppTelConf,
        options: suppliers,
        addFn: async (vals: any) => await editSupAppTelConf({ ...vals, conf_data: supAppTelConf }),
      },
      {
        key: '4',
        type: ['1', '2'],
        legendTitle: type === '1' ? '账号级供应商配置' : '客户供应商配置',
        columns: [],
        searchRender: () => {
          return (
            <Form.Item label="qcloud_appid" name="qappid" rules={[{ required: true }]}>
              <InputNumber style={{ width: '180px', marginRight: 5 }} />
            </Form.Item>
          );
        },
        list: supQappConf,
        options: suppliers,
        addFn: async (vals: any) =>
          type === '1'
            ? await editSupQappConf({ ...supQappConf[0], ...vals })
            : await editMktSupQappConf({ ...supQappConf[0], ...vals }),
      },
      {
        key: '5',
        type: ['1'],
        legendTitle: '账号级供应商运营商配置',
        columns: operators,
        searchRender: () => {
          return (
            <Form.Item label="qcloud_appid" name="qappid" rules={[{ required: true }]}>
              <InputNumber style={{ width: '180px', marginRight: 5 }} />
            </Form.Item>
          );
        },
        list: supQappTelConf,
        options: suppliers,
        addFn: async (vals: any) =>
          await editSupQappTelConf({ ...vals, conf_data: supQappTelConf }),
      },
    ],
    [type, supAppTelConf, supAppConf, supConf, supQappConf, supQappTelConf, supTelConf, suppliers],
  );

  return (
    <PageContainer>
      <Tabs defaultActiveKey="0">
        {tabsInfo.map((item) => {
          if (item.type.includes(type)) {
            return (
              <Tabs.TabPane tab={item.legendTitle} key={item.key}>
                <MonitorFieldset {...item} />
              </Tabs.TabPane>
            );
          }
          return null;
        })}
        <Tabs.TabPane tab="默认调度配置" key="7">
          <DispatchConfig
            legendTitle="默认调度配置"
            columns={[
              {
                title: '类型',
                dataIndex: 'type',
                key: 'type',
                align: 'center',
                render: (itemType: number) => <span>{getTypeText(itemType, type)}</span>,
              },
            ]}
            searchKeys={[
              {
                label: '默认类型',
                name: 'type',
                render: () => {
                  return (
                    <Select
                      options={type === '1' ? configOpts : configMktOpts}
                      style={{ width: 200 }}
                    />
                  );
                },
              },
            ]}
            getFn={async (vals: any) =>
              type === '1'
                ? await getDefaultConf({ ...vals })
                : await getMktDefaultConf({ ...vals })
            }
            setFn={editDefaultFn}
            initialVal={{ type: '' }}
            type="modify"
            showPagination={false}
            configType={type}
          />
        </Tabs.TabPane>
      </Tabs>
    </PageContainer>
  );
};
export default AddDispatchConfig;
