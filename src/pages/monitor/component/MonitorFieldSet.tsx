import React, { useEffect, useState } from 'react';
import { Table, Select, Button, message, Form, InputNumber } from 'antd';

interface FieldProps {
  columns: object[];
  list: object[];
  legendTitle: string;
  options: object[];
  searchRender?: () => React.ReactNode;
  addFn: Function;
}

const MonitorFieldset = ({ columns, list, options, searchRender, addFn }: FieldProps) => {
  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [newCols, setNewCols] = useState<object[]>([]);
  const normalColumns: any = [
    {
      title: '请求量过滤阈值',
      key: 'amount_threshold',
      align: 'center',
      render: (row: any) => {
        return (
          <InputNumber
            defaultValue={row.amount_threshold}
            onChange={(value) => {
              if (value === null) {
                row.amount_threshold = 0;
                return;
              }
              row.amount_threshold = value;
            }}
          />
        );
      },
    },
    {
      title: '请求成功面积阈值',
      key: 'send_succ_rate_threshold',
      align: 'center',
      render: (row: any) => {
        return (
          <InputNumber
            defaultValue={row.send_succ_rate_threshold}
            onChange={(value) => {
              if (value === null) {
                row.send_succ_rate_threshold = 0;
                return;
              }
              row.send_succ_rate_threshold = value;
            }}
          />
        );
      },
    },
    {
      title: '回执面积阈值',
      key: 'callback_rate_threshold',
      align: 'center',
      render: (row: any) => {
        return (
          <InputNumber
            defaultValue={row.callback_rate_threshold}
            onChange={(value) => {
              if (value === null) {
                row.callback_rate_threshold = 0;
                return;
              }
              row.callback_rate_threshold = value;
            }}
          />
        );
      },
    },
    {
      title: '回执失败面积阈值',
      key: 'callback_receipt_rate',
      align: 'center',
      render: (row: any) => {
        return (
          <InputNumber
            defaultValue={row.callback_receipt_rate}
            onChange={(value) => {
              if (value === null) {
                row.callback_receipt_rate = 0;
                return;
              }
              row.callback_receipt_rate = value;
            }}
          />
        );
      },
    },
    {
      title: '启用',
      key: 'enable',
      align: 'center',
      render: (row: any) => {
        return (
          <Select
            defaultValue={row.enable}
            options={[
              { label: '启用', value: 1 },
              { label: '禁用', value: 0 },
            ]}
            onChange={(value) => {
              row.enable = value;
            }}
            style={{ width: 100 }}
          />
        );
      },
    },
  ];

  useEffect(() => {
    setNewCols(columns.concat(normalColumns));
  }, [columns]);

  async function addConfig(vals: any) {
    try {
      setLoading(true);
      const res = await addFn(vals);
      setLoading(false);
      res?.code === 0 && message.success('操作成功');
    } catch (error) {
      setLoading(false);
    }
  }

  return (
    <div className="monitor-field">
      <Form
        form={form}
        layout="inline"
        requiredMark={false}
        labelAlign="right"
        style={{ marginBottom: 10, marginTop: 10 }}
        onFinish={addConfig}
      >
        {searchRender?.()}
        <Form.Item label="供应商ID" name="suplier_ids" rules={[{ required: true }]}>
          <Select
            showSearch
            allowClear
            mode="multiple"
            options={options}
            style={{ minWidth: '200px' }}
            filterOption={(input, option: any) =>
              option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={isLoading}>
            添加/更新
          </Button>
        </Form.Item>
      </Form>
      <Table columns={newCols} dataSource={list} pagination={false} />
    </div>
  );
};
export default MonitorFieldset;
