import React, { useState, useEffect } from 'react';
import {
  getProviderQuality,
  editProviderQuality,
  addProviderQuality,
  delProviderQuality,
} from '@/services/thrdAPI';
import { getGloablProviders } from '@/services/api';
import PatternTable from '@/pages/component/PatternLayout';

const columns: any = [
  {
    title: 'provider',
    dataIndex: 'provider_name',
    key: 'provider_name',
    align: 'center',
  },
  {
    title: '普通-移动',
    dataIndex: 'attr_normal_sms_qc_cmcc',
    key: 'attr_normal_sms_qc_cmcc',
    align: 'center',
  },
  {
    title: '普通-联通',
    dataIndex: 'attr_normal_sms_qc_unicom',
    key: 'attr_normal_sms_qc_unicom',
    align: 'center',
  },
  {
    title: '普通-电信',
    dataIndex: 'attr_normal_sms_qc_telecom',
    key: 'attr_normal_sms_qc_telecom',
    align: 'center',
  },
  {
    title: '营销-移动',
    dataIndex: 'attr_marketing_sms_qc_cmcc',
    key: 'attr_marketing_sms_qc_cmcc',
    align: 'center',
  },
  {
    title: '营销-联通',
    dataIndex: 'attr_marketing_sms_qc_unicom',
    key: 'attr_marketing_sms_qc_unicom',
    align: 'center',
  },
  {
    title: '营销-电信',
    dataIndex: 'attr_marketing_sms_qc_telecom',
    key: 'attr_marketing_sms_qc_telecom',
    align: 'center',
  },
  {
    title: 'app普通-移动',
    dataIndex: 'attr_app_normal_sms_qc_cmcc',
    key: 'attr_app_normal_sms_qc_cmcc',
    align: 'center',
  },
  {
    title: 'app普通-联通',
    dataIndex: 'attr_app_normal_sms_qc_unicom',
    key: 'attr_app_normal_sms_qc_unicom',
    align: 'center',
  },
  {
    title: 'app普通-电信',
    dataIndex: 'attr_app_normal_sms_qc_telecom',
    key: 'attr_app_normal_sms_qc_telecom',
    align: 'center',
  },
  {
    title: 'app营销-移动',
    dataIndex: 'attr_app_marketing_sms_qc_cmcc',
    key: 'attr_app_marketing_sms_qc_cmcc',
    align: 'center',
  },
  {
    title: 'app营销-联通',
    dataIndex: 'attr_app_marketing_sms_qc_unicom',
    key: 'attr_app_marketing_sms_qc_unicom',
    align: 'center',
  },
  {
    title: 'app营销-电信',
    dataIndex: 'attr_app_marketing_sms_qc_telecom',
    key: 'attr_app_marketing_sms_qc_telecom',
    align: 'center',
  },
];

const ProviderQuality = () => {
  const [providers, setProviders] = useState<any>([]);

  const operateForm = getFormItems();

  useEffect(() => {
    getGloablProviders().then((res) => {
      const providersList = (res?.data ?? []).map(
        (item: { provider_id: number; provider_name: string }) => {
          return {
            value: item.provider_id,
            label: item.provider_name,
          };
        },
      );
      setProviders(providersList);
    });
  }, []);

  function getFormItems() {
    const basicForm = columns.map((item: { key: string; title: string }) => {
      return {
        showOnAdd: false,
        name: item.key,
        label: item.title,
        disabled: item.key === 'provider_name',
        hidden: item.key === 'provider_name',
        isRequired: true,
        renderType: item.key === 'provider_name' ? '' : 'number',
      };
    });
    basicForm.unshift({
      showOnAdd: true,
      name: 'provider_id',
      label: 'provider',
      disabled: true,
      isRequired: true,
      renderType: 'select',
      options: providers,
    });
    return basicForm;
  }

  async function doEdit(vals: any) {
    return await editProviderQuality({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addProviderQuality({ ...vals });
  }

  async function doDel(vals: any) {
    return await delProviderQuality({ provider_id: vals.provider_id });
  }
  async function getList(vals?: any) {
    return await getProviderQuality({ ...vals });
  }

  return (
    <PatternTable
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      columns={columns}
      searchKeys={[]}
      operateForm={operateForm}
      operType={1}
    />
  );
};

export default ProviderQuality;
