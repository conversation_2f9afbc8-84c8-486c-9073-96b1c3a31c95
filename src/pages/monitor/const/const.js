export const configOpts = [
  { label: '所有配置类型', value: '' },
  { label: '供应商', value: 1 },
  { label: '供应商运营商', value: 2 },
  { label: '应用供应商', value: 3 },
  { label: '应用供应商运营商', value: 4 },
];
export const configMktOpts = [
  { label: '所有配置类型', value: '' },
  { label: '供应商', value: 1 },
  { label: '供应商运营商', value: 2 },
  { label: '客户供应商', value: 3 },
  { label: '应用供应商', value: 4 },
];

export const teleoperators = [
  { label: '所有运营商', value: '' },
  { label: '中国移动', value: 1 },
  { label: '中国联通', value: 2 },
  { label: '中国电信', value: 3 },
];

export const enableOpts = [
  { label: '查询所有', value: '' },
  { label: '只查询启用', value: 1 },
  { label: '只查询未启用', value: 0 },
];

export function getTelName(key) {
  switch (key) {
    case 1:
      return '中国移动';
    case 2:
      return '中国联通';
    case 3:
      return '中国电信';
    default:
      return '';
  }
}

export function getTypeText(itemType, type) {
  switch (itemType) {
    case 1:
      return '供应商';
    case 2:
      return '供应商运营商';
    case 3:
      return type === '1' ? '应用供应商' : '客户供应商';
    case 4:
      return type === '1' ? '应用供应商运营商' : '应用供应商';
    default:
      return '';
  }
}
