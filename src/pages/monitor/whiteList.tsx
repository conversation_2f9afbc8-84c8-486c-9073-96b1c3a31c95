import React, { useState } from 'react';
import { getSdkappidWhite, addSdkappidWhite, delSdkappidWhite } from '@/services/thrdAPI';
import PatternTable from '@/pages/component/PatternLayout';
import dayjs from 'dayjs';
import _ from 'lodash';
import { Button, DatePicker, Input } from 'antd';

const columns: any = [
  {
    title: 'SDKAPPID',
    dataIndex: 'sdkappid',
    key: 'sdkappid',
    align: 'center',
  },
  {
    title: '导白原因',
    dataIndex: 'comment',
    key: 'comment',
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'created_time',
    key: 'created_time',
    align: 'center',
  },
  {
    title: '到期时间',
    dataIndex: 'expire_time',
    key: 'expire_time',
    align: 'center',
  },
];

const WhiteList = () => {
  const [initVals, setInitVals] = useState<{ expire_time?: dayjs.Dayjs }>({
    expire_time: dayjs().add(1, 'month'),
  });

  const operateForm = [
    {
      showOnAdd: true,
      name: 'sdkappid',
      label: 'SDKAPPID',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'comment',
      label: '导白原因',
      isRequired: true,
      disabled: false,
      render: () => <Input.TextArea style={{ width: 250 }} placeholder="导白原因" />,
    },
    {
      showOnAdd: true,
      name: 'expire_time',
      label: '到期时间',
      isRequired: false,
      disabled: false,
      render: () => (
        <DatePicker
          showTime
          format="YYYY-MM-DD HH:mm:ss"
          placeholder="选择到期时间"
          showNow={false}
          renderExtraFooter={() => (
            <>
              <Button
                type="link"
                onClick={() => setInitVals({ expire_time: dayjs().add(1, 'months') })}
              >
                一个月后
              </Button>
              <Button
                type="link"
                onClick={() => setInitVals({ expire_time: dayjs().add(3, 'months') })}
              >
                三个月后
              </Button>
              <Button
                type="link"
                onClick={() => setInitVals({ expire_time: dayjs().add(0.5, 'years') })}
              >
                半年后
              </Button>
              <Button
                type="link"
                onClick={() => setInitVals({ expire_time: dayjs().add(1, 'years') })}
              >
                一年后
              </Button>
              <Button type="link" onClick={() => setInitVals({ expire_time: undefined })}>
                永久
              </Button>
            </>
          )}
        />
      ),
    },
  ];

  async function doAdd(vals: any) {
    return await addSdkappidWhite(
      _.pickBy(
        {
          ...vals,
          expire_time: vals.expire_time
            ? dayjs(vals.expire_time).format('YYYY-MM-DD HH:mm:ss')
            : '',
        },
        (v) => v !== '',
      ),
    );
  }

  async function doDel(vals: any) {
    return await delSdkappidWhite({ sdkappid: vals.sdkappid });
  }
  async function getList(vals?: any) {
    if (!vals.sdkappid) return;
    const params = {
      sdkappid: vals.sdkappid,
    };
    const res = await getSdkappidWhite({ ...params });
    return {
      data: {
        list: res?.data || [],
        count: res?.data?.length || 0,
      },
    };
  }

  return (
    <PatternTable
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        {
          label: 'sdkappid',
          name: 'sdkappid',
          isRequired: true,
        },
      ]}
      operateForm={operateForm}
      operType={2}
      initialValues={initVals}
    />
  );
};

export default WhiteList;
