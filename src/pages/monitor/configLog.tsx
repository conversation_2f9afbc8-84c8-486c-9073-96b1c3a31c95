import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, DatePicker, Typography, Tag, Select } from 'antd';
import dayjs from 'dayjs';
import { SearchOutlined } from '@ant-design/icons';
import { querySmsConfigLog, queryUserList } from '@/services/api';

const { Paragraph } = Typography;
const { Option } = Select;

const SmsConfigLog: React.FC<{}> = () => {
  const [form] = Form.useForm();
  const [list, setList] = useState<any[]>([]);
  const [count, setCount] = useState<number>(0);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [formData, setFormData] = useState<any>({});
  const [userList, setUserLIst] = useState([]);
  const { RangePicker } = DatePicker;

  const columns: any = [
    {
      title: '记录id',
      dataIndex: 'record_id',
      key: 'record_id',
      align: 'center',
    },
    {
      title: '接口名称',
      render: (row: any) => `${row.route}/${row.action}`,
      align: 'center',
    },
    {
      title: '请求参数json',
      dataIndex: 'req_ext',
      key: 'req_ext',
      render: (req_ext: any) => {
        return (
          <Paragraph
            copyable={{ text: req_ext }}
            ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}
            style={{ wordBreak: 'break-all' }}
          >
            {req_ext}
          </Paragraph>
        );
      },
      align: 'center',
      width: '20%',
    },
    {
      title: '响应参数json',
      dataIndex: 'rsp_ext',
      key: 'rsp_ext',
      render: (rsp_ext: any) => {
        return (
          <Paragraph
            copyable={{ text: rsp_ext }}
            ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}
            style={{ wordBreak: 'break-all' }}
          >
            {rsp_ext}
          </Paragraph>
        );
      },
      align: 'center',
      width: '20%',
    },
    {
      title: '操作时间',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
    },
    {
      title: '操作人',
      dataIndex: 'staff_name',
      key: 'staff_name',
      align: 'center',
    },
    {
      title: '审批人',
      dataIndex: 'apply_operator',
      key: 'apply_operator',
      render: (apply_operator: any) => apply_operator || '-',
      align: 'center',
    },
    {
      title: '审核状态',
      dataIndex: 'apply_status',
      key: 'apply_status',
      render: (apply_status: string) => {
        const mapColor: any = new Map([
          [0, 'orange'],
          [1, 'green'],
          [2, 'red'],
          [3, ''],
        ]);
        const mapText: any = new Map([
          [0, '待审核'],
          [1, '审核通过'],
          [2, '审核未执行'],
          [3, '已执行'],
        ]);
        return apply_status ? (
          <Tag color={mapColor.get(apply_status)}>{mapText.get(apply_status)}</Tag>
        ) : (
          '-'
        );
      },
      align: 'center',
    },
  ];

  useEffect(() => {
    getList(formData);
  }, [pageIndex, pageSize]);

  useEffect(() => {
    getUserList();
  }, []);

  async function getList(params: any) {
    const isEmpty = Object.keys(params).every((key: any) => params[key] === undefined);
    if (isEmpty) return;
    setLoading(true);
    const res = await querySmsConfigLog({
      operator_id: params.operator_id,
      api_id: params.api_id,
      apply_id: params.apply_id,
      search_key: params.search_key,
      from: dayjs(params.time[0]).format('YYYY-MM-DD HH:mm:ss'),
      to: dayjs(params.time[1]).format('YYYY-MM-DD HH:mm:ss'),
      page_index: pageIndex,
      page_size: pageSize,
    });
    setCount(res.data.count);
    setList(res.data.list);
    setLoading(false);
  }
  async function getUserList() {
    const res = await queryUserList({
      page_index: 1,
      page_size: 1000,
    });
    setUserLIst(res.data.list);
  }

  async function onSubmit(vals: any) {
    !vals.operator_id && delete vals.operator_id;
    !vals.api_id && delete vals.api_id;
    !vals.apply_id && delete vals.apply_id;
    !vals.search_key && delete vals.search_key;
    getList({ ...vals });
    setFormData(vals);
  }
  return (
    <PageContainer title="SmsConfig操作日志">
      <Form
        labelCol={{ span: 6 }}
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
        style={{ alignItems: 'center' }}
      >
        <div>
          <Form.Item name="operator_id" style={{ marginBottom: 10 }}>
            <Select
              placeholder="操作人"
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option!.children as unknown as string).toLowerCase().includes(input.toLowerCase())
              }
              style={{ width: 280 }}
            >
              {(userList ?? []).map((el: any) => {
                return (
                  <Option value={el.user_id} key={el.user_id}>
                    {el.staff_name}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="api_id">
            <Input placeholder="接口id" style={{ width: 280 }} />
          </Form.Item>
        </div>
        <div>
          <Form.Item name="apply_id" style={{ marginBottom: 10 }}>
            <Input placeholder="审核单id" style={{ width: 280 }} />
          </Form.Item>
          <Form.Item name="search_key">
            <Input placeholder="关键字" style={{ width: 280 }} />
          </Form.Item>
        </div>
        <Form.Item name="time" rules={[{ required: true, message: '请选择时间' }]}>
          <RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={isLoading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={list}
        rowKey={(record) => record.id}
        loading={isLoading}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPageSize(page_size),
          onChange: (page) => {
            setPageIndex(page);
          },
        }}
        style={{ marginTop: 20 }}
      />
    </PageContainer>
  );
};

export default SmsConfigLog;
