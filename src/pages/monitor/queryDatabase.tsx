import React, { useState } from 'react';
import { getSmsLog, getAntiBomb } from '@/services/thrdAPI';
import { SearchOutlined } from '@ant-design/icons';
import PatternTable from '@/pages/component/PatternLayout';
import { PageContainer } from '@ant-design/pro-layout';
import { DatePicker, Tabs, Table, Form, InputNumber, Button } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';

const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

const columns: any = [
  {
    title: '号码',
    dataIndex: 'mobile',
    key: 'mobile',
    align: 'center',
  },
  {
    title: '数量',
    dataIndex: 'count',
    key: 'count',
    align: 'center',
  },
  {
    title: '时间',
    dataIndex: 'time',
    key: 'time',
    align: 'center',
  },
];
const webColumns: any = [
  {
    title: '请求时间',
    dataIndex: 'ftime',
    key: 'ftime',
    align: 'center',
  },
  {
    title: 'skdappid',
    dataIndex: 'dwsdkappid',
    key: 'dwsdkappid',
    align: 'center',
  },
  {
    title: '请求量',
    dataIndex: 'req',
    key: 'req',
    align: 'center',
  },
  {
    title: '请求成功量',
    dataIndex: 'req_succ',
    key: 'req_succ',
    align: 'center',
  },
  {
    title: '去重下发手机号码量',
    dataIndex: 'dis_num',
    key: 'dis_num',
    align: 'center',
  },
  {
    title: '重复度',
    dataIndex: 'pro',
    key: 'pro',
    align: 'center',
  },
  {
    title: '下发1条号码量',
    dataIndex: 'count_1',
    key: 'count_1',
    align: 'center',
  },
  {
    title: '下发2条号码量',
    dataIndex: 'count_2',
    key: 'count_2',
    align: 'center',
  },
  {
    title: '下发3条号码量',
    dataIndex: 'count_3',
    key: 'count_3',
    align: 'center',
  },
  {
    title: '下发4条号码量',
    dataIndex: 'count_4',
    key: 'count_4',
    align: 'center',
  },
  {
    title: '下发5条号码量',
    dataIndex: 'count_5',
    key: 'count_5',
    align: 'center',
  },
  {
    title: '下发6条号码量',
    dataIndex: 'count_6',
    key: 'count_6',
    align: 'center',
  },
  {
    title: '下发7条号码量',
    dataIndex: 'count_7',
    key: 'count_7',
    align: 'center',
  },
  {
    title: '下发8条号码量',
    dataIndex: 'count_8',
    key: 'count_8',
    align: 'center',
  },
  {
    title: 'tag',
    dataIndex: 'tag',
    key: 'tag',
    align: 'center',
  },
];

const QueryDatabase = () => {
  const [list, setList] = useState<any>([]);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [tabKey, setTabKey] = useState<string>('1');
  const [initTime] = useState({ time: [dayjs().subtract(2, 'hours'), dayjs()], count: 5 });

  const searchKeys = [
    {
      label: 'sdkappid',
      name: 'sdkappid',
      renderType: 'number',
      isRequired: true,
    },
    {
      label: '起止时间',
      name: 'time',
      render: () => <RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />,
      isRequired: true,
    },
  ];

  async function getLogList(vals?: any) {
    const params: {
      sdkappid: number;
      start_time: string;
      end_time: string;
      count?: number;
    } = {
      sdkappid: vals.sdkappid,
      start_time: dayjs(vals.time[0]).format('YYYY-MM-DD HH:mm:ss'),
      end_time: dayjs(vals.time[1]).format('YYYY-MM-DD HH:mm:ss'),
      count: vals.count,
    };
    const isFull = _.every(params, (item) => !_.isNil(item));
    if (!isFull) return;
    setLoading(true);
    const res = await getSmsLog({ ...params });
    setList(res?.data || []);
    setLoading(false);
  }

  async function getList(vals: any) {
    if (!vals.sdkappid || !vals.time) return;
    const params = {
      sdkappid: vals.sdkappid,
      start_time: dayjs(vals.time[0]).format('YYYY-MM-DD HH:mm:ss'),
      end_time: dayjs(vals.time[1]).format('YYYY-MM-DD HH:mm:ss'),
    };
    const res = await getAntiBomb({ ...params });
    return {
      data: {
        list: res?.data || [],
        count: res?.data?.length || 0,
      },
    };
  }
  return (
    <>
      <PageContainer title="查询sms日志（限定最多两小时）" style={{ marginBottom: 50 }}>
        <Form
          layout="inline"
          requiredMark={false}
          labelAlign="right"
          onFinish={(vals) => {
            getLogList(vals);
          }}
          initialValues={{ ...initTime }}
        >
          <Form.Item name="sdkappid" label="sdkappid" rules={[{ required: true }]}>
            <InputNumber style={{ width: 250 }} />
          </Form.Item>
          <Form.Item name="time" label="起止时间" rules={[{ required: true }]}>
            <RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>
          <Form.Item
            name="count"
            label="数量筛选(单号码下发数低于此值不做展示, 最低值: 2)"
            rules={[{ required: true }]}
          >
            <InputNumber style={{ width: 250 }} />
          </Form.Item>
          <Form.Item>
            <Button htmlType="submit" type="primary" loading={isLoading} icon={<SearchOutlined />}>
              查询
            </Button>
          </Form.Item>
        </Form>
        <Tabs
          defaultActiveKey={tabKey}
          onChange={(key) => {
            setTabKey(key);
          }}
        >
          <TabPane tab="smslog" key="smslog">
            <Table columns={columns} loading={isLoading} dataSource={list.smslog} />
          </TabPane>
          <TabPane tab="smslog_large" key="smslog_large">
            <Table columns={columns} loading={isLoading} dataSource={list.smslog_large} />
          </TabPane>
        </Tabs>
      </PageContainer>
      <PatternTable
        breadcrumbRender={false}
        title="查询防刷数据"
        getFn={async (vals: object) => {
          return await getList(vals);
        }}
        columns={webColumns}
        searchKeys={searchKeys}
        searchInitials={{ ...initTime }}
        hideShowAdd={true}
        operateForm={[]}
        operType={3}
      />
    </>
  );
};

export default QueryDatabase;
