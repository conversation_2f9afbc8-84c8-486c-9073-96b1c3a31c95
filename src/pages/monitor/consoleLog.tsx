import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, DatePicker, Typography } from 'antd';
import dayjs from 'dayjs';
import { SearchOutlined } from '@ant-design/icons';
import { queryConsoleLog } from '@/services/api';

const { Paragraph } = Typography;

const ConsoleLog: React.FC<{}> = () => {
  const [form] = Form.useForm();
  const [list, setList] = useState<any[]>([]);
  const [count, setCount] = useState<number>(0);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [formConfig, setFormConfig] = useState<any>({});
  const { RangePicker } = DatePicker;

  const columns: any = [
    {
      title: '应用id',
      dataIndex: 'bizid',
      key: 'bizid',
      align: 'center',
    },
    {
      title: '腾讯云账号uin',
      dataIndex: 'uin',
      key: 'uin',
      align: 'center',
    },
    {
      title: '操作接口名',
      dataIndex: 'oper',
      key: 'oper',
      align: 'center',
    },
    {
      title: '请求参数json',
      dataIndex: 'req',
      key: 'req',
      render: (req: any) => {
        return (
          <Paragraph
            copyable={{ text: req }}
            ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}
            style={{ wordBreak: 'break-all' }}
          >
            {req}
          </Paragraph>
        );
      },
      align: 'center',
      width: '35%',
    },
    {
      title: '操作时间',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
    },
  ];

  useEffect(() => {
    getList(formConfig);
  }, [pageIndex, pageSize]);
  async function getList(params: any) {
    const isEmpty = Object.keys(params).every((key: any) => params[key] === undefined);
    if (isEmpty) return;
    setLoading(true);
    const res = await queryConsoleLog({
      sdkappid: params.sdkappid,
      search_key: params.search_key,
      from: dayjs(params.time[0]).format('YYYY-MM-DD HH:mm:ss'),
      to: dayjs(params.time[1]).format('YYYY-MM-DD HH:mm:ss'),
      page_index: pageIndex,
      page_size: pageSize,
    });
    setCount(res.data.count);
    setList(res.data.list);
    setLoading(false);
  }

  async function onSubmit(vals: any) {
    !vals.sdkappid && delete vals.sdkappid;
    !vals.search_key && delete vals.search_key;
    getList({ ...vals });
    setFormConfig(vals);
  }
  return (
    <PageContainer title="控制台操作日志">
      <Form
        labelCol={{ span: 6 }}
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="sdkappid">
          <Input placeholder="sdkappid" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item name="search_key">
          <Input placeholder="关键字" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item name="time" rules={[{ required: true, message: '请选择时间' }]}>
          <RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={isLoading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={list}
        rowKey={(record) => record.id}
        loading={isLoading}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPageSize(page_size),
          onChange: (page) => {
            setPageIndex(page);
          },
        }}
        style={{ marginTop: 20 }}
      />
    </PageContainer>
  );
};

export default ConsoleLog;
