import React from 'react';
import { getVipAlarm, addVipAlarm, delVipAlarm } from '@/services/thrdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const columns: any = [
  {
    title: 'app_name',
    dataIndex: 'appname',
    key: 'appname',
    align: 'center',
  },
  {
    title: 'qcloudappid',
    dataIndex: 'qappid',
    key: 'qappid',
    align: 'center',
  },
  {
    title: 'sdkappid',
    dataIndex: 'sdkappid',
    key: 'sdkappid',
    align: 'center',
  },
  {
    title: 'URL',
    dataIndex: 'url',
    key: 'url',
    align: 'center',
  },
  {
    title: 'created_time',
    dataIndex: 'created_time',
    key: 'created_time',
    align: 'center',
  },
];

const VipConfig = () => {
  const operateForm = [
    {
      showOnAdd: true,
      name: 'qappid',
      label: 'qappid',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'appname',
      label: 'appname',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'url',
      label: 'url(企业微信机器人)',
      disabled: false,
      isRequired: true,
    },
  ];

  async function doAdd(vals: any) {
    return await addVipAlarm({ ...vals });
  }

  async function doDel(vals: any) {
    return await delVipAlarm({ appid: vals.sdkappid, type: 'sdkappid' });
  }
  async function getList(vals?: any) {
    delete vals.page_size;
    delete vals.page_index;
    const res = await getVipAlarm({ ...vals });
    return {
      data: {
        list: res?.data || [],
        count: res?.data?.length || 0,
      },
    };
  }

  return (
    <PatternTable
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        {
          label: 'qcloudappid',
          name: 'qappid',
          renderType: 'number',
        },
        {
          label: 'sdkappid',
          name: 'sdkappid',
          renderType: 'number',
        },
      ]}
      operateForm={operateForm}
      operType={2}
    />
  );
};

export default VipConfig;
