import React, { useRef, useState } from 'react';
import {
  getCredentialAuthList,
  updateCredentialAuth,
  updateCredentialAuthStatus,
} from '@/services/enterprise';
import { Button, Form, Input, message, Popconfirm } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { notShowButtonStatus, statusType } from './const';
import { BetaSchemaForm } from '@ant-design/pro-components';
import _ from 'lodash';

const usedMethodOptions = [
  {
    label: '自用',
    value: '自用',
  },
  {
    label: '他用',
    value: '他用',
  },
];

const AuditList = () => {
  const ref = useRef<ActionType>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState({});

  async function updateStatus(id: string) {
    const res = await updateCredentialAuthStatus({ id });
    if (res?.code === 0) {
      message.success('撤回成功');
      ref?.current?.reload();
    }
  }

  function handleEdit(record: any) {
    const params = {
      used_method: record.used_method,
      id: record.id,
      company_name: record.company_name,
    };
    setInitialValues(params);
    setOpen(true);
    addFormRef.current?.setFieldsValue(params);
  }

  async function onFinish(values: any) {
    const res = await updateCredentialAuth({ ...values });
    if (res?.code === 0) {
      message.success('编辑成功');
      ref?.current?.reload();
      addFormRef.current?.setFieldsValue(initialValues);
      setOpen(false);
    }
  }

  const columns = [
    {
      title: '资质ID',
      key: 'id',
      align: 'center',
      dataIndex: 'id',
    },
    {
      title: '资质名称',
      key: 'credential_name',
      align: 'center',
      dataIndex: 'credential_name',
      search: false,
      editable: false,
    },
    {
      title: '资质属性',
      key: 'used_method',
      align: 'center',
      valueType: 'select',
      dataIndex: 'used_method',
      search: false,
      fieldProps: {
        options: usedMethodOptions,
      },
      // editable: false,
    },
    {
      title: '公司名称',
      key: 'company_name',
      align: 'center',
      dataIndex: 'company_name',
    },
    {
      title: '申请时间',
      key: 'created_at',
      align: 'center',
      dataIndex: 'created_at',
      search: false,
      editable: false,
    },
    {
      title: '审批时间',
      key: 'updated_at',
      align: 'center',
      dataIndex: 'updated_at',
      search: false,
      editable: false,
    },
    {
      title: '状态',
      key: 'status',
      align: 'center',
      valueType: 'select',
      dataIndex: 'status',
      search: false,
      fieldProps: {
        options: statusType.map((item) => ({ label: item.text, value: item.value })),
      },
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      render: (_, record: any) => {
        return (
          <>
            <Button type="link" onClick={() => handleEdit(record)}>
              修改资质信息
            </Button>
            {!notShowButtonStatus.includes(record.status) ? (
              <Popconfirm
                title="确定撤回审核吗?"
                onConfirm={() => updateStatus(record.id)}
                okText="确认"
                cancelText="取消"
              >
                <Button key={record.id} size="small" type="primary" danger>
                  撤回审核
                </Button>
              </Popconfirm>
            ) : null}
          </>
        );
      },
    },
  ];

  const formItems = _.cloneDeep(columns)
    .filter((el: any) => ['id', 'used_method', 'company_name'].includes(el.key))
    .map((el) => {
      if (el.key === 'id') {
        return {
          ...el,
          fieldProps: { disabled: true },
        };
      }
      return el;
    });

  return (
    <PageContainer>
      <BetaSchemaForm
        title="编辑资质属性"
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 4 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={450}
        initialValues={initialValues}
      />
      <ProTable
        onRequestError={() => {
          message.error('查询失败');
        }}
        actionRef={ref}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          optionRender: (row, config, defaultDom) => {
            return [defaultDom[1]];
          },
          collapseRender: false,
        }}
        options={false}
        columns={columns}
        request={async (params) => {
          if (!params.id) return { data: [], success: true };
          const { data } = await getCredentialAuthList({
            id: params.id,
            page_size: 1,
            page_index: 1,
          });
          return {
            data: data.list ?? [],
            success: true,
            total: data.count,
          };
        }}
        pagination={false}
      />
    </PageContainer>
  );
};
export default AuditList;
