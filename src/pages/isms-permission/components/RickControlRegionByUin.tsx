/* eslint-disable @typescript-eslint/no-use-before-define */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  addRickControlRegionUin,
  delRickControlRegionUin,
  getRickControlRegionUinList,
} from '@/services/ismsPermission';
import { ActionType, BetaSchemaForm, ProTable } from '@ant-design/pro-components';
import { Button, Modal } from 'antd';
import _ from 'lodash';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { rickControlOptions, RickControlType } from './const';

const RickControlRegionByUin = () => {
  const actionRef = useRef<ActionType>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const type = useRef(RickControlType.WHITELIST);
  const { regionOptions } = useFetchCountryInfo();
  const regionWithCNOptions = regionOptions?.concat({
    label: '中国大陆_CN',
    value: 'CN',
  });

  const columns: any = useMemo(
    () => [
      {
        title: 'uin',
        key: 'uin',
        dataIndex: 'uin',
        valueType: 'textarea',
        formItemProps: { rules: [{ required: true }] },
        fieldProps: {
          controls: false,
          style: { width: '100%' },
          placeholder: '请输入uin，每行一个',
        },
        transform: (value: string) => ({
          uins: value
            .split('\n')
            .map((v) => v.trim())
            .filter((v) => v),
        }),
        width: 180,
        render: (_: unknown, { uin }: any) => (uin === 0 ? '-' : `${uin}`),
      },
      {
        title: '国家/地区',
        key: 'country_code',
        valueType: 'select',
        dataIndex: 'country_code',
        fieldProps: { options: regionWithCNOptions, showSearch: true },
        formItemProps: { rules: [{ required: true }] },
      },
      {
        title: '类型',
        key: 'type',
        dataIndex: 'type',
        valueType: 'select',
        hideInSearch: true,
        hideInForm: true,
        fieldProps: { options: rickControlOptions },
      },
      {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time',
        hideInSearch: true,
        hideInForm: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        hideInForm: true,
        fixed: 'right',
        render: (_: unknown, row: any) => {
          return (
            <Button type="link" onClick={() => handleDelete(row)}>
              删除
            </Button>
          );
        },
      },
    ],
    [regionWithCNOptions],
  );
  const formItems = columns
    .filter((item: any) => ['country_code', 'uin'].includes(item.key))
    .map((item: any) => {
      if (item.key === 'country_code')
        return {
          ...item,
          fieldProps: { ...item.fieldProps, mode: 'multiple', options: regionWithCNOptions },
        };
      else return item;
    });
  async function onFinish(vals: any) {
    try {
      const res = await addRickControlRegionUin({
        uins: vals.uins,
        country_codes: vals.country_code,
      });
      if (res.code === 0) {
        actionRef.current?.reload();
        reset();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  function reset() {
    addFormRef.current?.setFieldsValue({
      country_code: [],
      uin: undefined,
    });
  }
  const requestFn = useCallback(async (params: any) => {
    const { data } = await getRickControlRegionUinList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      uin: params.uins,
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);
  async function handleDelete(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除该条数据吗?`,
        onOk: async () => {
          const res = await delRickControlRegionUin({
            country_code: row.country_code,
            uin: row.uin,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }
  useEffect(() => {
    reset();
  }, [open]);

  return (
    <>
      <BetaSchemaForm
        title={'新增UIN名单配置'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 6 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={500}
      />
      <Button
        type="primary"
        onClick={() => {
          setOpen(true);
        }}
        style={{ marginBottom: 20, marginRight: 20 }}
      >
        新增UIN名单配置
      </Button>
      <ProTable
        actionRef={actionRef}
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          labelWidth: 'auto',
        }}
        rowKey={({ country_code, uin }) => country_code + uin}
        columns={columns}
        request={requestFn}
        options={false}
      />
    </>
  );
};
export default RickControlRegionByUin;
