export enum RickControlType {
  WHITELIST,
  BLACKLIST,
}
export const rickControlOptions = [
  {
    label: '白名单',
    value: RickControlType.WHITELIST,
  },
  {
    label: '黑名单',
    value: RickControlType.BLACKLIST,
  },
];
export const validateNumericLinesWithoutSpaces = (inputText: string) => {
  return new Promise((resolve, reject) => {
    const lines = inputText.split('\n');
    for (let line of lines) {
      if (!/^\d+$/.test(line.trim()) || line.includes(' ')) {
        reject(''); // 如果某一行不是纯数字或包含空格，校验失败
      }
    }
    resolve('');
  });
};
