import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  addRickControlRegion,
  delRickControlRegion,
  getRickControlRegionList,
} from '@/services/ismsPermission';
import { ActionType, BetaSchemaForm, ProTable } from '@ant-design/pro-components';
import { Button, Modal } from 'antd';
import _ from 'lodash';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { rickControlOptions, RickControlType } from './const';
import { findLabel } from '@/utils/utils';

const RickControlRegionByCountry = () => {
  const actionRef = useRef<ActionType>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const { regionOptions } = useFetchCountryInfo();
  const addCNOptions = regionOptions?.concat({
    label: '中国大陆_CN',
    value: 'CN',
  });
  const columns: any = useMemo(
    () => [
      {
        title: '国家/地区',
        key: 'country_code',
        valueType: 'select',
        dataIndex: 'country_code',
        fieldProps: { options: addCNOptions, showSearch: true },
        formItemProps: { rules: [{ required: true }] },
      },
      {
        title: '类型',
        key: 'type',
        dataIndex: 'type',
        valueType: 'select',
        formItemProps: { rules: [{ required: true }] },
        fieldProps: { options: rickControlOptions },
      },
      {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time',
        hideInSearch: true,
        hideInForm: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        hideInForm: true,
        fixed: 'right',
        render: (_: any, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleDelete(row)}>
                删除
              </Button>
            </>
          );
        },
      },
    ],
    [addCNOptions],
  );
  const formItems = columns
    .filter((item: any) => ['country_code', 'type'].includes(item.key))
    .map((item: any) => {
      if (item.key === 'country_code')
        return { ...item, fieldProps: { ...item.fieldProps, mode: 'multiple' } };
      else return item;
    });
  async function onFinish(vals: any) {
    try {
      if (vals.type === RickControlType.WHITELIST && vals.country_code.includes('RU')) {
        Modal.confirm({
          title: '警告',
          content: '敏感操作，需提供详细说明（开通俄罗斯地域白名单需提供申请方gm审批凭证） ',
          onOk: async () => {
            await handleAddRegion(vals);
          },
        });
      } else {
        await handleAddRegion(vals);
      }
    } catch (err) {
      console.log(err);
    }
  }

  async function handleAddRegion(vals: any) {
    const res = await addRickControlRegion({
      ...vals,
      country_codes: vals.country_code,
    });
    if (res.code === 0) {
      setOpen(false);
      actionRef.current?.reload();
      reset();
    }
  }

  function reset() {
    addFormRef.current?.setFieldsValue({
      country_code: [],
      type: undefined,
    });
  }
  const requestFn = useCallback(async (params: any) => {
    const { data } = await getRickControlRegionList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);
  async function handleDelete(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: (
          <p>
            确定删除该条数据吗?
            <br />
            将删除配置的所有{findLabel(addCNOptions as any, row.country_code)}
            {row.type === RickControlType.WHITELIST ? '白名单' : '黑名单'}
          </p>
        ),
        onOk: async () => {
          const res = await delRickControlRegion({
            country_code: row.country_code,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }
  useEffect(() => {
    reset();
  }, [open]);
  return (
    <>
      <BetaSchemaForm
        title="新增"
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 6 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={500}
      />
      <Button
        type="primary"
        onClick={() => {
          setOpen(true);
        }}
        style={{ marginBottom: 20 }}
      >
        新增
      </Button>
      <ProTable
        actionRef={actionRef}
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        rowKey="country_code"
        columns={columns}
        request={requestFn}
        options={false}
      />
    </>
  );
};
export default RickControlRegionByCountry;
