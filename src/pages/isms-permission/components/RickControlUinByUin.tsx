import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  addRickControlUinRegion,
  delRickControlUinRegion,
  getRickControlUinRegionList,
} from '@/services/ismsPermission';
import { ActionType, BetaSchemaForm, ProTable } from '@ant-design/pro-components';
import { Button, Modal } from 'antd';
import _ from 'lodash';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { rickControlOptions, RickControlType, validateNumericLinesWithoutSpaces } from './const';
import SelectOptionsByInput from '@/components/SelectOptionsByInput';

const RickControlUinByUin = () => {
  const actionRef = useRef<ActionType>();
  const addFormRef = useRef<any>();
  const type = useRef(RickControlType.WHITELIST);
  const [selectItems, setSelectItems] = useState<any[]>([]);
  const [open, setOpen] = useState(false);

  const { regionOptions } = useFetchCountryInfo();
  const addCNOptions = regionOptions?.concat({
    label: '中国大陆_CN',
    value: 'CN',
  });
  const columns: any = useMemo(
    () => [
      {
        title: 'uin',
        key: 'uin',
        dataIndex: 'uin',
        valueType: 'textarea',
        formItemProps: {
          rules: [
            {
              required: true,
              message: '必填，uin由数字组成，使用换行分割并且不能有空格',
              validator: (_: unknown, value: string) => validateNumericLinesWithoutSpaces(value),
            },
          ],
        },
        width: 200,
        fieldProps: { placeholder: '请输入uin' },
        render: (_: unknown, { uin }: any) => (uin === 0 ? '-' : `${uin}`),
      },
      {
        title: '类型',
        key: 'type',
        dataIndex: 'type',
        valueType: 'select',
        hideInForm: true,
        formItemProps: { rules: [{ required: true }] },
        fieldProps: { options: rickControlOptions },
      },
      {
        title: '国家/地区',
        key: 'country_code',
        valueType: 'select',
        dataIndex: 'country_code',
        fieldProps: {
          showSearch: true,
          showAll: false,
          mode: 'default',
          options: addCNOptions,
        },
        renderFormItem: () => <SelectOptionsByInput options={addCNOptions} />,
        formItemProps: { rules: [{ required: true }] },
      },
      {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time',
        hideInSearch: true,
        hideInForm: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        hideInForm: true,
        fixed: 'right',
        render: (_: unknown, { uin, country_code }: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleDelete([{ uin, country_code }])}>
                删除
              </Button>
            </>
          );
        },
      },
    ],
    [addCNOptions],
  );
  const formItems = columns
    .filter((item: any) => ['country_code', 'uin'].includes(item.key))
    .map((item: any) => {
      if (item.key === 'country_code') {
        return {
          ...item,
          fieldProps: { ...item.fieldProps, mode: 'multiple', maxTagCount: 20, showAll: true },
        };
      }
      return item;
    });
  async function onFinish(vals: any) {
    try {
      const res = await addRickControlUinRegion({
        // ...vals,
        uins: vals.uin.split('\n').map((v: string) => Number(v.trim())),
        country_codes: vals.country_code,
        type: type.current,
      });
      if (res.code === 0) {
        actionRef.current?.reload();
        reset();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  function reset() {
    addFormRef.current?.setFieldsValue({
      country_code: undefined,
      uin: undefined,
    });
  }
  useEffect(() => {
    reset();
  }, [open]);
  const requestFn = useCallback(async (params: any) => {
    const { data } = await getRickControlUinRegionList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);
  async function handleDelete(data: any[], cb?: Function) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除所选数据吗?`,
        onOk: async () => {
          const res = await delRickControlUinRegion({
            params: data,
          });
          if (res.code === 0) {
            cb?.();
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }

  return (
    <>
      <BetaSchemaForm
        title={type.current === RickControlType.WHITELIST ? '新增白名单' : '新增黑名单'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 6 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={500}
      />
      <Button
        type="primary"
        onClick={() => {
          type.current = RickControlType.WHITELIST;
          setOpen(true);
        }}
        style={{ margin: '20px 20px 20px 0' }}
      >
        新增白名单
      </Button>
      <Button
        type="primary"
        onClick={() => {
          type.current = RickControlType.BLACKLIST;
          setOpen(true);
        }}
        style={{ marginBottom: 20 }}
      >
        新增黑名单
      </Button>
      <ProTable
        rowSelection={{
          onChange: (_, selectedRows: any[]) => {
            setSelectItems(selectedRows.map((v) => ({ uin: v.uin, country_code: v.country_code })));
          },
        }}
        actionRef={actionRef}
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          optionRender: (_, formProps, dom) => {
            return [
              ...dom,
              <Button
                key="delete"
                type="primary"
                danger
                disabled={selectItems.length === 0}
                onClick={() => {
                  handleDelete(selectItems, () => {
                    setSelectItems([]);
                  });
                }}
              >
                批量删除
              </Button>,
            ];
          },
        }}
        rowKey={({ uin, country_code }) => uin + country_code}
        columns={columns}
        request={requestFn}
        options={false}
      />
    </>
  );
};
export default RickControlUinByUin;
