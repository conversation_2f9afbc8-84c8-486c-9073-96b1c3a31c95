import { PageContainer } from '@ant-design/pro-components';
import RickControlUinByUin from './components/RickControlUinByUin';
import { Alert } from 'antd';

const RickControlUin = () => {
  return (
    <PageContainer>
      <Alert
        message={
          <span>
            黑名单: 列表中地区/国家不可下发，其余国家/地区均可下发。
            <br />
            白名单: 仅在列表中国家可以下发，其余国家均不可下发。
          </span>
        }
        type="info"
        showIcon
      />
      <RickControlUinByUin />
    </PageContainer>
  );
};
export default RickControlUin;
