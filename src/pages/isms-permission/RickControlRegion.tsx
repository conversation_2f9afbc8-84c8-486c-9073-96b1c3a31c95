import React from 'react'; // 添加此行以解决错误
import { PageContainer } from '@ant-design/pro-components';
import { Alert, Tabs, TabsProps } from 'antd';
import RickControlRegionByCountry from './components/RickControlRegionByCountry';
import RickControlRegionByUin from './components/RickControlRegionByUin';

const RickControlRegion = () => {
  const items: TabsProps['items'] = [
    {
      key: 'uin',
      label: 'UIN配置',
      destroyInactiveTabPane: true,
      children: <RickControlRegionByUin />,
    },
    {
      key: 'country',
      label: '国家配置',
      children: <RickControlRegionByCountry />,
    },
  ];

  const onChange = (key: string) => {
    console.log(key);
  };

  return (
    <PageContainer>
      <Alert
        message={
          <span>
            1.全局黑名单国家：绑定的uin该地区不可下发，其他uin允许下发
            <br />
            2.全局白名单国家：绑定的uin该地区可下发，其他uin不允许下发
          </span>
        }
        type="info"
        showIcon
      />
      <Tabs defaultActiveKey="uin" items={items} onChange={onChange} />
    </PageContainer>
  );
};
export default RickControlRegion;
