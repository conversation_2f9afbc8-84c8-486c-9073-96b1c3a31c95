import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Button, message, Modal, Select, Input } from 'antd';
import {
  ActionType,
  BetaSchemaForm,
  PageContainer,
  ProFormColumnsType,
  ProTable,
} from '@ant-design/pro-components';
import _ from 'lodash';
import {
  addUpAppConfig,
  deleteUpAppConfig,
  getUpAppConfigList,
  querySenderIdResource,
} from '@/services/upApp';
import { findLabel } from '@/utils/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import SelectOptionsByInput from '@/components/SelectOptionsByInput';

export type DataItem = {
  name: string;
  state: string;
};

const addFormFields = ['sdkappid', 'sender_id', 'country_code_sender', 'is_charge', 'country_code']; // 按照排列顺序
const UpAppConfig = () => {
  const actionRef = useRef<ActionType>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const { regionOptions = [] } = useFetchCountryInfo();
  const [countryOptions, setCountryOptions] = useState<any>([]);
  const [selectLoading, setSelectLoading] = useState<boolean>(false);
  const getSenderIdResource = useCallback(
    async ({ sender_id }: { sender_id: string }) => {
      try {
        setSelectLoading(true);
        const { code, data } = await querySenderIdResource({
          sender_id,
        });
        if (code !== 0) {
          return;
        }
        const list = data?.map((v: any) => v.country_code) ?? [];
        const uniqueList = _.uniq(list);
        const mapOptions = uniqueList.map((code) => ({
          label: findLabel(regionOptions, code) || code,
          value: code,
        }));
        setCountryOptions(() => mapOptions);
        if (uniqueList.length) {
          addFormRef.current?.setFieldsValue({ country_code_sender: uniqueList[0] });
        }
      } catch (e: any) {
      } finally {
        setSelectLoading(false);
      }
    },
    [regionOptions],
  );

  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const disableBatchDelete = selectedRowKeys.length === 0;

  function handleDelete(selected: any[]) {
    Modal.confirm({
      title: '提示',
      content: `确定删除已选择的${selected.length}条上行码号配置吗?`,
      onOk: async () => {
        const res = await deleteUpAppConfig({
          id: selected,
        });
        if (res.code === 0) {
          actionRef.current?.reload();
        }
      },
    });
  }

  const columns: ProFormColumnsType[] = useMemo(
    () => [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        hideInSearch: true,
        hideInForm: true,
      },
      {
        title: 'UIN',
        dataIndex: 'uin',
        key: 'uin',
        hideInForm: true,
      },
      {
        title: 'SDKAPPID',
        dataIndex: 'sdkappid',
        key: 'sdkappid',
        formItemProps: { rules: [{ required: true }] },
        fieldProps: { controls: false, min: 0, precision: 0 },
      },
      {
        title: '客户名称',
        dataIndex: 'cust_name',
        key: 'cust_name',
        hideInSearch: true,
      },
      {
        title: '上行码号',
        dataIndex: 'sender_id',
        key: 'sender_id',
        formItemProps: {
          rules: [{ required: true }],
        },
        renderFormItem: () => {
          return (
            <Input
              onBlur={async (e) => {
                if (e.target.value.trim() === '') {
                  return;
                }
                const sender_id = e.target.value;
                getSenderIdResource({ sender_id });
              }}
            />
          );
        },
      },
      {
        title: '供应商简称',
        dataIndex: 'supplier_abbr_names',
        key: 'supplier_abbr_names',
        hideInSearch: true,
        render: (_, { supplier_abbr_names }: any) => supplier_abbr_names?.join('|'),
      },
      {
        title: '账号ID+名称',
        dataIndex: 'supplier_accounts',
        key: 'supplier_accounts',
        hideInSearch: true,
        render: (_, { supplier_accounts }: any) => supplier_accounts?.join('|'),
      },
      {
        title: '上行码号归属国家',
        dataIndex: 'country_code_sender',
        key: 'country_code_sender',
        valueType: 'select',
        formItemProps: { rules: [{ required: true }] },
        fieldProps: {
          loading: selectLoading,
          options: countryOptions ?? [],
          placeholder: '请选择国家',
          showSearch: true,
          disabled: countryOptions.length === 1,
        },
        // hideInForm: true,
        hideInSearch: true,
        render: (_, { country_code_sender }: any) => findLabel(regionOptions, country_code_sender),
      },
      {
        title: '业务发送国家',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        fieldProps: {
          style: { width: 220 },
          showSearch: true,
          options: regionOptions,
          placeholder: '请选择国家',
          mode: 'multiple',
        },
        renderFormItem: ({ type }) => {
          if (type === 'table') {
            return (
              <Select
                mode={undefined}
                filterOption={(inputValue, option: any) =>
                  !!option?.label.toLowerCase().includes(inputValue.toLocaleLowerCase())
                }
                allowClear
              />
            );
          } else {
            return <SelectOptionsByInput />;
          }
        },
        render: (_, { country_code }: any) => findLabel(regionOptions, country_code),
      },
      {
        title: '是否收费',
        dataIndex: 'is_charge',
        key: 'is_charge',
        valueType: 'select',
        formItemProps: { rules: [{ required: true }] },
        fieldProps: {
          placeholder: '请选择',
          options: [
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ],
        },

        render: (_, { is_charge }) => {
          return is_charge ? '是' : '否';
        },
        hideInSearch: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        render: (text, row: any) => {
          return (
            <>
              <Button
                type="link"
                onClick={() => {
                  handleDelete([row.id]);
                }}
              >
                删除
              </Button>
            </>
          );
        },
      },
    ],
    [regionOptions, addFormRef, countryOptions, selectLoading],
  );
  const formItems = addFormFields
    .map((key) => columns.find((item) => item.key === key))
    .filter((item) => item !== undefined);

  const requestFn = useCallback(async (params: any) => {
    setSelectedRowKeys([]);
    const { data } = await getUpAppConfigList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish({
    sdkappid,
    sender_id,
    country_code,
    is_charge,
    country_code_sender,
  }: any) {
    try {
      const res = await addUpAppConfig({
        sdkappid,
        sender_id,
        country_codes: country_code,
        is_charge,
        country_code_sender,
      });
      if (res.code === 0) {
        actionRef.current?.reload();
        message.success('操作成功');
        setOpen(false);
        setCountryOptions([]);
        setSelectedRowKeys([]);
        return true;
      }
      return false;
    } catch (err) {
      return false;
    }
  }
  function reset() {
    const vals = _.reduce(
      addFormFields,
      (acc: { [key: string]: any }, key: string) => ({
        ...acc,
        [key]: undefined,
      }),
      {},
    );
    addFormRef.current?.setFieldsValue({ ...vals });
  }

  return (
    <PageContainer>
      <BetaSchemaForm<DataItem>
        formRef={addFormRef}
        layoutType="ModalForm"
        open={open}
        onOpenChange={setOpen}
        title={'上行码号配置'}
        layout="horizontal"
        onFinish={onFinish}
        columns={formItems}
        width={500}
      />
      <Button
        type="primary"
        onClick={() => {
          reset();
          setOpen(true);
        }}
      >
        上行码号配置
      </Button>
      <ProTable
        actionRef={actionRef}
        columns={
          columns.map((item) => ({
            ...item,
            align: 'center',
          })) as any
        }
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          labelWidth: 'auto',
          collapsed: false,
          span: 4,
          collapseRender: () => null,
          optionRender(searchConfig, props, dom) {
            return [
              ...dom,
              <Button
                key="batchDelete"
                type="primary"
                onClick={() => {
                  const keys = selectedRowKeys;
                  handleDelete(keys);
                }}
                disabled={disableBatchDelete}
              >
                批量删除
              </Button>,
            ];
          },
        }}
        request={requestFn}
        options={false}
        rowSelection={{
          selectedRowKeys,
          onChange: (selectedRowKeys: (number | string)[]) => {
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
      />
    </PageContainer>
  );
};
export default UpAppConfig;
