import React from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { addTransparentSender, editTransparentSender } from '@/services/sender';
import SenderAddForm from './component/addForm';
import { Modal } from 'antd';
interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
}

const TransparentSenderAdd = (props: DialogProps) => {
  const { dialogRef, reload } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const { initialValues } = defaultVal;

  return (
    <Modal
      title={initialValues?.id ? '编辑' : '添加'}
      open={visible}
      footer={null}
      onCancel={() => setVisible(false)}
      destroyOnClose={true}
    >
      <SenderAddForm
        keys="uin,provider_id,country_code,"
        submitFn={addTransparentSender}
        editFn={editTransparentSender}
        initialValues={initialValues}
        reload={() => {
          reload();
          setVisible(false);
        }}
      />
    </Modal>
  );
};

export default TransparentSenderAdd;
