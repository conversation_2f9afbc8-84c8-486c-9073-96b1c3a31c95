import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, message, Select, Popconfirm, Tabs, Tooltip } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useAsyncFn, useSetState } from 'react-use';
import {
  getUserApplySenderList,
  deleteUserApplySender,
  getUserTransportSenderList,
  deleteUserTransportSender,
} from '@/services/sender';
import { smsType, smsTypeValueMap } from '@/const/const';
import { useDialogRef } from '@/utils/react-use/useDialog';
import UserApplySenderAdd from './userApplySenderAdd';
import _ from 'lodash';
import UserApplySenderBatchAdd from './userApplySenderBatchAdd';
import type { TabsProps } from 'antd';
import { getExportData, saveCSV } from '../global-components/saveCsv';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const UserApplySenderManage = () => {
  const [form] = Form.useForm();
  const { regionOptions } = useFetchCountryInfo();
  const [pagination, setPagination] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [pagination2, setPagination2] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [searchKeys, setSearchKeys] = useState<{ [keu: string]: any }>({});
  const [activeKey, setActiveKey] = useState('1');
  const dialogRef = useDialogRef();
  const batchDialogRef = useDialogRef();
  const [exportLoading, setExportLoading] = useState(false);

  const [state, getList] = useAsyncFn(async () => {
    const res = await getUserApplySenderList({
      ...pagination,
      ...searchKeys,
      sms_type: searchKeys.sms_type ? searchKeys.sms_type : undefined,
    });
    return res.data;
  }, [pagination, searchKeys]);

  const [transport, getTransportList] = useAsyncFn(async () => {
    const res = await getUserTransportSenderList({
      ...pagination2,
      ..._.omit(searchKeys, ['sms_type']),
    });
    return res.data;
  }, [pagination2, searchKeys]);

  useEffect(() => {
    getList();
    getTransportList();
  }, [getList, getTransportList, pagination, searchKeys]);

  async function handleDelete(row: any) {
    try {
      const res =
        activeKey === '1'
          ? await deleteUserApplySender({ id: row.id })
          : await deleteUserTransportSender({
              uin: row.uin,
              sender_id: row.sender_id,
              country_code: row.country_code,
            });
      if (res.code === 0) {
        message.success('删除成功');
        getList();
        getTransportList();
      }
    } catch (err) {
      message.error('删除失败');
      console.log(err);
    }
  }

  const columns: any = [
    {
      title: 'uin',
      dataIndex: 'uin',
      key: 'uin',
      align: 'center',
    },
    {
      title: '国家/地区',
      dataIndex: 'country_name',
      key: 'country_name',
      align: 'center',
    },
    {
      title: 'mcc',
      dataIndex: 'mcc',
      key: 'mcc',
      align: 'center',
      render: (key: any) => key || '_',
    },
    {
      title: 'senderId',
      dataIndex: 'sender_id',
      key: 'sender_id',
      align: 'center',
    },
    {
      title: '短信类型',
      dataIndex: 'sms_type',
      key: 'sms_type',
      align: 'center',
      render: (key: number) => smsTypeValueMap.find((el) => el.value === key)?.text,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      render: (row: any) => {
        return (
          <>
            <Popconfirm
              title="确认删除此条数据吗？"
              description="删除会影响客户线上使用该sender ID，请谨慎操作！"
              onConfirm={() => {
                handleDelete(row);
              }}
              okText="Yes"
              cancelText="No"
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  function onSubmit(vals: any) {
    Object.keys(vals).forEach((k) => {
      if (!vals[k]) delete vals[k];
    });
    setSearchKeys({
      ...vals,
      sms_type: vals.sms_type?.reduce((pre = 0, cur: number) => pre + cur, 0),
    });
  }

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '客户sender',
      children: (
        <Table
          columns={columns}
          dataSource={state.value?.list}
          rowKey={(record: any) => record.id}
          loading={state.loading}
          pagination={{
            defaultCurrent: 1,
            total: state.value?.count,
            showSizeChanger: true,
            onShowSizeChange: (current, page_size) => setPagination({ page_size }),
            onChange: (page) => {
              setPagination({ page_index: page });
            },
          }}
        />
      ),
    },
    {
      key: '2',
      label: '可透传sender',
      children: (
        <Table
          columns={columns.filter((el: any) => el.key !== 'sms_type')}
          dataSource={transport.value?.list}
          rowKey={(record: any) =>
            `${record.uin}_${record.sender_id}_${record.country_code}_${record.mcc}`
          }
          loading={transport.loading}
          pagination={{
            defaultCurrent: 1,
            total: transport.value?.count,
            showSizeChanger: true,
            onShowSizeChange: (current, page_size) => setPagination({ page_size }),
            onChange: (page) => {
              setPagination2({ page_index: page });
            },
          }}
        />
      ),
    },
  ];

  async function handleExport() {
    try {
      setExportLoading(true);
      const title = activeKey === '1' ? '客户sender' : '客户可透传sender';
      const vals = form.getFieldsValue();
      const params = {
        ...vals,
        sms_type:
          activeKey === '1'
            ? vals.sms_type?.reduce((pre = 0, cur: number) => pre + cur, 0)
            : undefined,
        page_index: 1,
        page_size: 1000,
      };
      const res =
        activeKey === '1'
          ? await getUserApplySenderList({ ...params })
          : await getUserTransportSenderList({ ...params });
      const _columns =
        activeKey === '1'
          ? columns.slice(0, -1)
          : columns.slice(0, -1).filter((el: any) => el.key !== 'sms_type');
      const { head, data } = getExportData(_columns, res.data.list ?? []);
      saveCSV(`${title}${Date.now()}`, head, data, {
        params: vals,
        route: 'dispatch/csms-schedule',
      })
        .then(() => {
          message.success('导出成功');
        })
        .catch(() => {
          message.error('导出失败');
        });
      setExportLoading(false);
    } catch (error) {
      console.log(error);
      setExportLoading(false);
    }
  }

  return (
    <PageContainer>
      <Button
        type="primary"
        style={{ marginBottom: 10 }}
        onClick={() => {
          batchDialogRef.current.open({
            type: 'normal',
          });
        }}
      >
        添加
      </Button>
      <Button
        type="primary"
        style={{ marginLeft: 10 }}
        onClick={() => {
          batchDialogRef.current.open({
            type: 'transparenry',
          });
        }}
      >
        可透传国家配置
      </Button>
      <Form
        className="sender-search-form"
        labelCol={{ span: 6 }}
        form={form}
        // initialValues={{ ...formData }}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="uin">
          <Input placeholder="uin" />
        </Form.Item>
        <Form.Item name="sender_id">
          <Input placeholder="senderId" />
        </Form.Item>
        <Form.Item name="country_code">
          <Select
            showSearch
            allowClear
            placeholder="国家/地区"
            options={regionOptions}
            filterOption={(input, option) => {
              return (
                option?.label.includes(input) ||
                option?.value.includes(input.toLocaleUpperCase()) ||
                false
              );
            }}
            style={{ width: 250 }}
            // onSearch={(value) => setSearchVal(value)}
          />
        </Form.Item>
        {/* <Form.Item name="supplier_name">
          <Input placeholder="供应商名称" />
        </Form.Item> */}
        <Form.Item name="sms_type">
          <Select
            mode="multiple"
            showSearch
            allowClear
            placeholder="短信类型"
            options={smsType}
            style={{ width: 250 }}
            // onSearch={(value) => setSearchVal(value)}
          />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
          <Tooltip title={`导出${activeKey === '1' ? '客户sender' : '客户可透传sender'}`}>
            <Button
              style={{ marginLeft: 10 }}
              onClick={() => handleExport()}
              loading={exportLoading}
            >
              导出
            </Button>
          </Tooltip>
        </Form.Item>
      </Form>
      <Tabs
        defaultActiveKey="1"
        items={items}
        activeKey={activeKey}
        onChange={(val) => setActiveKey(val)}
      />
      <UserApplySenderAdd
        dialogRef={dialogRef}
        reload={() => {
          getList();
          getTransportList();
        }}
      />
      <UserApplySenderBatchAdd
        dialogRef={batchDialogRef}
        reload={() => {
          getList();
          getTransportList();
        }}
      />
    </PageContainer>
  );
};

export default UserApplySenderManage;
