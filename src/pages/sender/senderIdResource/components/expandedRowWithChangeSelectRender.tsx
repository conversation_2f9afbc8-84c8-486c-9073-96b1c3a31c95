import React from 'react';
import { Button, Table, TableColumnsType, Tag } from 'antd';
import _ from 'lodash';
import { findLabel } from '@/utils/utils';
import { senderResourceStatus, smsType } from '../../const';
import { SelectRowType, theme } from '../const';

export const expandedRowWithChangeSelectRender = (
  record: any,
  selectedRows: SelectRowType[],
  changeSelectRow: Function,
  openEditModal: Function,
  deleteResource: Function,
) => {
  const { resource_list, f_id } = record;
  const columns: TableColumnsType<Object> = [
    {
      key: 'account_id',
      dataIndex: 'account_id',
      title: '账号ID',
    },
    {
      key: 'account_name',
      dataIndex: 'account_name',
      title: '账号名称',
    },
    {
      key: 'account_name',
      dataIndex: 'smpp_account',
      title: 'SMPP账号',
    },
    {
      key: 'account_name',
      dataIndex: 'remark',
      title: '备注',
    },
    {
      key: 'account_name',
      dataIndex: 'status',
      title: '可用状态',
      render: (_: unknown, row: any) => {
        return (
          <Tag color={theme[row.status - 1]}>{findLabel(senderResourceStatus, row.status)}</Tag>
        );
      },
    },
    {
      key: 'sms_type',
      title: '注册流量类型',
      render: (row) => row?.sms_type?.map((v: string) => findLabel(smsType, v))?.join(','),
    },
    {
      key: 'sender_type',
      title: '是否通用sender ID',
      render: (row) => (row.sender_type === 1 ? '是' : '否'),
    },
    {
      key: 'account_name',
      dataIndex: 'updated_at',
      title: '更新时间',
    },
    {
      key: 'singleOperate',
      title: '操作',
      render: (row) => (
        <div>
          <Button
            type="link"
            onClick={() => {
              deleteResource([row.resource_id]);
            }}
          >
            删除
          </Button>
          <Button
            type="link"
            onClick={() => {
              openEditModal('Edit', {
                remark: row.remark,
                status: row.status,
                sms_type: row.sms_type,
                sender_type: row.sender_type + '',
                resource_ids: [row.resource_id],
              });
            }}
          >
            编辑
          </Button>
        </div>
      ),
    },
  ];
  const selectedRowKeys =
    (selectedRows as SelectRowType[]).find((v) => v.selectKey === f_id)?.selectResourceKeys || [];
  const resourceLen = resource_list.length;
  return (
    <Table
      size="small"
      columns={columns}
      dataSource={resource_list}
      pagination={false}
      rowKey="resource_id"
      rowSelection={{
        selectedRowKeys,
        onChange: (selectedRowKeys: (number | string)[]) => {
          changeSelectRow((prev: SelectRowType[]) => {
            const findRowIndex = prev.findIndex((v) => v.selectKey === f_id);
            const isAllSelected = selectedRowKeys.length === resourceLen;
            if (selectedRowKeys.length === 0) {
              // 如果没有选中任何行，移除当前行
              return prev.filter((v) => v.selectKey !== f_id);
            }
            // 创建新的行对象
            const newRow = {
              selectKey: f_id,
              isAll: isAllSelected,
              selectResourceKeys: [...selectedRowKeys],
            };
            if (findRowIndex === -1) {
              // 如果当前行不存在，添加新行
              return [...prev, newRow];
            } else {
              // 如果当前行存在，更新该行
              return prev.map((row) => (row.selectKey === f_id ? newRow : row));
            }
          });
        },
      }}
    />
  );
};
