import React, { useState, useEffect, FC } from 'react';
import { DialogRef, useDialog, useDialogRef } from '@/utils/react-use/useDialog';
import moment from 'moment';
import excel_icon from '@/assets/excel.png';
import * as XLSX from 'xlsx';
import { Button, message, Modal, Spin, Upload, UploadFile } from 'antd';
import _ from 'lodash';
import style from './style.module.less';
import { PageLoading } from '@ant-design/pro-components';
import { ImportResultDialog } from '@/components/ImportResultDialog';
interface IImportDialogProps {
  dialogRef: DialogRef;
  columns: any[];
  save?: boolean;
  onSubmit: (params: any) => Promise<any>;
  onSuccess?: () => void;
  header: string[];
  submitApiType?: string;
  tableAddons?: any[];
  handleStart?: (params: any) => Promise<any>;
}

// 将excel的日期格式转成Date()对象;
export function getFormatDate_XLSX(serial: number) {
  var utc_days = Math.floor(serial - 25569);
  var utc_value = utc_days * 86400;
  var date_info = new Date(utc_value * 1000);
  var fractional_day = serial - Math.floor(serial) + 0.0000001;
  var total_seconds = Math.floor(86400 * fractional_day);
  var seconds = total_seconds % 60;
  total_seconds -= seconds;
  var hours = Math.floor(total_seconds / (60 * 60));
  var minutes = Math.floor(total_seconds / 60) % 60;
  var d = new Date(
    date_info.getFullYear(),
    date_info.getMonth(),
    date_info.getDate(),
    hours,
    minutes,
    seconds,
  );
  return moment(d).format('YYYY-MM-DD');
}
const ImportDialog: FC<IImportDialogProps> = (props: IImportDialogProps) => {
  const { dialogRef, columns, onSubmit, save, onSuccess, header, submitApiType, tableAddons } =
    props;
  const [visible, setShowState] = useDialog(dialogRef);
  const importResultRef = useDialogRef();
  const [loading, setLoading] = useState(false);

  async function onChange({ file }: any) {
    setLoading(true);
    try {
      const data = await readFileAsArrayBuffer(file);
      console.log(data);

      if (!data) {
        message.error('导入数据为空');
      }

      const newArr = handleData(data);
      importResultRef.current.open({ list: newArr, submitApiType });
      setShowState(false);
    } catch (error) {
      message.error(error as string);
    } finally {
      setLoading(false);
    }
  }

  function readFileAsArrayBuffer(file: File): Promise<any> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const ab = e.target?.result;
        const wb = XLSX.read(ab, { type: 'array' });

        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];

        const data: any = XLSX.utils.sheet_to_json(ws, {
          header,
          defval: '',
          dateNF: 'string',
          range: 2,
        });

        if (!data?.length) {
          reject('导入数据为空');
        } else {
          resolve(data);
        }
      };
      reader.onerror = () => {
        reject('文件读取错误');
      };
      reader.readAsArrayBuffer(file);
    });
  }
  return (
    <>
      <Modal
        open={visible}
        title="导入数据"
        onCancel={() => setShowState(false)}
        footer={null}
        centered
      >
        <Spin spinning={loading}>
          <div className={style['import-dialog-wrapper']}>
            <div className={style.border}>
              <div>1、下载excel数据模版，填写数据</div>
              <img src={excel_icon} className={style.icon} alt="excel_icon" />
              <div className={style.operate}>
                <Button onClick={downloadFile}>下载模版</Button>
              </div>
            </div>
            <div>
              <div>2、上传填写好的excel文件</div>
              <img src={excel_icon} className={style.icon} alt="excel_icon" />
              <div className={style.operate}>
                <Upload
                  showUploadList={false}
                  customRequest={onChange as any}
                  maxCount={1}
                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                >
                  <Button type="primary">导入数据</Button>
                </Upload>
              </div>
            </div>
          </div>
        </Spin>
      </Modal>
      <ImportResultDialog
        dialogRef={importResultRef}
        columns={columns}
        onSubmit={onSubmit}
        onSuccess={onSuccess}
      />
    </>
  );
};
export default ImportDialog;

function downloadFile() {
  const link = document.createElement('a');
  link.download = '';
  link.href =
    'https://dscache.tencent-cloud.cn/upload/uploader/sender-resource-template-4d9260a1a83b9c13fd32718c59b9959f13835ebf.xlsx';
  link.click();
}

function handleData(data: any[]) {
  return _.map(data, (item) => {
    console.log(data);

    return {
      ...item,
      sender_id: `${item.sender_id}`,
      account_ids: [item.account_ids],
      country_code: item.country_code.toString().split(','),
      country_codes: item.country_code.toString().split(','),
      sms_type: item.sms_type.toString().split(','),
      sender_type: +item.sender_type,
      status: +item.status,
    };
  });
}
