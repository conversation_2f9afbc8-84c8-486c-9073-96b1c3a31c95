import { addSenderIdResource, editSenderIdResource } from '@/services/sender';
import { message } from 'antd';

export const theme: ('success' | 'error' | 'warning')[] = ['success', 'error', 'warning'];

export type SelectRowType = {
  selectKey: string;
  isAll: boolean;
  selectResourceKeys: number[];
};

export const senderIdTypes = [
  { value: 1, label: '普通sender' },
  { value: 2, label: '短号' },
  { value: 3, label: '上行码号' },
];

export const excelHeaders = [
  'sender_id',
  'country_code',
  'account_ids',
  'status',
  'sms_type',
  'sender_type',
  'remark',
];
export const ModalConfig: any = {
  Create: {
    columnsFilters: [
      'sender_id',
      'country_code',
      'provider',
      'sender_id_type',
      'status',
      'sms_type',
      'sender_type',
      'remark',
    ],
    title: '新增sender ID',
    onFinish: (vals: any, extraData: any, { actionRef }: any) => {
      return addSenderIdResource({
        params: [
          {
            ...vals,
            account_ids: vals.account_ids.map((v: number) => v.toString()),
          },
        ],
      })
        .then((res) => {
          if (res.code === 0) {
            actionRef.current?.reload();
            message.success('新增成功');
            return true;
          }
          return false;
        })
        .catch((err) => {
          message.error('新增失败');
          console.log(err);
        });
    },
  },
  Edit: {
    columnsFilters: ['status', 'sms_type', 'sender_type', 'remark'],
    title: '编辑',
    onFinish: (vals: any, extraData: any, { actionRef }: any) => {
      return editSenderIdResource({
        ...vals,
        resource_ids: extraData.resource_ids,
      })
        .then((res) => {
          if (res.code === 0) {
            actionRef.current?.reload();
            message.success('编辑成功');
            return true;
          }
          return false;
        })
        .catch((err) => {
          message.error('编辑失败');
          console.log(err);
        });
    },
  },
  ChannelAdd: {
    columnsFilters: ['provider', 'sender_id_type', 'remark'],
    title: '新增sender ID',
    onFinish: (vals: any, extraData: any, { actionRef }: any) => {
      return addSenderIdResource({
        params: [
          {
            ...extraData,
            ...vals,
            account_ids: vals.account_ids.map((v: number) => v.toString()),
            country_codes: extraData['country_codes'],
          },
        ],
      })
        .then((res) => {
          if (res.code === 0) {
            actionRef.current?.reload();
            message.success('新增成功');
            return true;
          }
          return false;
        })
        .catch((err) => {
          message.error('新增失败');
          console.log(err);
        });
    },
  },
};
