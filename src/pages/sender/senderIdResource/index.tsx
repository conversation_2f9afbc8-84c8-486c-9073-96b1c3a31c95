import { isMobile } from '@/const/jadgeUserAgent';
import {
  addSenderIdResource,
  deleteSenderIdResource,
  querySenderIdResource,
} from '@/services/sender';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Checkbox, message, Modal, Tag } from 'antd';
import React, { useState, useMemo, useCallback, useRef } from 'react';
import _ from 'lodash';
import { findLabel } from '@/utils/utils';
import { senderResourceStatus, smsType } from './../const';
import ProviderCascader from '../../global-components/ProviderCascader';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { expandedRowWithChangeSelectRender } from './components/expandedRowWithChangeSelectRender';
import { excelHeaders, ModalConfig, SelectRowType, senderIdTypes, theme } from './const';
import { useMultipleConfigWithExtraDataModal } from '@/hooks/useMultipleConfigWithExtraDataModal';
import { useDialogRef } from '@/utils/react-use/useDialog';
import ImportDialog from './components/ImportDialog/ImportDialog';

const formItemProps = {
  rules: [{ required: true }],
};
const sumStatus = senderResourceStatus.concat({
  value: 3,
  label: '部分启用',
});

type ModalDataKeyType = 'Create' | 'Edit' | 'ChannelAdd';

const SenderIdResource = () => {
  const { regionOptions = [] } = useFetchCountryInfo();
  const [selectRow, setSelectRows] = useState<SelectRowType[]>([]);
  const actionRef = useRef<ActionType>();
  const importDialogRef = useDialogRef();

  const columns: any[] = useMemo(
    () => [
      {
        key: 'sender_id',
        dataIndex: 'sender_id',
        title: 'Sender ID',
        formItemProps,
        sorter: {
          multiple: 1,
        },
      },
      {
        key: 'country_code',
        dataIndex: 'country_code',
        title: '国家/地区',
        valueType: 'select',
        formItemProps,
        fieldProps: {
          options: regionOptions,
          placeholder: '请选择国家',
          showSearch: true,
          mode: 'multiple',
        },
        sorter: {
          multiple: 1,
        },
        transform: (value: any) => ({ country_codes: value }),
        render: (_: unknown, { country_code }: any) => findLabel(regionOptions, country_code),
      },
      {
        key: 'supplier_name',
        dataIndex: 'supplier_name',
        title: '供应商名称',
        hideInSearch: true,
      },
      {
        key: 'account_ids',
        dataIndex: 'account_ids',
        title: '供应商ID',
        hideInSearch: true,
        hideInTable: true,
      },
      //   {
      //     key: 'sender_id_type',
      //     dataIndex: 'sender_id_type',
      //     title: 'sender ID类型',
      //     hideInSearch: true,
      //     formItemProps,
      //     fieldProps: { options: senderIdTypes },
      //     valueType: 'select',
      //   },
      {
        key: 'sms_type',
        dataIndex: 'sms_type',
        title: '注册流量类型',
        hideInSearch: true,
        valueType: 'select',
        fieldProps: { options: smsType, mode: 'multiple' },
        formItemProps,
        render: (_: unknown, row: any) =>
          row?.sms_type?.map((v: string) => findLabel(smsType, v))?.join(','),
      },
      {
        key: 'sender_type',
        dataIndex: 'sender_type',
        title: '是否通用sender ID',
        valueType: 'radio',
        formItemProps,
        hideInSearch: true,
        valueEnum: { '0': '否', '1': '是' },
        render: (_: unknown, row: any) => {
          return row.sender_type === '1' ? '是' : '否';
        },
      },
      {
        key: 'max_updated_at',
        dataIndex: 'max_updated_at',
        title: '更新时间',
        hideInSearch: true,
        sorter: {
          multiple: 1,
        },
      },

      {
        key: 'provider',
        dataIndex: 'provider',
        title: '注册通道',
        formItemProps,
        hideInTable: true,
        transform: (value: string[]) => {
          return { account_ids: _.flatten(value).filter((v) => typeof v === 'number') };
        },
        renderFormItem() {
          return <ProviderCascader type={1} />;
        },
      },
      {
        key: 'status',
        dataIndex: 'status',
        title: '可用状态',
        formItemProps,
        fixed: 'right',
        valueType: 'select',
        fieldProps: {
          options: senderResourceStatus,
        },
        width: 100,
        render: (_: unknown, row: any) => {
          return <Tag color={theme[row.status - 1]}>{findLabel(sumStatus, row.status)}</Tag>;
        },
      },
      {
        key: 'remark',
        dataIndex: 'remark',
        title: '备注',
        hideInSearch: true,
      },
      {
        key: 'operation',
        title: '操作',
        width: 240,
        hideInSearch: true,
        fixed: 'right',
        render: (row: any) => {
          return (
            <div>
              <Button
                type="link"
                onClick={() => {
                  openModal('ChannelAdd', {
                    ..._.pick(row, ['sender_id', 'sms_type', 'status']),
                    country_codes: [row.country_code],
                    sender_type: row.sender_type + '',
                  });
                }}
              >
                新增通道
              </Button>
              <Button
                type="link"
                onClick={() => {
                  openModal('Edit', {
                    remark: row.remark,
                    resource_ids: row.resource_list.map((v: any) => v.resource_id),
                    status: row.status,
                    sms_type: row.sms_type,
                    sender_type: row.sender_type + '',
                  });
                }}
              >
                编辑
              </Button>
              <Button
                type="link"
                onClick={() => {
                  deleteResource(row.resource_list.map((v: any) => v.resource_id));
                }}
              >
                删除
              </Button>
            </div>
          );
        },
      },
    ],
    [regionOptions],
  );
  const requestFn = useCallback(async (params: any, sort: any) => {
    const sorts = _.map(sort, (v, k) => ({
      by: k,
      order: v.slice(0, -3),
    }))
      .map((v) => JSON.stringify(v))
      .reverse();

    const { data } = await querySenderIdResource({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      sorts,
      page_size: params.pageSize,
      page_index: params.current,
    });
    const list =
      data?.list?.map((v: any) => ({
        ...v,
        f_id: _.map(v.resource_list, (o) => o.resource_id).join(','),
        sms_type: v.resource_list?.[0]?.sms_type,
        sender_type: v.resource_list?.[0]?.sender_type,
        remark: v.resource_list?.[0]?.remark,
      })) ?? [];

    return {
      data: list,
      success: true,
      total: data.count,
    };
  }, []);
  const deleteResource = async function (resource_ids: number[]) {
    Modal.confirm({
      content: '确认删除?',
      centered: true,
      onOk: async () =>
        deleteSenderIdResource({ resource_ids }).then((res) => {
          if (res.code === 0) {
            message.success('删除成功');
            setSelectRows([]);
            actionRef.current?.reload();
          }
        }),
    });
  };

  const disableBatchDelete = selectRow.length == 0;
  const selectedRowKeys = selectRow.map((v) => v.selectKey);

  const [open, formProps, openModal] = useMultipleConfigWithExtraDataModal<ModalDataKeyType>(
    columns,
    ModalConfig,
    {
      actionRef,
    },
  );
  return (
    <PageContainer>
      {open ? <BetaSchemaForm centered width={550} labelCol={{ span: 7 }} {...formProps} /> : null}
      <Button
        type="primary"
        onClick={() => {
          openModal('Create', {});
        }}
      >
        新增sender ID
      </Button>
      <Button
        style={{ marginLeft: 10 }}
        onClick={() => {
          importDialogRef.current.open();
        }}
      >
        批量导入
      </Button>
      <ProTable
        actionRef={actionRef}
        columns={columns}
        size="small"
        rowKey="f_id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          span: 4,
          collapseRender: () => null,
          optionRender(searchConfig, props, dom) {
            return [
              ...dom,
              <Button
                key="batchDelete"
                type="primary"
                onClick={() => {
                  const keys = _.flatten(selectRow.map((v) => v.selectResourceKeys));
                  deleteResource(keys);
                }}
                disabled={disableBatchDelete}
              >
                批量删除
              </Button>,
            ];
          },
        }}
        options={false}
        request={requestFn}
        expandable={{
          fixed: 'left',
          expandedRowRender: (record) =>
            expandedRowWithChangeSelectRender(
              record,
              selectRow,
              setSelectRows,
              openModal,
              deleteResource,
            ),
        }}
        rowSelection={{
          selectedRowKeys,
          renderCell(_, record: any) {
            const findResource = selectRow.find((v) => v.selectKey === record.f_id);
            const isAll = findResource?.isAll;
            const resourceIds = findResource?.selectResourceKeys || [];
            const handleChange = () => {
              setSelectRows((prev) => {
                const resourceExists = prev.some((v) => v.selectKey === record.f_id);
                if (resourceExists) {
                  // 如果资源已存在，移除它
                  return prev.filter((v) => v.selectKey !== record.f_id);
                } else {
                  // 如果资源不存在，添加新资源
                  return [
                    ...prev,
                    {
                      selectKey: record.f_id,
                      isAll: true,
                      selectResourceKeys: record.resource_list.map((v: any) => v.resource_id),
                    },
                  ];
                }
              });
            };
            return (
              <Checkbox
                indeterminate={!isAll && resourceIds.length > 0}
                checked={resourceIds.length > 0}
                onChange={handleChange}
              />
            );
          },
          preserveSelectedRowKeys: true,
          onChange: (_: unknown, selectedRows) => {
            setSelectRows(
              selectedRows.map(({ f_id, resource_list }: any) => ({
                selectKey: f_id,
                isAll: true,
                selectResourceKeys: resource_list.map((v: any) => v.resource_id),
              })),
            );
          },
        }}
      />
      <ImportDialog
        dialogRef={importDialogRef}
        columns={columns.filter((v) => excelHeaders.includes(v.key))}
        header={excelHeaders}
        onSubmit={async (params) => {
          return await addSenderIdResource({ params: params.params });
        }}
        onSuccess={() => {
          actionRef.current?.reload();
        }}
      />
    </PageContainer>
  );
};
export default SenderIdResource;
