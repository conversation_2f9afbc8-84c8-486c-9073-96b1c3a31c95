import React, { useEffect, useMemo, useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import {
  addUserApplySender,
  addUserTransportSender,
  getTransparentCountryList,
} from '@/services/sender';
import { Button, Form, Input, Modal, Select, message } from 'antd';
import { smsType } from '@/const/const';
import _ from 'lodash';
import { useAsyncFn } from 'react-use';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
}

const UserApplySenderBatchAdd = (props: DialogProps) => {
  const { dialogRef, reload } = props;
  const { regionOptions = [] } = useFetchCountryInfo();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [visible, setVisible, defaultVal] = useDialog<{ type: 'normal' | 'trannsparent' }>(
    dialogRef,
  );
  const { type } = defaultVal;

  const [state, getList] = useAsyncFn(async () => {
    const res = await getTransparentCountryList({ page_index: 1, page_size: 1000 });
    return _.uniq(res.data.list.map((el: any) => el.country_code)) ?? [];
  }, []);

  async function onFinish(vals: any) {
    setLoading(true);
    try {
      const _params = {
        ...vals,
        sms_type: vals.sms_type
          ? vals.sms_type.reduce((pre = 0, cur: number) => pre + cur, 0)
          : undefined,
        uins: _.map(_.compact(vals.uins.split(',')), _.trim),
        sender_ids: _.map(_.compact(vals.sender_ids.trim().split(',')), _.trim),
      };
      const res =
        type === 'normal'
          ? await addUserApplySender({ ..._params })
          : await addUserTransportSender({ ..._params });
      setLoading(false);
      if (res.code === 0) {
        setVisible(false);
        reload();
        message.success('添加成功');
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
      return message.error('添加失败');
    }
  }

  useEffect(() => {
    if (visible) {
      getList();
    } else {
      form.resetFields();
    }
  }, [form, getList, visible]);

  const countryOptions = useMemo(() => {
    return type === 'normal'
      ? regionOptions.filter((country: any) => !state?.value?.includes(country.value))
      : regionOptions.filter((country: any) => state?.value?.includes(country.value)) ?? [];
  }, [regionOptions, state?.value, type]);

  return (
    <Modal
      title="添加"
      open={visible}
      footer={null}
      onCancel={() => setVisible(false)}
      destroyOnClose={true}
    >
      <Form
        className="sender-search-form"
        labelCol={{ span: 5 }}
        form={form}
        initialValues={{ type: 0 }}
        labelAlign="left"
        onFinish={(vals) => onFinish(vals)}
      >
        <Form.Item name="uins" label="uin" rules={[{ required: true }]}>
          <Input placeholder="支持多个，用英文逗号分隔" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item name="sender_ids" label="senderId" rules={[{ required: true }]}>
          <Input placeholder="支持多个，用英文逗号分隔" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item name="country_codes" label="国家/地区" rules={[{ required: true }]}>
          <Select
            showSearch
            placeholder="请选择"
            onChange={(value) => {
              form.setFieldsValue({ country_codes: value, tactic_ids: undefined });
            }}
            mode="multiple"
            options={countryOptions}
            filterOption={(input, option) => {
              return (
                option?.label.includes(input) ||
                option?.value.includes(input.toLocaleUpperCase()) ||
                false
              );
            }}
            style={{ width: 250 }}
          />
        </Form.Item>
        {type === 'normal' && (
          <Form.Item name="sms_type" label="短信类型" rules={[{ required: true }]}>
            <Select
              mode="multiple"
              allowClear
              showSearch
              placeholder="请选择"
              options={smsType}
              onChange={(values) => {
                form.setFieldsValue({ sms_type: values, tactic_ids: undefined });
              }}
              style={{ width: 250 }}
            />
          </Form.Item>
        )}
        <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
          <Button htmlType="submit" type="primary" loading={loading}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UserApplySenderBatchAdd;
