import React, { useEffect, useState } from 'react';
import { Select, Form, Modal } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';

import { addMandatorySender, editMandatorySender } from '@/services/sender';
import SenderAddForm from './component/addForm';
import { senderRules } from './const';

interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
}

const MandatorySenderAdd = (props: DialogProps) => {
  const { dialogRef, reload } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const { initialValues } = defaultVal;

  const [keys, setKeys] = useState<any[]>([]);
  // @ts-ignore
  const [type, setType] = useState('1');

  useEffect(() => {
    switch (type) {
      case '1':
        setKeys(['uin', 'country_code', 'provider_id', 'sdkappid']);
        break;
      case '2':
        setKeys(['uin', 'country_code', 'sdkappid']);
        break;
      case '3':
        setKeys(['uin', 'country_code', 'provider_id']);
        break;
      case '4':
        setKeys(['uin', 'country_code']);
        break;
      case '5':
        setKeys(['uin']);
        break;
    }
  }, [type]);

  useEffect(() => {
    initialValues?.type ? setType(initialValues?.type.toString()) : setType('1');
  }, [initialValues]);

  return (
    <Modal
      title={initialValues?.id ? '编辑' : '添加'}
      open={visible}
      footer={null}
      onCancel={() => setVisible(false)}
      destroyOnClose={true}
    >
      <Form labelCol={{ span: 5 }}>
        <Form.Item label="sender规则">
          <Select
            showSearch
            placeholder="请选择"
            options={senderRules}
            style={{ width: 250 }}
            value={type}
            onChange={(val) => setType(val)}
          />
        </Form.Item>
      </Form>
      <SenderAddForm
        keys={keys.join(',')}
        submitFn={addMandatorySender}
        editFn={editMandatorySender}
        params={{ type: Number(type) }}
        initialValues={initialValues}
        reload={() => {
          reload();
          setVisible(false);
        }}
      />
    </Modal>
  );
};

export default MandatorySenderAdd;
