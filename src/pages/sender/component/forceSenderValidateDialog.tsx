import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { deleteMandatorySender } from '@/services/sender';
import { useDialog } from '@/utils/react-use/useDialog';
import { Button, message, Modal, Table } from 'antd';
import _ from 'lodash';
import React, { useMemo, useState } from 'react';

export function ForceSenderValidateDialog(props: { dialogRef: any; onContinue?: () => void }) {
  const { dialogRef, onContinue } = props;
  const { regionOptions } = useFetchCountryInfo();

  const [visible, setVisible, defaultValue] = useDialog<{ list: any[] }>(dialogRef);
  const [loading, setLoading] = useState(false);
  const { list } = defaultValue;
  const isDelete = useMemo(() => _.some(list, (el) => !el.provider_id), [list]);
  const selectedRowKeys = useMemo(
    () => _.filter(list, (el) => !el.provider_id)?.map((el) => el.id),
    [list],
  );

  const columns: any = useMemo(() => {
    return [
      {
        title: 'UIN',
        dataIndex: 'uin',
        key: 'uin',
      },
      {
        title: '国家/地区',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        fieldProps: {
          options: regionOptions,
        },
        render: (_r, row: any) => {
          return _.find(regionOptions, (el) => el.value === row.country_code)?.label;
        },
      },
      {
        title: 'SDKAPPID',
        dataIndex: 'sdkappid',
        key: 'sdkappid',
      },
      {
        title: '供应商账号ID',
        key: 'provider_id',
        render: (row: any) => row.provider_id || '-',
      },
      {
        title: '供应商名称',
        key: 'provider_name',
        render: (row: any) => row.provider_name || '-',
      },
      {
        title: 'Sender ID',
        dataIndex: 'sender_id',
        key: 'sender_id',
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
      },
    ];
  }, [regionOptions]);

  async function handleSubmit() {
    if (isDelete) {
      // 删除
      setLoading(true);
      const res = await deleteMandatorySender({
        ids: selectedRowKeys,
      });
      if (res.code === 0) {
        message.success('删除成功');
        setVisible(false);
      }
      setLoading(false);
      return;
    }
    try {
      setLoading(true);
      await onContinue?.();
      setVisible(false);
      setLoading(false);
    } catch (err) {}
  }

  return (
    <Modal
      title="强制sender配置"
      open={visible}
      width={1000}
      onCancel={() => setVisible(false)}
      footer={[
        <Button key="1" type="primary" onClick={handleSubmit} loading={loading}>
          {isDelete ? '删除已勾选项' : '确定'}
        </Button>,
        <Button key="2" onClick={() => setVisible(false)}>
          取消
        </Button>,
      ]}
    >
      <Table
        columns={columns}
        rowKey="id"
        dataSource={list}
        rowSelection={{
          selectedRowKeys,
          type: 'checkbox',
          getCheckboxProps: () => ({
            disabled: true,
          }),
        }}
      />
    </Modal>
  );
}
