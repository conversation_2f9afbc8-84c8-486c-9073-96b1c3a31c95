import React, { useState, useMemo } from 'react';
import { Button, InputNumber, Form, Input, message, Select } from 'antd';
import { smsType } from '@/const/const';
import { history } from 'umi';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
interface Props {
  keys: string;
  submitFn: (vals: any) => Promise<any>;
  editFn: (vals: any) => Promise<any>;
  params?: any;
  initialValues?: any;
  reload: () => void;
}

const SenderAddForm = (props: Props) => {
  const [form] = Form.useForm();
  const { regionOptions } = useFetchCountryInfo();
  const { keys, submitFn, params, initialValues, editFn, reload } = props;
  const [loading, setLoading] = useState(false);

  const keyArr = useMemo(() => {
    return keys.split(',');
  }, [keys]);

  async function onFinish(vals: any) {
    setLoading(true);
    const isEdit = !!initialValues?.id;
    try {
      const _params = { ...vals, ...params };
      if (vals.sms_type) {
        _params.sms_type =
          typeof vals.sms_type === 'number'
            ? vals.sms_type
            : vals.sms_type.reduce((pre = 0, cur: number) => pre + cur, 0);
      }
      const res = isEdit
        ? await editFn({
            ..._params,
            id: initialValues?.id,
          })
        : await submitFn({ ..._params });
      setLoading(false);
      if (res.code === 0) {
        reload();
        message.success(isEdit ? '编辑成功' : '添加成功');
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
      return message.error(isEdit ? '编辑失败' : '添加失败');
    }
  }

  return (
    <Form
      className="sender-search-form"
      labelCol={{ span: 5 }}
      form={form}
      initialValues={initialValues}
      labelAlign="left"
      onFinish={(vals) => onFinish(vals)}
    >
      {keyArr.includes('uin') && (
        <Form.Item name="uin" label="uin" rules={[{ required: true }]}>
          <InputNumber controls={false} placeholder="uin" style={{ width: 250 }} />
        </Form.Item>
      )}
      {keyArr.includes('level') && (
        <Form.Item name="level" label="级别" rules={[{ required: true }]}>
          <Select
            showSearch
            placeholder="请选择"
            options={[
              { label: 1, value: 1 },
              { label: 2, value: 2 },
              { label: 3, value: 3 },
            ]}
            style={{ width: 250 }}
            // onSearch={(value) => setSearchVal(value)}
          />
        </Form.Item>
      )}
      <Form.Item name="sender_id" label="senderId" rules={[{ required: true }]}>
        <Input placeholder="senderId" style={{ width: 250 }} />
      </Form.Item>
      {keyArr.includes('country_code') && (
        <Form.Item name="country_code" label="国家/地区" rules={[{ required: true }]}>
          <Select
            showSearch
            placeholder="请选择"
            options={regionOptions}
            filterOption={(input, option) => {
              return (
                option?.label.includes(input) ||
                option?.value.includes(input.toLocaleUpperCase()) ||
                false
              );
            }}
            style={{ width: 250 }}
            // onSearch={(value) => setSearchVal(value)}
          />
        </Form.Item>
      )}
      {keyArr.includes('sms_type') && (
        <Form.Item name="sms_type" label="短信类型" rules={[{ required: true }]}>
          <Select
            mode={history.location.pathname.includes('userApply') ? 'multiple' : 'tags'}
            allowClear
            showSearch
            placeholder="请选择"
            options={smsType}
            style={{ width: 250 }}
            // onSearch={(value) => setSearchVal(value)}
          />
        </Form.Item>
      )}

      {keyArr.includes('sdkappid') && (
        <Form.Item name="sdkappid" label="sdkappid" rules={[{ required: true }]}>
          <InputNumber controls={false} placeholder="sdkappid" style={{ width: 250 }} />
        </Form.Item>
      )}
      {keyArr.includes('provider_id') && (
        <Form.Item name="provider_id" label="供应商账号ID" rules={[{ required: true }]}>
          <InputNumber controls={false} placeholder="供应商账号ID" style={{ width: 250 }} />
        </Form.Item>
      )}
      <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
        <Button htmlType="submit" type="primary" loading={loading}>
          提交
        </Button>
      </Form.Item>
    </Form>
  );
};

export default SenderAddForm;
