import React, { useCallback, useMemo } from 'react';
import { Button, Form, Popconfirm, Select, Spin, Tag, Typography, message, Popover } from 'antd';
import {
  ActionType,
  ModalForm,
  PageContainer,
  ProForm,
  ProFormDigit,
  ProFormList,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import { findLabel, getPathByUrl } from '@/utils/utils';
import { isMobile } from '@/const/jadgeUserAgent';
import _ from 'lodash';
import {
  querySenderAttachment,
  addSenderAttachment,
  editSenderAttachment,
  deleteSenderAttachment,
  querySenderRegisterFields,
} from '@/services/sender';
import { LinkOutlined } from '@ant-design/icons';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import ImageUploadMultipleBucket from '@/components/ImageUploadMultipleBucket';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
const { Text } = Typography;

export const consoleBucket = [
  {
    region: 'ap-guangzhou',
    bucket: 'maz-consolebucket-1258344699',
  },
  {
    region: 'ap-singapore',
    bucket: 'maz-consolebucketsingapore-1258344699',
  },
  {
    region: 'eu-frankfurt',
    bucket: 'consolebucketgermany-1258344699',
  },
];

export const brandTypeOptions = [
  { label: '本地品牌', value: '1' },
  { label: '国际品牌', value: '0' },
];

const fileTypes = [
  { value: '.pdf', label: 'pdf' },
  { value: '.jpg', label: 'jpg' },
  { value: '.png', label: 'png' },
  { value: '.doc', label: 'doc' },
  { value: '.docx', label: 'docx' },
  { value: '.xlsx', label: 'xlsx' },
  { value: '.xls', label: 'xls' },
  { value: '.zip', label: 'zip' },
];

export const specialCountry = ['US', 'CA'];

interface ItemType {
  type: number;
  path: string[];
  file_type: string[];
  file_num: number;
}

const SenderAttachment = () => {
  const { regionOptions } = useFetchCountryInfo();
  const actionRef = React.useRef<ActionType>();
  const [form] = Form.useForm();
  const [visible, setVisible] = React.useState(false);
  const [type, setType] = React.useState<'create' | 'edit'>('create');

  const { value: attachmentTypes = [], loading } = useAsyncRetryFunc(async () => {
    const { data } = await querySenderRegisterFields();
    const list = data.list.map((el: any) => {
      return {
        label: el.name,
        value: el.type,
      };
    });
    return _.sortBy(list, 'type').reverse();
  });

  async function handleDelete({
    country_code,
    brand_type,
  }: {
    country_code: string;
    brand_type: number;
  }) {
    const { code } = await deleteSenderAttachment({ brand_type, country_code });
    if (code === 0) {
      message.success('删除成功');
      actionRef.current?.reload();
    }
  }

  async function handleEdit(row: any) {
    setType('edit');
    const values = _.mapValues(
      _.pickBy(_.omit(row, ['country_code', 'brand_type']), (v) => !_.isEmpty(v)),
      (v) => {
        return {
          type: v[0]?.type,
          path: Array.isArray(v[0]?.path)
            ? v[0]?.path.map((url: string) => decodeURIComponent(new URL(url).pathname?.slice(1)))
            : [],
          file_type: v[0]?.file_type.split(','),
          file_num: v[0]?.file_num,
          required: v[0]?.required,
        };
      },
    );
    setVisible(true);
    form?.setFieldsValue({
      country_codes: [row.country_code],
      brand_types: [row.brand_type.toString()],
      datas: _.values(values),
    });
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: '国家',
        dataIndex: 'country_code',
        key: 'country_code',
        width: 150,
        renderFormItem: () => <Select></Select>,
        fieldProps: {
          options: regionOptions,
          allowClear: true,
          showSearch: true,
          placeholder: '请选择国家',
          filterOption: (inputValue, option: any) =>
            !!option?.label.toLowerCase().includes(inputValue.toLocaleLowerCase()),
        },
        render: (country_code: number) => findLabel(regionOptions ?? [], country_code),
      },
      {
        title: '品牌类型',
        dataIndex: 'brand_type',
        key: 'brand_type',
        width: 150,
        renderFormItem: () => <Select></Select>,
        fieldProps: {
          options: brandTypeOptions,
          placeholder: '请选择品牌类型',
          allowClear: true,
          // showSearch: true,
        },
        render: (country_code: number) => findLabel(brandTypeOptions, country_code),
      },
      ...attachmentTypes?.map((el) => {
        return {
          title: el.label,
          dataIndex: `attachment_${el.value}`,
          key: `attachment_${el.value}`,
          // align: 'center',
          hideInSearch: true,
          render: (attachment: any[]) => {
            if (attachment.length) {
              return (
                <div style={{ textAlign: 'left' }}>
                  <p>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      附件：
                    </Text>
                    {attachment[0].path.length ? (
                      <Popover
                        trigger="click"
                        content={attachment[0]?.path?.map((path: string, i: number) => (
                          <p key={`${i}`}>
                            <a href={path} download target="_blank" rel="noreferrer">
                              <LinkOutlined />
                              {getPathByUrl(path)?.name}
                            </a>
                          </p>
                        ))}
                      >
                        <Button type="link">查看</Button>
                      </Popover>
                    ) : (
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        暂无
                      </Text>
                    )}
                  </p>
                  <p>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      文件个数：
                    </Text>
                    <Tag>{attachment[0]?.file_num}</Tag>
                  </p>
                  <p>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      文件类型：
                    </Text>
                    {attachment[0]?.file_type}
                  </p>
                  <p>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      是否必传：
                    </Text>
                    {attachment[0]?.required ? (
                      <Tag color="#87d068">是</Tag>
                    ) : (
                      <Tag color="#f50">否</Tag>
                    )}
                  </p>
                </div>
              );
            }
            return '-';
          },
        };
      }),
      // {
      //   title: '是否必传',
      //   dataIndex: 'required',
      //   key: 'required',
      //   render: (value: number) =>
      //     value ? <Tag color="#87d068">是</Tag> : <Tag color="#f50">否</Tag>,
      // },
      {
        title: '操作',
        key: 'operate',
        align: 'center',
        width: 180,
        render: (row: any) => (
          <>
            <Button type="link" onClick={() => handleEdit(row)}>
              编辑
            </Button>
            <Popconfirm
              title="确认删除吗？"
              onConfirm={() => handleDelete(row)}
              okText="Yes"
              cancelText="No"
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          </>
        ),
        hideInSearch: true,
      },
    ];
  }, [attachmentTypes, regionOptions]);
  async function onFinish(values: any) {
    const datas = values.datas.map((el) => el.type);
    const uniq = _.uniq(datas);
    if (datas.length !== uniq.length) {
      message.error('附件类型重复配置，请检查');
      return;
    }
    const { code } =
      type === 'edit'
        ? await editSenderAttachment({
            country_code: values.country_codes[0],
            brand_type: values.brand_types[0],
            params: values.datas?.map((item: ItemType) => ({
              ...item,
              file_type: item.file_type.join(','),
            })),
          })
        : await addSenderAttachment({
            country_codes: values.country_codes,
            brand_types: values.brand_types,
            params: values.datas?.map((item: ItemType) => ({
              ...item,
              file_type: item.file_type.join(','),
            })),
          });
    if (code === 0) {
      message.success('新增成功');
      setVisible(false);
      actionRef.current?.reload();
    }
  }

  const requestFn = useCallback(
    async (params: { pageSize: number; current: number }) => {
      const { data } = await querySenderAttachment({
        ..._.omit(params, ['pageSize', 'current']),
        page_index: params.current,
        page_size: params.pageSize,
      });
      const group = _.groupBy(data.list, (item) => `${item.country_code}_${item.brand_type}`);
      const list = _.map(group, (value, key) => {
        const allTypes = attachmentTypes.map((el) => {
          return {
            [`attachment_${el.value}`]: value.filter((v) => v.type === el.value),
          };
        });
        const a = _.reduce(allTypes, (r, c) => _.assign(r, c), {});
        return {
          id: value[0].id,
          country_code: value[0].country_code,
          brand_type: value[0].brand_type,
          required: value[0].required,
          ...a,
        };
      });
      return {
        data: list ?? [],
        success: true,
        total: data.count,
      };
    },
    [attachmentTypes],
  );

  return (
    <PageContainer>
      <Button
        onClick={() => {
          setType('create');
          setVisible(true);
        }}
        type="primary"
      >
        新增资料配置
      </Button>
      {loading ? (
        <div>
          <Spin></Spin>
        </div>
      ) : (
        <ProTable
          actionRef={actionRef}
          columns={columns}
          rowKey="id"
          pagination={{
            defaultPageSize: 0,
            showSizeChanger: true,
          }}
          search={{
            collapseRender: false,
            collapsed: false,
          }}
          scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 1300 }}
          request={requestFn}
        />
      )}
      <ModalForm
        title={type === 'create' ? '新增资料配置' : '编辑资料配置'}
        open={visible}
        onOpenChange={setVisible}
        form={form}
        width="70%"
        modalProps={{
          destroyOnClose: true,
          maskClosable: false,
        }}
        onFinish={(values) => onFinish(values)}
        initialValues={{}}
      >
        <ProForm.Group>
          <ProFormSelect
            options={regionOptions}
            name="country_codes"
            label="国家"
            mode="multiple"
            showSearch
            rules={[{ required: true }]}
            style={{ width: 200 }}
            disabled={type === 'edit'}
          />
          <ProFormSelect
            options={brandTypeOptions}
            name="brand_types"
            label="品牌类型"
            mode="multiple"
            rules={[{ required: true }]}
            style={{ width: 200 }}
            disabled={type === 'edit'}
          />
        </ProForm.Group>
        <ProFormList name="datas">
          <ProForm.Group>
            <ProFormSelect
              options={attachmentTypes}
              name="type"
              label="附件类型"
              rules={[{ required: true }]}
              style={{ width: 150 }}
            />
            <ProForm.Item name="path" label="模板文件" style={{ width: 200 }}>
              <ImageUploadMultipleBucket type="button" bucket={consoleBucket} scene="maz_cos" />
            </ProForm.Item>
            <ProFormSelect
              options={fileTypes}
              name="file_type"
              label="文件类型"
              rules={[{ required: true }]}
              mode="multiple"
              style={{ width: 150 }}
            />
            <ProFormDigit name="file_num" label="文件数量上限" rules={[{ required: true }]} />
            <ProFormSelect
              options={[
                { label: '是', value: 1 },
                { label: '否', value: 0 },
              ]}
              name="required"
              label="是否必传"
              rules={[{ required: true }]}
              style={{ width: 150 }}
            />
          </ProForm.Group>
        </ProFormList>
      </ModalForm>
    </PageContainer>
  );
};
export default SenderAttachment;
