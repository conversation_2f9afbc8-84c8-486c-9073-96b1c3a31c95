import React, { useMemo } from 'react';
import { Button, Form, Input, Select, Table, Typography } from 'antd';
import { useCss, useSetState } from 'react-use';
import { PageContainer } from '@ant-design/pro-layout';
import _ from 'lodash';
import { SearchOutlined } from '@ant-design/icons';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { queryCoverSender } from '@/services/sender';
import { findLabel, findText } from '@/utils/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
const { Text } = Typography;

const types = [
  { value: 'uin' },
  { value: 'sdkappid' },
  { value: 'group_type', label: '通道组类型', type: 1 },
  // { value: 'group_id', label: '通道组', type: 1 },
  // { value: 'tactic_id', label: '通道策略', type: 2 },
  // { value: 'provider_id', label: '供应商账号', type: 0 },
];

export const smsType = [
  { value: 0, text: '验证码' },
  { value: 1, text: '通知' },
  { value: 2, text: '营销' },
];

const groupTypeOptions = [
  { value: 'direct', label: '直连' },
  { value: 'mixed', label: '混合' },
];

const providerIdTypeOptions = [
  { value: 'sdkappid', label: 'sdkappid' },
  { value: 'uin', label: 'uin' },
  { value: 'overall', label: '全局通道' },
  { value: 'group', label: '通道组' },
];

const columns: any[] = [
  {
    title: '供应商ID',
    dataIndex: 'supplier_id',
    key: 'supplier_id',
    align: 'center',
  },
  {
    title: '供应商名称',
    dataIndex: 'supplier_name',
    key: 'supplier_name',
    align: 'center',
  },
  {
    title: '供应商账号ID',
    dataIndex: 'provider_id',
    key: 'provider_id',
    align: 'center',
  },
  {
    title: '账号名称',
    dataIndex: 'account_name',
    key: 'account_name',
    align: 'center',
  },
  {
    title: 'SMPP账号',
    dataIndex: 'smpp_account',
    key: 'smpp_account',
    align: 'center',
  },
  {
    title: '短信类型',
    dataIndex: 'sms_type',
    key: 'sms_type',
    align: 'center',
    render: (val: number[]) => _.map(val, (el) => findText(smsType, el)).join(','),
  },
  {
    title: '是否可透传国家',
    dataIndex: 'transparent_sender_country',
    key: 'transparent_sender_country',
    align: 'center',
    render: (transparent_sender_country: boolean) => (transparent_sender_country ? '是' : '否'),
  },
  {
    title: '是否已覆盖',
    dataIndex: 'hasCover',
    key: 'hasCover',
    align: 'center',
    render: (hasCover: boolean) => (
      <Text type={hasCover ? 'success' : 'danger'}>{hasCover ? '是' : '否'}</Text>
    ),
  },
  {
    title: '通道类型',
    dataIndex: 'provider_id_type',
    key: 'provider_id_type',
    align: 'center',
    render: (val: string) => findLabel(providerIdTypeOptions, val),
  },
];

const SenderCoverSearch = () => {
  const { regionOptions } = useFetchCountryInfo();
  const [form] = Form.useForm();

  const [searchKeys, setSearchKeys] = useSetState<{
    type: string;
    sender_id: string;
    value: string;
  }>({
    type: 'uin',
    sender_id: '',
    value: '',
  });

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    if (!searchKeys.sender_id) return;
    const res = await queryCoverSender({
      ..._.pickBy(searchKeys, (v) => !!v),
    });
    return res.data;
  }, [searchKeys]);

  const list = useMemo(() => {
    const unCoverList = _.map(state?.not_resource_provider, (v) => ({ ...v, hasCover: false }));
    const coverList = _.map(state?.has_resource_provider, (v) => ({ ...v, hasCover: true }));
    return _.union(unCoverList, coverList);
  }, [state]);

  const typeClass = useCss({
    'div.type_margin_0': {
      marginRight: 0,
    },
  });

  return (
    <PageContainer>
      <div>
        <Form
          form={form}
          layout="inline"
          labelAlign="right"
          onFinish={(vals) => setSearchKeys({ ...vals })}
          initialValues={{ type: 'uin' }}
          className={typeClass}
        >
          <Form.Item name="country_code" label="国家/地区" rules={[{ required: true }]}>
            <Select
              showSearch
              allowClear
              placeholder="国家/地区"
              options={regionOptions}
              filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item name="type" rules={[{ required: true }]} className="type_margin_0">
            <Select
              options={types}
              onChange={() => form.setFieldsValue({ value: undefined })}
              style={{ minWidth: 80 }}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.type !== curValues.type}
          >
            {({ getFieldValue }) => {
              const type = getFieldValue('type');
              // const item = _.find(types, (v) => v.value === type) as {
              //   label: string;
              //   type: 0 | 1 | 2;
              // };
              if (['uin', 'sdkappid'].includes(type)) {
                return (
                  <Form.Item label="" name="value" rules={[{ required: true }]}>
                    <Input style={{ width: 160 }} placeholder="请输入" />
                  </Form.Item>
                );
              }
              return (
                <Form.Item name="value" rules={[{ required: true }]}>
                  <Select options={groupTypeOptions} style={{ width: 120 }} />
                  {/* <ChannelSelect
                    mode="default"
                    value={getFieldValue('value')}
                    reloadOn={getFieldValue('type')}
                    type={item.type}
                    onChange={(value) => {
                      form.setFieldsValue({ value });
                    }}
                    placeholder={`请选择${item?.label}`}
                    style={{ width: 220 }}
                  /> */}
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item name="sender_id" label="sender_id" rules={[{ required: true }]}>
            <Input style={{ width: 120 }} />
          </Form.Item>
          <Form.Item>
            <Button htmlType="submit" type="primary" icon={<SearchOutlined />}>
              查询
            </Button>
          </Form.Item>
        </Form>
      </div>
      <div style={{ marginTop: 15 }}>
        <Table
          columns={columns}
          dataSource={list}
          rowKey={(r, i) => `${i}`}
          loading={loading}
          pagination={false}
        />
      </div>
    </PageContainer>
  );
};
export default SenderCoverSearch;
