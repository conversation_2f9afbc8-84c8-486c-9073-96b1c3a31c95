import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, message, Select, Popconfirm } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useAsyncFn, useSetState } from 'react-use';
import { getGlobalSenderList, deleteGlobalSender } from '@/services/sender';
import { useDialogRef } from '@/utils/react-use/useDialog';
import GlobalSenderAdd from './globalSenderAdd';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const GlobalSenderManage = () => {
  const [form] = Form.useForm();
  const { regionOptions } = useFetchCountryInfo();
  const [pagination, setPagination] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [searchKeys, setSearchKeys] = useState({});
  const dialogRef = useDialogRef();

  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: 'level',
      dataIndex: 'level',
      key: 'level',
      align: 'center',
    },
    // {
    //   title: 'country_code',
    //   dataIndex: 'country_code',
    //   key: 'country_code',
    //   align: 'center',
    // },
    {
      title: '国家/地区',
      dataIndex: 'country_name',
      key: 'country_name',
      align: 'center',
    },
    {
      title: 'mcc',
      dataIndex: 'mcc',
      key: 'mcc',
      align: 'center',
      render: (key: any) => key || '_',
    },
    {
      title: 'senderId',
      dataIndex: 'sender_id',
      key: 'sender_id',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      render: (row: any) => (
        <>
          <Popconfirm
            title="确认删除此条数据吗？"
            onConfirm={() => handleDelete(row)}
            okText="Yes"
            cancelText="No"
          >
            <Button type="link">删除</Button>
          </Popconfirm>
          <Button
            type="link"
            onClick={() => {
              dialogRef.current.open({ initialValues: row });
            }}
          >
            编辑
          </Button>
        </>
      ),
    },
  ];

  const [state, getList] = useAsyncFn(async () => {
    const res = await getGlobalSenderList({
      ...pagination,
      ...searchKeys,
    });
    return res.data;
  }, [pagination, searchKeys]);

  function onSubmit(vals: any) {
    Object.keys(vals).forEach((k) => {
      if (!vals[k]) delete vals[k];
    });
    setSearchKeys(vals);
  }

  async function handleDelete(row: any) {
    try {
      const res = await deleteGlobalSender({ id: row.id });
      if (res.code === 0) {
        message.success('删除成功');
        getList();
      }
    } catch (err) {
      message.error('删除失败');
      console.log(err);
    }
  }

  useEffect(() => {
    getList();
  }, [pagination, searchKeys]);

  return (
    <PageContainer>
      <Button
        type="primary"
        style={{ marginBottom: 10 }}
        onClick={() => {
          dialogRef.current.open({ initialValues: {} });
        }}
      >
        添加
      </Button>
      <Form
        className="sender-search-form"
        labelCol={{ span: 6 }}
        form={form}
        // initialValues={{ ...formData }}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="sender_id">
          <Input placeholder="senderId" />
        </Form.Item>
        <Form.Item name="country_code">
          <Select
            showSearch
            allowClear
            placeholder="国家/地区"
            options={regionOptions}
            filterOption={(input, option) => {
              return (
                option?.label.includes(input) ||
                option?.value.includes(input.toLocaleUpperCase()) ||
                false
              );
            }}
            style={{ width: 250 }}
            // onSearch={(value) => setSearchVal(value)}
          />
        </Form.Item>
        <Form.Item name="level">
          <Select
            showSearch
            allowClear
            placeholder="级别"
            options={[
              { label: 1, value: 1 },
              { label: 2, value: 2 },
              { label: 3, value: 3 },
            ]}
            style={{ width: 250 }}
            // onSearch={(value) => setSearchVal(value)}
          />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={state.value?.list}
        rowKey={(record: any) => record.id}
        loading={state.loading}
        pagination={{
          defaultCurrent: 1,
          total: state.value?.count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPagination({ page_size }),
          onChange: (page) => {
            setPagination({ page_index: page });
          },
        }}
      />
      <GlobalSenderAdd dialogRef={dialogRef} reload={getList} />
    </PageContainer>
  );
};

export default GlobalSenderManage;
