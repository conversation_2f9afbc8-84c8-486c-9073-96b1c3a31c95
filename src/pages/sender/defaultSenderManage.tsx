import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, message, Select, Popconfirm } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useAsyncFn, useSetState } from 'react-use';
import { getDefaultSenderList, deleteDefaultSender } from '@/services/sender';
import { useDialogRef } from '@/utils/react-use/useDialog';

import DefaultSenderAdd from './defaultSenderAdd';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const DefaultSenderManage = () => {
  const [form] = Form.useForm();
  const { regionOptions } = useFetchCountryInfo();
  const [pagination, setPagination] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [searchKeys, setSearchKeys] = useState({});
  const dialogRef = useDialogRef();

  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    // {
    //   title: 'type',
    //   dataIndex: 'type',
    //   key: 'type',
    //   align: 'center',
    // },
    {
      title: 'uin',
      dataIndex: 'uin',
      key: 'uin',
      align: 'center',
    },
    {
      title: '国家/地区',
      dataIndex: 'country_name',
      key: 'country_name',
      align: 'center',
      render: (key: any) => key || '_',
    },
    {
      title: 'mcc',
      dataIndex: 'mcc',
      key: 'mcc',
      align: 'center',
      render: (key: any) => key || '_',
    },
    {
      title: 'sdkappid',
      dataIndex: 'sdkappid',
      key: 'sdkappid',
      align: 'center',
      render: (key: any) => key || '_',
    },
    {
      title: '供应商账号ID',
      dataIndex: 'provider_id',
      key: 'provider_id',
      align: 'center',
      render: (key: any) => key || '_',
    },
    {
      title: '供应商名称',
      dataIndex: 'provider_name',
      key: 'provider_name',
      align: 'center',
      render: (key: any) => key || '_',
    },
    {
      title: 'senderId',
      dataIndex: 'sender_id',
      key: 'sender_id',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      render: (row: any) => (
        <>
          <Popconfirm
            title="确认删除此条数据吗？"
            onConfirm={() => handleDelete(row)}
            okText="Yes"
            cancelText="No"
          >
            <Button type="link">删除</Button>
          </Popconfirm>
          <Button
            type="link"
            onClick={() => {
              dialogRef.current.open({ initialValues: row });
            }}
          >
            编辑
          </Button>
        </>
      ),
    },
  ];

  const [state, getList] = useAsyncFn(async () => {
    const res = await getDefaultSenderList({
      ...pagination,
      ...searchKeys,
    });
    return res.data;
  }, [pagination, searchKeys]);

  function onSubmit(vals: any) {
    Object.keys(vals).forEach((k) => {
      if (!vals[k]) delete vals[k];
    });
    setSearchKeys(vals);
  }

  async function handleDelete(row: any) {
    try {
      const res = await deleteDefaultSender({ id: row.id });
      if (res.code === 0) {
        message.success('删除成功');
        getList();
      }
    } catch (err) {
      message.error('删除失败');
      console.log(err);
    }
  }

  useEffect(() => {
    getList();
  }, [pagination, searchKeys]);

  return (
    <PageContainer>
      <Button
        type="primary"
        style={{ marginBottom: 10 }}
        onClick={() => {
          dialogRef.current.open({ initialValues: {} });
        }}
      >
        添加
      </Button>
      <Form
        className="sender-search-form"
        labelCol={{ span: 6 }}
        form={form}
        // initialValues={{ ...formData }}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="uin">
          <Input placeholder="uin" />
        </Form.Item>
        <Form.Item name="sender_id">
          <Input placeholder="senderId" />
        </Form.Item>
        <Form.Item name="country_code">
          <Select
            showSearch
            allowClear
            placeholder="国家/地区"
            options={regionOptions}
            filterOption={(input, option) => {
              console.log(
                option?.value,
                input,
                option?.label.includes(input) ||
                  option?.value.includes(input.toLocaleUpperCase()) ||
                  false,
              );
              return (
                option?.label.includes(input) ||
                option?.value.includes(input.toLocaleUpperCase()) ||
                false
              );
            }}
            style={{ width: 250 }}
            // onSearch={(value) => setSearchVal(value)}
          />
        </Form.Item>
        <Form.Item name="sdkappid">
          <Input placeholder="sdkappid" />
        </Form.Item>
        <Form.Item name="supplier_name">
          <Input placeholder="供应商名称" />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={state.value?.list}
        rowKey={(record: any) => record.id}
        loading={state.loading}
        pagination={{
          defaultCurrent: 1,
          total: state.value?.count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPagination({ page_size }),
          onChange: (page) => {
            setPagination({ page_index: page });
          },
        }}
      />
      <DefaultSenderAdd dialogRef={dialogRef} reload={getList} />
    </PageContainer>
  );
};

export default DefaultSenderManage;
