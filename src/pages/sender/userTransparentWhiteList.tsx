import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Input, message, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  deleteWhiteUserSender,
  getWhiteUserSender,
  addWhiteUserSender,
  getMandatorySenderList,
} from '@/services/sender';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import SelectOptionsByInput from '@/components/SelectOptionsByInput';
import { ForceSenderValidateDialog } from './component/forceSenderValidateDialog';
import { useDialogRef } from '@/utils/react-use/useDialog';

const typeOptions = [
  { value: 'uin', label: 'UIN' },
  { value: 'sdkappid', label: 'SDKAPPID' },
];

const SenderCustomerTransparentWhiteList = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const dialogRef = useDialogRef();
  const { regionOptions } = useFetchCountryInfo();
  const [open, setOpen] = useState(false);
  const [type, setType] = useState<'create' | 'edit'>('create');
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);

  async function handleDelete(ids: (number | string)[]) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除该条数据吗?`,
        onOk: async () => {
          const res = await deleteWhiteUserSender({
            ids: ids.map((v) => v.toString()),
          });
          if (res.code === 0) {
            actionRef.current?.reload();
            message.success('删除成功');
          }
        },
      });
    } catch (err) {}
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: 'UIN',
        dataIndex: 'uin',
        key: 'uin',
      },
      {
        title: 'SDKAPPID',
        dataIndex: 'sdkappid',
        key: 'sdkappid',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        render: (_r, row: any) => row.sdkappid || '-',
      },
      {
        title: '客户名称',
        dataIndex: 'cust_name',
        key: 'cust_name',
        hideInSearch: true,
      },
      {
        title: '国家/地区',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        fieldProps: {
          options: regionOptions,
          showSearch: true,
        },
        render: (_r, row: any) => {
          return _.find(regionOptions, (el) => el.value === row.country_code)?.label;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        hideInSearch: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleDelete([row.id])}>
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, [regionOptions]);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getWhiteUserSender({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function getForceSenderList(vals: any) {
    try {
      const res = await getMandatorySenderList({
        country_codes: vals.country_code,
        [vals.type]: vals[vals.type],
      });
      if (res.code !== 0) {
        return false;
      }
      // 不存在强制sender，或每个强制sender有供应商
      if (res.data.count === 0) {
        return true;
      }
      dialogRef.current.open({ list: res.data.list ?? [] });
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  function reset() {
    addFormRef.current?.setFieldsValue({
      uin: '',
      sdkappid: '',
      country_code: undefined,
      type: '',
    });
  }

  async function addWhiteList(vals: any) {
    try {
      const res = await addWhiteUserSender({
        country_codes: vals.country_code,
        type: vals.type,
        value: vals[vals.type],
      });
      if (res.code === 0) {
        actionRef.current?.reload();
        setOpen(false);
        reset();
        return true;
      }
      return false;
    } catch (err) {}
  }

  async function onFinish(vals: any) {
    try {
      const isContinue = await getForceSenderList(vals);
      if (!isContinue) {
        return false;
      }
      return await addWhiteList(vals);
    } catch (err) {
      return false;
    }
  }

  const formItems = useMemo(() => {
    return [
      {
        title: '配置层级',
        key: 'type',
        valueType: 'select',
        fieldProps: {
          options: typeOptions,
        },
      },
      {
        name: ['type'],
        valueType: 'dependency',
        columns: ({ type }: any) => {
          return type === 'uin'
            ? [
                {
                  title: 'UIN',
                  dataIndex: 'uin',
                  renderFormItem: () => {
                    return <Input />;
                  },
                },
              ]
            : [];
        },
      },
      {
        name: ['type'],
        valueType: 'dependency',
        columns: ({ type }: any) => {
          return type === 'sdkappid'
            ? [
                {
                  title: 'SDKAPPID',
                  dataIndex: 'sdkappid',
                  renderFormItem: () => {
                    return <Input />;
                  },
                },
              ]
            : [];
        },
      },
      {
        title: '国家/地区',
        key: 'country_code',
        renderFormItem: () => {
          return (
            <SelectOptionsByInput
              mode="multiple"
              showSearch
              allowClear
              placeholder="国家/地区"
              filterOption={(val: string, opt: any) =>
                opt.label.toLowerCase().includes(val.toLowerCase())
              }
              options={regionOptions}
              maxTagCount={30}
            ></SelectOptionsByInput>
          );
        },
      },
    ];
  }, [regionOptions]);

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
    <PageContainer>
      <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 5 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={450}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setOpen(true);
        }}
      >
        新增
      </Button>
      <Button
        onClick={() => {
          handleDelete(selectedRowKeys);
        }}
        style={{ marginLeft: 10 }}
        disabled={!selectedRowKeys.length}
      >
        批量删除
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
        rowSelection={{
          selectedRowKeys,
          type: 'checkbox',
          onChange: (selectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
      />
      <ForceSenderValidateDialog
        dialogRef={dialogRef}
        onContinue={async () => {
          await addWhiteList(addFormRef?.current?.getFieldsValue());
        }}
      ></ForceSenderValidateDialog>
    </PageContainer>
  );
};
export default SenderCustomerTransparentWhiteList;
