import React, { useEffect } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Form, Input, Modal, Select, message } from 'antd';
import { addTransparentCountry } from '@/services/sender';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

const AddTransparentCountry = (props: DialogProps) => {
  const { regionOptions } = useFetchCountryInfo();
  const { dialogRef, onSuccess } = props;
  const [visible, setVisible] = useDialog<{
    initialValues: any;
  }>(dialogRef);

  const [form] = Form.useForm();

  async function onSubmit(vals: { country_code: string; mnc?: string }) {
    try {
      const res = await addTransparentCountry({ ...vals, mnc: vals.mnc || '000' });
      if (res.code === 0) {
        message.success('添加成功');
        setVisible(false);
        onSuccess();
      }
    } catch (error) {
      setVisible(false);
    }
  }

  useEffect(() => {
    !visible && form.resetFields();
  }, [form, visible]);

  return (
    <Modal
      title="添加"
      open={visible}
      onCancel={() => setVisible(false)}
      destroyOnClose={true}
      onOk={() => form.submit()}
    >
      <Form
        form={form}
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
        initialValues={{ mnc: '000' }}
      >
        <Form.Item name="country_code" label="国家/地区" rules={[{ required: true }]}>
          <Select
            showSearch
            allowClear
            placeholder="国家/地区"
            options={regionOptions}
            filterOption={(inputValue, option) =>
              !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
            }
            style={{ width: 250 }}
          />
        </Form.Item>
        <Form.Item name="mnc" label="mnc">
          <Input style={{ width: 120 }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddTransparentCountry;
