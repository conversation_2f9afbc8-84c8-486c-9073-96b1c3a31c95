import React, { useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, message, Select, Popconfirm } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useSetState } from 'react-use';
import { getTransparentCountryList, deleteTransparentCountry } from '@/services/sender';
import { useDialogRef } from '@/utils/react-use/useDialog';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import AddTransparentCountry from './AddTransparentCountry';
import _ from 'lodash';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const TransparentSenderCountry = () => {
  const [form] = Form.useForm();
  const { regionOptions } = useFetchCountryInfo();
  const [searchKeys, setSearchKeys] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const dialogRef = useDialogRef();

  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '国家/地区码',
      dataIndex: 'country_code',
      key: 'country_code',
      align: 'center',
    },
    {
      title: '国家/地区',
      dataIndex: 'country_name',
      key: 'country_name',
      align: 'center',
    },
    {
      title: 'mnc',
      dataIndex: 'mnc',
      key: 'mnc',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      render: (row: any) => (
        <Popconfirm
          title="确认删除此条数据吗？"
          onConfirm={() => handleDelete(row)}
          okText="Yes"
          cancelText="No"
        >
          <Button type="link">删除</Button>
        </Popconfirm>
      ),
    },
  ];

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const res = await getTransparentCountryList({
      ..._.pickBy(searchKeys, (v) => !!v),
    });
    return res.data;
  }, [searchKeys]);

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  function onSubmit(vals: any) {
    setSearchKeys(vals);
  }

  async function handleDelete(row: any) {
    try {
      const res = await deleteTransparentCountry({ ..._.pick(row, ['country_code', 'mnc']) });
      if (res.code === 0) {
        message.success('删除成功');
        retry();
      }
    } catch (err) {
      message.error('删除失败');
      console.log(err);
    }
  }

  return (
    <PageContainer>
      <Button
        type="primary"
        style={{ marginBottom: 10 }}
        onClick={() => {
          dialogRef.current.open({ initialValues: {} });
        }}
      >
        添加
      </Button>
      <Form
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
        style={{ marginBottom: 10 }}
      >
        <Form.Item name="country_code" label="国家/地区">
          <Select
            showSearch
            allowClear
            placeholder="国家/地区"
            options={regionOptions}
            filterOption={(inputValue, option) =>
              !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
            }
            style={{ width: 250 }}
          />
        </Form.Item>
        <Form.Item name="mnc" label="mnc">
          <Input />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={list}
        rowKey={(record: any) => record.id}
        loading={loading}
        pagination={{
          current: searchKeys.page_index,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
      />
      <AddTransparentCountry dialogRef={dialogRef} onSuccess={retry} />
    </PageContainer>
  );
};

export default TransparentSenderCountry;
