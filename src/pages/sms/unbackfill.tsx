import React, { useMemo, useState } from 'react';
import { isMobile } from '@/const/jadgeUserAgent';
import { getUnfillList } from '@/services/unbackfill';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, DatePicker, Form, Input, InputNumber, message, Select, Table } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import CopyToClipboard from 'react-copy-to-clipboard';
import { useSetState } from 'react-use';
import { saveCSV } from '../global-components/saveCsv';
import MncSelect from '../channel/commonComponent/MncSelect';
import { UnbackfillExportDialog } from './component/UnbackfillExportDialog';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const { TextArea } = Input;

interface DataType {
  req_time: string;
  message_id: string;
  phone_number: string;
  operator: string;
}

function getDispatchText(rows: DataType[]) {
  return `time(UTC+8)  msg_id  phonenumber  operator\n${rows
    .map((row) => {
      return `${row.req_time}  ${row.message_id}  ${row.phone_number}  ${row.operator}`;
    })
    .join('\n')}`;
}

const { RangePicker } = DatePicker;

const columns: any = [
  {
    title: 'serial_no',
    dataIndex: 'serial_no',
    key: 'serial_no',
    align: 'left',
  },
  {
    title: '国家/地区码',
    dataIndex: 'country_code',
    key: 'country_code',
    align: 'center',
  },
  {
    title: '请求时间',
    dataIndex: 'req_time',
    key: 'req_time',
    align: 'center',
  },
  {
    title: 'phone_number',
    dataIndex: 'phone_number',
    key: 'phone_number',
    align: 'center',
  },
  {
    title: 'message_id',
    dataIndex: 'message_id',
    key: 'message_id',
    align: 'center',
  },
  {
    title: '供应商账号ID',
    dataIndex: 'provider_id',
    key: 'provider_id',
    align: 'center',
  },
  {
    title: 'operator',
    dataIndex: 'operator',
    key: 'operator',
    align: 'center',
  },
];

function exportCsv({
  list,
  route,
  params,
}: {
  list: Record<string, any>[];
  params: Record<string, any>;
  route: string;
}) {
  try {
    const head = ['time', 'country_code', 'phone_number', 'message_id', 'operator', 'provider_id'];

    saveCSV(
      `未回填号码${Date.now()}`,
      head,
      _.map(list, (v) => {
        return [
          v.req_time,
          v.country_code,
          v.phone_number,
          v.message_id,
          v.operator,
          v.provider_id,
        ];
      }),
      { params, route },
    )
      .then(() => {
        message.success('导出成功');
      })
      .catch(() => {
        message.error('导出失败');
      });
  } catch (error) {
    message.error('导出失败');
  }
}

const UnbackFill = () => {
  const [form] = Form.useForm();
  const { regionOptions } = useFetchCountryInfo();
  const [selectedRows, setSelectRows] = useState<DataType[]>([]);
  const [searchKeys, setSearchKeys] = useSetState({
    from: dayjs().startOf('day'),
    to: dayjs().endOf('day'),
    sdkappid: '',
    serial_nos: '',
    message_ids: '',
    mnc: '',
  });

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    if (!searchKeys.sdkappid) {
      return;
    }
    const res = await getUnfillList({
      ...searchKeys,
      serial_nos: searchKeys.serial_nos ? searchKeys.serial_nos?.split(',') : undefined,
      message_ids: searchKeys.message_ids ? searchKeys.message_ids?.split(',') : undefined,
    });
    return res?.data ?? {};
  }, [searchKeys]);

  const list = useMemo(() => {
    return state?.unfilled_list ?? [];
  }, [state]);

  const { total_num, filled_num, unfilled_num, cr } = useMemo(() => {
    return _.pick(state, ['total_num', 'filled_num', 'unfilled_num', 'cr']);
  }, [state]);

  return (
    <PageContainer>
      <Form
        form={form}
        layout="inline"
        labelAlign="right"
        initialValues={{
          ...searchKeys,
          time: [dayjs().startOf('day'), dayjs().endOf('day')],
        }}
        onFinish={(vals) => {
          const params = _.cloneDeep(vals);
          params.from = dayjs(params.time[0]).format('YYYY-MM-DD HH:mm:ss');
          params.to = dayjs(params.time[1]).format('YYYY-MM-DD HH:mm:ss');
          setSearchKeys({ ..._.omit(params, 'time'), mnc: vals.mnc?.split('_')[1] });
        }}
      >
        <Form.Item name="sdkappid" label="sdkappid" rules={[{ required: true }]}>
          <InputNumber style={{ width: 150 }} controls={false} />
        </Form.Item>
        <Form.Item name="provider_id" label="provider_id">
          <InputNumber style={{ width: 150 }} controls={false} />
        </Form.Item>
        <Form.Item name="serial_nos" label="serial_nos" style={{ marginBottom: 10 }}>
          <TextArea rows={3} style={{ width: 200 }} placeholder="支持多个，英文逗号分隔" />
        </Form.Item>
        <Form.Item name="message_ids" label="message_ids">
          <TextArea rows={3} style={{ width: 200 }} placeholder="支持多个，英文逗号分隔" />
        </Form.Item>
        <Form.Item name="country_code" label="国家/地区" rules={[{ required: true }]}>
          <Select
            options={regionOptions}
            onChange={(val: any) => {
              form.setFieldsValue({ country_code: val, mnc: undefined });
            }}
            style={{ width: 180 }}
            placeholder="请选择"
            allowClear
            showSearch
            filterOption={(input, option: any) =>
              option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.country_code !== curValues.country_code
          }
        >
          {({ getFieldValue }) => {
            return (
              <Form.Item name="mnc" label="运营商">
                <MncSelect
                  initialValues={{ country_code: getFieldValue('country_code') }}
                  onChange={(mnc: any) => form.setFieldsValue({ mnc })}
                  value={form.getFieldValue('mnc')}
                  allowClear
                  style={{ width: 180 }}
                  placeholder="请选择"
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="time" rules={[{ required: true }]}>
          <RangePicker
            showTime
            presets={[
              { label: 'Today', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
              {
                label: 'Yesterday',
                value: [
                  dayjs().subtract(1, 'days').startOf('day'),
                  dayjs().subtract(1, 'days').endOf('day'),
                ],
              },
              {
                label: 'Week',
                value: [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
              },
              {
                label: 'Month',
                value: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
              },
            ]}
          />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading} icon={<SearchOutlined />}>
            查询
          </Button>
          <Button
            style={{ marginLeft: 10 }}
            loading={loading}
            type="primary"
            icon={<DownloadOutlined />}
            onClick={() => {
              exportCsv({ list, route: '/sms/unfilled-phone/query', params: searchKeys });
            }}
          >
            导出
          </Button>
          {/* 批量导出 */}
          <UnbackfillExportDialog></UnbackfillExportDialog>
        </Form.Item>

        <CopyToClipboard
          text={getDispatchText(selectedRows)}
          onCopy={() => {
            message.success('Copy successfully');
          }}
        >
          <Button type="primary" disabled={!selectedRows.length}>
            批量复制
          </Button>
        </CopyToClipboard>
      </Form>
      <div style={{ marginTop: 20, display: 'flex' }}>
        <div>总数 : {total_num} </div>
        <div style={{ color: '#faad14', marginLeft: 20 }}>回填数 : {filled_num} </div>
        <div style={{ color: '#f5222d', marginLeft: 20 }}>未回填数 : {unfilled_num} </div>
        <div style={{ marginLeft: 20 }}>cr : {cr} </div>
      </div>

      <Table
        dataSource={list}
        rowKey="serial_no"
        columns={columns}
        loading={loading}
        pagination={false}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        style={{ marginTop: 20 }}
        rowSelection={{
          type: 'checkbox',
          onChange: (selectedRowKeys: React.Key[], selectedRows: DataType[]) => {
            setSelectRows(selectedRows);
          },
        }}
      />
    </PageContainer>
  );
};
export default UnbackFill;
