import React, { useRef, useState } from 'react';
import { queryIsmsRecord } from '@/services/thrdAPI';
import { CopyOutlined, SearchOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { Alert, Button, DatePicker, Form, Input, message, Select } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useSetState } from 'react-use';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { processRowsData } from '../global-components/saveCsv';
import { copy } from '@/utils/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import styles from './index.module.less';
import IsmsSendDetail from './ismsSendDetail';
import { isMobile } from '@/const/jadgeUserAgent';

const { RangePicker } = DatePicker;

const columns: ProColumns<any>[] = [
  {
    title: 'uin',
    dataIndex: 'uin',
    key: 'uin',
    align: 'center',
  },

  {
    title: 'appid',
    dataIndex: 'appid',
    key: 'appid',
    align: 'center',
  },
  {
    title: 'sdkappid',
    dataIndex: 'sdkappid',
    key: 'sdkappid',
    align: 'center',
  },
  {
    title: '内容',
    dataIndex: 'content',
    key: 'content',
    align: 'center',
    width: 200,
  },
  {
    title: 'src_sender_id',
    dataIndex: 'src_sender_id',
    key: 'src_sender_id',
    align: 'center',
  },
  {
    title: 'dst_sender_id',
    dataIndex: 'dst_sender_id',
    key: 'dst_sender_id',
    align: 'center',
  },
  {
    title: '电话',
    key: 'phone',
    align: 'center',
    width: 160,
    render: (row: { phone_number: string; nationcode: number }) =>
      `（${row.nationcode}）${row.phone_number}`,
  },
  {
    title: '客户名称',
    dataIndex: 'customer_name',
    key: 'customer_name',
    align: 'center',
  },
  {
    title: '运营商',
    dataIndex: 'operator',
    key: 'operator',
    align: 'center',
  },
  {
    title: '归属地',
    dataIndex: 'belonging_place',
    key: 'belonging_place',
    align: 'center',
  },
  {
    title: 'template_id',
    dataIndex: 'template_id',
    key: 'template_id',
    align: 'center',
  },
  {
    title: '请求时间',
    dataIndex: 'req_time',
    key: 'req_time',
    align: 'center',
    width: 120,
  },
  {
    title: 'message_id',
    dataIndex: 'message_id',
    key: 'message_id',
    align: 'center',
    width: 120,
  },
  {
    title: 'serial_no',
    dataIndex: 'serial_no',
    key: 'serial_no',
    align: 'center',
    width: 180,
  },
  {
    title: 'fee',
    dataIndex: 'fee',
    key: 'fee',
    align: 'center',
  },
  {
    title: '供应商账号ID',
    dataIndex: 'provider_id',
    key: 'provider_id',
    align: 'center',
  },
  {
    title: '供应商名称',
    dataIndex: 'provider_name',
    key: 'provider_name',
    align: 'center',
  },
  {
    title: 'site_type',
    dataIndex: 'site_type',
    key: 'site_type',
    align: 'center',
  },
  {
    title: '国家/地区码',
    dataIndex: 'country_code',
    key: 'country_code',
    align: 'center',
  },
  {
    title: '提交结果',
    dataIndex: 'result_text',
    key: 'result_text',
    align: 'center',
  },
  {
    title: '回执状态',
    dataIndex: 'dr_status',
    key: 'dr_status',
    align: 'center',
  },
  {
    title: '回执时间',
    dataIndex: 'done_date',
    key: 'done_date',
    align: 'center',
    width: 120,
  },
  {
    title: '回执用时',
    key: 'done_date',
    align: 'center',
    render: (row: { req_time: string; done_date: string }) => {
      if (!row?.done_date) {
        return '--';
      }
      const duration = (+new Date(row.done_date) - +new Date(row.req_time)) / 1000;
      return duration >= 1 ? duration : 0;
    },
  },
];

const IsmsQuery = () => {
  const [form] = Form.useForm();
  const actionRef = useRef<ActionType>();
  const { regionOptionsNationCode = [] } = useFetchCountryInfo();
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [searchKeys, setSearchKeys] = useSetState({
    from: dayjs().subtract(2, 'day'),
    to: dayjs().add(1, 'day'),
    phone_number: '',
  });
  const [expandedRowKeys, setExpandKeys] = useState<number[]>([]);

  const requestFn = async (params: any) => {
    const vals = _.omit(
      _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
      ['pageSize', 'current'],
    );

    // 验证必填字段
    const requiredFields = ['customer_name', 'phone_number', 'uin'];
    const hasRequiredField = requiredFields.some(
      (field) => (vals[field as string] as string)?.trim(),
    );
    if (!hasRequiredField) {
      message.error('电话/uin/客户名称 三个字段至少填一个');
      return;
    }

    if (!vals.from || !vals.to) {
      message.error('时间范围不能为空');
      return;
    }

    const { from, to, ...restVals } = vals;

    // 处理字符串值
    const normalizedVals = _.mapValues(restVals, (v) => {
      if (typeof v !== 'string') return v;
      return v
        .split('\n')
        .map((s) => s.trim())
        .filter(Boolean);
    });

    const res = await queryIsmsRecord({
      ...normalizedVals,
      from,
      to,
      page_index: params.current,
      page_size: params.pageSize,
    });
    // 清空选中行
    setSelectedRows([]);
    return {
      data: res?.data?.list ?? [],
      success: true,
      total: res?.data?.count,
    };
  };

  const disabledCopy = selectedRows.length === 0;

  const handleFormSubmit = (vals: any) => {
    const params = _.cloneDeep(vals);
    params.from = dayjs(params.time[0]).format('YYYY-MM-DD HH:mm:ss');
    params.to = dayjs(params.time[1]).format('YYYY-MM-DD HH:mm:ss');

    actionRef.current?.reload();
    setSearchKeys({ ..._.omit(params, 'time') });
  };

  const handleCopy = () => {
    if (!selectedRows.length) return;
    const { headerText, exportData } = processRowsData(selectedRows, columns);
    const rowsText = exportData.map((row) => row.join(' '));
    const text = [headerText.join(' '), ...rowsText].join('\n');
    copy(text);
  };

  const renderFormItems = () => (
    <>
      {[
        'customer_name',
        'phone_number',
        'uin',
        'appid',
        'src_sender_id',
        'message_id',
        // 'sender',
        'dr_status',
        'nation_code',
      ].map((field) => {
        const fieldLabel =
          field === 'phone_number'
            ? '电话'
            : columns.find((col: any) => col.dataIndex === field)?.title;
        return (
          <Form.Item key={field} name={field}>
            {field === 'nation_code' ? (
              <Select
                mode="multiple"
                allowClear
                showSearch
                filterOption={(inputValue, option) =>
                  !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
                }
                options={regionOptionsNationCode}
                placeholder="请选择国家/地区码"
                style={{ width: 250 }}
              />
            ) : (
              <Input.TextArea
                // 除了phone_number，其他都是精确搜索
                placeholder={`${fieldLabel} ${
                  field === 'customer_name' ? '模糊搜索（必填）' : '精准搜索'
                }\n换行多个`}
                style={{ width: 250 }}
              />
            )}
          </Form.Item>
        );
      })}
      <Form.Item name="time" rules={[{ required: true }]}>
        <RangePicker
          showTime
          presets={[
            { label: 'Today', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
            {
              label: 'Yesterday',
              value: [
                dayjs().subtract(1, 'days').startOf('day'),
                dayjs().subtract(1, 'days').endOf('day'),
              ],
            },
            {
              label: 'Week',
              value: [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
            },
            {
              label: 'Month',
              value: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
            },
          ]}
        />
      </Form.Item>
    </>
  );

  return (
    <PageContainer>
      <Alert
        message="电话/uin/客户名称 三个字段至少填一个"
        type="info"
        showIcon
        style={{ marginBottom: 10 }}
      />
      <Form
        form={form}
        layout="inline"
        labelAlign="right"
        initialValues={{
          ...searchKeys,
          time: [dayjs().subtract(2, 'day'), dayjs().add(1, 'day')],
        }}
        onFinish={handleFormSubmit}
      >
        <div className={styles.formItemWrap}>
          {renderFormItems()}
          <Form.Item>
            <Button htmlType="submit" type="primary" icon={<SearchOutlined />}>
              查询
            </Button>
          </Form.Item>
          <Form.Item>
            <Button
              htmlType="button"
              type="primary"
              onClick={handleCopy}
              icon={<CopyOutlined />}
              disabled={disabledCopy}
            >
              批量复制
            </Button>
          </Form.Item>
        </div>
      </Form>
      <ProTable
        rowKey="serial_no"
        actionRef={actionRef}
        search={false}
        columns={columns}
        request={requestFn as any}
        params={searchKeys}
        rowSelection={{
          selectedRowKeys: selectedRows.map((row) => row.serial_no),
          onChange: (_, rows) => {
            setSelectedRows(rows);
          },
        }}
        columnsState={{
          persistenceKey: 'isms-query-columns',
          persistenceType: 'localStorage',
        }}
        expandable={{
          expandedRowKeys,
          rowExpandable: (record) => record?.reissue === 1,
          onExpandedRowsChange: (expandedKeys) => {
            setExpandKeys(expandedKeys as number[]);
          },
          expandedRowRender: (context, index, indent, expanded) => (
            <IsmsSendDetail
              expanded={expanded}
              params={{
                phone_number: context.phone_number,
                from: searchKeys.from,
                to: searchKeys.to,
                serial_no: context.serial_no,
                uin: context.uin,
                sdkappid: context.sdkappid,
                request_id: context.request_id,
              }}
              row={context}
            />
          ),
        }}
        scroll={isMobile() ? { x: 'max-content' } : { x: 2600 }}
        style={{ marginTop: 20 }}
      />
    </PageContainer>
  );
};
export default IsmsQuery;
