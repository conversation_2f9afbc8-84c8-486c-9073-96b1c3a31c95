import React, { useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, Space, Popconfirm, message } from 'antd';
import { getAppInfo, setPullApp } from '@/services/scdAPI';
import { SearchOutlined } from '@ant-design/icons';
import CopyText from '@/utils/CopyText';
import { history } from 'umi';
import { useSetState } from 'react-use';
import _ from 'lodash';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { isMobile } from '@/const/jadgeUserAgent';

const SmsSdkappidQuery = () => {
  const [form] = Form.useForm();
  const [searchVals, setSearchVals] = useSetState({
    page_index: 1,
    page_size: 10,
  });

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const _vals = _.pickBy(searchVals, (v) => v && !_.isNil(v));
    const params = _.omit(_vals, ['page_index', 'page_size']);
    if (!Object.keys(params).length) return;
    const res = await getAppInfo({ ..._vals });
    return res?.data;
  }, [searchVals]);

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  async function setPullCallback(row: any) {
    const res = await setPullApp({
      sdkappid: row.sdkappid,
      status: row.pull_result === 0 ? 1 : 0,
    });
    res?.code === 0 && message.success('操作成功');
    retry();
  }

  async function onSubmit(vals: any) {
    setSearchVals({ ...vals, page_index: 1 });
  }

  return (
    <PageContainer>
      <Form
        labelCol={{ span: 6 }}
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="qappid" normalize={(value) => value.trim()}>
          <Input style={{ width: 200 }} placeholder="qappid" />
        </Form.Item>
        <Form.Item name="uin" normalize={(value) => value.trim()}>
          <Input style={{ width: 200 }} placeholder="uin" />
        </Form.Item>
        <Form.Item name="sdkappid" normalize={(value) => value.trim()}>
          <Input style={{ width: 200 }} placeholder="sdkappid" />
        </Form.Item>
        <Form.Item name="appname" normalize={(value) => value.trim()}>
          <Input style={{ width: 200 }} placeholder="appname" />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={[
          {
            title: 'sdkappid',
            dataIndex: 'sdkappid',
            key: 'sdkappid',
            align: 'center',
            render: (sdkappid: number) => {
              return (
                <>
                  <CopyText
                    text={`${sdkappid}`}
                    width={120}
                    render={() => {
                      return (
                        <Button
                          type="link"
                          onClick={() => {
                            history.push(`/sms/sdkappid-detail?sdkappid=${sdkappid}`);
                          }}
                        >
                          {sdkappid}
                        </Button>
                      );
                    }}
                  />
                </>
              );
            },
          },
          {
            title: 'qappid',
            dataIndex: 'qappid',
            key: 'qappid',
            align: 'center',
          },
          {
            title: 'uin',
            dataIndex: 'uin',
            key: 'uin',
            align: 'center',
          },
          {
            title: 'name',
            dataIndex: 'name',
            key: 'name',
            align: 'center',
          },
          {
            title: 'channel_mask',
            dataIndex: 'channel_mask',
            key: 'channel_mask',
            align: 'center',
          },
          {
            title: '检查ip白名单',
            dataIndex: 'check_ip',
            key: 'check_ip',
            align: 'center',
          },
          {
            title: '检查验证短信模版',
            dataIndex: 'check_normal_template',
            key: 'check_normal_template',
            align: 'center',
          },
          {
            title: '检查营销短信模版',
            dataIndex: 'check_business_template',
            key: 'check_business_template',
            align: 'center',
          },
          {
            title: '默认签名',
            dataIndex: 'default_sign',
            key: 'default_sign',
            align: 'center',
          },
          {
            title: '校验签名',
            dataIndex: 'verify_sign',
            key: 'verify_sign',
            align: 'center',
          },
          {
            title: '强制调度',
            dataIndex: 'force_result',
            key: 'force_result',
            align: 'center',
            render: (res: number) => {
              return <span>{res === 1 ? '是' : '否'}</span>;
            },
          },
          {
            title: '启用拉取短信回调',
            align: 'center',
            render: (row: any) => {
              return (
                <Space>
                  <span>{row.pull_result === 1 ? '是' : '否'}</span>
                  <Popconfirm
                    title="Are you sure to operate?"
                    onConfirm={() => {
                      setPullCallback(row);
                    }}
                    okText="Yes"
                    cancelText="No"
                  >
                    <a>{row.pull_result === 1 ? '禁用' : '启用'}</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
        dataSource={list}
        rowKey={(record) => record.sdkappid}
        loading={loading}
        style={{ marginTop: 20 }}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        pagination={{
          current: searchVals.page_index,
          total,
          showSizeChanger: true,
          onShowSizeChange: (_, page_size) => setSearchVals({ page_size }),
          onChange: (page_index) => {
            setSearchVals({ page_index });
          },
        }}
      />
    </PageContainer>
  );
};

export default SmsSdkappidQuery;
