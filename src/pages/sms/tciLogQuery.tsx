import React, { useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { queryTciLog } from '@/services/thrdAPI';
import { Table, Form, Button, Select, InputNumber, DatePicker } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import CopyText from '@/utils/CopyText';
import dayjs from 'dayjs';
import _ from 'lodash';
import { useSetState } from 'react-use';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { isMobile } from '@/const/jadgeUserAgent';

const region = [
  { label: '新加坡', value: 'sg' },
  { label: '法兰克福', value: 'ge' },
  { label: '俄罗斯', value: 'ru' },
  { label: '国内', value: 'cn' },
];
const { RangePicker } = DatePicker;

const TciLogQuery = () => {
  const [form] = Form.useForm();
  const [searchVals, setSearchVals] = useSetState({
    page_index: 1,
    page_size: 10,
    time: [dayjs().subtract(1, 'days'), dayjs()],
    type: region[0].value,
  });

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    const vals: Record<string, any> = _.pickBy(searchVals, (v: any) => v !== '');
    vals.from = dayjs(vals.time[0]).format('YYYY-MM-DD HH:mm:ss');
    vals.to = dayjs(vals.time[1]).format('YYYY-MM-DD HH:mm:ss');
    const response = await queryTciLog({ ..._.omit(vals, 'time') });
    return response?.data || {};
  }, [searchVals]);

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const count = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const columns: any = [
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      align: 'center',
    },
    {
      title: '客户名称',
      dataIndex: 'main_acc',
      key: 'main_acc',
      align: 'center',
    },
    {
      title: '客户APPID',
      key: 'appid',
      dataIndex: 'appid',
      align: 'center',
    },
    {
      title: 'Source SenderID',
      dataIndex: 'sourceSenderId',
      key: 'sourceSenderId',
      align: 'center',
    },
    {
      title: 'Final SenderID',
      dataIndex: 'finalSenderId',
      key: 'finalSenderId',
      align: 'center',
    },
    {
      title: '供应商账户',
      dataIndex: 'agency',
      key: 'agency',
      align: 'center',
    },
    {
      title: '短信内容',
      dataIndex: 'content',
      key: 'content',
      align: 'center',
      ellipsis: true,
      width: 120,
      render: (content: string) => <CopyText text={content} width={120} />,
    },
    {
      title: '短信计费条数',
      dataIndex: 'smsNum',
      key: 'smsNum',
      align: 'center',
      ellipsis: true,
      width: 50,
    },
    {
      title: 'Message ID',
      dataIndex: 'msgId',
      key: 'msgId',
      align: 'center',
      ellipsis: true,
      width: 100,
      render: (msgId: string) => <CopyText text={msgId} width={100} />,
    },
    {
      title: '发送时间',
      dataIndex: 'drTime',
      key: 'drTime',
      align: 'center',
    },
    {
      title: '下发时间',
      dataIndex: 'sendTime',
      key: 'sendTime',
      align: 'center',
    },
    {
      title: 'DR返回状态',
      dataIndex: 'drStatus',
      key: 'drStatus',
      align: 'center',
    },
    {
      title: '回执Code',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
    },
    {
      title: 'DR耗时（秒）',
      dataIndex: 'drCostTime',
      key: 'drCostTime',
      align: 'center',
    },
  ];

  return (
    <PageContainer>
      <Form
        form={form}
        layout="inline"
        labelAlign="right"
        initialValues={searchVals}
        onFinish={(vals) => {
          setSearchVals({ ...vals, page_index: 1 });
        }}
      >
        <Form.Item name="type" rules={[{ required: true }]}>
          <Select options={region} style={{ width: 160 }} placeholder="国家地区" />
        </Form.Item>
        <Form.Item name="phone">
          <InputNumber style={{ width: 200 }} placeholder="手机号" />
        </Form.Item>
        <Form.Item name="time" rules={[{ required: true }]}>
          <RangePicker showTime />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading} icon={<SearchOutlined />}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        dataSource={list}
        columns={columns}
        loading={loading}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        pagination={{
          current: searchVals.page_index,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchVals({ page_size }),
          onChange: (page_index) => {
            setSearchVals({ page_index });
          },
        }}
        style={{ marginTop: 20 }}
      />
    </PageContainer>
  );
};
export default TciLogQuery;
