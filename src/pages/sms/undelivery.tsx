import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Button, Tooltip, message } from 'antd';
import { ActionType, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import dayjs from 'dayjs';
import { saveCSV } from '../global-components/saveCsv';
import { getUndelivery } from '@/services/undelivery';
import MncSelect from '../channel/commonComponent/MncSelect';
import { useAsyncFn } from 'react-use';
import CopyToClipboard from 'react-copy-to-clipboard';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

function getDispatchText(rows: any) {
  return `time(UTC+8)  msg_id  phonenumber  operator\n${rows
    .map((row) => {
      return `${row.req_time}  ${row.message_id}  ${row.phone_number}  ${row.operator}`;
    })
    .join('\n')}`;
}

const UndeliveryQuery = () => {
  const { regionOptions } = useFetchCountryInfo();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const [selectedRows, setSelectRows] = useState<Record<string, any>>([]);

  const columns: any = useMemo(() => {
    return [
      {
        title: 'uin',
        dataIndex: 'uin',
        key: 'uin',
        hideInTable: true,
      },
      {
        title: 'sdkappid',
        dataIndex: 'sdkappid',
        key: 'sdkappid',
        hideInTable: true,
      },
      {
        title: 'serial_no',
        dataIndex: 'serial_no',
        key: 'serial_no',
        hideInSearch: true,
      },
      {
        title: '国家/地区码',
        dataIndex: 'country_code',
        key: 'country_code',
        align: 'center',
        valueType: 'select',
        formItemProps: {
          rules: [{ required: true }],
          name: 'country_codes',
        },
        fieldProps: {
          options: regionOptions,
          showSearch: true,
          mode: 'multiple',
        },
      },
      {
        title: '运营商',
        dataIndex: 'mnc',
        key: 'mnc',
        align: 'center',
        renderFormItem: () => (
          <MncSelect
            disabled={formRef.current?.getFieldValue('country_codes')?.length > 1}
            initialValues={{
              country_code: formRef.current?.getFieldValue('country_codes')?.[0],
            }}
            allowClear
          ></MncSelect>
        ),
        render: (text, row) => `${row.operator}(${row.mnc})`,
      },
      {
        title: '请求时间',
        dataIndex: 'req_time',
        key: 'req_time',
        align: 'center',
        valueType: 'dateTimeRange',
        formItemProps: {
          rules: [{ required: true }],
        },
        fieldProps: {
          showTime: true,
          clearable: false,
          presets: [
            { label: 'Today', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
            {
              label: 'Yesterday',
              value: [
                dayjs().subtract(1, 'days').startOf('day'),
                dayjs().subtract(1, 'days').endOf('day'),
              ],
            },
            {
              label: 'Week',
              value: [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
            },
            {
              label: 'Month',
              value: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
            },
          ],
        },
        render: (text, row) => row.req_time ?? '-',
      },
      {
        title: '手机号',
        dataIndex: 'phone_number',
        key: 'phone_number',
        align: 'center',
        hideInSearch: true,
      },
      {
        title: 'message_id',
        dataIndex: 'message_id',
        key: 'message_id',
        align: 'center',
        hideInSearch: true,
      },
      {
        title: '供应商账号ID',
        dataIndex: 'provider_id',
        key: 'provider_id',
        align: 'center',
      },

      {
        title: 'smpp账号',
        dataIndex: 'smpp_account',
        key: 'smpp_account',
        align: 'center',
        hideInSearch: true,
      },
      {
        title: '回执状态码',
        dataIndex: 'dr_status',
        key: 'dr_status',
        align: 'center',
        hideInTable: true,
      },
    ];
  }, [regionOptions]);

  const requestFn = useCallback(async (params: any) => {
    if (!params?.country_codes || !params?.req_time) {
      return {};
    }
    const { data } = await getUndelivery({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current', 'req_time'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
      from: dayjs(params.req_time[0]).format('YYYY-MM-DD HH:mm:ss'),
      to: dayjs(params.req_time[1]).format('YYYY-MM-DD HH:mm:ss'),
      mnc: params.mnc?.split('_')[1],
    });
    return {
      data: data.undelivery_list ?? [],
      success: true,
      total: data.undelivery_list?.length ?? 0,
    };
  }, []);

  const [state, exportCsv] = useAsyncFn(
    async ({ route, params }: { params: Record<string, any>; route: string }) => {
      try {
        if (!params.country_codes) {
          message.warning('请选择国家/地区码');
          return;
        }
        const { data } = await requestFn({ ...params });
        if (!data.length) {
          message.warning('没有数据可以导出');
          return;
        }
        const head = columns
          .filter((el) => !['uin', 'sdkappid'].includes(el.key))
          .map((el) => el.title);
        saveCSV(
          `未回执号码${Date.now()}`,
          head,
          _.map(data, (v) => {
            return [
              v.serial_no,
              v.country_code,
              v.req_time,
              v.phone_number,
              v.message_id,
              v.provider_id,
              v.operator,
              v.smpp_account,
              v.dr_status,
            ];
          }),
          { params, route },
        )
          .then(() => {
            message.success('导出成功');
          })
          .catch(() => {
            message.error('导出失败');
          });
      } catch (error) {
        console.log(error);
        message.error('导出失败');
      }
    },
    [columns, requestFn],
  );

  return (
    <PageContainer>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        form={{
          ignoreRules: false,
          initialValues: { req_time: [dayjs().startOf('day'), dayjs().endOf('day')] },
          onValuesChange: (values) => {
            if (values.country_codes) {
              formRef.current?.setFieldsValue({ mnc: undefined });
            }
          },
        }}
        columns={columns}
        rowKey={(row, i) => `${i}`}
        pagination={false}
        search={{
          collapsed: false,
          collapseRender: () => null,
          labelWidth: 'auto',
          optionRender: (search, { form }) => [
            <Button
              key={0}
              onClick={() => {
                form?.resetFields();
              }}
            >
              重置
            </Button>,
            <Button
              key={1}
              type="primary"
              onClick={() => {
                form?.submit();
              }}
            >
              查询
            </Button>,
            <Button
              key={2}
              type="primary"
              ghost
              onClick={() => {
                exportCsv({
                  route: '/sms/undelivery-phone/query',
                  params: formRef.current?.getFieldsValue(),
                });
              }}
              loading={state.loading}
            >
              <Tooltip title="导出当前查询条件数据">
                <span>导出</span>
              </Tooltip>
            </Button>,
          ],
          span: {
            xs: 24,
            sm: 24,
            md: 12,
            lg: 12,
            xl: 6,
            xxl: 6,
          },
        }}
        toolBarRender={() => [
          <CopyToClipboard
            key={0}
            text={getDispatchText(selectedRows)}
            onCopy={() => {
              message.success('Copy successfully');
            }}
          >
            <Button type="primary" disabled={!selectedRows.length} style={{ marginTop: -30 }}>
              批量复制
            </Button>
          </CopyToClipboard>,
        ]}
        rowSelection={{
          type: 'checkbox',
          onChange: (selectedRowKeys: React.Key[], selectedRows: Record<string, any>[]) => {
            setSelectRows(selectedRows);
          },
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default UndeliveryQuery;
