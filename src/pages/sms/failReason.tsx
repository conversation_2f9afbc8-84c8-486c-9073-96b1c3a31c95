import React, { useMemo, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Table, Form, Button, InputNumber, DatePicker, Checkbox, Spin } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { useSetState } from 'react-use';
import _ from 'lodash';
import { queryErrAnaylysis } from '@/services/thrdAPI';
import { stateTypes } from '../global/deliveryStatus';
import { MobileQueryDialog } from './component/MobileQueryDialog';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { isMobile } from '@/const/jadgeUserAgent';

const { RangePicker } = DatePicker;

const FailReason = () => {
  const [form] = Form.useForm();
  const [searchKeys, setSearchKeys] = useSetState<{
    end_time?: string;
    start_time?: string;
    sdkappid?: number;
    qappid?: number;
  }>({});

  const mobileRef = useDialogRef();
  const [amountReqSum, setAmountReqSum] = useState(0);
  const [amountTotalReqSum, setAmountTotalReqSum] = useState(0);

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    if (!searchKeys.start_time || !searchKeys.end_time || !searchKeys.qappid) return;
    const result = await queryErrAnaylysis({
      ..._.pickBy(searchKeys, (v) => !_.isNil(v) && v !== ''),
    });
    return result;
  }, [searchKeys]);

  const list = useMemo(() => {
    return state?.data ?? [];
  }, [state]);

  const columns: any = useMemo(
    () => [
      {
        title: '月份',
        key: 'month',
        align: 'center',
        render: (row: any) => row?.month,
      },
      // {
      //   title: '应用id',
      //   key: 'sdkappid',
      //   align: 'center',
      //   render: (row: any) => row.sdkappid,
      // },
      {
        title: (
          <>
            总提交计费条数
            {amountTotalReqSum ? (
              <>
                <br />({amountTotalReqSum})
              </>
            ) : null}
          </>
        ),
        key: 'amount_req',
        align: 'center',
        render: (row: any) => (
          <Checkbox
            onChange={(e) => {
              if (e.target.checked) {
                setAmountTotalReqSum((pre) => pre + Number(row.amount_req));
              } else {
                setAmountTotalReqSum((pre) => pre - Number(row.amount_req));
              }
            }}
          >
            {row.amount_req}
          </Checkbox>
        ),
      },
      {
        title: '错误类型',
        key: 'error_type',
        align: 'center',
        width: 150,
        onCell: (): any => {
          return { colSpan: 5 };
        },
        render: (row: any) => {
          return (
            <div className="failure-reason">
              {(row?.error_data ?? []).map((v: any) => {
                return (
                  <div key={v.error_code} className="failure-reason-row">
                    <div style={{ width: 150 }} className="failure-reason-cell">
                      {_.find(stateTypes, (item) => item.value === v.error_type)?.label}
                    </div>
                    <div style={{ width: 150 }} className="failure-reason-cell">
                      {v.error_code}
                    </div>
                    <div style={{ width: 200 }} className="failure-reason-cell">
                      {v.error_info}
                    </div>
                    <div style={{ width: 180 }} className="failure-reason-cell">
                      <Checkbox
                        onChange={(e) => {
                          if (e.target.checked) {
                            setAmountReqSum((pre) => pre + Number(v.amount_req));
                          } else {
                            setAmountReqSum((pre) => pre - Number(v.amount_req));
                          }
                        }}
                      >
                        {v.amount_req}
                      </Checkbox>
                    </div>
                    <div style={{ width: 150 }} className="failure-reason-cell">
                      <Button
                        type="link"
                        onClick={() => {
                          mobileRef.current.open({
                            ...searchKeys,
                            month: row.month,
                            error_code: v.error_code,
                          });
                        }}
                      >
                        查看
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          );
        },
      },
      {
        title: '错误码（top5）',
        key: 'error_code',
        align: 'center',
        width: 150,
        onCell: () => {
          return { colSpan: 0 };
        },
      },
      {
        title: '错误码信息',
        key: 'error_info',
        width: 200,
        align: 'center',
        onCell: () => {
          return { colSpan: 0 };
        },
      },
      {
        title: (
          <>
            计费条数
            {amountReqSum ? (
              <>
                <br />({amountReqSum})
              </>
            ) : null}
          </>
        ),
        key: 'amount_req',
        width: 180,
        align: 'center',
        onCell: () => {
          return { colSpan: 0 };
        },
      },
      {
        title: '示例手机号码',
        key: 'mobile',
        width: 150,
        align: 'center',
        onCell: () => {
          return { colSpan: 0 };
        },
      },
    ],
    [amountTotalReqSum, amountReqSum, mobileRef, searchKeys],
  );

  return (
    <PageContainer>
      <Form
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => {
          const start_time = dayjs(vals?.time?.[0]).format('YYYY-MM-DD');
          const end_time = dayjs(vals?.time?.[1]).format('YYYY-MM-DD');
          setAmountReqSum(0);
          setAmountTotalReqSum(0);
          setSearchKeys({ ..._.omit(vals, 'time'), start_time, end_time });
        }}
      >
        <Form.Item name="qappid" label="qappid" rules={[{ required: true }]}>
          <InputNumber style={{ width: 200 }} placeholder="qappid" />
        </Form.Item>
        <Form.Item name="sdkappid" label="应用ID">
          <InputNumber style={{ width: 200 }} placeholder="应用ID" />
        </Form.Item>
        <Form.Item name="time" label="时间范围" rules={[{ required: true }]}>
          <RangePicker />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading} icon={<SearchOutlined />}>
            查询
          </Button>
        </Form.Item>
      </Form>
      {loading ? (
        <Spin size="large" />
      ) : (
        <Table
          bordered
          rowKey="month"
          dataSource={list}
          columns={columns}
          loading={loading}
          pagination={false}
          scroll={isMobile() ? { x: 'max-content' } : undefined}
          style={{ marginTop: 20 }}
        />
      )}
      <MobileQueryDialog dialogRef={mobileRef} />
    </PageContainer>
  );
};
export default FailReason;
