import React from 'react';
import { addPayList, getPayList, delPayList } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';
import { Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { dumpwarning } from '@/services/dumpWarning';

const typeOpts = [
  { label: '国内短信', value: 'pay_type' },
  { label: '语音短信', value: 'voice_pay_type' },
];

const PaymentQuery = () => {
  const columns: any = [
    {
      title: '腾讯云appid',
      dataIndex: 'qappid',
      key: 'qappid',
      align: 'center',
    },
    {
      title: '业务',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (type: string) => {
        return <span>{type === 'pay_type' ? '国内短信' : '语音短信'}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'modify_time',
      key: 'modify_time',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'type',
      label: '业务',
      disabled: false,
      isRequired: true,
      renderType: 'select',
      options: typeOpts,
    },
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
  ];

  async function doAdd(vals: any) {
    return await addPayList({ ...vals });
  }

  async function doDel(vals: any) {
    return await delPayList({ appid: vals.qappid, type: vals.type });
  }
  async function getList(vals?: any) {
    return await getPayList({ ...vals });
  }

  function fileExport() {
    const a = document.createElement('a');
    a.href = '/apis/sms/paid/export';
    a.click();
    dumpwarning({
      route: '/sms/paid/export',
    });
  }

  return (
    <PatternTable<{
      qappid: number;
      type: string;
    }>
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      initialValues={{ type: 'pay_type' }}
      searchKeys={[
        {
          label: '',
          name: 'appid',
        },
      ]}
      operateForm={operateForm}
      operType={2}
      searchRender={() => {
        return (
          <Button style={{ marginLeft: 10 }} icon={<DownloadOutlined />} onClick={fileExport}>
            导出
          </Button>
        );
      }}
    />
  );
};
export default PaymentQuery;
