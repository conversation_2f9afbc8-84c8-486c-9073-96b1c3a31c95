import React,{ useEffect,useState } from 'react';
import { querySmsUp } from '@/services/api';
import { SearchOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { Button,DatePicker,Form,Input,Select,Table } from 'antd';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

const SmsReplayQuery = () => {
  const [form] = Form.useForm();
  const [list, setList] = useState<any[]>([]);
  const [isLoading, setLoading] = useState<boolean>(false);

  const columns: any = [
    {
      title: 'sdkappid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '类型',
      dataIndex: 'command',
      key: 'command',
      align: 'center',
    },
    {
      title: '签名',
      dataIndex: 'sign',
      key: 'sign',
      align: 'center',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      key: 'mobile',
      align: 'center',
    },
    {
      title: 'port',
      dataIndex: 'port',
      key: 'port',
      align: 'center',
    },
    {
      title: 'extend',
      dataIndex: 'extend',
      key: 'extend',
      align: 'center',
    },
    {
      title: '内容',
      render: (row: any) => {
        return row.text || row.ori_text;
      },
      align: 'center',
    },
    {
      title: '上行时间',
      dataIndex: 'time',
      key: 'time',
      align: 'center',
    },
    {
      title: '入库时间',
      dataIndex: 'insert_time',
      key: 'insert_time',
      align: 'center',
    },
  ];

  useEffect(() => {
    form.setFieldsValue({
      iot_flag: '0',
    });
  }, []);
  async function getList(params: any) {
    setLoading(true);
    const res = await querySmsUp({
      sdkappid: params.sdkappid,
      phone: params.phone,
      sign: params.sign,
      iot_flag: params.iot_flag,
      from: dayjs(params.time[0]).format('YYYY-M-D H:m:s'),
      to: dayjs(params.time[1]).format('YYYY-M-D H:m:s'),
    });
    setList(res.data);
    setLoading(false);
  }

  async function onSubmit(vals: any) {
    if (!vals.sign) delete vals.sign;
    if (!vals.phone) delete vals.phone;
    if (!vals.sdkappid) delete vals.sdkappid;
    getList({ ...vals });
  }

  return (
    <PageContainer title="上行短信查询">
      <Form
        labelCol={{ span: 6 }}
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
        initialValues={{ time: [dayjs().startOf('day'), dayjs().endOf('day')] }}
      >
        <Form.Item name="phone" rules={[{ required: true, message: '请输入号码' }]}>
          <Input placeholder="号码" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="sdkappid">
          <Input placeholder="sdkappid" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="sign">
          <Input placeholder="签名" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="iot_flag">
          <Select style={{ width: 130 }}>
            <Option value="0">上行短信查询</Option>
            <Option value="1">上行物联卡查询</Option>
          </Select>
        </Form.Item>
        <Form.Item name="time" rules={[{ required: true, message: '请选择时间' }]}>
          <RangePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            presets={[
              { label: 'Today', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
              {
                label: 'Yesterday',
                value: [
                  dayjs().subtract(1, 'days').startOf('day'),
                  dayjs().subtract(1, 'days').endOf('day'),
                ],
              },
              {
                label: 'Week',
                value: [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
              },
              {
                label: 'Month',
                value: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
              },
            ]}
          />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={isLoading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={list}
        rowKey={(record) => record.sid}
        loading={isLoading}
        style={{ marginTop: 20 }}
      />
    </PageContainer>
  );
};

export default SmsReplayQuery;
