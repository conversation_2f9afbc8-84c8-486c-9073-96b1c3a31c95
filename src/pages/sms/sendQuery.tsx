import React, { useCallback, useMemo, useRef, useState } from 'react';
import { isMobile } from '@/const/jadgeUserAgent';
import { exportSmsSendQuery, querySmsSend } from '@/services/api';
import { submitIssue } from '@/services/issue';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { CopyOutlined, SearchOutlined, SettingOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import CopyToClipboard from 'react-copy-to-clipboard';
import { useSetState } from 'react-use';
import { CustomColsDialog } from '../global-components/CustomColsDialog';
import SendDetail from './sendDetail';
import { dumpwarning } from '@/services/dumpWarning';
import {
  ModalForm,
  ProFormInstance,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';

const { TextArea } = Input;

const { RangePicker } = DatePicker;

interface DataType {
  id: string;
  sid: any;
  callbacktime?: string;
  time: string;
  sign: string;
  text: string;
  aid: number;
  state: string;
  mobile: string;
  ori_mobile?: string;
  outsid: string;
  reissue_num: number;
}

const issueContentTemplate = [
  { value: '用户反馈收不到短信，辛苦核实' },
  { value: '送达失败，辛苦核实' },
  { value: '延时较长，辛苦核实延时原因' },
  { value: '短信未回执，辛苦核实' },
];

export function getCopyText(row: any) {
  let delay;
  if (!row?.callbacktime) {
    delay = '--';
  } else {
    const duration = (+new Date(row.callbacktime) - +new Date(row.time)) / 1000;
    delay = duration >= 1 ? duration : 0;
  }
  const contentIndex = row.text?.indexOf(row.sign) + row.sign?.length;
  const content = row.text?.slice(contentIndex + 1);
  let decodeText = content;
  if (content.length > 7) {
    decodeText = `${content.slice(0, 3)}*******${content.slice(-4)}`;
  }
  return { delay, decodeText };
}

function getDispatchText(rows: DataType[]) {
  return `${rows
    .map((row) => {
      const { decodeText, delay } = getCopyText(row);
      return `${row.mobile || row.ori_mobile}  ${row.time}  时延${delay}秒  ${
        row.state || ''
      }   「 【${row.sign}】${decodeText} 」`;
    })
    .join('\n')}\n\n请核查原因`;
}

const AllColsOptions = [
  { value: 'provider', label: '通道调度' },
  { value: 'appid', label: '应用ID' },
  { value: 'sid', label: '流水号' },
  { value: 'sign', label: '签名' },
  { value: 'type', label: '类型' },
  { value: 'mobile', label: '手机号' },
  { value: 'extend', label: '扩展' },
  { value: 'template', label: '模板ID' },
  { value: 'time', label: '发送时间' },
  { value: 'result', label: '提交状态' },
  { value: 'fee', label: '条数' },
  { value: 'callbacktime', label: '送达时间' },
  { value: 'delay', label: '时延' },
  { value: 'state', label: '返回状态' },
  { value: 'issue_number', label: '下发号码' },
  { value: 'callbackresult_text', label: '送达结果' },
  { value: 'text', label: '发送内容' },
  { value: 'outsid', label: '对客流水号' },
  { value: 'reissue_num', label: '补发次数' },
  { value: 'operation', label: '操作' },
];

const AllCols = [
  'provider',
  'appid',
  'sid',
  'sign',
  'type',
  'mobile',
  'extend',
  'template',
  'time',
  'result',
  'fee',
  'callbacktime',
  'delay',
  'state',
  'issue_number',
  'callbackresult_text',
  'text',
  'operation',
];

const SmsSendQuery = () => {
  const [form] = Form.useForm();
  const historyCols = localStorage.getItem('sendQueryCols');
  const [exportForm] = Form.useForm();
  const [isVisible, setVisible] = useState<boolean>(false);
  const [selectedRows, setSelectRows] = useState<DataType[]>([]);
  const [searchKeys, setSearchKeys] = useSetState<{
    sdkappid?: string;
    phone: string;
    time: [dayjs.Dayjs, dayjs.Dayjs];
  }>({
    time: [dayjs().subtract(3, 'days').startOf('day'), dayjs().endOf('day')],
    phone: '',
  });
  const dialogRef = useDialogRef();
  const [columns, setColumns] = useState<any[]>(
    historyCols ? JSON.parse(historyCols || '{}') : AllCols,
  );
  const [expandedRowKeys, setExpandKeys] = useState<number[]>([]);
  const [modalVisit, setModalVisit] = useSetState<{ show: boolean; data: any }>({
    show: false,
    data: {},
  });
  const formRef = useRef<ProFormInstance>();

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    try {
      if (!searchKeys.phone || !searchKeys.time) return;
      const params = {
        sdkappid: searchKeys.sdkappid,
        phone: searchKeys.phone.split('\n').join(','),
        from: dayjs(searchKeys.time[0]).format('YYYY-M-D H:m:s'),
        to: dayjs(searchKeys.time[1]).format('YYYY-M-D H:m:s'),
      };
      const response = await querySmsSend({ ..._.pickBy(params, (v) => v !== '' && !_.isNil(v)) });

      return (
        response?.data.map((el: Record<string, any>) => ({
          ...el,
          id: `${el.sid}_${Math.random() * 1000000}`,
        })) || []
      );
    } catch (error) {
      message.error('查询失败');
      return [];
    }
  }, [searchKeys]);

  const list = useMemo(() => {
    return state ?? [];
  }, [state]);

  const formatCopyText = useCallback((row: any) => {
    const { decodeText, delay } = getCopyText(row);
    return `${row.mobile || row.ori_mobile}\t${row.time}\t时延${delay}秒\t${
      row.state || ''
    }\t 「 【${row.sign}】${decodeText} 」\n\n请核查原因 `;
  }, []);

  const calculateDalay = (row: any) => {
    const callbacktime = row.callbacktime || dayjs().format('YYYY-MM-DD HH:mm:ss');
    const duration = (+new Date(callbacktime) - +new Date(row.time)) / 1000;
    return duration;
  };

  async function addIssue(values: any) {
    const { code } = await submitIssue({
      mobile: modalVisit.data.mobile,
      sid: modalVisit.data.sid,
      outsid: modalVisit.data.outsid,
      sms_content: `【${modalVisit.data.sign}】${getCopyText(modalVisit.data)?.decodeText}`,
      state: modalVisit.data.state,
      send_time: modalVisit.data.time,
      sdkappid: modalVisit.data.appid,
      issue_content: values.issue_content,
      delay_time: calculateDalay(modalVisit.data),
      sign: modalVisit.data.sign,
    });
    if (code === 0) {
      message.success('提交成功');
      retry();
      return true;
    }
    return false;
  }

  const initCols: any = useMemo(() => {
    return [
      {
        title: '通道调度',
        dataIndex: 'provider',
        key: 'provider',
        align: 'center',
        width: 100,
        filters: _.uniqBy(
          _.map(list, (v) => ({
            value: v.provider,
            text: v.provider,
          })),
          'value',
        ),
        filterMode: 'tree',
        filterSearch: true,
        onFilter: (value: string, record: any) => record.provider === value,
        render: (provider: string) => {
          return (
            <div className="send-query-text-ellipsis">
              <Tooltip
                placement="topLeft"
                title={() => {
                  return <>{provider}</>;
                }}
              >
                {provider}
              </Tooltip>
            </div>
          );
        },
      },
      {
        title: '应用ID',
        key: 'appid',
        align: 'center',
        width: 120,
        render: (row: { appid: number; app_name: string }) => (
          <div style={{ wordBreak: 'break-all' }}>
            <a
              href={`${window.location.origin}/sms/sdkappid-detail?sdkappid=${row.appid}`}
              target="_blank"
              rel="noreferrer"
            >
              {row.appid}
            </a>
            <span>{row.app_name}</span>
          </div>
        ),
        filters: _.uniqBy(
          _.map(list, (v) => ({
            value: `${v.appid}_${v.app_name}`,
            text: `${v.appid}_${v.app_name}`,
          })),
          'value',
        ),
        filterMode: 'tree',
        onFilter: (value: string, record: any) => `${record.appid}_${record.app_name}` === value,
        filterSearch: true,
      },
      {
        title: '流水号',
        dataIndex: 'sid',
        key: 'sid',
        width: 80,
        render: (sid: string) => {
          return (
            <div className="send-query-text-ellipsis">
              <Tooltip
                placement="topLeft"
                title={() => {
                  return <>{sid}</>;
                }}
              >
                {sid}
              </Tooltip>
            </div>
          );
        },
      },
      {
        title: '签名',
        key: 'sign',
        render: (row: any) => {
          return row.sign || row.ori_sign;
        },
        width: 100,
        align: 'center',
        filters: _.uniqBy(
          _.map(list, (v) => ({
            value: v.sign,
            text: v.sign,
          })),
          'value',
        ),
        filterMode: 'tree',
        filterSearch: true,
        onFilter: (value: string, record: any) => record.sign === value,
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        width: 80,
        filters: _.uniqBy(
          _.map(list, (v) => ({
            value: v.type,
            text: v.type,
          })),
          'value',
        ),
        filterMode: 'tree',
        filterSearch: true,
        onFilter: (value: string, record: any) => record.type === value,
      },
      {
        title: '手机号',
        key: 'mobile',
        render: (row: any) => {
          return row.mobile || row.ori_mobile;
        },
        align: 'center',
        width: 150,
        filters: _.uniqBy(
          _.map(list, (v) => ({
            value: v.mobile || v.ori_mobile,
            text: v.mobile || v.ori_mobile,
          })),
          'value',
        ),
        filterMode: 'tree',
        filterSearch: true,
        onFilter: (value: string, record: any) =>
          record.mobile === value || record.ori_mobile === value,
      },
      {
        title: '扩展',
        dataIndex: 'extend',
        key: 'extend',
        align: 'center',
        width: 50,
        ellipsis: true,
      },
      {
        title: '模板ID',
        dataIndex: 'template',
        key: 'template',
        align: 'center',
        width: 100,
      },
      {
        title: '发送时间',
        dataIndex: 'time',
        key: 'time',
        align: 'center',
        width: 100,
      },
      {
        title: '对客流水号',
        dataIndex: 'outsid',
        key: 'outsid',
        align: 'center',
        width: 100,
      },
      {
        title: '补发次数',
        dataIndex: 'reissue_num',
        key: 'reissue_num',
        align: 'center',
        width: 60,
      },
      {
        title: '提交状态',
        key: 'result',
        align: 'center',
        width: 80,
        render: (row: any) => (
          <Tooltip
            title={() => {
              return (
                <>
                  <div>result: {row.result}</div>
                  <div>发送结果: {row.result_text}</div>
                </>
              );
            }}
          >
            {row.result}
          </Tooltip>
        ),
        filters: _.uniqBy(
          _.map(list, (v) => ({
            value: v.result,
            text: v.result,
          })),
          'value',
        ),
        filterMode: 'tree',
        filterSearch: true,
        onFilter: (value: string, record: any) => record.result === value,
      },
      {
        title: '条数',
        dataIndex: 'fee',
        key: 'fee',
        align: 'center',
        width: 60,
      },
      {
        title: '送达时间',
        key: 'callbacktime',
        align: 'center',
        width: 100,
        render: (row: any) => (
          <Tooltip
            title={() => {
              return (
                <>
                  <div>送达时间: {row.callbacktime}</div>
                  <div>入库时间: {row.insert_time}</div>
                </>
              );
            }}
          >
            {row.callbacktime}
          </Tooltip>
        ),
      },
      {
        title: '时延',
        key: 'delay',
        align: 'center',
        width: 65,
        render: (row: any) => {
          const duration = calculateDalay(row);
          if (!row?.callbacktime) {
            return '--';
          }
          return (
            <Tag color={duration > 60 ? 'red' : duration > 10 ? 'blue' : 'green'}>
              {duration >= 1 ? duration : 0}
            </Tag>
          );
        },
      },
      {
        title: '返回状态',
        dataIndex: 'state',
        key: 'state',
        width: 100,
        align: 'center',
        filters: _.uniqBy(
          _.map(list, (v) => ({
            value: v.state,
            text: v.state,
          })),
          'value',
        ),
        filterMode: 'tree',
        filterSearch: true,
        onFilter: (value: string, record: any) => record.state === value,
      },
      {
        title: '下发号码',
        dataIndex: 'issue_number',
        key: 'issue_number',
        width: 80,
        align: 'center',
      },
      {
        title: '送达结果',
        dataIndex: 'callbackresult_text',
        key: 'callbackresult_text',
        width: 100,
        align: 'center',
      },
      {
        title: '发送内容',
        dataIndex: 'text',
        key: 'text',
        align: 'center',
        width: 160,
        render: (text: string) => (
          <div className="send-query-text-ellipsis">
            <Tooltip
              title={() => {
                return (
                  <>
                    {text}{' '}
                    <CopyToClipboard
                      text={text}
                      onCopy={() => {
                        message.success('Copy successfully');
                      }}
                    >
                      <Button
                        icon={<CopyOutlined />}
                        type="link"
                        style={{ width: '16px', height: '16px', verticalAlign: 'top' }}
                      />
                    </CopyToClipboard>
                  </>
                );
              }}
            >
              {text}
            </Tooltip>
          </div>
        ),
      },
      {
        title: '操作',
        key: 'operation',
        align: 'center',
        fixed: 'right',
        width: 160,
        render: (row: any) => {
          return (
            <>
              <CopyToClipboard
                text={formatCopyText(row)}
                onCopy={() => {
                  message.success('Copy successfully');
                }}
              >
                <Tag color="#108ee9">复制</Tag>
              </CopyToClipboard>
              <Button
                size="small"
                onClick={() => {
                  if (row.result !== 0) {
                    Modal.warning({ title: '此条记录提交失败', content: row.result_text });
                    return;
                  }
                  setModalVisit({ show: true, data: row });
                }}
              >
                自助报障
              </Button>
            </>
          );
        },
      },
    ].filter((v) => columns.includes(v.key));
  }, [list, formatCopyText, modalVisit, columns]);

  async function onSubmit(vals: any) {
    setSearchKeys({ ...vals });
    setExpandKeys([]);
  }

  async function onExport(vals: any) {
    const params = _.pickBy(vals, (item) => item !== '' && !_.isNil(item));
    if ([params.phone, params.sdkappid, params.template_id, params.sign].every((v) => !v)) {
      return message.error('号码、sdkappid、模板ID、sign至少包含一项');
    }
    const timeParam = {
      from: dayjs(vals.time[0]).format('YYYY-MM-DD HH:mm:ss'),
      to: dayjs(vals.time[1]).format('YYYY-MM-DD HH:mm:ss'),
    };
    delete params.time;
    const res = await exportSmsSendQuery({
      ...params,
      ...timeParam,
    });
    if (res?.code === 0) {
      setVisible(false);
      message.success('导出成功，请稍候并查看邮件导出结果');
    }
    dumpwarning({
      route: '/sms/sms/export',
      params: { ...params, ...timeParam },
    });
  }

  return (
    <>
      <PageContainer title="下行短信查询">
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Form
            labelCol={{ span: 6 }}
            form={form}
            layout="inline"
            labelAlign="right"
            onFinish={(vals) => onSubmit(vals)}
            initialValues={{ ...searchKeys }}
          >
            <Form.Item name="phone" rules={[{ required: true, message: '请输入号码' }]}>
              <TextArea placeholder="回车换行、一行输入一个电话号码" style={{ width: 250 }} />
            </Form.Item>
            <Form.Item name="sdkappid">
              <Input placeholder="sdkappid" style={{ width: 250 }} />
            </Form.Item>
            <Form.Item name="time" rules={[{ required: true, message: '请选择时间' }]}>
              <RangePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                presets={[
                  { label: 'Today', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
                  {
                    label: 'Yesterday',
                    value: [
                      dayjs().subtract(1, 'days').startOf('day'),
                      dayjs().subtract(1, 'days').endOf('day'),
                    ],
                  },
                  {
                    label: 'Week',
                    value: [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
                  },
                  {
                    label: 'Month',
                    value: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
                  },
                ]}
              />
            </Form.Item>
            <Form.Item>
              <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={loading}>
                查询
              </Button>
              <Button
                icon={<SettingOutlined />}
                type="primary"
                loading={loading}
                style={{ marginLeft: 5 }}
                onClick={() => dialogRef.current.open()}
              >
                列自定义
              </Button>
            </Form.Item>
          </Form>
          <div>
            <CopyToClipboard
              text={getDispatchText(selectedRows)}
              onCopy={() => {
                setSelectRows([]);
                message.success('Copy successfully');
              }}
            >
              <Button type="primary" disabled={!selectedRows.length} style={{ marginRight: 5 }}>
                批量复制
              </Button>
            </CopyToClipboard>
            <Button
              type="primary"
              onClick={() => {
                setVisible(true);
              }}
            >
              导出
            </Button>
          </div>
        </div>
        <Table
          columns={initCols}
          dataSource={list}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 1300 }}
          style={{ marginTop: 20 }}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys: selectedRows.map((item) => item.id),
            onChange: (selectedRowKeys: React.Key[], selectedRows: DataType[]) => {
              setSelectRows(selectedRows);
            },
          }}
          expandable={{
            expandedRowKeys,
            rowExpandable: (record) => record.reissue_num > 0,
            onExpandedRowsChange: (expandedKeys) => {
              setExpandKeys(expandedKeys as number[]);
            },
            expandedRowRender: (context, index, indent, expanded) => (
              <SendDetail
                expanded={expanded}
                params={{
                  phone: context.mobile,
                  from: dayjs(searchKeys.time[0]).format('YYYY-M-D H:m:s'),
                  to: dayjs(searchKeys.time[1]).format('YYYY-M-D H:m:s'),
                  outsid: context.outsid,
                }}
              />
            ),
          }}
        />
      </PageContainer>
      <Modal
        title="提交下行短信导出"
        open={isVisible}
        destroyOnClose
        onOk={() => exportForm.submit()}
        onCancel={() => {
          setVisible(false);
          exportForm.resetFields();
        }}
      >
        <Form form={exportForm} labelAlign="right" onFinish={onExport}>
          <Form.Item name="phone" label="号码">
            <Input placeholder="号码" style={{ width: 250 }} />
          </Form.Item>
          <Form.Item name="sdkappid" label="sdkappid">
            <Input placeholder="sdkappid" style={{ width: 250 }} />
          </Form.Item>
          <Form.Item name="time" label="时间" rules={[{ required: true, message: '请选择时间' }]}>
            <RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>
          <Form.Item name="template_id" label="模板ID">
            <InputNumber placeholder="模板ID" style={{ width: 250 }} />
          </Form.Item>
          <Form.Item name="sign" label="sign">
            <Input placeholder="sign" style={{ width: 250 }} />
          </Form.Item>
        </Form>
      </Modal>
      <CustomColsDialog
        dialogRef={dialogRef}
        current={columns}
        boxes={AllColsOptions.map((v) => v.value)}
        options={AllColsOptions}
        onSuccess={(params) => {
          localStorage.setItem('sendQueryCols', JSON.stringify(params));
          setColumns(params);
        }}
      />
      <ModalForm
        title="自助报障"
        formRef={formRef}
        onOpenChange={(val) => setModalVisit({ show: val })}
        open={modalVisit.show}
        width={400}
        layout="horizontal"
        modalProps={{
          destroyOnClose: true,
          maskClosable: false,
        }}
        onFinish={async (values) => {
          return await addIssue({
            ...values,
          });
        }}
      >
        <Form.Item label="手机号">{modalVisit.data?.mobile}</Form.Item>
        <Form.Item label="签名">{modalVisit.data?.sign}</Form.Item>
        <ProFormSelect
          name="issue_content_template"
          label="报障描述"
          options={issueContentTemplate}
          showSearch
          onChange={(v) => {
            formRef.current?.setFieldsValue({ issue_content: v });
          }}
        ></ProFormSelect>
        <ProFormTextArea name="issue_content"></ProFormTextArea>
      </ModalForm>
    </>
  );
};

export default SmsSendQuery;
