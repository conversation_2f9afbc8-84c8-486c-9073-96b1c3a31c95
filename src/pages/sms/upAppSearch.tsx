import React, { useCallback, useMemo, useRef, useState } from 'react';
import {
  ActionType,
  PageContainer,
  ProFormColumnsType,
  ProTable,
} from '@ant-design/pro-components';
import { isMobile } from '@/const/jadgeUserAgent';
import _ from 'lodash';
import { findLabel } from '@/utils/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { getUpAppSearch } from '@/services/upApp';
import { site } from '../profit-loss/const';
import dayjs from 'dayjs';
import { Button, message } from 'antd';
import CopyToClipboard from 'react-copy-to-clipboard';
import { processRowsData, saveCSV } from '../global-components/saveCsv';

export type DataItem = {
  name: string;
  state: string;
};

// 获取导出文本
const getDispatchText = (rows: any[], columns: any[]) => {
  if (!rows.length) return '';
  const { headerText, exportData } = processRowsData(rows, columns);
  const rowsText = exportData.map((row) => row.join(' '));
  return [headerText.join(' '), ...rowsText].join('\n');
};

// 导出 CSV
const exportCsv = (rows: any[], columns: any[], params: any) => {
  const { headerText, exportData } = processRowsData(rows, columns);
  saveCSV(`国际短信上行发送记录查询${Date.now()}`, headerText, exportData, {
    route: '/sms/up-app-search',
    params,
  })
    .then(() => {
      message.success('导出成功');
    })
    .catch(() => {
      message.error('导出失败');
    });
};
function UpAppSearch() {
  const actionRef = useRef<ActionType>();
  const addFormRef = useRef<any>();
  const searchFormRef = useRef<any>();
  const [selectRow, setSelectRows] = useState<any[]>([]);
  const { regionOptionsMcc = [] } = useFetchCountryInfo();

  const now = dayjs();
  const initTimeRange = {
    from: now.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    to: now.format('YYYY-MM-DD HH:mm:ss'),
  };

  const columns: ProFormColumnsType[] & any = useMemo(
    () => [
      {
        title: 'SDKAPPID',
        dataIndex: 'sdkappid',
        key: 'sdkappid',
        render: (_: unknown, { sdkappid }: any) => sdkappid || '-',
      },
      {
        title: '上行号码',
        dataIndex: 'sender_id',
        key: 'sender_id',
        render: (_: unknown, { sender_id }: any) => sender_id || '-',
      },
      {
        title: '电话号码',
        dataIndex: 'mobile',
        key: 'mobile',
      },
      {
        valueType: 'dateTimeRange',
        title: '时间范围',
        dataIndex: 'timeRange',
        key: 'timeRange',
        hideInTable: true,
        transform: (value: any) => ({
          from: value[0],
          to: value[1],
        }),
        fieldProps: {
          style: { width: 400 },
          defaultValue: [dayjs(initTimeRange.from), dayjs(initTimeRange.to)],
        },
      },
      {
        title: '内容',
        dataIndex: 'content',
        key: 'content',
        hideInSearch: true,
      },
      {
        title: '请求时间',
        dataIndex: 'submit_date',
        key: 'submit_date',
        hideInSearch: true,
      },
      {
        title: '消息ID',
        dataIndex: 'message_id',
        key: 'message_id',
        hideInSearch: true,
      },
      {
        title: '流水号',
        dataIndex: 'serial_no',
        key: 'serial_no',
        hideInSearch: true,
      },
      {
        title: '供应商账号ID',
        dataIndex: 'provider_id',
        key: 'provider_id',
        hideInSearch: true,
      },
      {
        title: '供应商名称',
        dataIndex: 'provider_name',
        key: 'provider_name',
        hideInSearch: true,
      },
      {
        title: '站点类型',
        dataIndex: 'site_type',
        key: 'site_type',
        valueType: 'select',
        fieldProps: { options: site },
        hideInSearch: true,
        render: (_: unknown, { site_type }: any) => findLabel(site, site_type),
      },
      {
        title: '业务发送国家',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        hideInSearch: true,
        width: 200,
        fieldProps: {
          showSearch: true,
          options: regionOptionsMcc,
          placeholder: '请选择国家',
        },
        render: (_: unknown, { country_code }: any) => findLabel(regionOptionsMcc, country_code),
      },
      {
        title: '是否允许推送上行',
        dataIndex: 'result',
        key: 'result',
        hideInSearch: true,
        render: (_: unknown, { result }: any) => (result === 0 ? '是' : '否'),
      },
    ],
    [regionOptionsMcc, addFormRef],
  );

  const requestFn = useCallback(async (params: any) => {
    let filterData = _.omit(
      _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
      ['pageSize', 'current'],
    );
    if (!params.from) {
      filterData = {
        ...filterData,
        ...initTimeRange,
      };
    }
    const { data } = await getUpAppSearch({
      ...filterData,
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);
  const exportListDataToCsv = async () => {
    let { timeRange, ...searchFormData } = searchFormRef.current?.getFieldsValue() || {};
    if (!timeRange?.length) {
      searchFormData = { ...searchFormData, ...initTimeRange };
    } else {
      const [from, to] = timeRange.map((v: string) => dayjs(v).format('YYYY-MM-DD HH:mm:ss'));
      searchFormData = { ...searchFormData, from, to }; // 使用简写语法
    }
    const { data } = await getUpAppSearch(searchFormData);
    if (!data?.list?.length) {
      message.warning('没有数据可以导出');
      return;
    }
    exportCsv(data.list, columns, searchFormData);
  };
  const selectedRowKeys = selectRow.map((v) => v.serial_no);

  return (
    <PageContainer>
      <Button
        key="output"
        type="primary"
        onClick={exportListDataToCsv}
        style={{ marginRight: 20, marginLeft: 20 }}
      >
        导出
      </Button>
      <CopyToClipboard
        key="batchCopy"
        text={getDispatchText(selectRow, columns)}
        onCopy={() => {
          setSelectRows([]);
          message.success('Copy successfully');
        }}
      >
        <Button type="primary" disabled={!selectRow.length} style={{ marginRight: 5 }}>
          批量复制
        </Button>
      </CopyToClipboard>
      <ProTable
        actionRef={actionRef}
        formRef={searchFormRef}
        columns={
          columns.map((item: any) => ({
            ...item,
            align: 'center',
          })) as any
        }
        rowKey="serial_no"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
          onChange: () => {
            setSelectRows([]);
          },
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: (_: unknown, selectedRows) => {
            setSelectRows([...selectedRows]);
          },
        }}
        search={{
          labelWidth: 'auto',
          span: 4,
          // },
        }}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
}
export default UpAppSearch;
