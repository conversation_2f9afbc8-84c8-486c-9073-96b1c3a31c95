import React, { useMemo } from 'react';
import { isMobile } from '@/const/jadgeUserAgent';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { Card, Table, Tag } from 'antd';
import { getAppWarnContactor } from '@/services/scdAPI';

function render(value: number) {
  return <Tag color={value === 1 ? 'green' : 'orange'}>{value === 1 ? '开启' : '关闭'}</Tag>;
}

const AppWarning = ({ sdkappid }: { sdkappid: string }) => {
  const { value: state, loading } = useAsyncRetryFunc(async () => {
    const res = await getAppWarnContactor({ sdkappid });
    return res || {};
  }, [sdkappid]);

  const list = useMemo(() => {
    return state?.data ?? [];
  }, [state]);

  return (
    <Card title="告警联系人" style={{ marginTop: 15 }}>
      <Table
        size="middle"
        dataSource={list}
        columns={[
          { title: 'id', dataIndex: 'id', key: 'id' },
          { title: '姓名', dataIndex: 'name', key: 'name', align: 'center' },
          { title: '手机', dataIndex: 'mobile', key: 'mobile', align: 'center' },
          { title: '邮箱', dataIndex: 'email', key: 'email', align: 'center' },
          {
            title: '接收频率限制提醒',
            dataIndex: 'warning_enable',
            key: 'warning_enable',
            align: 'center',
            render,
          },
          {
            title: '接收发送超量提醒',
            dataIndex: 'maxwarning_enable',
            key: 'maxwarning_enable',
            align: 'center',
            render,
          },
          {
            title: '接收模板和签名审核通知',
            dataIndex: 'notify_enable',
            key: 'notify_enable',
            align: 'center',
            render,
          },
          {
            title: '套餐包告警通知',
            dataIndex: 'smspkg_enable',
            key: 'smspkg_enable',
            align: 'center',
            render,
          },
          {
            title: '防盗刷监控告警提醒',
            dataIndex: 'brushwarning_enable',
            key: 'brushwarning_enable',
            align: 'center',
            render,
          },
        ]}
        loading={loading}
        pagination={false}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        style={{ marginTop: 20 }}
      />
    </Card>
  );
};
export default AppWarning;
