import React, { useRef } from 'react';
import { Card, Button, Space, Form, message, Input } from 'antd';
import { getIotsmsOperator, addIotsmsOperator, editIotsmsOperator } from '@/services/scdAPI';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

interface IotsmsOperatorProps {
  sdkappid: string;
}

const IotsmsOperator: React.FC<IotsmsOperatorProps> = ({ sdkappid }) => {
  const [form] = Form.useForm();
  const ref = useRef<ActionType>();

  const request = async () => {
    const res = await getIotsmsOperator({ sdkappid });
    return {
      data: res.data ?? [],
      success: true,
    };
  };

  async function onFormSub(vals: any) {
    const res = await addIotsmsOperator({ ...vals, sdkappid });
    res?.code === 0 && ref?.current?.reload();
  }

  async function editOperator(row: any) {
    const { code, account, name, srcid, id } = row;
    const res = await editIotsmsOperator({ id, code, account, name, srcid, sdkappid });
    if (res?.code === 0) {
      message.success('设置物联卡运营商账号信息成功');
      ref?.current?.reload();
    }
  }

  return (
    <Card title={'物联卡运营商账号信息'} style={{ marginTop: 15 }}>
      <div>
        <Space>
          <Form
            form={form}
            layout="inline"
            labelWrap={true}
            labelAlign="right"
            onFinish={(vals) => {
              onFormSub(vals);
            }}
          >
            <Form.Item
              name="code"
              label="运营商编号"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <Input style={{ width: 180 }} placeholder="运营商编号" />
            </Form.Item>
            <Form.Item
              name="account"
              label="分配的账号"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <Input style={{ width: 180 }} placeholder="分配的账号" />
            </Form.Item>
            <Form.Item
              name="name"
              label="运营商名称"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <Input style={{ width: 180 }} placeholder="运营商名称" />
            </Form.Item>
            <Form.Item
              name="srcid"
              label="短信显号"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <Input style={{ width: 180 }} placeholder="短信显号" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                添加
              </Button>
            </Form.Item>
          </Form>
        </Space>
      </div>
      <ProTable
        actionRef={ref}
        rowKey="id"
        style={{ margin: '20px -24px 0 -24px' }}
        options={false}
        search={false}
        columns={[
          {
            title: '运营商编号',
            key: 'code',
            dataIndex: 'code',
            align: 'center',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
          {
            title: '分配的账号',
            key: 'account',
            align: 'center',
            dataIndex: 'account',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
          {
            title: '运营商名称',
            key: 'name',
            align: 'center',
            dataIndex: 'name',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
          {
            title: '短信显号',
            key: 'srcid',
            align: 'center',
            dataIndex: 'srcid',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
          {
            title: '操作',
            valueType: 'option',
            align: 'center',
            render: (text, record, _, action) => [
              <a
                key="editable"
                onClick={() => {
                  action?.startEditable?.(record.id);
                }}
              >
                设置
              </a>,
            ],
          },
        ]}
        request={request}
        editable={{
          type: 'single',
          onSave: async (_, data) => {
            await editOperator(data);
          },
        }}
        pagination={{ pageSize: 10 }}
      />
    </Card>
  );
};
export default IotsmsOperator;
