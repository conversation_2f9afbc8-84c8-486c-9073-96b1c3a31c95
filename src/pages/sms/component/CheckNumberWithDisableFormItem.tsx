import React, { useState, useEffect } from 'react';
import { Checkbox, Form, Space } from 'antd';
import { createNumberInput } from './BaseCheckNumberFormItem';

interface CheckNumberWithDisableFormItemProps {
  value?: number;
  onChange?: (val: number) => void;
}

const CheckNumberWithDisableFormItem: React.FC<CheckNumberWithDisableFormItemProps> = ({
  value = 1,
  onChange,
}) => {
  const [isUnlimited, setIsUnlimited] = useState(value === -1);
  const [isDisabled, setIsDisabled] = useState(value === 0);

  useEffect(() => {
    // 同步外部值变化
    setIsUnlimited(value === -1);
    setIsDisabled(value === 0);
  }, [value]);

  const handleInputChange = (val: string | number | null) => {
    const newValue = val ? Number(val) : 1;
    onChange?.(newValue);
  };

  const handleUnlimitedChange = (checked: boolean) => {
    if (checked) {
      setIsUnlimited(true);
      setIsDisabled(false);
      onChange?.(-1);
    } else {
      setIsUnlimited(false);
      onChange?.(1);
    }
  };

  const handleDisableChange = (checked: boolean) => {
    if (checked) {
      setIsDisabled(true);
      setIsUnlimited(false);
      onChange?.(0);
    } else {
      setIsDisabled(false);
      onChange?.(1);
    }
  };

  return (
    <Space>
      {createNumberInput({
        disabled: isUnlimited || isDisabled,
        value: isUnlimited || isDisabled ? undefined : value,
        onChange: handleInputChange,
        min: 1,
        style: { width: 120 },
      })}
      <Checkbox
        checked={isUnlimited}
        onChange={(e) => handleUnlimitedChange(e.target.checked)}
        disabled={isDisabled}
      >
        不限制
      </Checkbox>
      <Checkbox
        checked={isDisabled}
        onChange={(e) => handleDisableChange(e.target.checked)}
        disabled={isUnlimited}
      >
        禁发
      </Checkbox>
    </Space>
  );
};

interface WrapperProps {
  name: string;
  label: string;
}

const Wrapper: React.FC<WrapperProps> = ({ name, label }) => (
  <Form.Item label={label} name={name} labelCol={{}}>
    <CheckNumberWithDisableFormItem />
  </Form.Item>
);

export default Wrapper;
