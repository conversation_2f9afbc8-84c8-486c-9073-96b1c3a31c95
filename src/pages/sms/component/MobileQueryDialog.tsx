import React from 'react';
import { Mo<PERSON>, Spin } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { queryErrAnaylysisMobile } from '@/services/thrdAPI';

interface DialogProps {
  dialogRef: DialogRef;
}

export const MobileQueryDialog = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    month: string;
    error_code: string;
    sdkappid: number;
    start_time: string;
    end_time: string;
  }>(dialogRef);

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    if (!defaultVal || !visible) return;
    const response = await queryErrAnaylysisMobile({ ...defaultVal });
    return response?.data || [];
  }, [defaultVal, visible]);

  return (
    <Modal
      open={visible}
      title="示例手机号码"
      onOk={() => setShowState(false)}
      onCancel={() => setShowState(false)}
    >
      {loading ? <Spin /> : state?.map((v: string) => <p key={v}>{v}</p>)}
    </Modal>
  );
};
