import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { Form, Input, InputNumber, Select, Button, Checkbox, Row, Col } from 'antd';
import { LoadingContainer } from '@/pages/global-components/loading-container';
import { omit } from 'lodash';

type FormLayout = 'horizontal' | 'inline' | 'vertical';

interface DetailFormProps {
  formSetting: Record<string, unknown>[];
  onSubmit: (allValues: any, changedValues?: any) => any;
  isLoading?: boolean;
  layout?: FormLayout;
  initValus?: Record<string, unknown>;
}

interface FormItemProps {
  name: string;
  label: string;
  placeholder: string;
  type: string;
  disabled: boolean;
  progeny: [];
  options: [];
  extra: string;
  isHide?: boolean;
  md?: number;
  render?: (config: Omit<Partial<FormItemProps>, 'render' | 'textRender'>) => React.ReactNode;
  textRender?: () => React.ReactNode;
}

const generateChilds = (childs: Partial<FormItemProps>, isProgeny?: boolean) => {
  let node;
  switch (childs.type) {
    case 'number':
      node = (
        <InputNumber
          style={{ width: 200 }}
          placeholder={childs.placeholder || childs.name}
          disabled={childs.disabled}
          controls={false}
        />
      );
      break;
    case 'select':
      node = <Select style={{ width: 200 }} options={childs.options} disabled={childs.disabled} />;
      break;
    case 'checkbox':
      node = <Checkbox.Group options={childs.options} disabled={childs.disabled} />;
      break;
    case 'text':
      node = childs.textRender?.();
      break;
    case 'input':
    default:
      node = (
        <Input
          style={{ width: 220, marginRight: isProgeny ? 10 : 0 }}
          placeholder={childs.placeholder || childs.name}
          disabled={childs.disabled}
        />
      );
      break;
  }
  return (
    <Col sm={isProgeny ? undefined : 24} md={isProgeny ? undefined : 12} key={childs.name}>
      <Form.Item noStyle={isProgeny} name={childs.name} label={childs.label} extra={childs.extra}>
        {node}
      </Form.Item>
    </Col>
  );
};

const DetailForm = (
  { formSetting, onSubmit, isLoading, layout, initValus }: DetailFormProps,
  ref: React.Ref<any>,
) => {
  const [form] = Form.useForm();

  useImperativeHandle(
    ref,
    () => {
      return {
        form,
      };
    },
    [form],
  );

  useEffect(() => {
    form.setFieldsValue({ ...initValus });
  }, [form, initValus]);

  return (
    <LoadingContainer loading={isLoading}>
      <Form
        form={form}
        layout={layout}
        initialValues={{ ...initValus }}
        labelAlign="left"
        labelWrap
        labelCol={{ flex: '130px' }}
        onFinish={(vals) => {
          onSubmit(vals);
        }}
      >
        <Row gutter={3}>
          {formSetting.map((item: Partial<FormItemProps>) => {
            if (item.isHide) return null;
            if (item.render)
              return (
                <Col sm={24} md={item.md || 12}>
                  {item.render(omit(item, ['render', 'textRender']))}
                </Col>
              );
            if (item.progeny) {
              return (
                <Col sm={24} md={12} key={item.label}>
                  <Form.Item label={item.label} extra={item.extra}>
                    <Input.Group compact>
                      {item.progeny.map((child) => generateChilds(child, true))}
                    </Input.Group>
                  </Form.Item>
                </Col>
              );
            }
            return generateChilds(item);
          })}
        </Row>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={isLoading}>
            set
          </Button>
        </Form.Item>
      </Form>
    </LoadingContainer>
  );
};

export default forwardRef(DetailForm);
