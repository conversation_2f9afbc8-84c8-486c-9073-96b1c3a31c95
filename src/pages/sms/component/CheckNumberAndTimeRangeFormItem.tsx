import React, { useState } from 'react';
import { Form, Space } from 'antd';
import {
  BaseCheckNumberFormItem,
  BaseFormValue,
  createNumberInput,
} from './BaseCheckNumberFormItem';

interface FormValue extends BaseFormValue {
  count?: number;
  interval?: number;
  type?: string;
}

interface CheckNumberAndTimeRangeFormItemProps {
  value?: FormValue;
  onChange?: (val: FormValue | undefined) => void;
}

const CheckNumberAndTimeRangeFormItem: React.FC<CheckNumberAndTimeRangeFormItemProps> = ({
  value,
  onChange,
}) => {
  const { count, interval, id = 0, type } = value ?? {};
  const [isCheck, setIsCheck] = useState(!id);

  const handleCountChange = (val: string | number | null) => {
    const newCount = val ? Number(val) : 1;
    onChange?.({ ...value, count: newCount });
  };

  const handleIntervalChange = (val: string | number | null) => {
    const newInterval = val ? Number(val) : 1;
    onChange?.({ ...value, interval: newInterval });
  };

  const handleCheckboxChange = (checked: boolean) => {
    setIsCheck(checked);
    onChange?.(checked ? { id } : { count: 1, id, interval: 1, type });
  };

  return (
    <BaseCheckNumberFormItem
      value={value}
      onChange={onChange}
      isCheck={isCheck}
      onCheckChange={handleCheckboxChange}
    >
      <Space direction="horizontal" size="small" style={{ marginBottom: 8 }}>
        <span>相同内容短信对同一个手机号发送条数限制：</span>
        {createNumberInput({
          disabled: isCheck,
          value: isCheck ? undefined : interval,
          onChange: handleIntervalChange,
          placeholder: '请输入秒数',
        })}
        <span>秒内发送短信条数不超过</span>
        {createNumberInput({
          disabled: isCheck,
          value: isCheck ? undefined : count,
          onChange: handleCountChange,
          placeholder: '请输入条数',
        })}
      </Space>
      <span style={{ marginLeft: 3, color: '#ccc' }}>（当前ID:{id}）</span>
    </BaseCheckNumberFormItem>
  );
};

interface WrapperProps {
  name: string;
}

const Wrapper: React.FC<WrapperProps> = ({ name }) => (
  <Form.Item name={name}>
    <CheckNumberAndTimeRangeFormItem />
  </Form.Item>
);

export default Wrapper;
