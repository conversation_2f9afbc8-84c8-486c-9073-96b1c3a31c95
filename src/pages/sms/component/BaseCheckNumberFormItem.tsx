import React from 'react';
import { Checkbox, InputNumber } from 'antd';
import type { InputNumberProps } from 'antd';

export interface BaseFormValue {
  id?: number;
  type?: string;
}

interface BaseCheckNumberFormItemProps<T extends BaseFormValue> {
  value?: T;
  onChange?: (val: T | undefined) => void;
  disabled?: boolean;
  isCheck?: boolean;
  onCheckChange?: (checked: boolean) => void;
  children?: React.ReactNode;
}

export const BaseCheckNumberFormItem = <T extends BaseFormValue>({
  isCheck,
  onCheckChange,
  children,
}: BaseCheckNumberFormItemProps<T>) => {
  return (
    <div style={{ width: '100%' }}>
      {children}
      <Checkbox
        style={{ marginLeft: 3 }}
        checked={isCheck}
        onChange={(e) => onCheckChange?.(e.target.checked)}
      >
        {isCheck ? '当前为不限制' : '不限制'}
      </Checkbox>
    </div>
  );
};

export const createNumberInput = (props: InputNumberProps & { disabled?: boolean }) => (
  <InputNumber {...props} size="small" min={1} placeholder={props.placeholder || '请输入数值'} />
);
