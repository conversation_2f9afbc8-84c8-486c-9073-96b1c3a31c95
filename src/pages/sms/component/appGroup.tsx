import React, { useState, useEffect, useRef } from 'react';
import { Card, Select, Button, Space, message } from 'antd';
import {
  getAppGroupIDs,
  getAppGroup,
  addAppGroup,
  editAppGroup,
  delAppGroup,
} from '@/services/scdAPI';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history } from 'umi';

interface groupProps {
  sdkappid: string;
}

type DataSourceType = {
  group_id: React.Key;
  decs: string;
  priority: number;
};

const AppGroupWeight: React.FC<groupProps> = ({ sdkappid }) => {
  const [groups, setGroupIDs] = useState<any>([]);
  const [id, setID] = useState<any>([]);
  const ref = useRef<ActionType>();

  const request = async () => {
    const res = await getAppGroup({ appid: sdkappid });
    return {
      data: res.data ?? [],
      success: true,
    };
  };

  async function doDel(row: any) {
    const { appid, group_id } = row;
    const res = await delAppGroup({ group_id, appid });
    if (res?.code === 0) {
      message.success('操作成功');
    }
    ref?.current?.reload();
  }

  async function doEdit(row: any) {
    const { appid, group_id, priority } = row;
    const res = await editAppGroup({ group_id, priority, appid });
    if (res?.code === 0) {
      message.success('操作成功');
      ref?.current?.reload();
    }
  }

  useEffect(() => {
    getAppGroupIDs({ appid: sdkappid }).then((res) => {
      const providersList = (res?.data ?? []).map((item: { group_id: number; name: string }) => {
        return {
          value: item.group_id,
          label: item.name,
        };
      });
      setGroupIDs(providersList);
    });
  }, [sdkappid]);

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '通道组ID',
      dataIndex: 'group_id',
      editable: false,
      align: 'center',
      render: (group_id) => (
        <Button
          type="link"
          onClick={() => {
            history.push('/group/channel-customer-detail', { group_id });
          }}
        >
          {group_id}
        </Button>
      ),
    },
    {
      title: '组信息',
      dataIndex: 'group_name',
      align: 'center',
      editable: false,
    },
    {
      title: '扩展码',
      dataIndex: 'pre_extend',
      align: 'center',
      editable: false,
    },
    {
      title: '优先级',
      key: 'priority',
      dataIndex: 'priority',
      valueType: 'select',
      align: 'center',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: {
        options: [
          {
            label: '0级(低)',
            value: 0,
          },
          {
            label: '1级(中)',
            value: 1,
          },
          {
            label: '2级(高)',
            value: 2,
          },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.group_id);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            doDel(record);
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  async function onAdd() {
    const res = await addAppGroup({ appid: sdkappid, group_id: id });
    res?.code === 0 && ref?.current?.reload();
  }

  return (
    <Card title="应用子码通道组权重" style={{ marginTop: 15 }}>
      <div>
        <Space>
          <>
            <span>组ID</span>
            <Select
              showSearch
              allowClear
              style={{ width: 250 }}
              onChange={(value) => setID(value)}
              placeholder="请选择"
            >
              {(groups ?? []).map((item: { value: number; label: string }) => {
                return (
                  <Select.Option value={item.value} key={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </>
          <Button type="primary" onClick={onAdd}>
            添加
          </Button>
        </Space>
      </div>
      <ProTable<DataSourceType>
        rowKey="group_id"
        actionRef={ref}
        style={{ margin: '20px -24px 0 -24px' }}
        options={false}
        search={false}
        columns={columns}
        request={request}
        editable={{
          type: 'single',
          onSave: async (rowKey, data) => {
            await doEdit(data);
          },
        }}
        pagination={{ pageSize: 10 }}
      />
    </Card>
  );
};
export default AppGroupWeight;
