import React, { useRef, useEffect, useState } from 'react';
import { Button, Space, Form, message, Card, Tabs, Select, InputNumber, Tag, Switch } from 'antd';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import {
  addReissueChannel,
  deleteReissueChannel,
  editReissueChannel,
  getReissueChannelList,
  getReissueGroup,
  getReissueInfo,
  setReissueApp,
} from '@/services/reissueConfig';
import { LoadingContainer } from '@/pages/global-components/loading-container';
import _ from 'lodash';
import SelectTooltip from '@/pages/global-components/SelectToolTip';

const formItemProps = {
  rules: [
    {
      required: true,
      message: '此项为必填项',
    },
  ],
  hasFeedback: false,
};

enum GroupType {
  '应用子码' = 1,
  '签名子码',
}

const options = [
  { value: 0, label: '不补发' },
  { value: 1, label: '携号转网补发' },
  { value: 2, label: '失败全补发' },
  { value: 3, label: '验证码补发' },
];

interface ReissueConfigProps {
  sdkappid: string;
}

const ReissueConfig: React.FC<ReissueConfigProps> = ({ sdkappid }) => {
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const [groups, setGroups] = useState<{ value: number; label: string }[]>([]);
  const ref = useRef<ActionType>();

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const response = await getReissueInfo({ sdkappid });
    return response?.data || {};
  }, [sdkappid]);

  useEffect(() => {
    const first_default_group = _.isEmpty(state) ? true : state.first_default_group;
    form2.setFieldsValue({
      ..._.pick(state, ['other_reissue', 'code_reissue', 'reissue_strategy']),
      first_default_group,
    });
  }, [form2, state]);

  const request = async () => {
    const res = await getReissueChannelList({ sdkappid });
    return {
      data: res.data ?? [],
      success: true,
    };
  };

  useEffect(() => {
    getReissueGroup().then((res: any) => {
      const groupList = (res?.data ?? []).map(
        (item: { group_id: number; name: string; group_type: number }) => {
          return {
            value: item.group_id,
            label: `${item.name}（${item.group_type === 1 ? '应用子码' : '签名子码'}）`,
          };
        },
      );
      setGroups(groupList);
    });
  }, []);

  async function onFormSub(vals: any) {
    const res = await addReissueChannel({ ...vals, sdkappid });
    res?.code === 0 && ref?.current?.reload();
  }

  async function editChannel(row: any) {
    const { sdkappid, group_id, times, order_id } = row;
    const res = await editReissueChannel({ sdkappid, group_id, times, order_id });
    if (res?.code === 0) {
      message.success('编辑应用通道组补发配置成功');
      ref?.current?.reload();
    }
  }

  async function doDel(row: any) {
    const { group_id, sdkappid } = row;
    const res = await deleteReissueChannel({ group_id, sdkappid });
    if (res?.code === 0) {
      message.success('操作成功');
    }
    ref?.current?.reload();
  }

  async function onSubmit(params: any) {
    const res = await setReissueApp({
      ...params,
      first_default_group: params.first_default_group ? 1 : 0,
      sdkappid,
    });
    if (res.code === 0) {
      message.success('设置成功');
      retry();
    }
  }

  return (
    <Card title="补发配置" style={{ marginTop: 15 }}>
      <Tabs defaultValue="1">
        <Tabs.TabPane tab="补发应用配置" key="app-reissue">
          <LoadingContainer loading={loading}>
            <Form
              form={form2}
              labelWrap={true}
              labelAlign="right"
              onFinish={(vals) => {
                onSubmit(vals);
              }}
            >
              <Form.Item
                name="reissue_strategy"
                label="补发策略"
                rules={[{ required: true }]}
                style={{ marginBottom: 5 }}
              >
                <Select style={{ width: 180 }} options={options} placeholder="补发策略" />
              </Form.Item>
              <Form.Item
                name="code_reissue"
                label="验证补发时效（s）"
                rules={[{ required: true }]}
                style={{ marginBottom: 5 }}
              >
                <InputNumber style={{ width: 180 }} min={10} max={300} addonAfter="s" />
              </Form.Item>
              <Form.Item
                name="other_reissue"
                label="其他补发时效（s）"
                rules={[{ required: true }]}
                style={{ marginBottom: 5 }}
              >
                <InputNumber style={{ width: 180 }} min={10} max={300} addonAfter="s" />
              </Form.Item>
              <Form.Item
                name="first_default_group"
                label="优先原调度组补发"
                tooltip="应用原通道配置，从通道组配置、App权重配置、全局配置依次选择第一个存在配置，不做降级补发"
                style={{ marginBottom: 5 }}
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
              <Form.Item>
                <Button type="primary" htmlType="submit">
                  set
                </Button>
              </Form.Item>
            </Form>
          </LoadingContainer>
        </Tabs.TabPane>
        <Tabs.TabPane tab="补发通道组配置" key="channel">
          <div>
            <Space>
              <Form
                form={form}
                layout="inline"
                labelWrap={true}
                labelAlign="right"
                onFinish={(vals) => {
                  onFormSub(vals);
                }}
              >
                <Form.Item
                  name="group_id"
                  label="通道组"
                  rules={[{ required: true }]}
                  style={{ marginBottom: 5 }}
                >
                  <SelectTooltip title={form.getFieldValue('group_id')}>
                    <Select
                      options={groups}
                      style={{ width: 680 }}
                      placeholder="请选择"
                      value={form.getFieldValue('group_id')}
                      onChange={(value) => form.setFieldsValue({ group_id: value })}
                      allowClear
                      showSearch
                      filterOption={(input, option: any) =>
                        option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                      }
                    />
                  </SelectTooltip>
                </Form.Item>
                <Form.Item
                  name="times"
                  label="补发次数"
                  rules={[{ required: true }]}
                  style={{ marginBottom: 5 }}
                >
                  <InputNumber min={1} style={{ width: 180 }} placeholder="补发次数" />
                </Form.Item>
                <Form.Item
                  name="order_id"
                  label="排序"
                  rules={[{ required: true }]}
                  style={{ marginBottom: 5 }}
                >
                  <InputNumber min={1} style={{ width: 180 }} placeholder="排序" />
                </Form.Item>
                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    添加
                  </Button>
                </Form.Item>
              </Form>
            </Space>
          </div>
          <ProTable
            actionRef={ref}
            rowKey="id"
            style={{ margin: '20px -24px 0 -24px' }}
            options={false}
            search={false}
            columns={[
              {
                title: '通道ID',
                key: 'group_id',
                dataIndex: 'group_id',
                align: 'center',
                readonly: true,
              },
              {
                title: '通道名称',
                key: 'group_name',
                dataIndex: 'group_name',
                align: 'center',
                editable: false,
              },
              {
                title: '扩展码',
                key: 'pre_extend',
                align: 'center',
                dataIndex: 'pre_extend',
                editable: false,
              },
              {
                title: '补发次数',
                key: 'times',
                align: 'center',
                dataIndex: 'times',
                valueType: 'digit',
                formItemProps,
              },
              {
                title: '顺序',
                key: 'order_id',
                align: 'center',
                dataIndex: 'order_id',
                valueType: 'digit',
                formItemProps,
              },
              {
                title: '通道类型',
                key: 'group_type',
                dataIndex: 'group_type',
                align: 'center',
                render: (text, record) => <span>{GroupType[record.group_type] || '--'}</span>,
                editable: false,
              },
              {
                title: '创建时间',
                key: 'create_time',
                align: 'center',
                dataIndex: 'create_time',
                editable: false,
              },
              {
                title: '编辑时间',
                key: 'modify_time',
                align: 'center',
                dataIndex: 'modify_time',
                editable: false,
              },
              {
                title: '操作',
                valueType: 'option',
                align: 'center',
                render: (text, record, _, action) => {
                  if (record.deleted) return <Tag>已删除</Tag>;
                  return [
                    <a
                      key="editable"
                      onClick={() => {
                        action?.startEditable?.(record.id);
                      }}
                    >
                      设置
                    </a>,
                    <a
                      key="delete"
                      onClick={() => {
                        doDel(record);
                      }}
                    >
                      删除
                    </a>,
                  ];
                },
              },
            ]}
            request={request}
            editable={{
              type: 'single',
              onSave: async (_, data) => {
                await editChannel(data);
              },
            }}
            pagination={{ pageSize: 10 }}
          />
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
};
export default ReissueConfig;
