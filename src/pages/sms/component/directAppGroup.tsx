import React, { useState, useEffect, useRef } from 'react';
import { Card, Select, Button, Space, message } from 'antd';
import {
  addDirectAppList,
  delDirectAppList,
  editDirectAppList,
  getDirectAppList,
  getDirectGroup,
} from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import { history } from 'umi';

interface groupProps {
  sdkappid: string;
}

type DataSourceType = {
  group_id: React.Key;
  decs: string;
  priority: number;
};

const DirectGroupWeight: React.FC<groupProps> = ({ sdkappid }) => {
  const [groups, setGroupIDs] = useState<any>([]);
  const [id, setID] = useState<any>([]);
  const ref = useRef<ActionType>();

  const request = async () => {
    const res = await getDirectAppList({ appids: [sdkappid] });
    return {
      data: res.data ?? [],
      success: true,
    };
  };

  async function doDel(row: any) {
    const { group_id, appid } = row;
    const res = await delDirectAppList({ group_id, appids: [appid] });
    if (res?.code === 0) {
      message.success('操作成功');
    }
    ref?.current?.reload();
  }

  async function doEdit(row: any) {
    const { group_id, priority, appid } = row;
    const params = { group_id, appid, priority };
    const res = await editDirectAppList({ ...params });
    if (res?.code === 0) {
      message.success('操作成功');
      ref?.current?.reload();
    }
  }

  useEffect(() => {
    const params = {
      page_index: 1,
      page_size: 100,
    };
    getDirectGroup(params).then((res) => {
      const providersList = (res?.data?.list ?? [])
        .filter((v: { enable: number }) => v.enable)
        .map((item: { group_id: number; desc: string }) => {
          return {
            value: item.group_id,
            label: `${item.desc}（${item.group_id}）`,
          };
        });
      setGroupIDs(providersList);
    });
  }, [sdkappid]);

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '通道组ID',
      dataIndex: 'group_id',
      editable: false,
      align: 'center',
      render: (group_id) => (
        <Button
          type="link"
          onClick={() => {
            history.push('/group/direct-customer-detail', { group_id });
          }}
        >
          {group_id}
        </Button>
      ),
    },
    {
      title: '表述信息',
      dataIndex: 'desc',
      align: 'center',
      editable: false,
    },
    {
      title: '优先级',
      key: 'priority',
      dataIndex: 'priority',
      valueType: 'select',
      align: 'center',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: {
        options: [
          {
            label: '0级(低)',
            value: 0,
          },
          {
            label: '1级(中)',
            value: 1,
          },
          {
            label: '2级(高)',
            value: 2,
          },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.group_id);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            doDel(record);
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  async function onAdd() {
    const res = await addDirectAppList({ appids: [sdkappid], group_id: id });
    res?.code === 0 && ref?.current?.reload();
  }

  return (
    <Card title="签名子码通道组权重" style={{ marginTop: 15 }}>
      <div>
        <Space>
          <>
            <span>组ID</span>
            <Select
              showSearch
              allowClear
              style={{ width: 250 }}
              onChange={(value) => setID(value)}
              placeholder="请选择"
            >
              {(groups ?? []).map((item: { value: number; label: string }) => {
                return (
                  <Select.Option value={item.value} key={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </>
          <Button type="primary" onClick={onAdd}>
            添加
          </Button>
        </Space>
      </div>
      <ProTable<DataSourceType>
        rowKey="group_id"
        actionRef={ref}
        style={{ margin: '20px -24px 0 -24px' }}
        options={false}
        search={false}
        columns={columns}
        request={request}
        editable={{
          type: 'single',
          onSave: async (rowKey, data) => {
            await doEdit(data);
          },
        }}
        pagination={{ pageSize: 10 }}
      />
    </Card>
  );
};
export default DirectGroupWeight;
