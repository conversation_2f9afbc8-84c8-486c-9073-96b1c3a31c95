import React, { useState, useEffect, useCallback } from 'react';
import { Card, Table, InputNumber, Button, Space, Form } from 'antd';
import { getDirtyWhite, addDirtyWhite } from '@/services/scdAPI';

interface AppWhiteProps {
  sdkappid: string;
}

const AppWhite: React.FC<AppWhiteProps> = ({ sdkappid }) => {
  const [form] = Form.useForm();
  const [list, setList] = useState<any>([]);
  const [count, setCount] = useState<number>(0);
  const [isLoading, setLoading] = useState<boolean>(false);

  const getList = useCallback(async () => {
    try {
      setLoading(false);
      const res = await getDirtyWhite({ sdkappid });
      setList(res?.data || []);
      setCount(res?.data?.length || 0);
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  }, [sdkappid]);

  useEffect(() => {
    getList();
  }, [getList]);

  async function onFormSub(vals: any) {
    const res = await addDirtyWhite({ ...vals, sdkappid });
    res?.code === 0 && getList();
  }

  return (
    <Card title={'全局脏字白名单'} style={{ marginTop: 15 }}>
      <div>
        <Space>
          <Form
            form={form}
            layout="inline"
            labelWrap={true}
            labelAlign="right"
            onFinish={(vals) => {
              onFormSub(vals);
            }}
          >
            <Form.Item name="id" label="id">
              <InputNumber style={{ width: 180 }} placeholder="id" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                添加
              </Button>
            </Form.Item>
          </Form>
        </Space>
      </div>
      <Table
        columns={[
          {
            title: 'id',
            dataIndex: 'id',
            key: 'id',
            align: 'center',
          },
          {
            title: 'value',
            dataIndex: 'value',
            key: 'value',
            align: 'center',
          },
        ]}
        dataSource={list}
        loading={isLoading}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
        }}
        style={{ marginTop: 20 }}
      />
    </Card>
  );
};
export default AppWhite;
