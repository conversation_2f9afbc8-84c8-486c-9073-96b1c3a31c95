import React, { useEffect, useRef, useState } from 'react';
import { Button, DatePicker, Form, InputNumber, Select, Upload, message } from 'antd';
import { ModalForm, ProFormInstance } from '@ant-design/pro-components';
import { exportUnbackfill } from '@/services/unbackfill';
import _ from 'lodash';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { UploadOutlined } from '@ant-design/icons';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
type RangeValue = [Dayjs | null, Dayjs | null] | null;

const { RangePicker } = DatePicker;

const uploadProps = {
  name: 'file',
  maxCount: 1,
  // action: '',
  headers: {
    authorization: 'authorization-text',
  },
  customRequest(option: any) {
    option.onSuccess({});
  },
  onChange(info) {
    if (info.file.status !== 'uploading') {
      console.log(info.file, info.fileList);
    }
    // if (info.file.status === 'done') {
    //   return;
    // }
    if (info.file.status === 'error') {
      message.error(`${info.file.name} file upload failed.`);
    }
  },
};

export function UnbackfillExportDialog() {
  const { regionOptions } = useFetchCountryInfo();
  const formRef = useRef<ProFormInstance>();
  const [form] = Form.useForm();
  const [time, setTime] = useState<RangeValue>(null);
  const [dates, setDates] = useState<RangeValue>(null);
  const [open, setOpen] = useState(false);

  async function onFinish(vals) {
    if (!time) {
      message.error('请选择时间');
      return;
    }
    const formData = new FormData();
    _.map(_.omit(vals, ['country_codes', 'serial_no_file', 'time']), (v, k) => {
      formData.append(k, v);
    });
    formData.append(
      'from',
      dayjs(time?.[0])
        .startOf('day')
        .format('YYYY-MM-DD HH:mm:ss'),
    );
    formData.append(
      'to',
      dayjs(time?.[1])
        .endOf('day')
        .format('YYYY-MM-DD HH:mm:ss'),
    );
    vals.serial_no_file &&
      formData.append('serial_no_file', vals.serial_no_file.file.originFileObj);
    vals.country_codes.forEach((v, i) => formData.append(`country_codes[${i}]`, v));
    try {
      const res = await exportUnbackfill(formData);
      if (res.code === 0) {
        message.success('导出成功，请稍后查看邮件');
      } else {
        message.error('导出失败');
      }
    } catch (err) {
      console.log(err);
    }
  }

  const disabledDate = (current: Dayjs) => {
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], 'days') >= 7;
    const tooEarly = dates[1] && dates[1].diff(current, 'days') >= 7;
    return !!tooEarly || !!tooLate;
  };

  function reset() {
    formRef.current?.resetFields();
    setTime(null);
    setDates(null);
  }

  const onOpenChange = (open: boolean) => {
    setOpen(open);
  };

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);
  return (
    <>
      <Button type="primary" style={{ marginLeft: 10 }} onClick={() => setOpen(true)}>
        批量导出
      </Button>
      <ModalForm
        formRef={formRef}
        layout="horizontal"
        open={open}
        onOpenChange={onOpenChange}
        labelCol={{ span: 6 }}
        width={600}
        onFinish={onFinish}
        initialValues={{ time: null }}
      >
        <Form.Item name="uin" label="uin" rules={[{ required: true }]}>
          <InputNumber style={{ width: 300 }} controls={false} />
        </Form.Item>
        <Form.Item name="sdkappid" label="sdkappid">
          <InputNumber style={{ width: 300 }} controls={false} />
        </Form.Item>
        <Form.Item name="country_codes" label="国家/地区" rules={[{ required: true }]}>
          <Select
            style={{ width: 300 }}
            options={regionOptions}
            onChange={(val: any) => {
              form.setFieldsValue({ country_code: val, mnc: undefined });
            }}
            mode="multiple"
            placeholder="请选择"
            allowClear
            showSearch
            filterOption={(input, option: any) =>
              option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        </Form.Item>
        <Form.Item name="provider_id" label="provider_id">
          <InputNumber style={{ width: 300 }} controls={false} />
        </Form.Item>
        <Form.Item label="起止时间" name="time" rules={[{ required: true }]}>
          <RangePicker
            value={dates || time}
            disabledDate={disabledDate}
            onCalendarChange={(val) => setDates(val)}
            onChange={(val) => setTime(val)}
            onBlur={() => console.log('blur has been triggered')}
          />
        </Form.Item>
        <Form.Item name="serial_no_file" label="文件" style={{ marginBottom: 10 }}>
          <Upload {...uploadProps}>
            <Button icon={<UploadOutlined />}>Click to Upload</Button>
          </Upload>
        </Form.Item>
      </ModalForm>
    </>
  );
}
