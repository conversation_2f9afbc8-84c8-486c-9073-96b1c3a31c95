import React, { useState, useEffect, useMemo } from 'react';
import ReactEcharts from 'echarts-for-react';
import { Table, Statistic, Row, Col } from 'antd';
import _ from 'lodash';
import { isMobile } from '@/const/jadgeUserAgent';

interface ChartProps {
  status: {
    value?:
      | {
          code: number;
          msg: string;
          data: object[];
        }
      | undefined;
    loading: boolean;
  };
  type: string;
}

interface DataProps {
  amount_succ: number;
  amount_cb_succ: number;
  charge_num: number;
}

function getColor(p: number) {
  if (p >= 20) {
    return 'green';
  }
  if (p <= -20) {
    return 'red';
  }
  return '#000';
}

function getChartsOption(data: DataProps[]) {
  if (!data) return { option: {}, sucSum: 0, sendSucSum: 0, chargeSum: 0 };
  const xData: string[] = [];
  const seriesData: number[] = [];
  const sucSum = _.sumBy(data, (o: DataProps) => o.amount_succ);
  const sendSucSum = _.sumBy(data, (o: DataProps) => o.amount_cb_succ);
  const chargeSum = _.sumBy(data, (o: DataProps) => o.charge_num);

  data.forEach((item: any) => {
    xData.push(item.ddate);
    seriesData.push(item.charge_num);
  });
  const option = {
    legend: { left: 'right', data: [{ name: '单日计费总量（条）' }] },
    xAxis: {
      type: 'category',
      data: xData,
    },
    yAxis: {
      type: 'value',
    },
    tooltip: {
      trigger: 'axis',
      position: ['50%', '50%'],
    },
    series: [
      {
        name: '单日计费总量（条）',
        data: seriesData,
        type: 'line',
      },
    ],
  };
  return { option, sucSum, sendSucSum, chargeSum };
}

const basic = [
  {
    title: '时间',
    dataIndex: 'ddate',
    key: 'ddate',
  },
  {
    title: '提交成功总量（条）',
    dataIndex: 'amount_succ',
    key: 'amount_succ',
  },
  {
    title: '送达成功总量（条）',
    dataIndex: 'amount_cb_succ',
    key: 'amount_cb_succ',
  },
  {
    title: '计费总量（条）',
    dataIndex: 'charge_num',
    key: 'charge_num',
  },
  {
    title: '日环比（%）',
    dataIndex: 'compare_day',
    key: 'compare_day',
    render: (compare_day: string | number) => {
      return <span style={{ color: getColor(Number(compare_day)) }}>{compare_day}</span>;
    },
  },
  {
    title: '周同比（%）',
    dataIndex: 'compare_week',
    key: 'compare_week',
    render: (compare_week: string | number) => {
      return <span style={{ color: getColor(Number(compare_week)) }}>{compare_week}</span>;
    },
  },
];

const CustomerDailyData = (props: ChartProps) => {
  const { status, type } = props;
  const [option, setOption] = useState({});
  const [sucSum, setSucSum] = useState(0);
  const [sendSucSum, setSendSucSum] = useState(0);
  const [chargeSum, setChargeSum] = useState(0);
  const columns = useMemo(() => {
    if (type === 'internal') {
      return basic.concat([
        {
          title: '送达成功量',
          dataIndex: 'num_cb_succ',
          key: 'num_cb_succ',
        },
        {
          title: '提交量',
          dataIndex: 'num_req',
          key: 'num_req',
        },
        {
          title: '提交成功量',
          dataIndex: 'num_succ',
          key: 'num_succ',
        },
        {
          title: '回执量',
          dataIndex: 'receipt',
          key: 'receipt',
        },
        {
          title: '回执率',
          dataIndex: 'receipt_rate',
          key: 'receipt_rate',
        },
        {
          title: '回执成功率',
          dataIndex: 'receipt_succ_rate',
          key: 'receipt_succ_rate',
        },
      ]);
    }
    return basic.concat([
      {
        title: '回执量',
        dataIndex: 'receipt',
        key: 'receipt',
      },
    ]);
  }, [type]);

  useEffect(() => {
    if (!status?.value) return;
    const { option, sucSum, sendSucSum, chargeSum } = getChartsOption(
      status?.value?.data as DataProps[],
    );
    setOption(option);
    setSucSum(sucSum);
    setSendSucSum(sendSucSum);
    setChargeSum(chargeSum);
  }, [status]);

  return (
    <div>
      {status?.value?.data?.length ? (
        <>
          <Row gutter={24}>
            <Col span={8}>
              <Statistic title="总提交成功量（条）" value={sucSum} />
            </Col>
            <Col span={8}>
              <Statistic title="总送达成功量（条）" value={sendSucSum} />
            </Col>
            <Col span={8}>
              <Statistic title="总计费量（条）" value={chargeSum} />
            </Col>
          </Row>
          <ReactEcharts option={option} />
          <Table
            rowKey="ddate"
            columns={columns}
            dataSource={status?.value?.data || []}
            scroll={isMobile() ? { x: 'max-content' } : undefined}
          />
        </>
      ) : (
        <div style={{ textAlign: 'center' }}>暂无数据</div>
      )}
    </div>
  );
};
export default CustomerDailyData;
