import React, { useState } from 'react';
import { Form } from 'antd';
import {
  BaseCheckNumberFormItem,
  BaseFormValue,
  createNumberInput,
} from './BaseCheckNumberFormItem';

interface FormValue extends BaseFormValue {
  count?: number;
  type?: string;
}

interface CheckNumberInputFormItemProps {
  value?: FormValue;
  onChange?: (val: FormValue | undefined) => void;
}

const CheckNumberInputFormItem: React.FC<CheckNumberInputFormItemProps> = ({ value, onChange }) => {
  const { count, id = 0, type } = value || {};

  const [isCheck, setIsCheck] = useState(!id);

  const handleInputChange = (val: string | number | null) => {
    const newCount = val ? Number(val) : 1;
    onChange?.(newCount ? { ...value, count: newCount } : undefined);
  };

  const handleCheckboxChange = (checked: boolean) => {
    setIsCheck(checked);
    onChange?.(checked ? { id } : { count: 1, id, type });
  };

  return (
    <BaseCheckNumberFormItem
      value={value}
      onChange={onChange}
      isCheck={isCheck}
      onCheckChange={handleCheckboxChange}
    >
      {createNumberInput({
        disabled: isCheck,
        value: isCheck ? undefined : count,
        onChange: handleInputChange,
      })}
      <span style={{ marginLeft: 3, color: '#ccc' }}>（当前ID:{id}）</span>
    </BaseCheckNumberFormItem>
  );
};

interface WrapperProps {
  name: string;
  label: string;
}

const Wrapper: React.FC<WrapperProps> = ({ name, label }) => (
  <Form.Item label={label} name={name} labelCol={{}}>
    <CheckNumberInputFormItem />
  </Form.Item>
);

export default Wrapper;
