import React, { useState, useEffect, useRef } from 'react';
import { Card, Select, Button, Space, InputNumber, Form, message } from 'antd';
import { getWeight, insertWeight, updateWeight } from '@/services/scdAPI';
import { getGloablProviders } from '@/services/api';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';

interface AppWeightProps {
  sdkappid: string;
}

const options = [
  { value: 0, label: '0级(低)' },
  { value: 1, label: '1级(中)' },
  { value: 2, label: '2级(高)' },
];

type DataSourceType = {
  provider_id: React.Key;
  provider: string;
  multi_b: number;
  multi: number;
  single_b: number;
  single: number;
};

const AppWeight: React.FC<AppWeightProps> = ({ sdkappid }) => {
  const [form] = Form.useForm();
  const [providers, setProviders] = useState<any>([]);
  const ref = useRef<ActionType>();

  const request = async (params: any) => {
    const res = await getWeight({
      appid: sdkappid,
      page_index: params.current,
      page_size: params.pageSize,
    });
    return {
      data: res.data?.list ?? [],
      total: res.data?.count || 0,
      success: true,
    };
  };

  async function doEdit(row: any) {
    const { appid, provider_id, hot_backup, single, single_b, multi2, multi2_b } = row;
    const res = await updateWeight({
      appid,
      provider_id,
      hot_backup,
      single,
      single_b,
      multi2,
      multi2_b,
    });
    if (res?.code === 0) {
      message.success('操作成功');
      ref?.current?.reload();
    }
  }

  useEffect(() => {
    getGloablProviders().then((res: any) => {
      const providersList = (res?.data ?? []).map(
        (item: { provider_id: number; provider_name: string }) => {
          return {
            value: item.provider_id,
            label: item.provider_name,
          };
        },
      );
      setProviders(providersList);
    });
  }, []);

  async function onFormSub(vals: any) {
    const res = await insertWeight({ ...vals, appid: sdkappid });
    res?.code === 0 && ref?.current?.reload();
  }

  return (
    <Card
      title={'app权重配置'}
      style={{ marginTop: 15 }}
      extra={
        <div>
          <Button
            type="link"
            onClick={() => {
              window.open(
                `http://observe.woa.com:8081/explore?orgId=**********&left=%5B%22now-30m%22,%22now%22,%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%7B%22datasource%22:%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%22groupBy%22:%5B%7B%22type%22:%22tag%22,%22params%22:%5B%22supplier%22%5D%7D,%7B%22type%22:%22tag%22,%22params%22:%5B%22teleoperator%22%5D%7D,%7B%22params%22:%5B%221m%22%5D,%22type%22:%22time%22%7D%5D,%22measurement%22:%22sms%22,%22namespace%22:%22%22,%22orderByTime%22:%22ASC%22,%22policy%22:%22default%22,%22region%22:%22gz_new%22,%22resultFormat%22:%22table%22,%22select%22:%5B%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(req_success_count)%2Fsum(req_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_count)%2Fsum(req_success_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_success_count)%2Fsum(cb_count)%22%5D%7D%5D%5D,%22slimit%22:%2210%22,%22tags%22:%5B%7B%22key%22:%22sdkappid%22,%22operator%22:%22%3D%22,%22value%22:%22${sdkappid}%22%7D%5D,%22timeShift%22:%22%22%7D%5D`,
              );
            }}
          >
            云监控（全局）
          </Button>
          <Button
            type="link"
            onClick={() => {
              window.open(
                `http://observe.woa.com:8081/explore?orgId=**********&left=%5B%22now-30m%22,%22now%22,%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%7B%22datasource%22:%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%22groupBy%22:%5B%7B%22type%22:%22tag%22,%22params%22:%5B%22supplier%22%5D%7D,%7B%22type%22:%22tag%22,%22params%22:%5B%22teleoperator%22%5D%7D,%7B%22params%22:%5B%221m%22%5D,%22type%22:%22time%22%7D%5D,%22measurement%22:%22sms%22,%22namespace%22:%22%22,%22orderByTime%22:%22ASC%22,%22policy%22:%22default%22,%22region%22:%22gz_new%22,%22resultFormat%22:%22table%22,%22select%22:%5B%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(req_success_count)%2Fsum(req_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_count)%2Fsum(req_success_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_success_count)%2Fsum(cb_count)%22%5D%7D%5D%5D,%22slimit%22:%2210%22,%22tags%22:%5B%7B%22key%22:%22sdkappid%22,%22operator%22:%22%3D%22,%22value%22:%22${sdkappid}%22%7D,%7B%22condition%22:%22AND%22,%22key%22:%22sms_type%22,%22operator%22:%22%3D%22,%22value%22:%220%22%7D%5D,%22timeShift%22:%22%22%7D%5D`,
              );
            }}
          >
            云监控（行业）
          </Button>
          <Button
            type="link"
            style={{ paddingRight: 0 }}
            onClick={() => {
              window.open(
                `http://observe.woa.com:8081/explore?orgId=**********&left=%5B%22now-30m%22,%22now%22,%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%7B%22datasource%22:%22%E4%BA%91%E7%9F%AD%E4%BF%A1%E6%95%B0%E6%8D%AE%E6%BA%90%22,%22groupBy%22:%5B%7B%22type%22:%22tag%22,%22params%22:%5B%22supplier%22%5D%7D,%7B%22type%22:%22tag%22,%22params%22:%5B%22teleoperator%22%5D%7D,%7B%22params%22:%5B%221m%22%5D,%22type%22:%22time%22%7D%5D,%22measurement%22:%22sms%22,%22namespace%22:%22%22,%22orderByTime%22:%22ASC%22,%22policy%22:%22default%22,%22region%22:%22gz_new%22,%22resultFormat%22:%22table%22,%22select%22:%5B%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22req_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22cb_success_count%22%5D%7D,%7B%22type%22:%22sum%22,%22params%22:%5B%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(req_success_count)%2Fsum(req_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_count)%2Fsum(req_success_count)%22%5D%7D%5D,%5B%7B%22type%22:%22field%22,%22params%22:%5B%22100*sum(cb_success_count)%2Fsum(cb_count)%22%5D%7D%5D%5D,%22slimit%22:%2210%22,%22tags%22:%5B%7B%22key%22:%22sdkappid%22,%22operator%22:%22%3D%22,%22value%22:%22${sdkappid}%22%7D,%7B%22condition%22:%22AND%22,%22key%22:%22sms_type%22,%22operator%22:%22%3D%22,%22value%22:%221%22%7D%5D,%22timeShift%22:%22%22%7D%5D`,
              );
            }}
          >
            云监控（营销）
          </Button>
        </div>
      }
    >
      <div>
        <Space>
          <Form
            form={form}
            layout="inline"
            labelWrap={true}
            labelAlign="right"
            onFinish={(vals) => {
              onFormSub(vals);
            }}
            initialValues={{ single: 0, single_b: 0, multi2: 0, multi2_b: 0, hot_backup: 1 }}
          >
            <Form.Item
              name="provider_id"
              label="provider_id"
              style={{ marginBottom: 5 }}
              rules={[{ required: true }]}
            >
              <Select showSearch style={{ width: 250 }} placeholder="请选择">
                {(providers ?? []).map((item: { value: number; label: string }) => {
                  return (
                    <Select.Option value={item.value} key={item.value}>
                      {item.label}
                    </Select.Option>
                  );
                })}
              </Select>
            </Form.Item>
            <Form.Item
              name="single"
              label="单发普通"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <InputNumber />
            </Form.Item>
            <Form.Item
              name="single_b"
              label="单发营销"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <InputNumber />
            </Form.Item>
            <Form.Item
              name="multi2"
              label="群发2普通"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <InputNumber />
            </Form.Item>
            <Form.Item
              name="multi2_b"
              label="群发2营销"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <InputNumber />
            </Form.Item>
            <Form.Item
              name="hot_backup"
              label="优先级"
              rules={[{ required: true }]}
              style={{ marginBottom: 5 }}
            >
              <Select options={options} style={{ width: 100 }} />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                配置
              </Button>
            </Form.Item>
          </Form>
        </Space>
      </div>
      <ProTable<DataSourceType>
        rowKey="provider_id"
        actionRef={ref}
        style={{ margin: '20px -24px 0 -24px' }}
        options={false}
        search={false}
        columns={[
          {
            title: 'provider_id',
            dataIndex: 'provider_id',
            key: 'provider_id',
            align: 'center',
            readonly: true,
          },
          {
            title: 'provider',
            dataIndex: 'provider',
            key: 'provider',
            align: 'center',
            editable: false,
          },
          {
            title: '单发普通',
            align: 'center',
            dataIndex: 'single',
            valueType: 'digit',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
          {
            title: '单发营销',
            align: 'center',
            dataIndex: 'single_b',
            valueType: 'digit',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
          {
            title: '群发2普通',
            align: 'center',
            dataIndex: 'multi2',
            valueType: 'digit',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
          {
            title: '群发2营销',
            align: 'center',
            dataIndex: 'multi2_b',
            valueType: 'digit',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
          {
            title: '优先级',
            key: 'hot_backup',
            dataIndex: 'hot_backup',
            valueType: 'select',
            align: 'center',
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
            fieldProps: {
              options: [
                {
                  label: '0级(低)',
                  value: 0,
                },
                {
                  label: '1级(中)',
                  value: 1,
                },
                {
                  label: '2级(高)',
                  value: 2,
                },
              ],
            },
          },
          {
            title: '操作',
            valueType: 'option',
            align: 'center',
            render: (text, record, _, action) => [
              <a
                key="editable"
                onClick={() => {
                  action?.startEditable?.(record.provider_id);
                }}
              >
                设置
              </a>,
            ],
          },
        ]}
        request={request}
        pagination={{ defaultPageSize: 10, showSizeChanger: true }}
        editable={{
          type: 'single',
          onSave: async (rowKey, data) => {
            await doEdit(data);
          },
        }}
      />
    </Card>
  );
};
export default AppWeight;
