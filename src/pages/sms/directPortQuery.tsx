import React, { useRef, useState } from 'react';
import { Button } from 'antd';
import { ActionType, PageContainer, ProTable } from '@ant-design/pro-components';
import { isMobile } from '@/const/jadgeUserAgent';
import _ from 'lodash';
import { getDirectPortList } from '@/services/directPort';
import { downloadCsvFile, findLabel } from '@/utils/utils';
import { cardType, smsTemplateType } from './component/const';
import qs from 'query-string';

const DirectPortList = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const [exportLoading, setExportLoading] = useState(false);

  const columns: any = [
    {
      title: 'sdkappid',
      dataIndex: 'sdkappid',
      key: 'sdkappid',
      width: 100,
    },
    {
      title: '供应商ID',
      dataIndex: 'provider_id',
      key: 'provider_id',
      hideInSearch: true,
    },
    {
      title: 'provider_str',
      dataIndex: 'provider_str',
      key: 'provider_str',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '模版类型',
      dataIndex: 'template_type',
      key: 'template_type',
      hideInSearch: true,
      render: (text, row: any) => {
        const f = row.template_type.split(',');
        return smsTemplateType
          .filter((el) => f.includes(el.value))
          .map((el) => el.label)
          .join(',');
      },
    },
    {
      title: '签名短信内容',
      dataIndex: 'msg',
      key: 'msg',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '签名',
      dataIndex: 'sign',
      key: 'sign',
      hideInSearch: true,
    },
    {
      title: '短信主端口号',
      dataIndex: 'access_code',
      key: 'access_code',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '短信子端口号',
      dataIndex: 'cb_extend',
      key: 'cb_extend',
      hideInSearch: true,
      width: 100,
    },
    {
      title: '子端口新增时间',
      dataIndex: 'add_time',
      key: 'add_time',
      hideInSearch: true,
    },
    {
      title: 'qappid',
      dataIndex: 'qappid',
      key: 'qappid',
      hideInSearch: true,
    },
    {
      title: 'uin',
      dataIndex: 'uin',
      key: 'uin',
      hideInSearch: true,
    },
    {
      title: '企业名称',
      dataIndex: 'company_name',
      key: 'company_name',
      hideInSearch: true,
    },
    {
      title: '企业统一社会信用代码',
      dataIndex: 'company_number',
      key: 'company_number',
      hideInSearch: true,
    },
    {
      title: '责任人名称',
      dataIndex: 'corp_name',
      key: 'corp_name',
      hideInSearch: true,
    },
    {
      title: '责任人证件类型',
      dataIndex: 'corp_cr_type',
      key: 'corp_cr_type',
      hideInSearch: true,
      render: (text, record: Record<string, any>) => findLabel(cardType, record.corp_cr_type),
    },
    {
      title: '责任人证件号',
      dataIndex: 'corp_cr_num',
      key: 'corp_cr_num',
      hideInSearch: true,
    },
    {
      title: '经办人名称',
      dataIndex: 'transactor_name',
      key: 'transactor_name',
      hideInSearch: true,
    },
    {
      title: '经办人证件类型',
      dataIndex: 'transactor_cr_type',
      key: 'transactor_cr_type',
      hideInSearch: true,
      render: (text, record: Record<string, any>) => findLabel(cardType, record.transactor_cr_type),
    },
    {
      title: '经办人证件号',
      dataIndex: 'transactor_cr_num',
      key: 'transactor_cr_num',
      hideInSearch: true,
    },
    {
      title: '经办人手机号',
      dataIndex: 'transactor_phone',
      key: 'transactor_phone',
      hideInSearch: true,
    },
    {
      title: '调度ID',
      dataIndex: 'scheduler_id',
      key: 'scheduler_id',
    },
  ];

  const requestFn = async (params: any) => {
    const { data } = await getDirectPortList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_index: params.current,
      page_size: params.pageSize,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  };

  async function handleExport() {
    try {
      setExportLoading(true);
      const values = formRef.current?.getFieldsValue();
      const params = {
        ..._.omit(
          _.pickBy(values, (v) => !_.isNil(v) && v !== ''),
          ['pageSize', 'current'],
        ),
      };
      const url = `${window.location.origin}/apis/sms/direct-port/export?${qs.stringify(params)}`;
      downloadCsvFile(url);
      setExportLoading(false);
    } catch (error) {
      console.log(error);
      setExportLoading(false);
    }
  }

  return (
    <PageContainer>
      <Button
        type="primary"
        style={{ marginLeft: 10 }}
        onClick={handleExport}
        loading={exportLoading}
      >
        导出
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapseRender: false,
          collapsed: false,
          span: 5,
        }}
        scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 2000 }}
        request={requestFn}
        options={false}
        form={{
          initialValues: {
            sdkappid: '1400001052',
          },
        }}
      />
    </PageContainer>
  );
};
export default DirectPortList;
