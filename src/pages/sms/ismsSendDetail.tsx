import React, { useMemo } from 'react';
import { Table } from 'antd';
import { useAsyncFn, useThrottleFn } from 'react-use';
import { queryIsmsReissue } from '@/services/thrdAPI';
import _ from 'lodash';

const IsmsSendDetail = (props: any) => {
  const { params, expanded, row } = props;

  const [state, doFetch] = useAsyncFn(async () => {
    const response = await queryIsmsReissue({ ...params });
    return response?.data || [];
  }, [params]);

  useThrottleFn(
    (expanded) => {
      if (expanded) {
        doFetch();
      }
    },
    200,
    [expanded],
  );

  const list = useMemo(() => {
    state?.value?.unshift(row);
    return state.value ?? [];
  }, [row, state.value]);

  const total = useMemo(() => {
    return state.value?.length ?? 0;
  }, [state]);

  return (
    <Table
      size="small"
      columns={[
        {
          title: '请求时间',
          dataIndex: 'req_time',
          key: 'req_time',
        },
        {
          title: 'message_id',
          dataIndex: 'message_id',
          key: 'message_id',
        },
        {
          title: 'serial_no',
          key: 'serial_no',
          dataIndex: 'serial_no',
        },

        {
          title: '供应商账号ID',
          dataIndex: 'provider_id',
          key: 'provider_id',
        },
        {
          title: '供应商名称',
          dataIndex: 'provider_name',
          key: 'provider_name',
        },
        {
          title: '提交结果',
          dataIndex: 'result_text',
          key: 'result_text',
        },
        {
          title: '回执状态',
          key: 'dr_status',
          render: (v, row: any, index: number) => {
            return index === 0 ? row.tmp_status : row?.dr_status || '-';
          },
        },
        {
          title: '回执时间',
          key: 'done_date',
          render: (v, row: any, index: number) => {
            return index === 0 ? row.tmp_done_date : row?.done_date || '-';
          },
        },
        {
          title: '回执用时',
          key: 'delay',
          render: (v, row: any, index) => {
            const key = index === 0 ? 'tmp_done_date' : 'done_date';
            const delay = row?.[key]
              ? new Date(row?.[key]).getTime() - new Date(row.req_time).getTime()
              : null;
            if (_.isNil(delay)) {
              return '-';
            }
            return delay < 1000 ? `${delay}毫秒` : `${delay / 1000}秒`;
          },
        },
      ]}
      dataSource={list}
      rowKey={(record) => `${record.serial_no}_child`}
      loading={state.loading}
      pagination={{ defaultCurrent: 1, total, showSizeChanger: true }}
      style={{ marginTop: 20 }}
    />
  );
};
export default IsmsSendDetail;
