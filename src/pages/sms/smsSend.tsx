import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Form, Input, Select } from 'antd';

export default function SmsSend() {
  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);

  async function onSubmit() {
    setLoading(true);
  }

  return (
    <PageContainer>
      <Form
        form={form}
        labelAlign="right"
        initialValues={{ type: '0', nationcode: '86' }}
        onFinish={onSubmit}
      >
        <Form.Item name="nationcode" label="国家码" rules={[{ required: true }]}>
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="mobile" label="手机号" rules={[{ required: true }]}>
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="type" label="短信类型" rules={[{ required: true }]}>
          <Select style={{ width: 200 }}>
            <Select.Option value="0">普通短信</Select.Option>
            <Select.Option value="1">营销短信</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item name="msg" label="短信内容" rules={[{ required: true }]}>
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="extend" label="扩展码" rules={[{ required: true }]}>
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="ext" label="透传字段" rules={[{ required: true }]}>
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="appid" label="appid" rules={[{ required: true }]}>
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={isLoading}>
            发送
          </Button>
        </Form.Item>
      </Form>
    </PageContainer>
  );
}
