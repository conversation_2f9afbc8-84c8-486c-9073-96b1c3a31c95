/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { useAsyncFn } from 'react-use';
import dayjs from 'dayjs';
import { getDailyAmount } from '@/services/scdAPI';
import { Card, Button, Form, Input, Select, DatePicker, Tabs, Space, message } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import { SearchOutlined, LoadingOutlined } from '@ant-design/icons';
import CustomerDailyData from './component/CustomerDailyData';

const { RangePicker } = DatePicker;

const tabs = [
  { id: 'internal', label: '国内短信' },
  { id: 'abroad', label: '国际短信' },
];

const stationOptions = [
  { value: 'all', label: '全部' },
  { value: 'domestic', label: '国内站' },
  { value: 'international', label: '国际站' },
];

const accountTypeOptions = [
  {
    value: 'uin',
    label: 'uin',
  },
  {
    value: 'sdkappid',
    label: 'sdkAppid',
  },
];

const businessTypeOptions = [
  {
    value: 'all',
    label: '全部',
  },
  {
    value: 'normal',
    label: '行业',
  },
  {
    value: 'market',
    label: '营销',
  },
];

const paymentTypeOptions = [
  {
    value: 'all',
    label: '全部',
  },
  {
    value: 'before',
    label: '预付费',
  },
  {
    value: 'after',
    label: '后付费',
  },
];

const customerTypeOptions = [
  {
    value: 'all',
    label: '全部',
  },
  {
    value: 'inside',
    label: '内部客户',
  },
  {
    value: 'outside',
    label: '外部客户',
  },
];

const DailyQuery = () => {
  const [dataRange] = useState([
    dayjs().subtract(1, 'days').startOf('d'),
    dayjs().subtract(1, 'days').endOf('d'),
  ]);
  const [regionType, setRegionType] = useState<string>('internal');
  const [loading, setLoading] = useState<boolean>(false);
  const [isInit, setInit] = useState<boolean>(true);
  const [searchValues, setSearchValues] = useState<{
    businessType: string;
    customerType: string;
    paymentType: string;
    dataRange: any;
    accountType: string;
    station: string;
    account?: string;
  }>({
    businessType: 'all',
    customerType: 'all',
    paymentType: 'all',
    dataRange,
    accountType: 'uin',
    station: 'all',
  });

  const prefixSelector = (
    <Form.Item name="accountType" noStyle>
      <Select style={{ width: 70 }} options={accountTypeOptions} />
    </Form.Item>
  );

  function getParams() {
    const { paymentType, businessType, customerType, dataRange, station, accountType, account } =
      searchValues;
    const query: Record<string, any> = {
      pay_type: paymentType,
      business_type: businessType,
      customer_type: customerType,
      start_time: dayjs(dataRange[0]).format('YYYY-MM-DD 00:00:00'),
      end_time: dayjs(dataRange[1]).format('YYYY-MM-DD 23:59:59'),
      region_type: regionType,
      station,
    };
    if (accountType === 'uin' && account?.includes(',')) {
      message.error({
        content: 'uin维度仅支持单个查询',
      });
      return;
    }
    query[accountType] = account;
    return query;
  }

  const [status, queryData] = useAsyncFn(
    async () => {
      const query = getParams();
      if (!query) return;
      setLoading(true);
      try {
        const res: any = await getDailyAmount(query);
        setLoading(false);
        return res;
      } catch (error) {
        setLoading(false);
      }
    },
    [regionType, searchValues],
    {
      value: {
        code: 0,
        msg: '',
        data: [],
      },
      loading: false,
      error: null || undefined,
    },
  );

  useEffect(() => {
    if (!isInit) {
      queryData();
    }
  }, [regionType, searchValues]);

  useEffect(() => {
    return () => {
      setInit(true);
    };
  }, []);

  return (
    <PageContainer>
      <Card>
        <Form
          layout="inline"
          initialValues={searchValues}
          onFinish={(vals) => {
            setInit(false);
            setSearchValues(vals);
          }}
          requiredMark={false}
        >
          <Form.Item name="account" style={{ marginBottom: 10 }}>
            <Input addonBefore={prefixSelector} placeholder="请输入内容" />
          </Form.Item>
          <Form.Item label="短信类型" name="businessType" rules={[{ required: true }]}>
            <Select options={businessTypeOptions} style={{ width: 80 }} />
          </Form.Item>
          <Form.Item
            label="日期"
            name="dataRange"
            rules={[{ required: true }]}
            style={{ marginBottom: 10 }}
          >
            <RangePicker />
          </Form.Item>
          <Form.Item label="付费方式" name="paymentType" rules={[{ required: true }]}>
            <Select options={paymentTypeOptions} style={{ width: 100 }} />
          </Form.Item>
          <Form.Item label="客户类型" name="customerType" rules={[{ required: true }]}>
            <Select options={customerTypeOptions} style={{ width: 120 }} />
          </Form.Item>
          <Form.Item label="站点" name="station" rules={[{ required: true }]}>
            <Select options={stationOptions} style={{ width: 100 }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={loading}>
                查询
              </Button>
              {/* <Button onClick={downloadFile}>下载</Button> */}
            </Space>
          </Form.Item>
        </Form>
      </Card>
      <Card>
        <Tabs
          defaultActiveKey={tabs[0].id}
          onTabClick={(val: string) => {
            setRegionType(val);
          }}
        >
          {tabs.map((tab: { id: string; label: string }) => {
            return (
              <Tabs.TabPane key={tab.id} tab={tab.label}>
                {!isInit ? (
                  !status.error ? (
                    status.loading ? (
                      <div style={{ textAlign: 'center', padding: '40px 0' }}>
                        <Button icon={<LoadingOutlined />} type="text">
                          努力加载中...
                        </Button>
                      </div>
                    ) : (
                      <CustomerDailyData status={status} type={tab.id} />
                    )
                  ) : (
                    <p style={{ color: 'red' }}>
                      {JSON.parse(JSON.stringify(status.error))?.status !== 200
                        ? `出现错误，错误信息【${JSON.parse(JSON.stringify(status.error))
                            ?.status}】`
                        : `出现错误，错误信息【${JSON.parse(JSON.stringify(status.error))?.data
                            ?.msg}】`}
                    </p>
                  )
                ) : (
                  <Button icon={<SearchOutlined />} type="text">
                    点击查询按钮开始
                  </Button>
                )}
              </Tabs.TabPane>
            );
          })}
        </Tabs>
      </Card>
    </PageContainer>
  );
};
export default DailyQuery;
