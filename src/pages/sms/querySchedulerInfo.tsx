import React, { useMemo } from 'react';
import { Button, Form, Input, Select, Table, Tag } from 'antd';
import { useCss, useSetState } from 'react-use';
import { PageContainer } from '@ant-design/pro-layout';
import _ from 'lodash';
import { SearchOutlined } from '@ant-design/icons';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { smsType } from '@/const/const';
import { queryChannelScheduler } from '@/services/schedulerInfo';
import { findText } from '@/utils/utils';
import { routeTypes } from '../intl-resource/const';
import SelectAll from '@/components/SelectAll';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const types = [{ value: 'uin' }, { value: 'sdkappid' }];
const confTypes = [
  {
    value: '0',
    label: (
      <>
        sdkappid
        <Tag color="green" style={{ marginLeft: 10 }}>
          优先级最高
        </Tag>
      </>
    ),
  },
  { value: '1', label: 'uin' },
  {
    value: '2',
    label: (
      <>
        全局
        <Tag color="red" style={{ marginLeft: 10 }}>
          优先级最低
        </Tag>
      </>
    ),
  },
];

function onCell(row: any, key?: string) {
  const spanKey = ['conf_type', 'sms_type'];
  if (row.isGrandFlag) {
    return { colSpan: key === spanKey[0] ? 9 : 0 };
  }
  if (row.isParentFlag) {
    return { colSpan: key === spanKey[0] ? 1 : key === spanKey[1] ? 8 : 0 };
  }
  return { colSpan: 1 };
}

const columns: any[] = [
  {
    width: 80,
    key: 'conf_type',
    render: (row: any) => {
      if (row.isGrandFlag) {
        return row.title;
      }
    },
    onCell: (row: any) => onCell(row, 'conf_type'),
  },
  {
    title: '短信类型',
    key: 'sms_type',
    width: 100,
    render: (row: any) => {
      if (row.isParentFlag) {
        return row.title;
      }
    },
    onCell: (row: any) => onCell(row, 'sms_type'),
  },
  {
    title: '通道ID',
    dataIndex: 'channel_id',
    key: 'channel_id',
    align: 'center',
    onCell,
  },
  {
    title: '通道名称(smpp账号)<sdkappid>',
    key: 'channel_name',
    align: 'center',
    render: (row: any) =>
      `${row.channel_name}(${row.smpp_account})${row.sdkappid ? `<${row.sdkappid}>` : ''}`,
    onCell,
  },
  {
    title: '供应商名称',
    dataIndex: 'supplier_name',
    key: 'supplier_name',
    align: 'center',
    onCell,
  },
  {
    title: 'mcc',
    dataIndex: 'mcc',
    key: 'mcc',
  },
  {
    key: 'route_type',
    title: '路由类型',
    render: (row: any) =>
      _.filter(
        _.map(row.route_type, (v: number) => findText(routeTypes, v)) ?? [],
        (v) => v !== '',
      ).join('/'),
    onCell,
  },
  {
    title: '权重',
    dataIndex: 'weight',
    key: 'weight',
    align: 'center',
    onCell,
  },
  {
    title: '优先级',
    dataIndex: 'level',
    key: 'level',
    align: 'center',
    onCell,
  },
];

const QuerySchedulerInfo = () => {
  const [form] = Form.useForm();
  const { regionOptions } = useFetchCountryInfo();
  const [searchKeys, setSearchKeys] = useSetState<{
    type: string;
    value: string;
    country_code: string;
    page_index: number;
    page_size: number;
  }>({
    type: 'uin',
    value: '',
    country_code: '',
    page_index: 1,
    page_size: 10000,
  });

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    if (!searchKeys.value) return;
    const res = await queryChannelScheduler({
      ..._.omit(searchKeys, ['type', 'value']),
      [searchKeys.type]: searchKeys.value,
    });
    return res.data;
  }, [searchKeys]);

  const list = useMemo(() => {
    const _list = _.map(_.groupBy(state?.list ?? [], 'conf_type'), (v, k) => {
      return {
        key: k,
        isGrandFlag: true,
        title: _.find(confTypes, (item) => item.value === k)?.label,
        children: _.map(_.groupBy(v, 'sms_type'), (v1, k1) => {
          return {
            key: `${k}_${k1}`,
            isParentFlag: true,
            title: _.find(smsType, (v) => v.value.toString() === k1)?.label,
            children: _.sortBy(
              _.map(v1, (o) => ({
                ...o,
                key: `${o.sdkappid || ''}_${o.id}_${o.sms_type}_${o.channel_id}_${o.mcc}_${o.type}`,
              })),
              ['sdkappid', 'operator_name'],
            ),
          };
        }),
      };
    });
    return _list;
  }, [state]);

  const typeClass = useCss({
    'div.type_margin_0': {
      marginRight: 0,
    },
  });

  return (
    <PageContainer>
      <div>
        <Form
          form={form}
          layout="inline"
          labelAlign="right"
          onFinish={(vals) => setSearchKeys({ ...vals })}
          initialValues={{ type: 'uin' }}
          className={typeClass}
        >
          <Form.Item name="type" rules={[{ required: true }]} className="type_margin_0">
            <Select
              options={types}
              onChange={() => form.setFieldsValue({ value: undefined })}
              style={{ minWidth: 80 }}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.type !== curValues.type}
          >
            <Form.Item label="" name="value" rules={[{ required: true }]}>
              <Input style={{ width: 160 }} placeholder="请输入" allowClear />
            </Form.Item>
          </Form.Item>
          <Form.Item name="country_code" label="国家/地区" rules={[{ required: true }]}>
            <Select
              showSearch
              allowClear
              placeholder="国家/地区"
              options={regionOptions}
              filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item name="sms_types" label="短信类型" rules={[{ required: true }]}>
            <SelectAll
              mode="multiple"
              allowClear
              placeholder="请选择"
              options={smsType}
              style={{ width: 120 }}
            />
          </Form.Item>
          <Form.Item>
            <Button htmlType="submit" type="primary" icon={<SearchOutlined />}>
              查询
            </Button>
          </Form.Item>
        </Form>
      </div>
      <div style={{ marginTop: 15 }}>
        <Table
          bordered
          expandable={{
            defaultExpandedRowKeys: ['0', '1', '2'],
          }}
          columns={columns}
          dataSource={list}
          rowKey="key"
          loading={loading}
          pagination={false}
        />
      </div>
    </PageContainer>
  );
};
export default QuerySchedulerInfo;
