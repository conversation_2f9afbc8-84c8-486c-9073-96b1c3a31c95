import React, { useMemo } from 'react';
import { queryReissueInfo } from '@/services/api';
import { message, Table, Tag } from 'antd';
import { useAsyncFn, useThrottleFn } from 'react-use';
import CopyToClipboard from 'react-copy-to-clipboard';
import { getCopyText } from './sendQuery';
import { isMobile } from '@/const/jadgeUserAgent';

const SendDetail = (props: any) => {
  const { params, expanded } = props;

  const [state, doFetch] = useAsyncFn(async () => {
    const response = await queryReissueInfo({ ...params });
    return response?.data || [];
  }, [params]);

  useThrottleFn(
    (expanded) => {
      if (expanded) {
        doFetch();
      }
    },
    200,
    [expanded],
  );

  const list = useMemo(() => {
    return state.value ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state.value?.length ?? 0;
  }, [state]);

  return (
    <Table
      size="small"
      columns={[
        {
          title: '通道调度',
          dataIndex: 'provider',
          key: 'provider',
        },
        {
          title: '流水号',
          key: 'sid',
          dataIndex: 'sid',
        },

        {
          title: '发送时间',
          dataIndex: 'time',
          key: 'time',
        },
        {
          title: '送达时间',
          dataIndex: 'callbacktime',
          key: 'callbacktime',
        },
        {
          title: '时延',
          key: 'delay',
          render: (row: any) => {
            if (!row?.callbacktime) {
              return '--';
            }
            const duration = (+new Date(row.callbacktime) - +new Date(row.time)) / 1000;
            return (
              <Tag color={duration > 60 ? 'red' : duration > 10 ? 'blue' : 'green'}>
                {duration >= 1 ? duration : 0}
              </Tag>
            );
          },
        },
        {
          title: '返回状态',
          dataIndex: 'state',
          key: 'state',
        },
        {
          title: '送达结果',
          dataIndex: 'callbackresult_text',
          key: 'callbackresult_text',
        },
        {
          title: '操作',
          key: 'operation',
          align: 'center',
          fixed: 'right',
          render: (row: any) => {
            const { decodeText, delay } = getCopyText(row);
            return (
              <div>
                <CopyToClipboard
                  text={`${row.mobile || row.ori_mobile}\t${row.time}\t时延${delay}秒\t${
                    row.state || ''
                  }\t 「 【${row.sign}】${decodeText} 」\n\n请核查原因 `}
                  onCopy={() => {
                    message.success('Copy successfully');
                  }}
                >
                  <Tag color="#108ee9">复制</Tag>
                </CopyToClipboard>
              </div>
            );
          },
        },
      ]}
      dataSource={list}
      rowKey={(record) => record.aid}
      loading={state.loading}
      pagination={{ defaultCurrent: 1, total, showSizeChanger: true }}
      scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 1300 }}
      style={{ marginTop: 20 }}
    />
  );
};
export default SendDetail;
