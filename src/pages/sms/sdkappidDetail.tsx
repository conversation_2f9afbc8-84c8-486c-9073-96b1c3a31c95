import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { But<PERSON>, Card, FloatButton, message, Select, Table, Tabs, Tag } from 'antd';
import {
  getAppDetail,
  setAppDetail,
  delAppCmq,
  getAppCmq,
  setAppCmq,
  getCommonBitDefine,
} from '@/services/scdAPI';
import { history, Navigate } from 'umi';
import DetailForm from '@/pages/sms/component/detailForm';
import AppWeight from '@/pages/sms/component/AppWeight';
import AppWhite from '@/pages/sms/component/AppWhite';
import AppBlack from '@/pages/sms/component/AppBlack';
import AppGroupWeight from '@/pages/sms/component/appGroup';
import IotsmsOperator from '@/pages/sms/component/IotsmsOperator';
import _ from 'lodash';
import DirectGroupWeight from './component/directAppGroup';
import CopyToClipboard from 'react-copy-to-clipboard';
import ReissueConfig from './component/ReissueConfig';
import AppWarning from './component/AppWarning';
import {
  startUpOpts,
  majorSalesTypes,
  billTypes,
  quickRepOpts,
  disabledOpts,
  cmqFormSetting,
  billingCycle,
} from './component/const';
import { useQuery } from '@/utils/react-use/useQuery';
import { useAsyncRetry } from 'react-use';
import CheckNumberInputFormItem from './component/CheckNumberInputFormItem';
import CheckNumberAndTimeRangeFormItem from './component/CheckNumberAndTimeRangeFormItem';
import CheckNumberWithDisableFormItem from './component/CheckNumberWithDisableFormItem';
import { isChina } from '@/const/const';
function checkAvailableItem(
  initValues: Record<string, any> = {},
  newValues: Record<string, any> = {},
) {
  return _.pickBy(newValues, (value, key) => {
    return !_.isEqual(value, initValues[key]);
  });
}

function getBillingCycleCn(value?: 0 | 1) {
  const props =
    value === undefined
      ? {
          type: 'text',
          textRender: () => _.find(billingCycle, (v) => v.value === value)?.label || '',
        }
      : {
          type: 'select',
          options: billingCycle,
        };

  return {
    label: '国内短信计费周期',
    name: 'billing_cycle_cn',
    ...props,
  };
}
const rule_keys = [
  { key: 'natural_day_frq', type: 1 },
  { key: 'same_mobile_naturalday_frq', type: 2 },
  { key: 'same_mobile_30s_frq', type: 3 },
  { key: 'same_mobile_1h_frq', type: 4 },
  {
    key: 'same_content_and_mobile_frq',
    type: 5,
    use_interval: true,
  },
];
const transformRuleData = (data: any) => {
  const ruleData: Record<string, any> = {};
  rule_keys.forEach((v) => {
    const count_key = `${v.key}_count`;
    const id_key = `${v.key}_id`;
    const time_key = `${v.key}_interval`;

    ruleData[v.key] = {
      count: data[count_key],
      id: data[id_key],
      type: v.type,
      ...(v.use_interval ? { interval: data[time_key] } : {}),
    };
  });
  return {
    ...data,
    ...ruleData,
  };
};
const transformRuleResult = (data: any) => {
  const ruleData: Record<string, any>[] = [];
  rule_keys.forEach((v) => {
    if (data[v.key]?.count) {
      ruleData.push({
        count: data[v.key].count,
        id: data[v.key].id,
        type: v.type,
        ...(v.use_interval ? { interval: data[v.key].interval } : {}),
      });
    }
  });
  return ruleData;
};
const SmsSdkappidDetail = () => {
  const { sdkappid } = useQuery();
  const [isLoading, setLoading] = useState<boolean>(false);
  const formRef = useRef<Record<string, any>>({});

  const [list, setList] = useState<any>([]);
  const [signOpts, setSignOpts] = useState<any>([]);
  const [initValus, setInitValus] = useState<Record<string, any>>({});
  const [cmqInfo, setCmqInfo] = useState({});
  const [cmqValue, setCmqValue] = useState('0');
  const [tabKey, setTabKey] = useState('1');

  const { value: bitList } = useAsyncRetry(async () => {
    const { data } = await getCommonBitDefine();
    return data ?? [];
  }, []);

  const getBitOptions = useCallback(
    (scope: number) => {
      const antiSiteScope = !isChina ? 1 : 2;
      return _.map(
        bitList?.filter((el: any) => el.app_scope !== scope && el.site_scope !== antiSiteScope),
        (el) => ({
          label: el.bit_desc,
          type: 'select',
          name: el.bit_index,
          options: [
            { value: 0, label: '否' },
            { value: 1, label: '是' },
          ],
        }),
      );
    },
    [bitList],
  );

  const appBit = useMemo(() => getBitOptions(2), [getBitOptions]);
  const customerBit = useMemo(() => getBitOptions(1), [getBitOptions]);

  const configTabs = useMemo(() => {
    return [
      {
        id: 'basic',
        key: '1',
        title: '基础',
        setting: [
          {
            name: 'sdkappid',
            label: 'sdkappid',
            type: 'text',
            textRender: () => initValus.sdkappid,
          },
          {
            name: 'name',
            label: 'name',
            type: 'input',
          },
          {
            label: 'sdkappid状态',
            name: 'enabled',
            type: 'select',
            options: [
              { value: 0, label: '禁用' },
              { value: 1, label: '启用' },
            ],
          },
          {
            label: '短信服务状态',
            name: 'start_up',
            type: 'text',
            textRender: () => _.find(startUpOpts, (v) => v.value === initValus.start_up)?.label,
          },
          {
            name: 'channel_mask',
            label: 'channel_mask',
            type: 'input',
          },
          {
            label: '腾讯云appid',
            type: 'input',
            name: 'qappid',
          },
          {
            label: '创建时间',
            name: 'create_time',
            type: 'text',
            textRender: () => initValus.create_time,
          },
          {
            label: '客户类型',
            name: 'major_sales_type',
            type: 'text',
            textRender: () =>
              _.find(majorSalesTypes, (v) => v.value === initValus.major_sales_type)?.label,
          },
          {
            label: '短信付费方式',
            type: 'select',
            name: 'pay_type',
            options: [
              { value: 1, label: '预付费' },
              { value: 0, label: '后付费' },
            ],
            extra: '会修改该应用对应账号下所有应用付费方式',
          },
          {
            label: '认证类型',
            type: 'select',
            name: 'user_type',
            options: [
              { value: 1, label: '企业认证' },
              { value: 0, label: '个人认证' },
            ],
            extra: '会修改该应用对应账号下所有应用认证类型',
          },
          {
            label: '计费类型',
            type: 'text',
            name: 'billing_type',
            textRender: () => _.find(billTypes, (v) => v.value === initValus.billing_type)?.label,
          },
          getBillingCycleCn(initValus?.billing_cycle_cn),
        ],
      },
      {
        id: 'audit',
        key: '2',
        title: '审核校验',
        setting: [
          {
            label: '检查ip白名单',
            name: 'check_ip',
            type: 'select',
            options: [
              { value: 0, label: '不检查' },
              { value: 1, label: '检查' },
            ],
          },
          {
            label: '检查普通短信模版',
            name: 'check_normal_template',
            type: 'select',
            options: [
              { value: 0, label: '不检查' },
              { value: 1, label: '检查' },
            ],
          },
          {
            label: '检查营销短信模版',
            name: 'check_business_template',
            type: 'select',
            options: [
              { value: 0, label: '不检查' },
              { value: 1, label: '检查' },
            ],
          },
          {
            label: '默认签名',
            name: 'default_sign',
            type: 'select',
            options: signOpts,
          },
          {
            label: '校验签名',
            type: 'select',
            name: 'verify_sign',
            options: [
              { value: 0, label: '否' },
              { value: 1, label: '是' },
            ],
          },
          {
            label: '替换无效签名',
            type: 'select',
            name: 'replace_ineffective_sign',
            options: [
              { value: 0, label: '不替换' },
              { value: 1, label: '替换' },
            ],
          },
        ],
      },
      {
        id: 'special',
        key: '3',
        setting: [
          {
            label: '扩展码长度',
            name: 'self_extend_len',
            type: 'number',
          },
          {
            label: '快速回包',
            type: 'select',
            name: 'quick_response',
            options: [
              { value: 0, label: '没有' },
              { value: 1, label: '普通' },
              { value: 2, label: '极速' },
            ],
          },
          {
            label: '送达报告开扩展',
            type: 'checkbox',
            name: 'callbackNeedExt',
            options: quickRepOpts,
          },
          {
            label: '检查普通短信退订',
            type: 'select',
            name: 'check_unsubscribe_normal',
            options: [
              { value: 0, label: '否' },
              { value: 1, label: '是' },
            ],
          },
          {
            label: '检查营销短信退订',
            type: 'select',
            name: 'check_unsubscribe_business',
            options: [
              { value: 0, label: '否' },
              { value: 1, label: '是' },
            ],
          },
          {
            label: '检查模板参数长度',
            type: 'select',
            name: 'check_templ_param_len',
            options: [
              { value: 0, label: '不检查' },
              { value: 1, label: '检查' },
            ],
          },
          {
            label: '检查验证码模板参数',
            type: 'select',
            name: 'check_verify_code_templ_param',
            options: [
              { value: 0, label: '不检查' },
              { value: 1, label: '检查' },
            ],
          },
          {
            label: '检查模板参数是否带了URL',
            type: 'select',
            name: 'check_use_url_variable',
            options: [
              { value: 0, label: '不检查' },
              { value: 1, label: '检查' },
            ],
          },
          {
            label: 'cmpp soap短信类型',
            type: 'select',
            name: 'cmpp_type',
            options: [
              { value: 0, label: '普通' },
              { value: 1, label: '营销' },
            ],
          },
          {
            label: '强制调度',
            type: 'select',
            name: 'force_route',
            options: [
              { value: 1, label: '强制' },
              { value: 0, label: '不强制' },
            ],
          },
          {
            label: '日志大客户通道',
            type: 'select',
            name: 'large_log_channel',
            options: [
              { value: 0, label: '不使用' },
              { value: 1, label: '使用' },
            ],
          },

          {
            label: '短信回复扩展位是否检查',
            type: 'select',
            name: 'is_need_check_reply_extend',
            options: [
              { value: 1, label: '是' },
              { value: 0, label: '否' },
            ],
          },
          {
            label: '推送回执是否需要sig',
            type: 'select',
            name: 'is_need_trusted_key',
            options: [
              { value: 1, label: '是' },
              { value: 0, label: '否' },
            ],
          },
          {
            label: '是否强制组调度',
            type: 'select',
            name: 'group_router_force',
            isHide: !isChina,
            options: [
              { value: 1, label: '强制' },
              { value: 0, label: '不强制' },
            ],
          },
          {
            label: '是否支持全ascii字符字数计费',
            type: 'select',
            name: 'can_ascii_calc_fee',
            isHide: !isChina,
            options: [
              { value: 1, label: '支持' },
              { value: 0, label: '不支持' },
            ],
          },
          {
            label: '检查脏字',
            type: 'select',
            name: 'dirty_check_on',
            options: [
              { value: 0, label: '不开启' },
              { value: 1, label: '开启全局检查' },
              { value: 2, label: '开启APP检查' },
              { value: 3, label: '开启全局和APP检查' },
              { value: 4, label: 'isec检查' },
              { value: 7, label: '开启所有检查' },
            ],
          },
          ...appBit,
        ],
        title: '特殊配置',
      },
      {
        id: 'freqLimit',
        key: '4',
        setting: [
          {
            name: 'natural_day_frq',
            label: '自然日发送不超过条数',
            render: ({ name, label }: any) => {
              return <CheckNumberInputFormItem name={name} label={label} />;
            },
          },
          {
            name: 'same_mobile_naturalday_frq',
            label: '同手机号一个自然日内发送条数',
            render: ({ name, label }: any) => {
              return <CheckNumberInputFormItem name={name} label={label} />;
            },
          },
          {
            name: 'same_mobile_30s_frq',
            label: '同手机号30s内发送条数',
            render: ({ name, label }: any) => {
              return <CheckNumberInputFormItem name={name} label={label} />;
            },
          },
          {
            name: 'same_mobile_1h_frq',
            label: '同手机号1h内发送条数',
            render: ({ name, label }: any) => {
              return <CheckNumberInputFormItem name={name} label={label} />;
            },
          },
          {
            name: 'same_content_and_mobile_frq',
            md: 24,
            label: '相同内容同手机号一段时间发送条数',
            render: ({ name }: any) => {
              return <CheckNumberAndTimeRangeFormItem name={name} />;
            },
          },
          {
            name: 'app_china_daily_max_limit',
            label: '国内短信日下发限制（条数）',
            type: 'number',
            extra: '条数“-1”表示不限制',
            render: ({ name, label }: any) => {
              return <CheckNumberWithDisableFormItem name={name} label={label} />;
            },
          },
          {
            name: 'app_abroad_daily_max_limit',
            label: '国际短信日下发限制（条数）',
            render: ({ name, label }: any) => {
              return <CheckNumberWithDisableFormItem name={name} label={label} />;
            },
            extra: '条数“-1”表示不限制',
          },
          {
            label: '国内短信告警',
            progeny: [
              {
                name: 'enable_text_threshold',
                type: 'text',
                textRender: () => (
                  <Tag color={initValus.enable_text_threshold === 0 ? 'error' : 'success'}>
                    {
                      _.find(disabledOpts, (v) => v.value === initValus.enable_text_threshold)
                        ?.label
                    }
                  </Tag>
                ),
              },
              {
                name: 'text_threshold',
                type: 'text',
                textRender: () => initValus.text_threshold,
              },
            ],
          },
          {
            render: () => {
              return (
                <Button
                  type="link"
                  style={{ paddingLeft: 0 }}
                  onClick={() => {
                    history.push('/app/intlfreq', { sdkappid });
                  }}
                >
                  国际/港澳台短信可发送国家/地区设置
                </Button>
              );
            },
          },
          {
            label: '国际短信告警',
            progeny: [
              {
                name: 'enable_text_oversea_threshold',
                type: 'text',
                textRender: () => (
                  <Tag color={initValus.enable_text_oversea_threshold === 0 ? 'error' : 'success'}>
                    {
                      _.find(
                        disabledOpts,
                        (v) => v.value === initValus.enable_text_oversea_threshold,
                      )?.label
                    }
                  </Tag>
                ),
              },
              {
                name: 'text_oversea_threshold',
                type: 'text',
                textRender: () => initValus.text_oversea_threshold,
              },
            ],
          },
        ],
        title: '频率限制',
      },
      {
        id: 'callback',
        key: '5',
        setting: [
          {
            label: '短信送达回调',
            progeny: [
              {
                name: 'sms_callback_used',
                type: 'select',
                options: [
                  { value: 0, label: '禁用' },
                  { value: 1, label: '启用' },
                ],
              },
              {
                name: 'sms_callback',
                type: 'input',
              },
            ],
          },
          {
            label: '短信送达回调类型',
            name: 'sms_callback_type',
            type: 'select',
            options: [
              { value: 0, label: 'json' },
              { value: 3, label: 'jd' },
              { value: 5, label: 'bigo' },
              { value: 6, label: '其他' },
            ],
          },
          {
            label: '短信回复回调',
            progeny: [
              {
                name: 'reply_callback_used',
                type: 'select',
                options: [
                  { value: 0, label: '禁用' },
                  { value: 1, label: '启用' },
                ],
              },
              {
                name: 'reply_callback',
                type: 'input',
              },
            ],
          },
          {
            label: '短信回复回调类型',
            name: 'reply_type',
            type: 'select',
            options: [
              { value: 0, label: 'json' },
              { value: 1, label: 'post' },
              { value: 2, label: 'get' },
              { value: 3, label: 'jd' },
              { value: 4, label: '其他' },
            ],
          },
          {
            label: '上行短信回调',
            progeny: [
              {
                name: 'upsms_callback_used',
                type: 'select',
                options: [
                  { value: 0, label: '禁用' },
                  { value: 1, label: '启用' },
                ],
              },
              {
                name: 'upsms_callback',
                type: 'input',
              },
            ],
          },
          {
            label: '上行短信回调类型',
            name: 'upsms_type',
            type: 'select',
            options: [
              { value: 0, label: 'json' },
              { value: 1, label: 'post' },
              { value: 2, label: 'get' },
              { value: 3, label: 'jd' },
              { value: 4, label: '其他' },
            ],
          },
          {
            label: 'qappid上行回调',
            extra: '会修改该应用对应账号下所有应用的配置',
            progeny: [
              {
                name: 'reply_url_used',
                type: 'select',
                options: [
                  { value: 0, label: '禁用' },
                  { value: 1, label: '启用' },
                ],
              },
              {
                name: 'reply_url',
                type: 'input',
              },
            ],
          },
        ],
        title: '回调配置',
      },
      {
        id: 'isms',
        key: '6',
        setting: [
          {
            label: '每日海外短信限制条数,0表示不限制（appid维度）',
            type: 'input',
            name: 'abroad_max_amount',
            extra: '会修改该应用对应账号下所有应用的限制',
          },
          {
            label: '国际短信计费周期',
            type: 'select',
            name: 'billing_cycle_foreign',
            options: billingCycle,
          },
        ],
        title: '国际短信',
      },
      {
        id: 'monitor',
        key: '7',
        setting: [
          {
            label: '请求量',
            name: 'attr_req_total',
            type: 'number',
          },
          {
            label: '应答成功量',
            name: 'attr_req_succ',
            type: 'number',
          },
          {
            label: '应答失败量',
            name: 'attr_req_fail',
            type: 'number',
          },
          {
            label: '回调量',
            name: 'attr_rsp_total',
            type: 'number',
          },
          {
            label: '回调成功量',
            name: 'attr_rsp_succ',
            type: 'number',
          },
          {
            label: '回调失败量',
            name: 'attr_rsp_fail',
            type: 'number',
          },
        ],
        title: 'monitor监控',
      },
      {
        id: 'voice',
        key: '8',
        setting: [
          {
            label: '语音回调',
            progeny: [
              {
                type: 'select',
                name: 'voice_callback_url_used',
                options: [
                  { value: 0, label: '禁用' },
                  { value: 1, label: '启用' },
                ],
              },
              {
                type: 'input',
                name: 'voice_callback_url',
              },
            ],
          },
          {
            label: '语音显示号码',
            type: 'input',
            name: 'voice_display_number',
          },
          {
            label: '校验语音模板',
            type: 'select',
            name: 'voice_verify_template',
            options: [
              { value: 0, label: '否' },
              { value: 1, label: '是' },
            ],
          },
          {
            label: '发送语音文件',
            type: 'select',
            name: 'can_send_voice_file',
            options: [
              { value: 0, label: '否' },
              { value: 1, label: '是' },
            ],
          },
          {
            label: '检查语音文件状态',
            type: 'select',
            name: 'check_voice_file_status',
            options: [
              { value: 0, label: '否' },
              { value: 1, label: '是' },
            ],
          },
          {
            label: '语音付费方式',
            type: 'select',
            name: 'voice_pay_type',
            options: [
              { value: 1, label: '预付费' },
              { value: 0, label: '后付费' },
            ],
            extra: '会修改该应用对应账号下所有应用付费方式',
          },
        ],
        title: '语音',
      },
      {
        id: 'appid_special',
        key: '9',
        setting: [...customerBit],
        title: 'appid维度特殊配置',
      },
    ];
  }, [appBit, customerBit, initValus, isChina, sdkappid, signOpts]);

  const getCmqQueue = useCallback(async () => {
    const res = await getAppCmq({ sdkappid });
    Object.keys(res.data).length ? setCmqValue('1') : setCmqValue('0');
    setCmqInfo(res?.data || {});
  }, [sdkappid]);

  const pickNeedExt = useCallback(
    (data: Record<string, any>) => {
      if (data) {
        const needExt: string[] = [];
        const extAr = _.pick(data, [
          'callback_need_ext_bit_sms_ext',
          'callback_need_ext_bit_sms_fee',
          'callback_need_ext_bit_voice_ext',
        ]);
        _.each(extAr, (item, key) => {
          item === 1 && needExt.push(key);
        });
        const appBitFlag = _.reduce(
          appBit,
          (res, cur) => {
            return {
              ...res,
              [cur.name]: (data?.app_common_bit_flag ?? []).includes(cur.name) ? 1 : 0,
            };
          },
          {},
        );
        const customerBitFlag = _.reduce(
          customerBit,
          (res, cur) => {
            return {
              ...res,
              [cur.name]: (data?.customer_common_bit_flag ?? []).includes(cur.name) ? 1 : 0,
            };
          },
          {},
        );
        const bitValue =
          tabKey === '3' || tabKey === '9' ? (tabKey === '3' ? appBitFlag : customerBitFlag) : {};
        setInitValus({ ...data, callbackNeedExt: needExt, ...bitValue });
      }
    },
    [tabKey, appBit, customerBit],
  );

  const getDetail = useCallback(async () => {
    if (!sdkappid) return;
    setLoading(true);
    try {
      const res = await getAppDetail({ sdkappid });
      console.log('res', res);
      // 打补丁转换配置
      res.data = transformRuleData(res.data);
      console.log('res.data', res.data);
      const opts: object[] = [];
      if (res?.code === 0) {
        pickNeedExt(res.data);
        setList(res?.data?.sign_list || []);
        _.each(res?.data?.sign_list ?? [], (item) => {
          opts.push({
            label: item.sign,
            value: item.sign,
          });
        });
        setSignOpts(opts);
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  }, [pickNeedExt, sdkappid]);

  useEffect(() => {
    if (!sdkappid) return;
    getDetail();
    getCmqQueue();
  }, [getCmqQueue, getDetail, sdkappid]);

  if (!sdkappid) {
    return <Navigate to="/sms/sdkappid-query" />;
  }

  const onSetCmq = async (vals: any) => {
    const res = await setAppCmq({ ...vals, sdkappid });
    res?.code === 0 && message.success('设置cmp消息队列信息成功');
  };

  async function onSubmit() {
    const formValues = _.reduce(
      formRef.current,
      (result, item: any) => {
        const v = item?.form?.getFieldsValue() ?? {};
        return { ...result, ...v };
      },
      {},
    );
    console.log('formValues', transformRuleResult(formValues));

    const result: any = checkAvailableItem(initValus, formValues);

    const needExtAr = [
      'callback_need_ext_bit_voice_ext',
      'callback_need_ext_bit_sms_fee',
      'callback_need_ext_bit_sms_ext',
    ];
    if (result.callbackNeedExt) {
      _.each(needExtAr, () => {
        result.callback_need_ext_bit_voice_ext =
          result.callbackNeedExt.indexOf('callback_need_ext_bit_voice_ext') > -1 ? 1 : 0;
        result.callback_need_ext_bit_sms_fee =
          result.callbackNeedExt.indexOf('callback_need_ext_bit_sms_fee') > -1 ? 1 : 0;
        result.callback_need_ext_bit_sms_ext =
          result.callbackNeedExt.indexOf('callback_need_ext_bit_sms_ext') > -1 ? 1 : 0;
      });
      delete result.callbackNeedExt;
    }
    if (_.isEmpty(result)) {
      return message.error('未修改任何字段');
    }
    const appBitFlag: string[] = [];
    const customerBitFlag: string[] = [];
    const _bitList = tabKey === '3' ? appBit : customerBit;

    _bitList.forEach((el: any) => {
      if (tabKey === '3' && formRef.current['3']?.form.getFieldValue(el.name) === 1) {
        appBitFlag.push(el.name);
      }
      if (tabKey === '9' && formRef.current['9']?.form.getFieldValue(el.name) === 1) {
        customerBitFlag.push(el.name);
      }
    });
    const res = await setAppDetail({
      ..._.omit(
        result,
        bitList.map((el: any) => el.bit_index),
      ),
      sdkappid,
      app_common_bit_flag: tabKey === '3' ? appBitFlag : undefined,
      customer_common_bit_flag: tabKey === '9' ? customerBitFlag : undefined,
      // 频率限制
      frq_count_rule: tabKey === '4' ? transformRuleResult(formValues) : undefined,
    });
    if (res?.code === 0) {
      message.success('修改成功');
      getDetail();
    }
    setLoading(false);
  }

  async function delCmqQueue() {
    const res = await delAppCmq({ sdkappid });
    res?.code === 0 && getCmqQueue();
  }

  return (
    <PageContainer title={false}>
      <Card title="修改短信应用信息">
        <Tabs defaultValue="1" onChange={setTabKey}>
          {configTabs.map((tab) => (
            <Tabs.TabPane tab={tab.title} key={tab.key}>
              <DetailForm
                ref={(v) => {
                  if (v && formRef.current) formRef.current[tab.key] = v;
                }}
                formSetting={tab.setting}
                isLoading={isLoading}
                onSubmit={onSubmit}
                initValus={{ ...initValus }}
              />
            </Tabs.TabPane>
          ))}
        </Tabs>
      </Card>
      <Card style={{ marginTop: 15 }}>
        需要cmq消息队列推送:
        <Select
          style={{ marginLeft: 15, width: 200, marginBottom: 10 }}
          value={cmqValue}
          onChange={(val) => {
            setCmqValue(val);
          }}
        >
          <Select.Option value="0">否</Select.Option>
          <Select.Option value="1">是</Select.Option>
        </Select>
        {cmqValue === '0' && (
          <Button
            style={{ marginLeft: 5 }}
            type="primary"
            onClick={() => {
              delCmqQueue();
            }}
          >
            set
          </Button>
        )}
        {cmqValue === '1' && (
          <>
            <div style={{ borderTop: '1px solid #eee', marginBottom: 10 }} />
            <DetailForm
              initValus={cmqInfo}
              formSetting={cmqFormSetting}
              onSubmit={(vals: any) => {
                onSetCmq(vals);
              }}
            />
          </>
        )}
      </Card>
      {/* 应用子码通道组权重 */}
      {isChina && <AppGroupWeight sdkappid={sdkappid} />}
      {/* 签名子码通道组权重 */}
      {isChina && <DirectGroupWeight sdkappid={sdkappid} />}
      {/* app权重配置 */}
      <AppWeight sdkappid={sdkappid} />
      {/* 补发通道组配置 */}
      {isChina && <ReissueConfig sdkappid={sdkappid} />}
      <Card
        title="签名"
        style={{ marginTop: 15 }}
        extra={
          <CopyToClipboard
            text={_.map(list, (v) => v.sign).join('\n')}
            onCopy={() => {
              message.success('Copy successfully');
            }}
          >
            <Button
              type="link"
              style={{ width: '16px', height: '16px', marginRight: 15, verticalAlign: 'top' }}
            >
              复制
            </Button>
          </CopyToClipboard>
        }
      >
        <Table
          columns={[
            {
              title: 'sign',
              dataIndex: 'sign',
              key: 'sign',
              align: 'center',
            },
          ]}
          rowKey="sign"
          dataSource={list}
          loading={isLoading}
          pagination={{
            defaultCurrent: 1,
            total: list?.length || 0,
            showSizeChanger: true,
          }}
          style={{ marginTop: 20 }}
        />
      </Card>
      {/* 全局脏字白名单 */}
      <AppWhite sdkappid={sdkappid} />
      {/* app脏字黑名单 */}
      <AppBlack sdkappid={sdkappid} />
      {/* 告警联系人 */}
      <AppWarning sdkappid={sdkappid} />
      {/* 物联卡运营商账号信息 */}
      <IotsmsOperator sdkappid={sdkappid} />
      <FloatButton.BackTop />
    </PageContainer>
  );
};

export default SmsSdkappidDetail;
