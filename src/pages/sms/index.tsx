import React from 'react';
import { getAllowRoutes } from '@/components/GlobalHeader/RightContent';
import type { ConnectState } from '@/models/connect';
import { connect, history } from 'umi';
import { Button } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import { useCss } from 'react-use';

interface RouteProp {
  value: string;
  label: string;
  children?: RouteProp[];
}

const oldNameMap: Record<string, string> = {
  '/sms/sdkappid-query': '业务管理',
};

const Index = (props: { authRoutes: any }) => {
  const { authRoutes = [] } = props;
  const allRoutes = getAllowRoutes(authRoutes);

  const className = useCss({
    '.ant-pro-page-container-children-container': {
      paddingLeft: '24px',
      paddingTop: '8px',
    },
  });

  return (
    <PageContainer title={false} breadcrumbRender={false} className={className}>
      <div style={{ padding: '-24px' }}>
        {allRoutes.map((o: RouteProp) => {
          if (o.value === '/index' || o.value === '/') return null;
          return (
            <div key={o.value}>
              {o.label ? `${o.label}：` : null}
              {(o.children?.length ? o.children : [o]).map((item) => {
                if (!item.label) return null;
                return (
                  <span key={item.value}>
                    <Button
                      type="link"
                      onClick={() => {
                        history.push(item.value);
                      }}
                    >
                      {item.label}
                      {oldNameMap[item.value] ? `(${oldNameMap[item.value]})` : null}
                    </Button>
                  </span>
                );
              })}
            </div>
          );
        })}
      </div>
    </PageContainer>
  );
};
export default connect(({ routes }: ConnectState) => ({
  authRoutes: routes.routes,
}))(Index);
