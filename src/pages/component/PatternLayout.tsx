/* eslint-disable no-nested-ternary */
import React, {
  useState,
  useEffect,
  useCallback,
  useRef,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  Table,
  Space,
  Modal,
  Button,
  Form,
  Input,
  message,
  Select,
  Popconfirm,
  InputNumber,
  Tag,
  Col,
  Row,
} from 'antd';
import type { PageContainerProps } from '@ant-design/pro-layout';
import { PageContainer } from '@ant-design/pro-layout';
import type { GetRowKey } from '../../../node_modules/rc-table/lib/interface';
import type { Rule } from 'rc-field-form/lib/interface';
import { SearchOutlined } from '@ant-design/icons';
import _ from 'lodash';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { useSetState } from 'react-use';
import { isMobile } from '@/const/jadgeUserAgent';
import type { FormInstance } from 'antd/es/form/Form';

interface PatternProps<T> {
  getFn: (params: any) => Promise<any>;
  addFn?: (params: any) => Promise<any>;
  editFn?: (params: any, changed?: any) => Promise<any>;
  delFn?: (params: any) => Promise<any>;
  breadcrumbRender?: PageContainerProps['breadcrumbRender'] | false;
  title?: React.ReactNode | false;
  rowKey?: string | GetRowKey<T>;
  columns: Record<string, unknown>[];
  searchKeys: Record<string, unknown>[];
  operateForm: SearchType[];
  operType: number; // 0 编辑 1 编辑+删除 2 删除
  initialValues?: Record<string, unknown>;
  upRender?: () => React.ReactNode;
  searchRender?: () => React.ReactNode;
  modalTipRender?: () => React.ReactNode;
  searchInitials?: Record<string, unknown>;
  hideShowAdd?: boolean;
  exportFile?: (params: any) => any;
  formProps?: any;
  tableProps?: any;
}

interface SearchType {
  label: string;
  name?: string;
  isRequired?: boolean;
  placeholder?: string;
  showOnAdd?: boolean;
  disabled?: boolean;
  render?: (form: FormInstance<any>) => React.ReactNode;
}

function checkAvailableItem(
  oldValue: { [key in string]: any },
  newValue: { [key in string]: any },
  operateForm: SearchType[],
) {
  const disableKeys = _.reduce(
    operateForm,
    (arr: string[], v) => {
      if (v.disabled && v.name) {
        arr.push(v.name);
      }
      return arr;
    },
    [],
  );
  const result = _.pickBy(newValue, (value, key) => {
    return !_.isEqual(value, oldValue[key]) || disableKeys.includes(key);
  });
  return result;
}

interface FormType extends SearchType {
  renderType?: string;
  options?: Record<string, unknown>[];
  extra?: '';
  hidden?: boolean;
  appendRules?: Rule[];
  editOptinal?: boolean;
}
export interface PatternTableRef {
  reload: () => void;
  form: FormInstance;
}
function getRules(item: any, operType: string) {
  if (item?.appendRules) {
    return [{ required: item.isRequired }, ...item.appendRules];
  }
  return [
    { required: operType === 'add' ? item.isRequired : item.editOptinal ? false : item.isRequired },
  ];
}

const PatternTable = forwardRef<PatternTableRef, any>(
  (
    {
      getFn,
      addFn,
      editFn,
      delFn,
      columns,
      rowKey,
      searchKeys,
      operateForm,
      operType,
      initialValues,
      upRender,
      searchRender,
      modalTipRender,
      breadcrumbRender,
      title,
      searchInitials,
      hideShowAdd,
      exportFile,
      formProps,
      tableProps,
    },
    ref: React.ForwardedRef<PatternTableRef>,
  ) => {
    const [form] = Form.useForm();
    const [searchForm] = Form.useForm();
    const [isVisible, setIsVisible] = useState<boolean>(false);
    const [type, setType] = useState<string>('add');
    const [isConfirmLoading, setConfirmLoading] = useState<boolean>(false);
    const [initValues, setInitValues] = useState({});
    const [searchVals, setSearchVals] = useSetState({
      ...searchInitials,
      page_index: 1,
      page_size: 10,
    });
    const callbackRef = useRef<PatternProps<any>['getFn']>();

    useEffect(() => {
      callbackRef.current = getFn;
    }, [getFn]);

    const {
      value: state,
      loading,
      retry,
    } = useAsyncRetryFunc(async () => {
      const vals = _.pickBy(searchVals, (v: any) => v !== '' && !_.isNil(v));
      const response = await callbackRef.current?.({ ...vals });

      return response?.data || {};
    }, [searchVals]);

    useImperativeHandle(ref, () => ({
      reload: retry,
      form,
    }));

    const list = useMemo(() => {
      return state?.list ?? [];
    }, [state]);

    const total = useMemo(() => {
      return state?.count ?? 0;
    }, [state]);

    async function onSubmit(vals: any) {
      try {
        setConfirmLoading(true);
        let res;
        if (type === 'add') {
          res = await addFn?.({ ...vals });
        } else {
          const newValues = checkAvailableItem(initValues, vals, operateForm);
          res = await editFn?.({ ...vals }, newValues);
        }
        setConfirmLoading(false);
        setIsVisible(false);
        if (res.code === 0) {
          message.success('操作成功');
          form.resetFields();
          retry();
        }
      } catch (error) {
        console.log(error);
        setConfirmLoading(false);
      }
    }

    // const onSubmit = useCallback(
    //   async (vals: any) => {
    //     try {
    //       setConfirmLoading(true);
    //       // const vals = checkAvailableItem(initValues, _vals, operateForm);
    //       let res;
    //       if (type === 'add') {
    //         res = await addFn?.({ ...vals });
    //       } else {
    //         res = await editFn?.({ ...vals });
    //       }
    //       setConfirmLoading(false);
    //       if (res.code === 0) {
    //         message.success('操作成功');
    //         setIsVisible(false);
    //         form.resetFields();
    //         retry();
    //       }
    //     } catch (error) {
    //       setConfirmLoading(false);
    //     }
    //   },
    //   [addFn, editFn, form, retry, type],
    // );

    const doEdit = useCallback(
      (row: Record<string, any>) => {
        setType('edit');
        setInitValues({ ...row });
        setIsVisible(true);
        form.setFieldsValue({ ...row });
      },
      [form],
    );

    function getInnerContent({
      item,
      operType,
      form,
    }: {
      item: Partial<FormType>;
      operType: string;
      form: FormInstance<any>;
    }) {
      const isRender = (operType === 'add' && item.showOnAdd) || operType !== 'add';
      let renderNode = (
        <Input
          placeholder={item.placeholder || item.name}
          style={{ width: 200, marginBottom: operType === 'search' ? 10 : 0 }}
          disabled={operType === 'edit' && item.disabled}
        />
      );
      if (!isRender) {
        return null;
      }
      if (item.render) {
        return (
          <Form.Item
            name={item.name}
            key={item.name}
            label={item.label}
            rules={getRules(item, operType)}
            extra={item.extra}
            hidden={item.hidden}
          >
            {item.render(form)}
          </Form.Item>
        );
      }
      if (item.renderType === 'select') {
        renderNode = (
          <Select
            style={{ width: 200 }}
            placeholder={item.placeholder || '请选择'}
            disabled={operType === 'edit' && item.disabled}
            options={item.options}
            showSearch
            allowClear
          />
        );
      }
      if (item.renderType === 'number') {
        renderNode = (
          <InputNumber placeholder={item.placeholder} style={{ width: 200 }} controls={false} />
        );
      }
      if (item.renderType === 'textArea') {
        renderNode = (
          <Input.TextArea autoSize placeholder={item.placeholder} style={{ width: 200 }} />
        );
      }
      return (
        <Form.Item
          name={item.name}
          key={item.name}
          label={item.label}
          rules={[
            {
              required:
                operType === 'add' ? item.isRequired : item.editOptinal ? false : item.isRequired,
            },
          ]}
          extra={item.extra}
          hidden={item.hidden}
        >
          {renderNode}
        </Form.Item>
      );
    }

    const doDel = useCallback(
      async (row: any) => {
        const res = await delFn?.({ ...row });
        if (res.code === 0) {
          message.success('删除成功');
          retry();
        }
      },
      [delFn, retry],
    );

    useEffect(() => {
      if (isVisible && type === 'add') {
        form.setFieldsValue(initialValues);
      }
    }, [form, initialValues, isVisible, type]);

    const newCol = useMemo(() => {
      if (operType === 3) return columns;
      const cols = _.cloneDeep(columns);
      cols.push({
        title: '操作',
        render: (row: any) => {
          if (operType === 0) {
            return (
              <a
                onClick={() => {
                  doEdit(row);
                }}
              >
                编辑
              </a>
            );
          }
          if (operType === 1) {
            if (row.deleted || row.delete) {
              return <Tag>已删除</Tag>;
            }
            return (
              <Space>
                <a
                  onClick={() => {
                    doEdit(row);
                  }}
                >
                  编辑
                </a>
                <Popconfirm
                  title="确认删除？"
                  onConfirm={() => doDel(row)}
                  okText="确定"
                  cancelText="取消"
                >
                  <a>删除</a>
                </Popconfirm>
              </Space>
            );
          }
          if (operType === 2) {
            return (
              <Popconfirm
                title="确认删除？"
                onConfirm={() => doDel(row)}
                okText="确定"
                cancelText="取消"
              >
                <a>删除</a>
              </Popconfirm>
            );
          }
          return '';
        },
        align: 'center',
      });
      return cols;
    }, [columns, doDel, doEdit, operType]);

    return (
      <>
        <PageContainer breadcrumbRender={breadcrumbRender} title={title}>
          <Row className="query-form" justify="space-between">
            <Col className="left-panel" style={{ marginRight: 10, marginBottom: 10 }}>
              {!hideShowAdd ? (
                <Button
                  type="primary"
                  style={{ marginRight: 5 }}
                  onClick={() => {
                    setType('add');
                    setInitValues({});
                    setIsVisible(true);
                  }}
                >
                  添加
                </Button>
              ) : null}
              {exportFile && (
                <Button
                  style={{ marginRight: 5 }}
                  type="primary"
                  onClick={() => {
                    exportFile(searchVals);
                  }}
                >
                  导出
                </Button>
              )}
              {upRender ? upRender() : null}
            </Col>
            <Col className="right-panel">
              <Form
                form={searchForm}
                layout="inline"
                requiredMark={false}
                labelAlign="right"
                initialValues={searchInitials}
                onFinish={(vals) => {
                  setSearchVals({ ...vals, page_index: 1 });
                }}
              >
                {searchKeys.map((item: Partial<SearchType>) => {
                  return getInnerContent({ item, operType: 'search', form: searchForm });
                })}
                {searchKeys.length ? (
                  <Form.Item>
                    <Button
                      htmlType="submit"
                      type="primary"
                      loading={loading}
                      icon={<SearchOutlined />}
                    >
                      查询
                    </Button>
                    {searchRender ? searchRender() : null}
                  </Form.Item>
                ) : null}
              </Form>
            </Col>
          </Row>

          <Table
            dataSource={list}
            columns={newCol}
            loading={loading}
            rowKey={rowKey}
            scroll={isMobile() ? { x: 'max-content' } : undefined}
            pagination={{
              current: searchVals.page_index,
              total,
              showSizeChanger: true,
              onShowSizeChange: (current, page_size) => setSearchVals({ page_size }),
              onChange: (page) => {
                setSearchVals({ page_index: page });
                tableProps?.onPageSizeChange?.(page);
              },
            }}
            style={{ marginTop: 20 }}
            {...tableProps}
          />
        </PageContainer>
        <Modal
          open={isVisible}
          confirmLoading={isConfirmLoading}
          // destroyOnClose
          onOk={() => form.submit()}
          onCancel={() => {
            setIsVisible(false);
            form.resetFields();
          }}
        >
          <Form
            form={form}
            labelAlign="right"
            onFinish={(vals) => onSubmit(vals)}
            initialValues={{ ...initValues }}
            style={{ maxHeight: 500, overflow: 'auto' }}
            {...formProps}
          >
            {modalTipRender?.()}
            {operateForm.map((item: Partial<FormType>) => {
              return getInnerContent({ item, operType: type, form });
            })}
          </Form>
        </Modal>
      </>
    );
  },
);
export default PatternTable;
