import React, { useMemo, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Select, message } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useSetState } from 'react-use';
import { history } from 'umi';
import { smsType, smsTypeValueMap } from '@/const/const';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import _ from 'lodash';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { getOperatorGroupResources } from '@/services/resources';
import { getMncOptions } from '../channel/component/utils';
import type { TableProps } from 'antd/es/table';
import { TAGS } from './const';
import { getResourceType } from '@/services/tacticResources';
import { getExportData, saveCSV } from '../global-components/saveCsv';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import ProviderCascader from '../global-components/ProviderCascader';

const OperatorGroupInfo = () => {
  const { regionOptions = [] } = useFetchCountryInfo();
  const [form] = Form.useForm();
  const initSearchKeys =
    (history.location.state as { searchKeys?: Record<string, any> })?.searchKeys ?? {};
  const [searchKeys, setSearchKeys] = useSetState<{
    page_index: number;
    page_size: number;
    sms_type: number[];
    sorts?: { order?: string; by?: string }[];
    country_codes?: string[];
    provider_ids?: number[];
  }>({
    page_index: 1,
    page_size: 10,
    sms_type: [],
    ...initSearchKeys,
  });
  const [exportLoading, setExportLoading] = useState(false);

  const { value: resourceType } = useAsyncRetryFunc(async () => {
    const res = await getResourceType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);
  const transSearchKeys = useMemo(() => {
    const { provider_ids = [], ...other } = searchKeys;
    const [supplier_name, account_id] = provider_ids;
    const searchIds = {
      supplier_name,
      ...(provider_ids.length > 1 && { account_id }),
    };
    let sms_type;
    if (searchKeys.sms_type?.length) {
      sms_type = searchKeys.sms_type?.reduce((pre: number, cur: number) => {
        return pre + cur;
      }, 0);
    }
    return {
      ...searchIds,
      ...other,
      sms_type,
      country_codes: searchKeys.country_codes
        ? _.union(_.flattenDeep(_.map(searchKeys.country_codes, (v) => v.split(','))))
        : undefined,
    };
  }, [searchKeys]);

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    const result = await getOperatorGroupResources(transSearchKeys);
    return result?.data ?? {};
  }, [transSearchKeys]);

  const [mccMncInfo] = useMccMncInfo();

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state?.list]);

  const columns: any[] = useMemo(() => {
    return [
      {
        title: '国家地区',
        key: 'country_name',
        sorter: true,
        render: (row: any) =>
          regionOptions.find((el) => el.value === row.country_code)?.label ?? row.country_code,
      },
      {
        title: '国家地区码',
        dataIndex: 'country_code',
        key: 'country_code',
      },
      {
        title: '资源类型',
        dataIndex: 'resource_type',
        key: 'resource_type',
        render: (val: number) =>
          resourceType?.find((el: any) => el.value.toString() === val?.toString())?.label ??
          'testa',
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        key: 'sms_type',
        render: (val: number) =>
          smsTypeValueMap.find((el) => el.value.toString() === val?.toString())?.text,
      },
      {
        title: '运营商',
        // dataIndex: 'mnc',
        key: 'mnc',
        render: (row: any) =>
          mccMncInfo.find(
            (el) => el.mcc.toString() === row.mcc.toString() && el.mnc.toString() === row.mnc,
          )?.operator_name,
      },
      {
        title: '市占比',
        dataIndex: 'market_share',
        key: 'market_share',
      },
      {
        title: (
          <div>
            <span>可用资源</span>
            <div>(账号ID-供应商简称 通道名-当前成本-实时CR-近24小时下发总量)</div>
            <div style={{ color: '#aaa' }}>(cr为null表示无足够来自腾讯系内自用流量来监控CR)</div>
          </div>
        ),
        width: 500,
        key: 'useful-resource',
        exportRender: (row: any) => {
          const list = JSON.parse(row.resources ?? '[]');
          return list
            .sort((a: any, b: any) => a.price - b.price)
            .map(
              ({
                account_id,
                supplier_abbr_name,
                price,
                account_name,
                cr,
                total,
              }: {
                account_id: string;
                supplier_abbr_name: string;
                price: string;
                account_name: string;
                cr: number;
                total: number;
              }) => {
                {
                  // eslint-disable-next-line react/jsx-key
                  return `${account_id} - ${supplier_abbr_name} ${account_name} - ${Number(
                    price,
                  ).toFixed(5)} - ${cr === null ? 'null' : `${cr}%`} - ${total}\n`;
                }
              },
            );
        },
        render: (row: any) => {
          const list = JSON.parse(row.resources ?? '[]');
          return (
            <div>
              {list
                .sort((a: any, b: any) => a.price - b.price)
                .map(
                  ({
                    account_id,
                    supplier_abbr_name,
                    price,
                    account_name,
                    cr,
                    total,
                  }: {
                    account_id: string;
                    supplier_abbr_name: string;
                    price: string;
                    account_name: string;
                    cr: number;
                    total: number;
                  }) => {
                    return (
                      // eslint-disable-next-line react/jsx-key
                      <p>{`${account_id} - ${supplier_abbr_name} ${account_name} - ${Number(
                        price,
                      ).toFixed(5)} - ${cr === null ? 'null' : `${cr}%`} - ${total}`}</p>
                    );
                  },
                )}
            </div>
          );
        },
      },
      {
        title: '最低成本',
        dataIndex: 'lowest_price',
        key: 'lowest_price',
        render: (lowest_price: string) => Number(lowest_price).toFixed(5),
      },
      {
        title: '中位成本',
        dataIndex: 'median_price',
        key: 'median_price',
        render: (median_price: string) => Number(median_price).toFixed(5),
      },
      {
        title: '最高成本',
        dataIndex: 'highest_price',
        key: 'highest_price',
        render: (highest_price: string) => Number(highest_price).toFixed(5),
      },

      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        // width: 120,
      },
    ];
  }, [mccMncInfo, regionOptions, resourceType]);
  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  function onSubmit(vals: any) {
    setSearchKeys({
      ...vals,
      mnc: vals.mnc?.split('_')?.[1],
      mcc: vals.mnc?.split('_')?.[0],
      page_index: 1,
    });
  }

  const onTableChange: TableProps<{ country_code: string }>['onChange'] = (
    _pagination,
    _filters,
    sorter: Record<string, any>,
  ) => {
    setSearchKeys({
      sorts: sorter.order
        ? [{ by: sorter.field, order: sorter?.order === 'ascend' ? 'asc' : 'desc' }]
        : [],
    });
  };

  async function exportCsv({ route, params }: { route: string; params: Record<string, any> }) {
    setExportLoading(true);
    const result = await getOperatorGroupResources({
      ...transSearchKeys,
      sms_type: searchKeys.sms_type?.length ? _.sum(searchKeys.sms_type) : undefined,
      country_codes: searchKeys.country_codes
        ? _.union(_.flattenDeep(_.map(searchKeys.country_codes, (v) => v.split(','))))
        : undefined,
      page_size: 10000,
    });
    setExportLoading(false);
    try {
      const { head, data } = getExportData(
        columns.map((el) => {
          if (el.key === 'useful-resource') {
            return {
              ...el,
              title: '可用资源\n(账号ID-供应商简称 通道名-当前成本-实时CR-近24小时下发总量)',
            };
          }
          return el;
        }),
        result.data.list,
      );
      saveCSV('分网可用资源池', head, data, { route, params })
        .then(() => {
          message.success('导出成功');
        })
        .catch(() => {
          message.error('导出失败');
        });
    } catch (error) {
      message.error('导出失败');
    }
  }

  return (
    <PageContainer>
      <Form
        className="sender-search-form"
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
        initialValues={{ ...searchKeys }}
      >
        <Form.Item name="resource_type" label="资源类型">
          <Select placeholder="资源类型" options={resourceType} style={{ width: 150 }} allowClear />
        </Form.Item>
        <Form.Item name="country_codes" label="国家/地区码">
          <Select
            mode="multiple"
            placeholder="国家/地区码"
            options={_.union(TAGS, regionOptions)}
            value={form.getFieldValue('country_codes')}
            onChange={(value) => {
              form.setFieldsValue({ country_codes: value, mnc: undefined });
            }}
            style={{ minWidth: 150 }}
            allowClear
            showSearch
            filterOption={(inputValue, option) =>
              !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
            }
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            !_.isEqual(prevValues.country_codes, curValues.country_codes)
          }
        >
          {({ getFieldValue }) => {
            const _countryCodes = getFieldValue('country_codes');
            const countryCodes = _.union(_.flattenDeep(_.map(_countryCodes, (v) => v.split(','))));
            return (
              <Form.Item name="mnc" label="运营商">
                <Select
                  style={{ width: 150 }}
                  placeholder="mnc"
                  options={getMncOptions(
                    mccMncInfo,
                    countryCodes.length ? countryCodes : undefined,
                  )}
                  allowClear
                  showSearch
                  filterOption={(inputValue, option) => !!option?.label.includes(inputValue)}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="sms_type" label="短信类型">
          <Select
            allowClear
            placeholder="短信类型"
            options={smsType}
            mode="multiple"
            style={{ minWidth: 100 }}
          />
        </Form.Item>
        <Form.Item label="供应商" name="provider_ids">
          <ProviderCascader levelValueIsName={[true]} />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
          <Button
            style={{ marginLeft: 10 }}
            loading={exportLoading}
            onClick={() => {
              exportCsv({ route: '/resources/operator-group', params: transSearchKeys });
            }}
          >
            导出
          </Button>
        </Form.Item>
      </Form>
      <Table
        rowKey="group_id"
        columns={columns}
        // bordered
        dataSource={list}
        loading={loading}
        pagination={{
          current: searchKeys.page_index,
          pageSize: searchKeys.page_size,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
        onChange={onTableChange}
        scroll={{ x: 1200 }}
      />
    </PageContainer>
  );
};

export default OperatorGroupInfo;
