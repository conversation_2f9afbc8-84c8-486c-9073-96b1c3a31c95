import React, { useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Select } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useSetState } from 'react-use';
import { history } from 'umi';
import { smsType, smsTypeValueMap, strategyTypes } from '@/const/const';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import _ from 'lodash';
import { isMobile } from '@/const/jadgeUserAgent';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { getMncOptions } from '../channel/component/utils';
import { getTacticResources } from '@/services/resources';
import type { TableProps } from 'antd/es/table';
import { TAGS } from './const';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import ProviderCascader from '../global-components/ProviderCascader';

const TacticInfo = () => {
  const { regionOptions = [] } = useFetchCountryInfo();
  const [form] = Form.useForm();
  const initSearchKeys =
    (history.location.state as { searchKeys?: Record<string, any> })?.searchKeys ?? {};
  const [searchKeys, setSearchKeys] = useSetState<{
    page_index: number;
    page_size: number;
    sms_type: number[];
    sorts?: { order?: string; by?: string }[];
    country_codes?: string[];
    provider_ids?: number[];
  }>({
    page_index: 1,
    page_size: 10,
    sms_type: [],
    ...initSearchKeys,
  });

  const transSearchKeys = useMemo(() => {
    const { provider_ids = [], ...other } = searchKeys;
    const [supplier_name, account_id] = provider_ids;
    const searchIds = {
      supplier_name,
      ...(provider_ids.length > 1 && { account_id }),
    };
    let sms_type;
    if (searchKeys.sms_type?.length) {
      sms_type = searchKeys.sms_type?.reduce((pre: number, cur: number) => {
        return pre + cur;
      }, 0);
    }
    return {
      ...searchIds,
      ...other,
      sms_type,
      country_codes: searchKeys.country_codes
        ? _.union(_.flattenDeep(_.map(searchKeys.country_codes, (v) => v.split(','))))
        : undefined,
    };
  }, [searchKeys]);

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    const result = await getTacticResources(transSearchKeys);
    return result?.data ?? {};
  }, [transSearchKeys]);

  const [mccMncInfo] = useMccMncInfo();

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const columns: any[] = useMemo(() => {
    return [
      {
        title: '国家地区',
        dataIndex: 'country_code',
        key: 'country_code',
        sorter: true,
        align: 'center',
        render: (val: string) => regionOptions.find((el) => el.value === val)?.label,
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        key: 'sms_type',
        align: 'center',
        render: (val: number) =>
          smsTypeValueMap.find((el) => el.value.toString() === val?.toString())?.text,
      },
      {
        title: '标品类型',
        dataIndex: 'tactic_type',
        key: 'sms_type',
        align: 'center',
        render: (val: number) =>
          strategyTypes.find((el) => el.value.toString() === val?.toString())?.label,
      },
      {
        title: (
          <div>
            <span>通道配置</span>
            <div>(供应商-通道名-当前成本-CR值-通道占比-近24小时下发总量)</div>
            <div style={{ color: '#aaa' }}>(cr为null表示无足够来自腾讯系内自用流量来监控CR)</div>
          </div>
        ),
        width: 500,
        key: 'channel-setting',
        render: (row: any) => {
          const list = JSON.parse(row.resources ?? '[]');
          return (
            <div>
              {list
                .sort((a: any, b: any) => a.price - b.price)
                .map(
                  ({
                    supplier_name,
                    price,
                    account_name,
                    cr,
                    weight,
                    total,
                  }: {
                    supplier_name: string;
                    price: string;
                    account_name: string;
                    cr: number;
                    weight: number;
                    total: number;
                  }) => {
                    return (
                      // eslint-disable-next-line react/jsx-key
                      <p>{`${supplier_name} - ${account_name} - ${Number(price).toFixed(5)} - ${
                        cr === null ? 'null' : `${cr}%`
                      } - ${weight} - ${total}`}</p>
                    );
                  },
                )}
            </div>
          );
        },
      },
      {
        title: '综合成本',
        key: 'tactic_price',
        align: 'center',
        render: ({
          tactic_price_curr,
          tactic_price,
        }: {
          tactic_price: string;
          tactic_price_curr: string;
        }) => {
          const priceCurr = tactic_price_curr ? `(${tactic_price_curr})` : '';
          return `${Number(tactic_price).toFixed(5)}${priceCurr}`;
        },
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        align: 'center',
      },
    ];
  }, [regionOptions]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  function onSubmit(vals: any) {
    setSearchKeys({
      ...vals,
      mnc: vals.mnc?.split('_')?.[1],
      mcc: vals.mnc?.split('_')?.[0],
      page_index: 1,
    });
  }

  const onTableChange: TableProps<{ country_code: string }>['onChange'] = (
    _pagination,
    _filters,
    sorter: Record<string, any>,
  ) => {
    setSearchKeys({
      sorts: sorter.order
        ? [{ by: sorter.field, order: sorter?.order === 'ascend' ? 'asc' : 'desc' }]
        : [],
    });
  };

  return (
    <PageContainer>
      <Form
        className="sender-search-form"
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
        initialValues={{ ...searchKeys }}
      >
        <Form.Item name="tactic_type" label="标品类型">
          <Select placeholder="标品类型" options={strategyTypes} allowClear />
        </Form.Item>
        <Form.Item name="country_codes" label="国家/地区码">
          <Select
            placeholder="国家/地区码"
            mode="multiple"
            options={_.union(TAGS, regionOptions)}
            value={form.getFieldValue('country_codes')}
            onChange={(value) => {
              form.setFieldsValue({ country_codes: value, mnc: undefined });
            }}
            style={{ minWidth: 150 }}
            allowClear
            showSearch
            filterOption={(inputValue, option) =>
              !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
            }
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            !_.isEqual(prevValues.country_codes, curValues.country_codes)
          }
        >
          {({ getFieldValue }) => {
            const _countryCodes = getFieldValue('country_codes');
            const countryCodes = _.union(_.flattenDeep(_.map(_countryCodes, (v) => v.split(','))));
            return (
              <Form.Item name="mnc" label="运营商">
                <Select
                  style={{ width: 150 }}
                  placeholder="mnc"
                  options={getMncOptions(
                    mccMncInfo,
                    countryCodes.length ? countryCodes : undefined,
                  )}
                  allowClear
                  showSearch
                  filterOption={(inputValue, option) => !!option?.label.includes(inputValue)}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="sms_type" label="短信类型">
          <Select
            allowClear
            placeholder="短信类型"
            options={smsType}
            mode="multiple"
            style={{ minWidth: 100 }}
          />
        </Form.Item>
        <Form.Item label="供应商" name="provider_ids">
          <ProviderCascader levelValueIsName={[true]} />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        rowKey="tactic_id"
        columns={columns}
        dataSource={list}
        loading={loading}
        pagination={{
          current: searchKeys.page_index,
          pageSize: searchKeys.page_size,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
        onChange={onTableChange}
        scroll={isMobile() ? { x: 'max-content' } : { y: 'calc(100vh - 425px)' }}
      />
    </PageContainer>
  );
};

export default TacticInfo;
