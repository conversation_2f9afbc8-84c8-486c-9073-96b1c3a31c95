import React, { useMemo, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Select, message, List, Row, Col, Typography, Modal } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useSetState } from 'react-use';
import { history } from 'umi';
import { smsType, smsTypeValueMap } from '@/const/const';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import _ from 'lodash';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { getMncOptions } from '../channel/component/utils';
import { getOperatorTacticResources } from '@/services/resources';
import type { TableProps } from 'antd/es/table';
import { TAGS } from './const';
import { getProductType } from '@/services/tacticResources';
import { getExportData, saveCSV } from '../global-components/saveCsv';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import ProviderCascader from '../global-components/ProviderCascader';
import ViewSupplyConfig from '../channelRessiue/ViewSupplyConfig';
import { deleteRessiueConfiguration } from '@/services/ressiue';

const { Text } = Typography;

const OperatorTacticInfo = (props: { isSimple?: boolean; tacticId?: number }) => {
  const { isSimple = false, tacticId } = props;
  const { regionOptions = [] } = useFetchCountryInfo();

  const [form] = Form.useForm();
  const initSearchKeys = history.location.state?.searchVals ?? {};

  console.log(history.location.state);
  const [searchKeys, setSearchKeys] = useSetState<{
    page_index: number;
    page_size: number;
    sms_type: number[];
    sorts?: { order?: string; by?: string }[];
    country_codes?: string[];
    provider_ids?: number[];
  }>({
    page_index: 1,
    page_size: 10,
    sms_type: [],
    ...initSearchKeys,
  });
  const [exportLoading, setExportLoading] = useState(false);
  const [subTableLoading, setSubTableLoading] = useState(false);

  const { value: productType } = useAsyncRetryFunc(async () => {
    const res = await getProductType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);

  const transSearchKeys = useMemo(() => {
    const { provider_ids = [], ...other } = searchKeys;
    const [supplier_name, account_id] = provider_ids;

    const searchIds = {
      supplier_name,
      ...(provider_ids.length > 1 && { account_id }),
    };
    let sms_type;
    if (searchKeys.sms_type?.length) {
      sms_type = searchKeys.sms_type?.reduce((pre: number, cur: number) => {
        return pre + cur;
      }, 0);
    }
    return {
      ...searchIds,
      ...other,
      sms_type,
      country_codes: searchKeys.country_codes
        ? _.union(_.flattenDeep(_.map(searchKeys.country_codes, (v) => v.split(','))))
        : undefined,
    };
  }, [searchKeys]);

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    const result = await getOperatorTacticResources({ ...transSearchKeys, tactic_id: tacticId });
    return result?.data ?? {};
  }, [tacticId, transSearchKeys]);

  const [mccMncInfo] = useMccMncInfo();

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state?.list]);

  function viewSupplyCondition(row: any) {
    Modal.info({
      title: '补发场景',
      width: 1200,
      content: (
        <ViewSupplyConfig
          _smsType={row.sms_type}
          showChannel={false}
          from="dialog"
          row={row}
        ></ViewSupplyConfig>
      ),
    });
  }

  async function handleDeleteRessiue(row: any) {
    Modal.confirm({
      title: '删除补发配置',
      content: '确定删除该补发策略吗？删除后无法恢复，请确认。',
      onOk: async () => {
        try {
          const res = await deleteRessiueConfiguration({
            id: row.tactic_id,
            mcc: row.mcc,
            mnc: '000',
            type: 'product',
          });
          if (res.code === 0) {
            message.success('删除补发配置成功');
          }
        } catch (err) {
          message.error('删除补发配置失败');
          console.log(err);
        }
      },
    });
  }

  const columns: any[] = useMemo(() => {
    return [
      {
        title: '国家地区',
        key: 'country_name',
        width: 120,
        render: (row: any) =>
          regionOptions.find((el) => el.value === row.country_code)?.label ?? row.country_code,
      },
      {
        title: '国家地区码',
        dataIndex: 'country_code',
        key: 'country_code',
        sorter: true,
        width: 100,
      },
      {
        title: '标品类型',
        width: 100,
        dataIndex: 'product_type',
        key: 'product_type',
        render: (val: number) =>
          productType?.find((el: any) => el.value.toString() === val?.toString())?.label ?? '--',
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        width: 120,
        key: 'sms_type',
        render: (val: number) =>
          smsTypeValueMap.find((el) => el.value.toString() === val?.toString())?.text,
      },
      {
        title: '运营商-市占比',
        width: 220,
        key: 'mnc',
        render: (row: any) => {
          const resources = JSON.parse(row.resources ?? '{}');
          const data = _.map(resources, (v, k) => {
            return { mnc: k, ...v };
          });
          return (
            <List>
              {data.map((item) => {
                const name =
                  mccMncInfo.find(
                    (el) =>
                      el.country_code.toString() === row.country_code?.toString() &&
                      item.mnc?.toString() === el.mnc.toString(),
                  )?.operator_name ?? '--';
                return (
                  <List.Item key={item.mnc}>
                    <p
                      style={{ width: 220, marginBottom: 0 }}
                    >{`${name} - ${item.market_share}%`}</p>
                    <p style={{ width: 120, marginRight: 60, marginLeft: 20, marginBottom: 0 }}>
                      {typeof item.price === 'number'
                        ? `${item.price?.toFixed(5)}(${item.price_curr})`
                        : '--'}
                    </p>
                    <p style={{ width: 450, marginBottom: 0 }}>
                      {item.accounts?.length
                        ? item.accounts.map((account: any) => {
                            return (
                              // eslint-disable-next-line react/jsx-key
                              <p>{`${account.account_id} - ${
                                account.supplier_abbr_name ?? account.supplier_name
                              } ${account.account_name} - ${Number(account.price).toFixed(5)} - ${
                                account.cr === null ? 'null' : `${account.cr}%`
                              } - ${account.total} - ${account.weight}`}</p>
                            );
                          })
                        : '--'}
                    </p>
                  </List.Item>
                );
              })}
            </List>
          );
        },
        exportRender: (row: any) => {
          const name =
            mccMncInfo.find(
              (el) =>
                el.country_code.toString() === row.country_code?.toString() &&
                el.mnc?.toString() === row.mnc,
            )?.operator_name ?? '--';
          return `${name} - ${row.market_share}%`;
        },
        onCell: () => ({ colSpan: 3 }),
      },
      {
        title: '单网成本',
        // dataIndex: 'market_share',
        key: 'price',
        width: 100,
        render: (row: any) => `${row.price}(${row.price_curr})`,
        onCell: () => ({ colSpan: 0 }),
      },
      {
        title: (
          <div>
            <span>主通道</span>
            <div>(账号ID-供应商简称 通道名-当前成本-实时CR-近24小时下发总量-通道占比)</div>
            <div style={{ color: '#aaa' }}>(cr为null表示无足够来自腾讯系内自用流量来监控CR)</div>
          </div>
        ),
        key: 'useful-resource',
        width: 450,
        onCell: () => ({ colSpan: 0 }),
        exportRender: (row: any) => {
          const list = row.accounts ?? [];
          return list
            .sort((a: any, b: any) => a.price - b.price)
            .map(
              ({
                account_id,
                supplier_abbr_name,
                supplier_name,
                price,
                account_name,
                cr,
                total,
                weight,
              }: {
                account_id: string;
                supplier_abbr_name: string;
                supplier_name: string;
                price: string;
                account_name: string;
                cr: number;
                scheduler_cr: number;
                total: number;
                weight: number;
              }) => {
                {
                  // eslint-disable-next-line react/jsx-key
                  return `${account_id} - ${
                    supplier_abbr_name ?? supplier_name
                  } ${account_name} - ${Number(price).toFixed(5)} - ${
                    cr === null ? 'null' : `${cr}%`
                  } - ${total} - ${weight}\n`;
                }
              },
            );
        },
      },
      {
        title: '综合成本',
        key: 'tactic_price',
        width: 120,
        render: ({
          tactic_price_curr,
          tactic_price,
        }: {
          tactic_price: string;
          tactic_price_curr: string;
        }) => {
          const priceCurr = tactic_price_curr ? `(${tactic_price_curr})` : '';
          return `${Number(tactic_price).toFixed(5)}${priceCurr}`;
        },
      },
      {
        title: '综合CR',
        width: 80,
        key: 'tactic_cr',
        dataIndex: 'tactic_cr',
      },
      {
        title: (
          <div>
            <span>补发通道</span>
            <div>(账号ID-供应商简称 通道名-当前成本-实时CR-近24小时下发总量-通道占比)</div>
            <div style={{ color: '#aaa' }}>(cr为null表示无足够来自腾讯系内自用流量来监控CR)</div>
          </div>
        ),
        key: 'reissue_resources',
        width: 600,
        render: (row: any) => {
          const resources = JSON.parse(row.reissue_resources ?? '{}');
          const data = _.map(resources, (v, k) => {
            return { mnc: k, ...v };
          });
          const listRender = (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <List style={{ width: 680 }}>
                {data.map((item) => {
                  const name =
                    mccMncInfo.find(
                      (el) =>
                        el.country_code.toString() === row.country_code?.toString() &&
                        item.mnc?.toString() === el.mnc.toString(),
                    )?.operator_name ?? '--';
                  return (
                    <List.Item key={item.mnc}>
                      <p style={{ width: 150, marginBottom: 0 }}>{`${name}`}</p>
                      <p style={{ width: 450, marginBottom: 0 }}>
                        {item.accounts?.length
                          ? item.accounts.map((account: any) => {
                              const isTactic = account?.channel_type.toString() === '2';
                              return (
                                // eslint-disable-next-line react/jsx-key
                                <p>
                                  {isTactic ? (
                                    <Text>{`${account.account_id} - ${
                                      account.account_name
                                    } - ${Number(account.price).toFixed(5)} - ${
                                      account.cr === null ? 'null' : `${account.cr}%`
                                    } - ${account.total} - ${account.weight}`}</Text>
                                  ) : (
                                    <Text>{`${account.account_id} - ${
                                      account.supplier_abbr_name ?? account.supplier_name
                                    } ${account.account_name} - ${Number(account.price).toFixed(
                                      5,
                                    )} - ${account.cr === null ? 'null' : `${account.cr}%`} - ${
                                      account.total
                                    } - ${account.weight}`}</Text>
                                  )}
                                  {isTactic && (
                                    <Button type="link" onClick={() => viewTactic(account)}>
                                      查看标品
                                    </Button>
                                  )}
                                </p>
                              );
                            })
                          : '--'}
                      </p>
                    </List.Item>
                  );
                })}
              </List>
              <div>
                <Button type="link" onClick={() => viewSupplyCondition(row)}>
                  查看补发条件
                </Button>
                <Button
                  type="link"
                  onClick={() =>
                    history.push('/resources/operator-tactic/ressiue', {
                      row,
                      searchVals: form.getFieldsValue(),
                    })
                  }
                >
                  配置补发
                </Button>
                <br />
                <Button type="link" onClick={() => handleDeleteRessiue(row)}>
                  删除补发
                </Button>
              </div>
            </div>
          );
          return listRender;
        },
        exportRender: (row: any) => {
          const name =
            mccMncInfo.find(
              (el) =>
                el.country_code.toString() === row.country_code?.toString() &&
                el.mnc?.toString() === row.mnc,
            )?.operator_name ?? '--';
          return `${name} - ${row.market_share}%`;
        },
      },
      {
        title: '更新时间',
        width: 140,
        dataIndex: 'updated_at',
        key: 'updated_at',
      },
    ];
  }, [mccMncInfo, productType, regionOptions]);

  const simpleColumns = useMemo(
    () =>
      columns.filter((el) =>
        ['country_name', 'sms_type', 'product_type', 'mnc', 'price', 'useful-resource'].includes(
          el.key,
        ),
      ),
    [columns],
  );

  const onTableChange: TableProps<{ country_code: string }>['onChange'] = (
    _pagination,
    _filters,
    sorter: Record<string, any>,
  ) => {
    setSearchKeys({
      sorts: sorter.order
        ? [{ by: sorter.field, order: sorter?.order === 'ascend' ? 'asc' : 'desc' }]
        : [],
    });
  };

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  function renderTable(dataSource: any[], total: number, _loading = loading, isSimple?: boolean) {
    return (
      <Table
        rowKey="tactic_id"
        columns={isSimple ? simpleColumns : columns}
        dataSource={dataSource}
        loading={_loading ?? loading}
        pagination={{
          current: searchKeys.page_index,
          pageSize: searchKeys.page_size,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
        onChange={onTableChange}
        scroll={isSimple ? undefined : { x: 2200 }}
      />
    );
  }
  async function viewTactic(row: any) {
    setSubTableLoading(true);
    const { data } = await getOperatorTacticResources({
      tactic_id: row.account_id,
    });

    setSubTableLoading(false);
    Modal.info({
      title: '标品详情',
      width: '68%',
      content: renderTable(data?.list ?? [], data?.total, subTableLoading, true),
    });
  }

  function onSubmit(vals: any) {
    setSearchKeys({
      ...vals,
      mnc: vals.mnc?.split('_')?.[1],
      mcc: vals.mnc?.split('_')?.[0],
      page_index: 1,
    });
  }

  async function exportCsv({ route, params }: { route: string; params: Record<string, any> }) {
    setExportLoading(true);
    const result = await getOperatorTacticResources({
      ...transSearchKeys,
      sms_type: searchKeys.sms_type?.length ? _.sum(searchKeys.sms_type) : undefined,
      country_codes: searchKeys.country_codes
        ? _.union(_.flattenDeep(_.map(searchKeys.country_codes, (v) => v.split(','))))
        : undefined,
      page_size: 10000,
    });
    setExportLoading(false);
    const exportData = _.flatten(
      _.map(result.data.list, (el) => {
        const resources = JSON.parse(el.resources ?? '{}');
        return _.map(resources, (v, k) => ({ ...el, mnc: k, ...v }));
      }),
    );
    try {
      const { head, data } = getExportData(
        columns.map((el) => {
          if (el.key === 'useful-resource') {
            return {
              ...el,
              title:
                '可用资源\n(账号ID-供应商简称 通道名-当前成本-实时CR-近24小时下发总量-通道占比)',
            };
          }
          return el;
        }),
        exportData,
      );
      saveCSV('分网标品组', head, data, { route, params })
        .then(() => {
          message.success('导出成功');
        })
        .catch(() => {
          message.error('导出失败');
        });
    } catch (error) {
      message.error('导出失败');
    }
  }

  return isSimple ? (
    renderTable(list, total, loading, true)
  ) : (
    <PageContainer>
      <Form
        className="sender-search-form"
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
        initialValues={{ ...searchKeys }}
      >
        <Form.Item name="product_type" label="标品类型">
          <Select placeholder="标品类型" options={productType} style={{ width: 180 }} allowClear />
        </Form.Item>
        <Form.Item name="country_codes" label="国家/地区码">
          <Select
            placeholder="国家/地区码"
            mode="multiple"
            options={_.union(TAGS, regionOptions)}
            value={form.getFieldValue('country_codes')}
            onChange={(value) => {
              form.setFieldsValue({ country_codes: value, mnc: undefined });
            }}
            style={{ minWidth: 150 }}
            allowClear
            showSearch
            filterOption={(inputValue, option) =>
              !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
            }
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            !_.isEqual(prevValues.country_codes, curValues.country_codes)
          }
        >
          {({ getFieldValue }) => {
            const _countryCodes = getFieldValue('country_codes');
            const countryCodes = _.union(_.flattenDeep(_.map(_countryCodes, (v) => v.split(','))));
            return (
              <Form.Item name="mnc" label="运营商">
                <Select
                  style={{ width: 150 }}
                  placeholder="mnc"
                  options={getMncOptions(
                    mccMncInfo,
                    countryCodes.length ? countryCodes : undefined,
                  )}
                  allowClear
                  showSearch
                  filterOption={(inputValue, option) => !!option?.label.includes(inputValue)}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="sms_type" label="短信类型">
          <Select
            allowClear
            placeholder="短信类型"
            options={smsType}
            mode="multiple"
            style={{ minWidth: 100 }}
          />
        </Form.Item>
        <Form.Item label="供应商" name="provider_ids">
          <ProviderCascader levelValueIsName={[true]} />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
          <Button
            style={{ marginLeft: 10 }}
            loading={exportLoading}
            onClick={() => {
              exportCsv({ route: '/resources/operator-tactic', params: transSearchKeys });
            }}
          >
            导出
          </Button>
        </Form.Item>
      </Form>
      {renderTable(list, total)}
    </PageContainer>
  );
};

export default OperatorTacticInfo;
