import React, { useMemo, useRef, useState } from 'react';
import {
  Button,
  Form,
  Input,
  Select,
  Table,
  DatePicker,
  Tooltip,
  Row,
  Col,
  Tag,
  message,
  Spin,
} from 'antd';
import _ from 'lodash';
import dayjs from 'dayjs';
import { PageContainer } from '@ant-design/pro-layout';
import { EditOutlined, SearchOutlined } from '@ant-design/icons';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { useSetState } from 'react-use';
import { getAccountList, getConnectionNum, setConnectionNum } from '@/services/smppAccount';
import { ChangeStatusDialog } from './components/ChangeStatusDialog';
import { GetAgentsInfoDialog } from './components/GetAgentsInfoDialog';
import { EditAccountDialog } from './components/EditAccountDialog';
import { isMobile } from '@/const/jadgeUserAgent';
import {
  ModalForm,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
} from '@ant-design/pro-components';
const { RangePicker } = DatePicker;

const tencentNodes = [
  { value: 1, label: '香港' },
  { value: 2, label: '新加坡' },
  { value: 3, label: '法兰克福' },
  { value: 9, label: 'ALL' },
  { value: 0, label: '测试' },
];

const protocolTypes = [
  { value: 0, label: 'SMPP' },
  { value: 1, label: 'SMPP_TLS' },
  { value: 2, label: 'HTTP' },
];

export const connTypes = [
  { value: 0, label: 'IP直连' },
  { value: 1, label: '域名访问' },
];

const boxes: any = [
  {
    title: 'SMPP密码',
    dataIndex: 'smpp_password',
    key: 'smpp_password',
    align: 'center',
    render: (row: any) => (
      <Input.Password style={{ width: 160 }} value={row.smpp_password} readOnly />
    ),
  },
  {
    title: '腾讯云网络节点',
    dataIndex: 'tencent_node',
    key: 'tencent_node',
    align: 'center',
    render: (row: { tencent_node: number }) =>
      _.find(tencentNodes, (v) => v.value === row.tencent_node)?.label || '',
  },
  {
    title: '供应商网络节点',
    dataIndex: 'supplier_node',
    key: 'supplier_node',
    align: 'center',
  },
  {
    title: '时区',
    dataIndex: 'timezone',
    key: 'timezone',
    align: 'center',
  },
  {
    title: '主用IP（主用端口）',
    dataIndex: 'smpp_main_ip',
    key: 'smpp_main_ip',
    align: 'center',
  },
  {
    title: '备用IP（备用端口）',
    dataIndex: 'smpp_spare_ip',
    key: 'smpp_spare_ip',
    align: 'center',
  },
  {
    title: 'smpp域名',
    dataIndex: 'smpp_domain',
    key: 'smpp_domain',
    align: 'center',
  },
  {
    title: '质量类型',
    dataIndex: 'quality_type',
    key: 'quality_type',
    align: 'center',
  },
  {
    title: '连接数限制',
    dataIndex: 'max_conn_num',
    key: 'max_conn_num',
    align: 'center',
  },
  {
    title: '协议类型',
    dataIndex: 'protocol_type',
    key: 'protocol_type',
    align: 'center',
    render: (row: { protocol_type: number }) =>
      _.find(protocolTypes, (v) => v.value === row.protocol_type)?.label || '',
  },
  {
    title: '优先选择连接方式',
    dataIndex: 'conn_type',
    key: 'conn_type',
    align: 'center',
    render: (row: { conn_type: number }) =>
      _.find(connTypes, (v) => v.value === row.conn_type)?.label || '',
  },
  {
    title: '版本',
    dataIndex: 'version',
    key: 'version',
    align: 'center',
  },
];

// 1待审核/2审核通过/3审核驳回/4测试中/5测试通过/6测试不通过/7已启用/8已停用
export const statusOptions = [
  { value: 1, label: '待审核' },
  { value: 2, label: '审核通过' },
  { value: 3, label: '审核驳回' },
  { value: 4, label: '测试中' },
  { value: 5, label: '测试通过' },
  { value: 6, label: '测试不通过' },
  { value: 7, label: '已启用' },
  { value: 8, label: '已停用' },
];

const GetStatusTag = function ({ status }: { status: number }) {
  switch (status) {
    case 1:
      return <Tag color="orange">待审核</Tag>;
    case 2:
      return <Tag color="cyan">审核通过</Tag>;
    case 3:
      return <Tag color="red">审核驳回</Tag>;
    case 4:
      return <Tag color="blue">测试中</Tag>;
    case 5:
      return <Tag color="lime">测试通过</Tag>;
    case 6:
      return <Tag color="magenta">测试不通过</Tag>;
    case 7:
      return <Tag color="green">已启用</Tag>;
    case 8:
      return <Tag color="default">已停用</Tag>;
    default:
      return <Tag>--</Tag>;
  }
};

const AccountManage = () => {
  const [form] = Form.useForm();
  const setConnectionForm = useRef<ProFormInstance>();
  const [searchKeys, setSearchKeys] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [selectedRowKeys, setSelectRowKeys] = useState<React.Key[]>([]);
  const [changeLoading, setChangeLoading] = useState(false);
  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const keys = _.pickBy(searchKeys, (v: number | string) => v !== '');
    const res = await getAccountList({ ...keys });
    return res?.data || {};
  }, [searchKeys]);

  const dialogRef = useDialogRef();
  const editDialogRef = useDialogRef();
  const agentDialogRef = useDialogRef();

  const packageList = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const _handlerSubmit = async (formValue: any) => {
    const _values = _.cloneDeep(_.omit(formValue, 'time'));
    if (formValue.time) {
      _values.start_time = dayjs(formValue.time[0]).format('YYYY-MM-DD HH:mm:ss');
      _values.end_time = dayjs(formValue.time[1]).format('YYYY-MM-DD HH:mm:ss');
    }
    if (formValue.account_ids) {
      _values.account_ids = _.compact(formValue.account_ids.split('\n'));
    }
    setSearchKeys({
      ..._values,
      page_index: 1,
    });
  };

  async function onFinish(values: any, { account_id }: any) {
    const { code } = await setConnectionNum({ account_id, ...values });
    if (code === 0) {
      message.success('设置成功');
      retry();
      return true;
    }
  }

  async function onTencentNodeChange({ account_id }: any) {
    setChangeLoading(true);
    const { code, data } = await getConnectionNum({
      account_id,
      tencent_node: setConnectionForm.current?.getFieldValue('tencent_node'),
    });
    setChangeLoading(false);
    if (code === 0) {
      setConnectionForm.current?.setFieldsValue({
        old_connection_num: data.connection_num,
      });
    }
  }

  return (
    <PageContainer>
      <Form
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => _handlerSubmit(vals)}
        style={{ maxHeight: 500, overflow: 'auto' }}
      >
        <Form.Item style={{ marginBottom: 5 }} name="account_ids" label="供应商账号id">
          <Input.TextArea placeholder="换行输入多个" autoSize />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="account_name" label="账号名称">
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="supplier_name" label="供应商名称">
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="smpp_account" label="smpp账号">
          <Input style={{ width: 200 }} />
        </Form.Item>
        {/* <Form.Item style={{ marginBottom: 5 }} name="smpp_main_ip" label="smpp主路由">
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="smpp_spare_ip" label="smpp备用路由">
          <Input style={{ width: 200 }} />
        </Form.Item> */}
        <Form.Item style={{ marginBottom: 5 }} name="tencent_node" label="腾讯云网络节点">
          <Select
            options={tencentNodes.filter((v) => [1, 2, 3, 9].includes(v.value))}
            style={{ width: 200 }}
            allowClear
            placeholder="请选择"
          />
        </Form.Item>
        {/* <Form.Item style={{ marginBottom: 5 }} name="tps" label="并发数">
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="max_conn_num" label="最大连接数">
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="protocol_type" label="协议类型">
          <Select options={protocolTypes} style={{ width: 200 }} allowClear />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="conn_type" label="优先选择连接方式">
          <Select options={connTypes} style={{ width: 200 }} allowClear />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="version" label="版本号">
          <Input style={{ width: 200 }} />
        </Form.Item> */}
        <Form.Item style={{ marginBottom: 5 }} name="status" label="状态">
          <Select options={statusOptions} style={{ width: 200 }} allowClear placeholder="请选择" />
        </Form.Item>
        <Form.Item style={{ marginBottom: 5 }} name="time" label="时间范围">
          <RangePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            placeholder={['开始时间', '结束时间']}
            presets={[
              { label: 'Today', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
              {
                label: 'Yesterday',
                value: [
                  dayjs().subtract(1, 'days').startOf('day'),
                  dayjs().subtract(1, 'days').endOf('day'),
                ],
              },
              {
                label: 'Week',
                value: [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
              },
              {
                label: 'Month',
                value: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
              },
            ]}
          />
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading} icon={<SearchOutlined />}>
            查询
          </Button>
        </Form.Item>
        <Form.Item>
          <Button
            type="primary"
            loading={loading}
            disabled={!selectedRowKeys.length}
            icon={<EditOutlined />}
            onClick={() => {
              dialogRef.current.open({
                account_ids: selectedRowKeys,
              });
            }}
          >
            批量更新状态
          </Button>
        </Form.Item>
      </Form>
      <Table
        size="middle"
        dataSource={packageList}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        columns={[
          {
            title: '账号ID',
            dataIndex: 'account_id',
            key: 'account_id',
            align: 'center',
          },
          {
            title: '账号名称',
            dataIndex: 'account_name',
            key: 'account_name',
            align: 'center',
          },
          {
            title: '供应商ID',
            dataIndex: 'supplier_id',
            key: 'supplier_id',
            align: 'center',
          },
          {
            title: '供应商名称',
            dataIndex: 'supplier_name',
            key: 'supplier_name',
            align: 'center',
          },
          {
            title: 'SMPP账号',
            dataIndex: 'smpp_account',
            key: 'smpp_account',
            align: 'center',
          },

          {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            align: 'center',
            render: (status: number) => <GetStatusTag status={status} />,
          },
          {
            title: '连接方式',
            dataIndex: 'conn_type',
            key: 'conn_type',
            align: 'center',
            render: (conn_type: number) =>
              _.find(connTypes, (v) => v.value === conn_type)?.label || '',
          },
          {
            title: '并发数',
            dataIndex: 'tps',
            key: 'tps',
            align: 'center',
          },
          {
            title: '创建时间',
            dataIndex: 'created_at',
            key: 'created_at',
            align: 'center',
          },
          {
            title: '更新时间',
            dataIndex: 'updated_at',
            key: 'updated_at',
            align: 'center',
          },
          {
            title: '备注',
            dataIndex: 'ext',
            key: 'ext',
            align: 'center',
            ellipsis: true,
            render: (ext) => {
              const _ext = JSON.parse(ext || '{}');
              const item: { msg?: string; rtx_name?: string } = _.last(_ext.apply_ext || []) || {};
              return (
                <Tooltip title={`${item.rtx_name}：${item.msg}`} placement="topLeft">
                  {item.msg}
                </Tooltip>
              );
            },
          },
          {
            title: '操作',
            align: 'center',
            key: 'operation',
            render: (row: any) => {
              return (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      dialogRef.current.open({
                        account_ids: [row.account_id],
                      });
                    }}
                  >
                    更新状态
                  </Button>
                  <Button
                    type="link"
                    onClick={() => {
                      editDialogRef.current.open({
                        row: { ...row },
                      });
                    }}
                  >
                    编辑
                  </Button>
                  <Button
                    type="link"
                    onClick={() => {
                      agentDialogRef.current.open({
                        account_id: row.account_id,
                      });
                    }}
                  >
                    查看agents信息
                  </Button>
                  <ModalForm
                    title="设置agent连接数"
                    trigger={<Button type="link">设置agent连接数</Button>}
                    formRef={setConnectionForm}
                    layout="horizontal"
                    labelCol={{ span: 7 }}
                    width={500}
                    modalProps={{
                      destroyOnClose: true,
                      maskClosable: false,
                    }}
                    onFinish={(values) => onFinish(values, row)}
                  >
                    <Form.Item label="账号id">{row.account_id}</Form.Item>
                    <ProFormSelect
                      options={tencentNodes.filter((v) => [1, 2, 3, 0].includes(v.value))}
                      name="tencent_node"
                      label="腾讯云网络节点"
                      rules={[{ required: true }]}
                      onChange={() => {
                        onTencentNodeChange({ account_id: row.account_id });
                      }}
                    />
                    <Form.Item
                      name="old_connection_num"
                      label="旧连接数"
                      rules={[{ required: true }]}
                    >
                      {changeLoading ? <Spin /> : <Input disabled />}
                    </Form.Item>
                    <ProFormDigit
                      name="connection_num"
                      label="新连接数"
                      placeholder="请输入"
                      rules={[{ required: true }]}
                    />
                  </ModalForm>
                </>
              );
            },
          },
        ]}
        expandable={{
          expandedRowRender: (record: Record<string, any>) => (
            <div style={{ margin: '10px 20px' }}>
              <Row gutter={6} align="middle">
                {boxes.map((item: { title: string; render?: (params: any) => any; key: any }) => {
                  return (
                    <Col span={8} key={item.key} style={{ marginBottom: 10 }}>
                      {`${item.title}：`}
                      {item?.render ? item?.render(record) : record[item.key]}
                    </Col>
                  );
                })}
              </Row>
            </div>
          ),
        }}
        loading={loading}
        rowKey="account_id"
        pagination={{
          current: searchKeys.page_index,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSelectRowKeys([]);
            setSearchKeys({ page_index: page });
          },
        }}
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys,
          onChange: (selectedRowKeys: React.Key[]) => {
            setSelectRowKeys(selectedRowKeys);
          },
        }}
        style={{ marginTop: 20 }}
      />
      <ChangeStatusDialog
        dialogRef={dialogRef}
        onSuccess={() => {
          setSelectRowKeys([]);
          retry();
        }}
      />
      <EditAccountDialog
        dialogRef={editDialogRef}
        onSuccess={() => {
          setSelectRowKeys([]);
          retry();
        }}
      />
      <GetAgentsInfoDialog dialogRef={agentDialogRef} />
    </PageContainer>
  );
};

export default AccountManage;
