import React, { useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, Input, message, Select } from 'antd';
import { statusOptions } from '../accountManage';
import { updateAccountStatus } from '@/services/smppAccount';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export const ChangeStatusDialog = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [form] = Form.useForm();
  const [visible, setShowState, defaultVal] = useDialog<{
    account_ids: number[];
  }>(dialogRef);
  const { account_ids = [] } = defaultVal;
  const [isLoading, setLoading] = useState<boolean>(false);

  const _handlerSubmit = async (vals: any) => {
    try {
      setLoading(true);
      const res = await updateAccountStatus({ ...vals, account_ids });
      if (res?.code === 0) {
        message.success('编辑成功');
        onSuccess();
      }
      setShowState(false);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setShowState(false);
    }
  };

  return (
    <Modal
      open={visible}
      confirmLoading={isLoading}
      onOk={() => form.submit()}
      onCancel={() => {
        setShowState(false);
        form.resetFields();
      }}
    >
      <Form
        form={form}
        labelAlign="right"
        onFinish={(vals) => _handlerSubmit(vals)}
        style={{ maxHeight: 500, overflow: 'auto', marginTop: 10 }}
      >
        <Form.Item label="账号ID">
          <div>{account_ids.join(',')}</div>
        </Form.Item>
        <Form.Item name="status" label="状态" rules={[{ required: true }]}>
          <Select options={statusOptions} placeholder="请选择" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="msg" label="备注" rules={[{ required: true }]}>
          <Input.TextArea placeholder="请输入" style={{ width: 200 }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
