import React, { useEffect, useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, Table, Tabs } from 'antd';
import { getAccountAgents } from '@/services/smppAccount';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';

interface DialogProps {
  dialogRef: DialogRef;
}

const tabs = [
  {
    id: 'hk',
    key: 'hk_agents',
    title: '香港节点',
  },
  {
    id: 'sg',
    key: 'sg_agents',
    title: '新加坡节点',
  },
  {
    id: 'gm',
    key: 'gm_agents',
    title: '法兰克福节点',
  },
  {
    id: 'test',
    key: 'test_agents',
    title: '测试节点',
  },
];

export const GetAgentsInfoDialog = (props: DialogProps) => {
  const { dialogRef } = props;
  const [form] = Form.useForm();
  const [activeKey, setActiveKey] = useState('hk_agents');
  const [visible, setShowState, defaultVal] = useDialog<{
    account_id: number;
  }>(dialogRef);
  const { account_id } = defaultVal;
  const { value: state, loading } = useAsyncRetryFunc(async () => {
    if (!account_id) return;
    const res = await getAccountAgents({ account_id });
    return res?.data || {};
  }, [account_id]);

  useEffect(() => {
    visible && setActiveKey('hk_agents');
  }, [visible]);

  return (
    <Modal
      open={visible}
      confirmLoading={loading}
      onOk={() => form.submit()}
      onCancel={() => {
        setShowState(false);
        form.resetFields();
      }}
      footer={null}
    >
      <Tabs
        activeKey={activeKey}
        onChange={(activeKey) => {
          setActiveKey(activeKey);
        }}
      >
        {tabs.map((tab) => {
          return (
            <Tabs.TabPane tab={tab.title} key={tab.key}>
              <Table
                loading={loading}
                size="small"
                rowKey="task_id"
                dataSource={loading ? [] : state?.[tab.key]}
                columns={[
                  {
                    title: '任务ID',
                    dataIndex: 'task_id',
                    key: 'task_id',
                    align: 'center',
                  },
                  {
                    title: '供应商账号ID',
                    dataIndex: 'provider_id',
                    key: 'provider_id',
                    align: 'center',
                  },
                  {
                    title: '成功连接数量',
                    dataIndex: 'success_connection_num',
                    key: 'success_connection_num',
                    align: 'center',
                  },
                  {
                    title: '错误连接数量',
                    dataIndex: 'error_connection_num',
                    key: 'error_connection_num',
                    align: 'center',
                  },
                  {
                    title: '描述信息',
                    dataIndex: 'msg',
                    key: 'msg',
                    align: 'center',
                  },
                ]}
              />
            </Tabs.TabPane>
          );
        })}
      </Tabs>
    </Modal>
  );
};
