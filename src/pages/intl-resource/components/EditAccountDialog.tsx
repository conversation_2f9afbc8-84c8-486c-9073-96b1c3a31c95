import React, { useEffect, useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, message, Select, Input } from 'antd';
import { editAccount } from '@/services/smppAccount';
import { connTypes } from '../accountManage';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

const typeOptions = [
  { value: 'Direct' },
  { value: 'HQ' },
  { value: 'MHQ' },
  { value: 'MLQ' },
  { value: 'LQ' },
  { value: 'SIM' },
  { value: 'TTS' },
  { value: 'Local2Local' },
  { value: 'Local2Intl' },
  { value: 'OTT' },
  { value: 'MO' },
];

export const EditAccountDialog = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [form] = Form.useForm();
  const [visible, setShowState, defaultVal] = useDialog<{
    row: any;
  }>(dialogRef);
  const { row } = defaultVal;
  const [isLoading, setLoading] = useState<boolean>(false);

  const _handlerSubmit = async (vals: any) => {
    try {
      setLoading(true);
      const res = await editAccount({ ...vals, account_id: row?.account_id });
      if (res?.code === 0) {
        message.success('编辑成功');
        onSuccess();
      }
      setShowState(false);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setShowState(false);
    }
  };

  useEffect(() => {
    form.setFieldsValue({
      quality_type: row?.quality_type,
      smpp_main_ip: row?.smpp_main_ip,
      smpp_spare_ip: row?.smpp_spare_ip,
      smpp_domain: row?.smpp_domain,
      conn_type: row?.conn_type,
      tps: row?.tps,
    });
    return () => {
      form.resetFields();
    };
  }, [row]);

  return (
    <Modal
      open={visible}
      confirmLoading={isLoading}
      onOk={() => form.submit()}
      onCancel={() => {
        setShowState(false);
        form.resetFields();
      }}
      destroyOnClose
    >
      <Form
        form={form}
        labelAlign="right"
        onFinish={(vals) => _handlerSubmit(vals)}
        style={{ maxHeight: 500, overflow: 'auto', marginTop: 10 }}
        labelCol={{ span: 5 }}
      >
        <Form.Item label="账号ID">
          <div>{row?.account_id}</div>
        </Form.Item>
        <Form.Item name="quality_type" label="质量类型">
          <Select options={typeOptions} placeholder="请选择" style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="smpp_main_ip" label="主路由">
          <Input placeholder="请输入" style={{ width: 300 }}></Input>
        </Form.Item>
        <Form.Item name="smpp_spare_ip" label="备用路由">
          <Input placeholder="请输入" style={{ width: 300 }}></Input>
        </Form.Item>
        <Form.Item name="smpp_domain" label="域名">
          <Input placeholder="请输入" style={{ width: 300 }}></Input>
        </Form.Item>
        <Form.Item name="conn_type" label="连接方式">
          <Select options={connTypes} placeholder="请选择" style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="tps" label="并发数">
          <Input placeholder="请输入" style={{ width: 300 }}></Input>
        </Form.Item>
      </Form>
    </Modal>
  );
};
