import React, { useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, Select, Modal, DatePicker } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useSetState } from 'react-use';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import _ from 'lodash';
import { isMobile } from '@/const/jadgeUserAgent';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { getMncOptions } from '../channel/component/utils';
import { getPriceAttributeChangeLog } from '@/services/priceAttributeLog';
import { getAccountList } from '@/services/smppAccount';
import type { ColumnGroupType, ColumnType } from 'antd/lib/table';
import { findText } from '@/utils/utils';
import { smstype, crOptions, channeltype, routeTypes, yesOrNo } from './const';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const PriceAttributeLog = () => {
  const { regionOptions = [] } = useFetchCountryInfo();

  const [form] = Form.useForm();
  const [searchKeys, setSearchKeys] = useSetState<{
    page_index: number;
    page_size: number;
    [key: string]: any;
  }>({
    page_index: 1,
    page_size: 10,
  });

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    const result = await getPriceAttributeChangeLog({
      ...searchKeys,
      ddate: searchKeys?.ddate?.format('YYYYMMDD'),
    });
    return result?.data ?? {};
  }, [searchKeys]);

  const [mccMncInfo] = useMccMncInfo();

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const { value: accountList } = useAsyncRetryFunc(async () => {
    const res = await getAccountList({ page_size: 2000, page_index: 1 });
    return res.data.list.map((el: any) => ({
      value: el.account_id,
      label: `${el.account_name}(${el.account_id})`,
    }));
  }, []);

  function getColumns(diffKeys: string[]): (ColumnGroupType<any> | ColumnType<any>)[] {
    const diffColumns = [
      {
        key: 'price_id',
        dataIndex: 'price_id',
        title: '报价id',
        render: (v, r, i) => {
          return `${v}(${i === 0 ? '新' : '旧'})`;
        },
      },
      {
        key: 'sms_type',
        title: '短信类型',
        render: (row: any) => row.sms_type.map((v) => findText(smstype, v))?.join('/'),
      },
      {
        key: 'route_type',
        title: '路由类型',
        render: (row: any) =>
          _.filter(row.route_type.map((v) => findText(routeTypes, v)) ?? [], (v) => v !== '').join(
            '/',
          ),
      },
      {
        key: 'channel_type',
        title: '通道质量',
        render: (row: any) => findText(channeltype, row.channel_type),
      },
      {
        key: 'CR',
        title: 'CR',
        render: (row: any) => findText(crOptions, row.CR),
      },
      {
        key: 'has_test_resource',
        title: '是否有测试资源',
        render: (row: any) => findText(yesOrNo, row.has_test_resource),
      },
      {
        key: 'change_content',
        title: '短信内容是否改写',
        render: (row: any) => findText(yesOrNo, row.change_content),
      },
      {
        key: 'change_voice_msg',
        title: '是否转换为语音消息',
        render: (row: any) => findText(yesOrNo, row.change_voice_msg),
      },
      {
        key: 'include_sim_farm',
        title: '是否包含卡发',
        render: (row: any) => findText(yesOrNo, row.include_sim_farm),
      },
      {
        key: 'send_by_wa',
        title: '是否通过WA，viber等发送',
        render: (row: any) => findText(yesOrNo, row.send_by_wa),
      },
      {
        key: 'has_month_min_amount',
        title: '是否有保底量',
        render: (row: any) => <span>{row.month_min_amount === 0 ? t('否') : t('是')}</span>,
      },
      {
        key: 'month_min_amount',
        dataIndex: 'month_min_amount',
        title: '保底量（条/月）',
      },
      {
        key: 'has_month_max_amount',
        title: '是否有发送数量限制',
        render: (row: any) => <span>{row.month_max_amount === 0 ? t('否') : t('是')}</span>,
      },
      {
        key: 'month_max_amount',
        dataIndex: 'month_max_amount',
        title: '下发数量限制（条/月）',
      },
    ];
    return diffColumns.filter((el) => diffKeys.includes(el.key)) as any;
  }

  function openModal(row: any) {
    const attribute_diff = JSON.parse(row.attribute_diff ?? {});
    const attributeDiffData = _.reduce(
      attribute_diff,
      (result: any[], value, key) => {
        result[0][key] = value.new;
        result[1][key] = value.old;
        return result;
      },
      [{ price_id: row.new_price_id }, { price_id: row.old_price_id }],
    );
    const diffKeys = Object.keys(attribute_diff);
    return Modal.info({
      title: '差异对比',
      width: '50%',
      icon: false,
      closable: true,
      content: (
        <Table
          dataSource={diffKeys.length ? attributeDiffData : []}
          rowKey={'price_id'}
          scroll={{ x: 'max-content' }}
          pagination={false}
          columns={diffKeys.length ? getColumns([...diffKeys, 'price_id']) : []}
        />
      ),
    });
  }

  const columns: any[] = useMemo(() => {
    return [
      {
        title: '新报价id',
        dataIndex: 'new_price_id',
        key: 'new_price_id',
        align: 'center',
      },
      {
        title: '旧报价id',
        dataIndex: 'old_price_id',
        key: 'old_price_id',
        align: 'center',
      },
      {
        title: '账号名称',
        dataIndex: 'channel_name',
        key: 'channel_name',
        align: 'center',
      },
      {
        title: '供应商名称',
        // dataIndex: 'supplier_name',
        key: 'supplier_name',
        align: 'center',
        render: (row: any) => `${row.supplier_name}(${row.account_id})`,
      },
      {
        title: '国家',
        dataIndex: 'country_code',
        key: 'country_code',
        align: 'center',
        render: (val: string) => regionOptions.find((el) => el.value === val)?.label,
      },
      {
        title: '运营商',
        // dataIndex: 'mnc',
        key: 'mnc',
        align: 'center',
        render: (row: any) => {
          const name =
            mccMncInfo?.find((el) => el.mcc === row.mcc && el.mnc === row.mnc)?.operator_name ?? '';
          return `${name}(${row.mnc})`;
        },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        align: 'center',
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        align: 'center',
      },
      {
        title: '差异属性',
        // dataIndex: 'attribute_diff',
        key: 'attribute_diff',
        align: 'center',
        render: (row: string) => (
          <Button type="link" onClick={() => openModal(row)}>
            点击查看
          </Button>
        ),
      },
    ];
  }, [mccMncInfo, regionOptions]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  function onSubmit(vals: any) {
    setSearchKeys({
      ...vals,
      page_index: 1,
    });
  }

  return (
    <PageContainer>
      <Form
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
        initialValues={{ ...searchKeys }}
      >
        <Form.Item name="account_id" label="供应商账号id" style={{ marginBottom: 10 }}>
          <Select
            options={accountList}
            showSearch
            allowClear
            style={{ width: 200 }}
            placeholder="请选择"
            filterOption={(inputValue, option) =>
              !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
            }
          />
        </Form.Item>
        <Form.Item name="country_codes" label="国家/地区码">
          <Select
            placeholder="国家/地区码"
            mode="multiple"
            options={regionOptions}
            value={form.getFieldValue('country_code')}
            onChange={(value) => {
              form.setFieldsValue({ country_code: value, mnc: undefined });
            }}
            style={{ minWidth: 150 }}
            allowClear
            showSearch
            filterOption={(inputValue, option) =>
              !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
            }
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.country_code !== curValues.country_code
          }
        >
          {({ getFieldValue }) => {
            const countryCode = getFieldValue('country_code');
            return (
              <Form.Item name="mnc" label="运营商">
                <Select
                  style={{ width: 150 }}
                  placeholder="mnc"
                  options={getMncOptions(mccMncInfo, countryCode)}
                  allowClear
                  showSearch
                  filterOption={(inputValue, option) => !!option?.label.includes(inputValue)}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="new_price_id" label="新报价id">
          <Input placeholder="新报价id" />
        </Form.Item>
        <Form.Item name="old_price_id" label="旧报价id">
          <Input placeholder="旧报价id" />
        </Form.Item>
        <Form.Item name="remark" label="备注">
          <Input placeholder="备注" />
        </Form.Item>
        <Form.Item name="ddate" label="日期">
          <DatePicker placeholder="请选择日期" style={{ width: 200 }} format="YYYY-MM-DD" />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        rowKey="tactic_id"
        columns={columns}
        dataSource={list}
        loading={loading}
        style={{ marginTop: 10 }}
        pagination={{
          defaultCurrent: searchKeys.page_index,
          pageSize: searchKeys.page_size,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
      />
    </PageContainer>
  );
};

export default PriceAttributeLog;
