export const yesOrNo = [
  { value: 1, text: '是' },
  { value: 0, text: '否' },
];

export const smstype = [
  { value: 0, text: '验证码' },
  { value: 1, text: '通知' },
  { value: 2, text: '营销' },
];

export const routeTypes = [
  { value: 0, text: '混合通道' },
  { value: 1, text: '纯卡发' },
  { value: 2, text: '纯语音' },
  { value: 3, text: '纯直连扣量' },
  { value: 4, text: '纯OTT' },
  // { value: 5, text:'纯SMS' },
  { value: 6, text: '纯本地发国际' },
  { value: 7, text: '纯本地发本地' },
  { value: 8, text: '纯国际' },
];
export const channeltype = [
  { value: 1, text: '纯直连' },
  { value: 2, text: '高质量混合' },
  { value: 3, text: '中高质量混合' },
  { value: 4, text: '中低质量混合' },
  { value: 5, text: '低质量混合' },
];

export const crOptions = [
  { value: 1, text: '>=80%' },
  { value: 2, text: '70%~79%' },
  { value: 3, text: '60%~69%' },
  { value: 4, text: '40%~59%' },
  { value: 5, text: '<=39%' },
];
