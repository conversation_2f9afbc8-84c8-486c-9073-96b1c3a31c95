import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { smsType } from '@/const/const';
import { saleType } from '@/pages/tactic-resources/tacticSaleProduct';
import { getProductType } from '@/services/tacticResources';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { findLabel } from '@/utils/utils';
import { Descriptions, Spin } from 'antd';
import React from 'react';

export function SaleTypeBindTacticTypeDetail(props: { row: any }) {
  const { regionOptions } = useFetchCountryInfo();
  const { row } = props;
  const { value: productType, loading } = useAsyncRetryFunc(async () => {
    const res = await getProductType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);
  return loading ? (
    <Spin></Spin>
  ) : (
    <Descriptions>
      <Descriptions.Item label="国家">
        {row.country_code
          ? findLabel(regionOptions ?? [], row.country_code)
          : row.country_codes?.map((c) => findLabel(regionOptions ?? [], c)).join(',')}
      </Descriptions.Item>
      <Descriptions.Item label="短信类型">
        {row.sms_type
          ? findLabel(smsType, row.sms_type)
          : row.sms_types?.map((c) => findLabel(smsType, c)).join(',')}
      </Descriptions.Item>
      <Descriptions.Item label="售卖类型">{findLabel(saleType, row.sale_type)}</Descriptions.Item>
      <Descriptions.Item label="标品类型">
        {findLabel(productType, row.product_type)}
      </Descriptions.Item>
    </Descriptions>
  );
}
