import React, { useEffect, useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, message, Radio, Progress, Typography } from 'antd';
import { operateAudit } from '@/services/api';
import request from '@/utils/request';
import _ from 'lodash';
import { useList } from 'react-use';
import { CheckCircleTwoTone } from '@ant-design/icons';

const { Text } = Typography;

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

function getOptions(type: string) {
  // 1审核通过/2审核未通过/3已执行/4放弃执行
  if (type === '0') {
    return [
      { value: 0, label: '执行' },
      { value: 4, label: '放弃执行' },
    ];
  }
  return [
    { value: 1, label: '审核通过' },
    { value: 2, label: '审核驳回' },
  ];
}

export const DispatchOperate = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [form] = Form.useForm();
  const [visible, setShowState, defaultVal] = useDialog<{
    apply_ids: number[];
    type: string;
    selectedRows: any[];
  }>(dialogRef);
  const { apply_ids = [], type, selectedRows } = defaultVal;
  const [isConfirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [percent, setPercent] = useState<number>(0);
  const [failedId, setFailedId] = useState<number[]>();
  const [successId, { push, set: setSuccessId }] = useList<number[]>();

  function handleSuccess() {
    message.success('操作成功');
    onSuccess();
  }
  function handleEnd() {
    setConfirmLoading(false);
    setShowState(false);
  }

  const _handlerSubmit = async (vals: any) => {
    if (failedId) {
      message.error('有执行失败的审核单，请关注，如需执行请重新选择');
      return;
    }
    if (vals.status === 0) {
      const completed = [];
      setConfirmLoading(true);
      const sorted = _.sortBy(selectedRows, 'apply_id');
      // eslint-disable-next-line @typescript-eslint/prefer-for-of
      for (let i = 0; i < sorted.length; i++) {
        const row = sorted[i];
        const { code, data } = await request(`${row.route}/${row.action}`, {
          method: 'post',
          data: {
            ...JSON.parse(row.req_ext),
            apply_id: row.apply_id,
          },
        });
        if (code === 0 && !data?.errors?.length) {
          completed.push(row.apply_id);
          push(row.apply_id);
          setPercent(Math.floor((completed.length / selectedRows.length) * 100));

          continue;
        } else {
          setFailedId(row.apply_id);
          setConfirmLoading(false);
          onSuccess();
          break;
        }
      }
      return;
    }
    try {
      setConfirmLoading(true);
      const res = await operateAudit({ status: vals.status, apply_ids });
      if (res?.code === 0) {
        handleSuccess();
      }
      handleEnd();
    } catch (error) {
      handleEnd();
    }
  };

  useEffect(() => {
    if (percent === 100) {
      setConfirmLoading(false);
      handleSuccess();
      setShowState(false);
      onSuccess();
    }
    if (!visible) {
      setPercent(0);
      setFailedId(undefined);
      setSuccessId([]);
      setConfirmLoading(false);
    }
  }, [percent, visible]);

  return (
    <Modal
      open={visible}
      width={600}
      confirmLoading={isConfirmLoading}
      onOk={() => form.submit()}
      onCancel={() => {
        setShowState(false);
        form.resetFields();
      }}
      destroyOnClose
    >
      <Form
        form={form}
        labelAlign="right"
        onFinish={(vals) => _handlerSubmit(vals)}
        style={{ maxHeight: 500, overflow: 'auto' }}
      >
        <Form.Item label="审核单id">
          <div>{apply_ids.sort().join(',')}</div>
        </Form.Item>
        <Form.Item name="status" label="操作" rules={[{ required: true }]}>
          <Radio.Group options={getOptions(type)} />
        </Form.Item>
      </Form>
      {(isConfirmLoading || percent !== 0 || failedId) && <Progress percent={percent} />}
      <p>
        {successId.map((id) => (
          <Text key={id.toString()} type="success" style={{ marginRight: 10 }}>
            {id}
            <CheckCircleTwoTone twoToneColor="#52c41a" />
          </Text>
        ))}
      </p>
      <p>
        {failedId && (
          <Text type="danger">审核单{failedId}执行失败，执行被终止，后续审核单未执行</Text>
        )}
      </p>
    </Modal>
  );
};
