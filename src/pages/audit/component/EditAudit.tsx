import ImageUpload from '@/components/ImageUpload';
import { editAudit } from '@/services/api';
import { DialogRef, useDialog } from '@/utils/react-use/useDialog';
import { Form, Modal, message } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React from 'react';

export default function EditAudit(props: { dialogRef: DialogRef }) {
  const { dialogRef } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    apply_id: number;
    onSuccess: () => void;
  }>(dialogRef);
  const [form] = Form.useForm();

  const { apply_id, onSuccess } = defaultVal;

  async function onSubmit(vals: any) {
    const res = await editAudit({
      ...vals,
      apply_id,
    });
    if (res.code === 0) {
      message.success('提交成功，请等待审核');
      setVisible(false);
      onSuccess();
    } else {
      message.error('提交失败');
    }
  }

  return (
    <>
      <Modal open={visible} onCancel={() => setVisible(false)} onOk={() => form.submit()}>
        <Form
          labelAlign="left"
          form={form}
          size="small"
          onFinish={(vals) => {
            onSubmit(vals);
          }}
        >
          <Form.Item label="申请单ID">{apply_id}</Form.Item>
          <Form.Item name="remark" label="备注">
            <TextArea rows={3} style={{ width: 300 }} />
          </Form.Item>
          <Form.Item name="pictures" label="附件">
            <ImageUpload />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
