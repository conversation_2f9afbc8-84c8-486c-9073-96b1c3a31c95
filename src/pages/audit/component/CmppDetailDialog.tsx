import React, { useEffect, useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, Descriptions, Tabs } from 'antd';
import { queryAP, queryCmppList } from '@/services/api';
import _ from 'lodash';
import { useMount } from 'react-use';
import { isChina } from '@/const/const';

interface DialogProps {
  dialogRef: DialogRef;
}

const mapAttrText: any = new Map([
  [1, ['电信']],
  [2, ['移动']],
  [4, ['联通']],
  [3, ['电信+移动']],
  [5, ['电信+联通']],
  [6, ['移动+联通']],
  [7, ['移动+联通+电信']],
]);

export const CmppDetailDialog = (props: DialogProps) => {
  const { dialogRef } = props;
  const [form] = Form.useForm();
  const [tabKey, setTabKey] = useState<number>(0);
  const [allAp, setAllAp] = useState<Record<string, any>>({});
  const [detailData, setDetailData] = useState<Record<string, any>[]>([]);
  const [visible, setShowState, defaultVal] = useDialog<{
    type: string;
    req_ext: string;
    action?: string;
  }>(dialogRef);

  const { type, req_ext, action } = defaultVal;

  function getAllIp() {
    const _allAp: Record<string, any> = {};
    queryAP({
      page_size: 1000,
      page_index: 1,
    }).then((res) => {
      _.forEach(res?.data?.list, (point) => {
        _allAp[point.id] = point.name;
      });
      setAllAp(_allAp);
    });
  }

  useMount(() => {
    isChina && getAllIp();
  });

  useEffect(() => {
    if (!visible) return;
    async function getTabsData() {
      const rowToJson = JSON.parse(req_ext);
      const res = await queryCmppList({ sp_ids: rowToJson.sp_ids });
      res?.data?.list?.forEach((element: any) => {
        element.account_attribute = mapAttrText.get(element.account_attribute);
        element.master_vip = allAp[element.master_vip_id];
        element.slaver_vip = allAp[element.slaver_vip_id];
        element.domain = allAp[element.domain_id];
        element.ext =
          'http://rainbow.oa.com/console/9bd405df-7324-40c7-9525-3afc59167cd6/Production/list?group_id=250760&group_name=cmpp-proxy-file';
      });
      setTabKey(res?.data?.list?.[0].sp_id);
      setDetailData(res?.data?.list || []);
    }

    async function getAccountAttr() {
      const rowToJson = JSON.parse(req_ext);
      rowToJson.account_attribute = mapAttrText.get(rowToJson.account_attribute);
      rowToJson.master_vip = allAp[rowToJson.master_vip];
      rowToJson.slaver_vip = allAp[rowToJson.slaver_vip];
      rowToJson.domain = allAp[rowToJson.domain];
      if (action === 'edit') {
        const res = await queryCmppList({ sp_ids: rowToJson.sp_id });
        rowToJson.sdkappid = res?.data?.list?.[0]?.sdkappid || '';
      }
      setTabKey(rowToJson.sp_id);
      setDetailData([rowToJson]);
    }

    type === 'tabs' ? getTabsData() : getAccountAttr();
  }, [action, allAp, req_ext, type, visible]);

  return (
    <Modal
      open={visible}
      width={1100}
      onOk={() => setShowState(false)}
      onCancel={() => {
        setShowState(false);
        form.resetFields();
      }}
    >
      {!detailData.length && <h4>暂无数据</h4>}
      <Tabs
        defaultActiveKey={tabKey?.toString()}
        onChange={(key) => {
          setTabKey(Number(key));
        }}
      >
        {detailData.map((item: any, index: number) => {
          return (
            <Tabs.TabPane tab={item.sp_id} key={item.sp_id || index}>
              <Descriptions>
                <Descriptions.Item label="用户名称">{item.customer_name}</Descriptions.Item>
                <Descriptions.Item label="接受账号邮箱">{item.customer_email}</Descriptions.Item>
                <Descriptions.Item label="短信应用">{item.sdkappid}</Descriptions.Item>
                <Descriptions.Item label="短信类型">
                  {item.sms_type.toString() === '0' ? '行业' : '营销'}
                </Descriptions.Item>
                <Descriptions.Item label="账号格式">
                  {item.sp_type.toString() === '0' ? '标准格式' : '完全发海外且国家码前面不带+号'}
                </Descriptions.Item>
                <Descriptions.Item label="业务类型">
                  {item.business_type.toString() === '0' ? '常规业务' : '三大类业务'}
                </Descriptions.Item>
                <Descriptions.Item label="账号属性">{item.account_attribute}</Descriptions.Item>
                <Descriptions.Item label="用途描述">{item.use_description}</Descriptions.Item>
                <Descriptions.Item label="备注">{item.remark}</Descriptions.Item>
                <Descriptions.Item label="客户类型">
                  {item.customer_type.toString() === '0' ? '签名子码' : '应用子码'}
                </Descriptions.Item>
                <Descriptions.Item label="主接入点">{item.master_vip}</Descriptions.Item>
                <Descriptions.Item label="备接入点">{item.slaver_vip}</Descriptions.Item>
                <Descriptions.Item label="域名接入点">{item.domain}</Descriptions.Item>
              </Descriptions>
            </Tabs.TabPane>
          );
        })}
      </Tabs>
    </Modal>
  );
};
