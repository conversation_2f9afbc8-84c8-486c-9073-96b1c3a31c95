import React, { useEffect, useState, useMemo, ReactElement, ReactNode } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Button,
  Table,
  Form,
  Input,
  Typography,
  Tag,
  message,
  Select,
  Tabs,
  Cascader,
  Tooltip,
  Modal,
} from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { LinkOutlined, SearchOutlined } from '@ant-design/icons';
import type { Dispatch } from 'umi';
import { connect, history } from 'umi';
import type { ConnectState } from '@/models/connect';
import request from '@/utils/request';
import { queryAuditList, operateAudit, queryApiList } from '@/services/api';
import _ from 'lodash';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { DispatchOperate } from './component/DispatchOperate';
import { isMobile } from '@/const/jadgeUserAgent';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { useSetState } from 'react-use';
import { CmppDetailDialog } from './component/CmppDetailDialog';
import { useQuery } from '@/utils/react-use/useQuery';
import EditAudit from './component/EditAudit';
import dayjs from 'dayjs';
// import { SaleTypeBindTacticTypeDetail } from './component/SaleTypeBindTacticTypeDetail';

const { Paragraph } = Typography;
const { Option } = Select;

function getDecodeName(url: string) {
  try {
    const _url = new URL(url);
    return _.last(
      decodeURIComponent(decodeURIComponent(decodeURIComponent(_url.pathname))).split('/'),
    );
  } catch (error) {
    return '';
  }
}

const mapColor: any = new Map([
  [0, 'orange'],
  [1, 'green'],
  [2, 'red'],
  [3, ''],
  [4, 'cyan'],
  [5, 'blue'],
]);
const mapText: any = new Map([
  [0, '待审核'],
  [1, '审核通过'],
  [2, '审核未通过'],
  [3, '已执行'],
  [4, '已放弃'],
  [5, '已撤回'],
]);

const auditStatus = [
  {
    text: '待审核',
    value: 0,
  },
  {
    text: '审核通过',
    value: 1,
  },
  {
    text: '审核未通过',
    value: 2,
  },
  {
    text: '已执行',
    value: 3,
  },
  {
    text: '已放弃',
    value: 4,
  },
  {
    text: '已撤回',
    value: 5,
  },
];

interface AuditListProps {
  dispatch: Dispatch;
  currentUser: any;
}

const mapAccountAttr: any = new Map([
  [1, [1]],
  [2, [2]],
  [4, [4]],
  [3, [1, 2]],
  [5, [1, 4]],
  [6, [2, 4]],
  [7, [1, 2, 4]],
]);

const AuditList = (props: AuditListProps) => {
  const { id: searchId } = useQuery();
  const [form] = Form.useForm();
  const [tabKey, setTabKey] = useState('0');
  const [searchVals, setSearchVals] = useSetState<{
    page_index: number;
    page_size: number;
    apply_id?: number | string;
    status?: number | string;
  }>({
    page_index: 1,
    page_size: 10,
    apply_id: searchId || '',
    status: '',
  });
  const { currentUser, dispatch } = props;
  const [moduleOptions, setModuleOptions] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const dialogRef = useDialogRef();
  const detailRef = useDialogRef();
  const editDialogRef = useDialogRef();
  const [executeLoading, setExecuteLoading] = useState<boolean | number>(false);
  const [auditLoading, setAuditLoading] = useState<boolean | number>(false);

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    if (!currentUser.user_id) {
      return;
    }
    const userID = currentUser.user_id;
    const vals = _.pickBy(searchVals, (v: any) => v !== '' && !_.isNil(v));
    tabKey === '1' ? (vals.auditor_id = userID) : (vals.user_id = userID);
    const response = await queryAuditList({ ...vals });
    return response?.data || {};
  }, [currentUser.user_id, tabKey, searchVals]);

  const list = useMemo(() => {
    return _.map(state?.list, (v) => ({
      ...v,
      operator: v.operator || v.auditors,
    }));
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  function checkNeedCustomized(row: any): ReactNode | null {
    if (row.route === '/cmpp/account') {
      if (['add', 'edit'].includes(row.action)) {
        return (
          <a
            onClick={() => {
              detailRef.current.open({
                type: 'account',
                req_ext: row?.req_ext,
                action: row?.action,
              });
            }}
          >
            查看详情
          </a>
        );
      }
      if (['release-account-conf'].includes(row.action)) {
        return (
          <a
            onClick={() => {
              detailRef.current.open({
                type: 'tabs',
                req_ext: row?.req_ext,
              });
            }}
          >
            查看详情
          </a>
        );
      }
    }
    // if (row.route === '/abroad-scheduler/tactic-sale-product') {
    //   if (['add', 'edit'].includes(row.action)) {
    //     return (
    //       <a
    //         onClick={() => {
    //           Modal.info({
    //             title: '查看详情',
    //             width: 1200,
    //             content: (
    //               <SaleTypeBindTacticTypeDetail
    //                 row={JSON.parse(row.req_ext)}
    //               ></SaleTypeBindTacticTypeDetail>
    //             ),
    //           });
    //         }}
    //       >
    //         查看详情
    //       </a>
    //     );
    //   }
    // }
    return null;
  }

  const columns: ColumnsType<any> = useMemo(() => {
    async function onOperateAudit(status: number, apply_id: number) {
      // 审核
      setAuditLoading(apply_id);
      const res = await operateAudit({
        apply_ids: [apply_id],
        status,
      });
      setAuditLoading(false);
      if (res.code === 0) {
        message.success(mapText.get(status));
        retry();
      }
    }

    function doAudit(row: any) {
      const data = JSON.parse(row.req_ext);
      // 执行
      setExecuteLoading(row.apply_id);
      request(`${row.route}/${row.action}`, {
        method: 'post',
        data: {
          ..._.pickBy(data, (v) => !_.isNil(v)),
          apply_id: row.apply_id,
        },
      })
        .then((res: any) => {
          setExecuteLoading(false);
          if (res.code === 0) {
            message.success('执行成功');
          }
          retry();
        })
        .catch(() => {
          setExecuteLoading(false);
        });
    }

    function doEdit(row: any) {
      const rowToJson = JSON.parse(row?.req_ext);
      rowToJson.account_attribute = mapAccountAttr.get(rowToJson.account_attribute);
      dispatch({
        type: 'audit/editCmppForm',
        payload: {
          cmppInfo: rowToJson,
        },
      });
      history.push({
        pathname: '/cmpp/account',
      });
    }

    return [
      {
        title: 'id',
        dataIndex: 'apply_id',
        key: 'apply_id',
      },
      {
        title: '接口名称',
        render: (row: any) => `${row.route}/${row.action}`,
        width: 180,
      },
      {
        title: '接口中文名',
        render: (row: any) => (
          <div style={{ wordWrap: 'break-word', wordBreak: 'break-word', minWidth: 120 }}>
            {row.module_name} - {row.api_name}
          </div>
        ),
        width: 200,
      },
      {
        title: '请求参数json',
        render: (row: any) => {
          const render = checkNeedCustomized(row);
          if (render) {
            return render;
          }

          return isMobile() ? (
            <div
              style={{
                wordBreak: 'break-all',
                wordWrap: 'break-word',
                whiteSpace: 'normal',
              }}
            >
              {row.req_ext}
            </div>
          ) : (
            <Paragraph
              copyable={{ text: row.req_ext }}
              ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}
              style={{ wordBreak: 'break-all', minWidth: 250 }}
            >
              {row.req_ext}
            </Paragraph>
          );
        },
        // width: 300,
      },
      {
        title: '变更开始时间',
        dataIndex: 'start_time',
        key: 'start_time',
        width: 150,
      },
      {
        title: '变更结束时间',
        dataIndex: 'end_time',
        key: 'end_time',
        width: 150,
      },
      {
        title: '变更背景',
        dataIndex: 'remark',
        key: 'remark',
        width: 180,
      },
      {
        title: '变更影响',
        dataIndex: 'modify_impact',
        key: 'modify_impact',
        width: 180,
      },
      {
        title: '发起人',
        dataIndex: 'staff_name',
        key: 'staff_name',
        width: 120,
      },
      {
        title: '审核人',
        dataIndex: 'operator',
        key: 'operator',
        render: (operator) => (
          <div style={{ wordWrap: 'break-word', wordBreak: 'break-word', minWidth: 100 }}>
            {operator}
          </div>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        // width: 220,
      },

      {
        title: '附件',
        dataIndex: 'pictures',
        key: 'pictures',
        width: 100,
        render: (pictures: string[]) => {
          return pictures.map((url, index) => {
            const name = getDecodeName(url);
            const changePortol = url.replace('http:', '');
            return (
              <Tooltip title={name} key={name}>
                <Button
                  type="link"
                  onClick={() => {
                    window.open(changePortol);
                  }}
                  icon={<LinkOutlined />}
                >
                  附件{index + 1}
                </Button>
              </Tooltip>
            );
          });
        },
      },
      {
        title: '审核状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string) => {
          return <Tag color={mapColor.get(status)}>{mapText.get(status)}</Tag>;
        },
        width: 120,
      },
      {
        title: '操作',
        key: 'action',
        render: (row: any) => {
          if (currentUser.staff_name === row.staff_name) {
            if (row.status === 1) {
              return (
                <>
                  <Button
                    type="primary"
                    size="small"
                    onClick={() => doAudit(row)}
                    loading={executeLoading === row.apply_id}
                    disabled={dayjs(row.end_time).isBefore(dayjs())}
                  >
                    执行
                  </Button>
                  <Button
                    type="primary"
                    size="small"
                    style={{ marginTop: 5 }}
                    className="audit-reject"
                    loading={auditLoading === row.apply_id}
                    onClick={() => {
                      Modal.confirm({
                        title: '提示',
                        content: '是否放弃执行',
                        onOk: () => onOperateAudit(4, row.apply_id),
                      });
                    }}
                  >
                    放弃执行
                  </Button>
                </>
              );
            }
            if (row.status === 2 && row.route === '/cmpp/account' && row.action === 'add') {
              return (
                <Button type="primary" size="small" onClick={() => doEdit(row)}>
                  编辑
                </Button>
              );
            }
            if (row.status === 0) {
              return (
                <>
                  <Button
                    size="small"
                    type="primary"
                    onClick={() => {
                      editDialogRef.current?.open({ apply_id: row.apply_id, onSuccess: retry });
                    }}
                    style={{ marginBottom: 10 }}
                  >
                    编辑
                  </Button>
                  <Button
                    size="small"
                    danger
                    onClick={() => onOperateAudit(5, row.apply_id)}
                    loading={auditLoading === row.apply_id}
                  >
                    撤回
                  </Button>
                </>
              );
            }
            return false;
          }
          return (
            row.status === 0 && (
              <>
                <Button
                  size="small"
                  className="audit-approve"
                  onClick={() => onOperateAudit(1, row.apply_id)}
                  loading={auditLoading === row.apply_id}
                >
                  通过
                </Button>
                <br />
                <Button
                  size="small"
                  className="audit-reject"
                  onClick={() => onOperateAudit(2, row.apply_id)}
                  loading={auditLoading === row.apply_id}
                >
                  拒绝
                </Button>
              </>
            )
          );
        },
      },
    ];
  }, [
    auditLoading,
    checkNeedCustomized,
    currentUser.staff_name,
    dispatch,
    editDialogRef,
    executeLoading,
    retry,
  ]);

  async function getModuleOptions() {
    const options: any = [];
    const res = await queryApiList({
      page_size: 1000,
      page_index: 1,
    });
    const groupList = _.groupBy(res.data?.list, 'module_name');
    _.forEach(groupList, (modules, key) => {
      const obj: {
        value: string;
        label: string;
        children: { value: number; label: string }[];
      } = {
        value: '',
        label: '',
        children: [],
      };
      obj.value = key;
      obj.label = key;
      obj.children = [];
      _.forEach(modules, (item) => {
        obj.children.push({
          value: item.api_id,
          label: item.name,
        });
      });
      options.push(obj);
    });
    setModuleOptions(options);
  }

  useEffect(() => {
    getModuleOptions();
  }, []);

  async function onSubmit(vals: any) {
    setSearchVals({ ...vals, api_id: vals.api_id?.[1], page_index: 1 });
  }

  function onListTabChange(key: string) {
    setTabKey(key);
    form.setFieldsValue({ status: key === '1' ? 0 : '' });
    setSelectedRowKeys([]);
    setSelectedRows([]);
    setSearchVals({ status: key === '1' ? 0 : '', page_index: 1 });
  }

  function renderContent() {
    return (
      <>
        <Form
          labelCol={{ span: 6 }}
          form={form}
          initialValues={{ ...searchVals }}
          layout="inline"
          labelAlign="right"
          onFinish={(vals) => onSubmit(vals)}
        >
          <Form.Item name="apply_id">
            <Input placeholder="审核单id" style={{ width: 250 }} />
          </Form.Item>
          <Form.Item name="api_id">
            <Cascader placeholder="接口名称" options={moduleOptions} style={{ width: 250 }} />
          </Form.Item>
          <Form.Item name="status">
            <Select style={{ width: 250 }}>
              <Option value={''}>{'全部状态'}</Option>
              {auditStatus.map((item) => {
                return (
                  <Option value={item.value} key={item.value}>
                    {item.text}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item name="search_key">
            <Input placeholder="json" style={{ width: 250 }} />
          </Form.Item>
          <Form.Item>
            <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={loading}>
              查询
            </Button>
          </Form.Item>
          <Button
            type="primary"
            disabled={!selectedRowKeys.length}
            onClick={() => {
              dialogRef.current.open({
                apply_ids: selectedRowKeys,
                type: tabKey,
                selectedRows,
              });
            }}
          >
            {tabKey === '0' ? '批量执行' : '批量审核'}
          </Button>
        </Form>
        <Table
          columns={columns}
          dataSource={list}
          rowKey={(record) => record.apply_id}
          loading={loading}
          pagination={{
            current: searchVals.page_index,
            total,
            showSizeChanger: true,
            onShowSizeChange: (current, page_size) => setSearchVals({ page_size }),
            onChange: (page_index) => {
              setSearchVals({ page_index });
              setSelectedRowKeys([]);
              setSelectedRows([]);
            },
          }}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
              setSelectedRowKeys(selectedRowKeys);
              setSelectedRows(selectedRows);
            },
          }}
          style={{ marginTop: 20 }}
          scroll={{ x: 1800 }}
        />
      </>
    );
  }

  return (
    <>
      <PageContainer>
        <Tabs
          defaultValue={tabKey}
          onChange={(key) => {
            onListTabChange(key);
          }}
          items={[
            {
              key: '0',
              label: '我的申请',
              disabled: loading,
              children: renderContent(),
            },
            {
              key: '1',
              label: '我的审核',
              disabled: loading,
              children: renderContent(),
            },
          ]}
        ></Tabs>
      </PageContainer>
      <CmppDetailDialog dialogRef={detailRef} />
      <DispatchOperate
        dialogRef={dialogRef}
        onSuccess={() => {
          retry();
          setSelectedRowKeys([]);
          setSelectedRows([]);
        }}
      />
      <EditAudit dialogRef={editDialogRef}></EditAudit>
    </>
  );
};

export default connect(({ user }: ConnectState) => ({
  currentUser: user.currentUserConfig || {},
}))(AuditList);
