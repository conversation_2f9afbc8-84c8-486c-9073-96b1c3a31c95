import React from 'react';
import { Result } from 'antd';
import { useQuery } from '@/utils/react-use/useQuery';

export default () => {
  const { page, nouser, api } = useQuery();
  const type = page ? 'page' : 'api';
  return (
    <Result
      status="403"
      title="403"
      style={{
        background: 'none',
      }}
      subTitle={`暂无权限【${
        // eslint-disable-next-line no-nested-ternary
        nouser !== '1' ? (type === 'page' ? `PAGE:${page}` : `API:${api}`) : '该账号尚未开通'
      }】，请复制本条提示信息联系zcanzhu，miamwu添加权限`}
    />
  );
};
