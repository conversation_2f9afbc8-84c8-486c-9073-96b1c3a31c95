import React from 'react';
import { getYeheMsgList, addSendYeheMsg } from '@/services/yeheAlarm';
import PatternTable from '@/pages/component/PatternLayout';
import { Button, DatePicker, Input, Select, Typography } from 'antd';
import { useList } from 'react-use';
import _ from 'lodash';
import dayjs from 'dayjs';
const { RangePicker } = DatePicker;
const { Text, Paragraph } = Typography;

const sendChannel = [
  { value: 1, label: '站内信' },
  { value: 2, label: '邮件' },
  { value: 4, label: '短信' },
  { value: 8, label: '微信' },
  { value: 16, label: '语音' },
  { value: 32, label: '企业微信' },
  { value: 64, label: '安灯' },
  { value: 128, label: '机器人' },
];

const statusText = [
  { value: 0, text: <Text>待提交</Text> },
  {
    value: 1,
    text: (
      <Text type="success" style={{ whiteSpace: 'nowrap' }}>
        提交成功
      </Text>
    ),
  },
  {
    value: 2,
    text: (
      <Text type="danger" style={{ whiteSpace: 'nowrap' }}>
        提交失败
      </Text>
    ),
  },
];

const YeheAlarm = () => {
  const [tplParams, { push, removeAt, updateAt }] = useList(['']);
  const [tplParamsEn, { push: pushEn, removeAt: removeAtEn, updateAt: updateAtEn }] = useList(['']);

  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '模版id',
      dataIndex: 'theme_id',
      key: 'theme_id',
      align: 'center',
    },
    {
      title: '中文参数',
      dataIndex: 'tpl_params',
      key: 'tpl_params',
      align: 'center',
      width: 400,
      render: (v: string) =>
        v !== '' ? (
          <Paragraph
            ellipsis={{
              rows: 3,
              expandable: true,
            }}
            style={{ wordBreak: 'break-all' }}
            copyable
          >
            {v}
          </Paragraph>
        ) : null,
    },
    {
      title: '英文参数',
      dataIndex: 'tpl_params_en',
      key: 'tpl_params_en',
      align: 'center',
      width: 400,
      render: (v: string) =>
        v !== '' ? (
          <Paragraph
            ellipsis={{
              rows: 3,
              expandable: true,
            }}
            style={{ wordBreak: 'break-all' }}
            copyable
          >
            {v}
          </Paragraph>
        ) : null,
    },
    {
      title: 'uin',
      dataIndex: 'uin',
      key: 'uin',
      align: 'center',
    },
    {
      title: '发送类型',
      dataIndex: 'send_channel',
      key: 'send_channel',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (v: number) => statusText.find((el) => el.value === v)?.text,
    },
    {
      title: '操作人',
      dataIndex: 'staffname',
      key: 'staffname',
      align: 'center',
    },
    {
      title: '野鹤logid',
      dataIndex: 'yehe_log_id',
      key: 'yehe_log_id',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'theme_id',
      label: '模版id',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'send_channel',
      label: '推送方式',
      disabled: false,
      isRequired: true,
      render: () => (
        <Select mode="multiple" style={{ width: 200 }} allowClear options={sendChannel} />
      ),
    },
    {
      showOnAdd: true,
      name: 'tpl_params',
      label: '中文参数',
      disabled: false,
      render: () => (
        <>
          {tplParams.map((el, i) => (
            <>
              <Input
                key={i}
                style={{ width: 200, marginBottom: 10 }}
                value={el}
                onChange={(e) => updateAt(i, e.target.value)}
              />
              <Button danger type="link" style={{ paddingRight: 0 }} onClick={() => removeAt(i)}>
                删除
              </Button>
            </>
          ))}
          <Button type="link" onClick={() => push('')}>
            添加
          </Button>
        </>
      ),
    },
    {
      showOnAdd: true,
      name: 'tpl_params_en',
      label: '英文参数',
      disabled: false,
      render: () => (
        <>
          {tplParamsEn.map((el, i) => (
            <>
              <Input
                key={i}
                style={{ width: 200, marginBottom: 10 }}
                value={el}
                onChange={(e) => updateAtEn(i, e.target.value)}
              />
              <Button danger type="link" style={{ paddingRight: 0 }} onClick={() => removeAtEn(i)}>
                删除
              </Button>
            </>
          ))}
          <Button type="link" onClick={() => pushEn('')}>
            添加
          </Button>
        </>
      ),
    },
    {
      showOnAdd: true,
      name: 'uins',
      label: 'uins',
      disabled: false,
      isRequired: true,
      render: () => <Input.TextArea rows={4} placeholder="一行一个" style={{ width: 200 }} />,
    },
  ];

  async function doAdd(vals: any) {
    return await addSendYeheMsg({
      theme_id: vals.theme_id,
      tpl_params: tplParams,
      tpl_params_en: tplParamsEn,
      send_channel: _.sum(vals.send_channel),
      uins: vals.uins
        .split('\n')
        ?.map((uin: string) => Number(uin))
        ?.filter((el: number) => el),
    });
  }

  async function getList(vals?: any) {
    return await getYeheMsgList({
      theme_id: vals.theme_id,
      uin: vals.uin,
      from_time: vals.range_time ? dayjs(vals.range_time[0]).format('YYYY-MM-DD HH:mm:ss') : '',
      to_time: vals.range_time ? dayjs(vals.range_time[1]).format('YYYY-MM-DD HH:mm:ss') : '',
    });
  }

  return (
    <PatternTable<{
      qappid: number;
      type: string;
    }>
      formProps={{ labelCol: { span: 4 } }}
      getFn={getList}
      addFn={doAdd}
      columns={columns}
      initialValues={{ type: 'pay_type' }}
      searchKeys={[
        {
          label: '',
          name: 'theme_id',
          placeholder: '模版id',
        },
        {
          label: '',
          name: 'uin',
          placeholder: 'uin',
        },
        {
          label: '',
          name: 'range_time',
          render: () => <RangePicker showTime />,
        },
      ]}
      operateForm={operateForm}
      operType={3}
    />
  );
};
export default YeheAlarm;
