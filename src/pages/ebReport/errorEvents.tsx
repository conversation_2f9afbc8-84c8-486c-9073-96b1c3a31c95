import React, { useEffect, useMemo, useState } from 'react';
import _ from 'lodash';
import { getErrorEvents } from '@/services/ebReports';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { isMobile } from '@/const/jadgeUserAgent';
import { useSetState } from 'react-use';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { PutBreakDownEventDialog } from './components/PutBreakDownEventDialog';
import { Button, Descriptions, Alert, Table, Typography } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';

export const statusOptions = [
  { label: '已恢复', value: 1 },
  { label: '故障中', value: 2 },
];

export type InstancesType = {
  qappid: number;
  status: number;
  uin?: number;
  instance_id?: string;
};

const ErrorEvents = () => {
  const [searchKeys, setSearchKeys] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const res = await getErrorEvents({ ...searchKeys });
    return res?.data || {};
  }, [searchKeys]);

  const [expandedKeys, setExpandedKeys] = useState<number[]>([]);

  const dialogRef = useDialogRef();

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  useEffect(() => {
    setExpandedKeys(_.map(list, (v) => v.id));
  }, [list]);

  return (
    <PageContainer>
      <Alert
        message="注意：同一故障，可二次上报以更新故障信息、追加实例、更新实例故障状态，此时须上报相同的Id，可在列表操作里追加上报。"
        type="error"
        style={{ marginBottom: 10 }}
      />
      <Button
        type="primary"
        onClick={() => {
          dialogRef.current.open({
            type: 0,
          });
        }}
      >
        新增上报
      </Button>
      <Table
        dataSource={list}
        loading={loading}
        rowKey="id"
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        pagination={{
          current: searchKeys.page_index,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current: any, page_size: number) => setSearchKeys({ page_size }),
          onChange: (page_index: number) => {
            setSearchKeys({ page_index });
          },
        }}
        style={{ marginTop: 20 }}
        columns={[
          {
            title: '事件唯一ID',
            dataIndex: 'breakdown_id',
            key: 'breakdown_id',
            render: (breakdown_id: string) => (
              <Typography.Paragraph
                copyable={{ text: breakdown_id }}
                ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}
                style={{ wordBreak: 'break-all', width: 120 }}
              >
                {breakdown_id}
              </Typography.Paragraph>
            ),
          },
          {
            title: '事件名称(英文)',
            dataIndex: 'event_type',
            key: 'event_type',
            align: 'center',
          },
          {
            title: '故障发生时间',
            dataIndex: 'alarm_time',
            key: 'alarm_time',
            align: 'center',
          },
          {
            title: '故障恢复时间',
            dataIndex: 'recover_time',
            key: 'recover_time',
            align: 'center',
          },
          // {
          //   title: '地域信息',
          //   dataIndex: 'region',
          //   key: 'region',
          //   align: 'center',
          // },
          // {
          //   title: '机房可用区信息',
          //   dataIndex: 'zone',
          //   key: 'zone',
          //   align: 'center',
          // },
          {
            title: '事件状态',
            dataIndex: 'status',
            key: 'status',
            align: 'center',
            render: (status: 1 | 2) => (
              <div style={{ wordWrap: 'break-word', wordBreak: 'break-word', minWidth: 60 }}>
                {_.find(statusOptions, (v) => v.value === status)?.label}
              </div>
            ),
          },
          {
            title: '故障标题',
            dataIndex: 'title',
            key: 'title',
            align: 'center',
            render: (title: string) => (
              <div style={{ wordWrap: 'break-word', wordBreak: 'break-word', minWidth: 80 }}>
                {title}
              </div>
            ),
          },
          // {
          //   title: '故障描述',
          //   dataIndex: 'description',
          //   key: 'description',
          //   align: 'center',
          //   render: (description: string) => <pre>{description}</pre>,
          // },
          // {
          //   title: '故障原因（对内）',
          //   dataIndex: 'internal_reason',
          //   key: 'internal_reason',
          //   align: 'center',
          //   render: (internal_reason: string) => <pre>{internal_reason}</pre>,
          // },
          // {
          //   title: '故障原因（对外）',
          //   dataIndex: 'external_reason',
          //   key: 'external_reason',
          //   align: 'center',
          //   render: (external_reason: string) => <pre>{external_reason}</pre>,
          // },
          // {
          //   title: '故障恢复建议',
          //   dataIndex: 'recover_advise',
          //   key: 'recover_advise',
          //   align: 'center',
          //   render: (recover_advise: string) => <pre>{recover_advise}</pre>,
          // },
          {
            title: '故障关注人',
            dataIndex: 'follower',
            key: 'follower',
            align: 'center',
            render: (follower: string[]) => follower?.join(','),
          },
          {
            title: '故障当前处理人',
            dataIndex: 'handler',
            key: 'handler',
            align: 'center',
            render: (handler: string[]) => handler?.join(','),
          },
          {
            title: '故障技术owner',
            dataIndex: 'owner',
            key: 'owner',
            align: 'center',
            render: (owner: string[]) => owner?.join(','),
          },
          {
            title: (
              <>
                <div>受故障影响qappid</div>
              </>
            ),
            dataIndex: 'instances',
            key: 'instances',
            align: 'center',
            render: (instances: InstancesType[]) => {
              return instances.map((item) => {
                const { qappid } = item;
                return <p key={qappid} style={{ width: 200 }}>{`${qappid}`}</p>;
              });
            },
          },
          {
            title: '操作',
            key: 'operation',
            align: 'center',
            render: (row: any) => {
              return (
                <Button
                  type="link"
                  onClick={() => {
                    dialogRef.current.open({ ...row, type: 1 });
                  }}
                >
                  追加上报
                </Button>
              );
            },
          },
        ]}
        expandable={{
          expandedRowKeys: expandedKeys,
          expandedRowRender: (record) => (
            <Descriptions style={{ paddingLeft: 50 }}>
              <Descriptions.Item label="地域信息">{record.region}</Descriptions.Item>
              <Descriptions.Item label="机房可用区信息">{record.zone}</Descriptions.Item>
              <Descriptions.Item label="故障描述">{record.description}</Descriptions.Item>
              <Descriptions.Item label="故障原因（对内）">
                {record.internal_reason}
              </Descriptions.Item>
              <Descriptions.Item label="故障原因（对外）">
                {record.external_reason}
              </Descriptions.Item>
              <Descriptions.Item label="故障恢复建议">{record.recover_advise}</Descriptions.Item>
            </Descriptions>
          ),
          onExpandedRowsChange: (expandedKeys) => {
            setExpandedKeys(expandedKeys as number[]);
          },
        }}
      />
      <PutBreakDownEventDialog dialogRef={dialogRef} onSuccess={retry} />
    </PageContainer>
  );
};

export default ErrorEvents;
