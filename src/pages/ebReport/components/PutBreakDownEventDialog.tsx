import React, { useEffect, useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, message, Select, Input, DatePicker } from 'antd';
import { addErrorEvents } from '@/services/ebReports';
import dayjs from 'dayjs';
import { smsRegion } from '@/const/const';
import _ from 'lodash';
import type { InstancesType } from '../errorEvents';
import { statusOptions } from '../errorEvents';
import style from './index.module.less';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

function getZoneOptions(region: string) {
  const regionByFilter = smsRegion.filter((o) => o.value === region);
  const res = _.reduce(
    regionByFilter,
    (res: { label: string; value: string }[], item) => {
      const _opt = item.children.map((v) => ({ label: v.title, value: v.value }));
      const _res = res.concat(_opt);
      return _res;
    },
    [],
  );
  return res;
}

export const PutBreakDownEventDialog = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [form] = Form.useForm();
  const [visible, setShowState, defaultVal] = useDialog<{
    breakdown_id?: number;
    type: number;
    [k: string]: any;
  }>(dialogRef);
  const { breakdown_id = '', type } = defaultVal;

  useEffect(() => {
    if (visible && type) {
      const _vals = {
        ..._.omit(defaultVal, ['type']),
        follower: defaultVal.follower?.join(','),
        handler: defaultVal.handler?.join(','),
        owner: defaultVal.owner?.join(','),
        alarm_time: dayjs(defaultVal?.alarm_time),
        recover_time: defaultVal.recover_time ? dayjs(defaultVal?.alarm_time) : undefined,
        instances: _.reduce(
          defaultVal.instances,
          (res, item: InstancesType) => {
            const { qappid } = item;
            return `${res}${res ? '\n' : ''}${qappid}`;
          },
          '',
        ),
      };
      form.setFieldsValue({ ..._vals });
    } else {
      form.setFieldsValue({
        event_type: '短信故障通知',
        title: '短信故障通知',
        description: '广东电信网关异常',
        internal_reason:
          '腾讯云短信监控到广东电信网关异常，影响短信回执率，我们已向运营商报障，相关进展将第一时间向您同步。\n对您造成的不便我们深感歉意，非常感谢您的理解与支持！',
        external_reason:
          '腾讯云短信监控到广东电信网关异常，影响短信回执率，我们已向运营商报障，相关进展将第一时间向您同步。\n对您造成的不便我们深感歉意，非常感谢您的理解与支持！',
      });
    }
  }, [breakdown_id, defaultVal, form, type, visible]);

  const [isLoading, setLoading] = useState<boolean>(false);

  const _handlerSubmit = async (vals: any) => {
    try {
      setLoading(true);
      const instances = _.map(_.compact(vals.instances.split('\n')), (v: string) => ({
        qappid: _.trim(v),
      }));
      const res = await addErrorEvents({
        ..._.pickBy(vals, (v) => v && !_.isNil(v)),
        follower: vals.follower ? _.compact(vals.follower?.split(',')) : undefined,
        handler: vals.handler ? _.compact(vals.handler?.split(',')) : undefined,
        owner: vals.owner ? _.compact(vals.owner?.split(',')) : undefined,
        alarm_time: dayjs(vals.alarm_time).format('YYYY-MM-DD HH:mm:ss'),
        recover_time: vals.recover_time
          ? dayjs(vals.recover_time).format('YYYY-MM-DD HH:mm:ss')
          : undefined,
        instances,
      });
      if (res?.code === 0) {
        message.success('添加成功');
        onSuccess();
      }
      setShowState(false);
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
      setShowState(false);
    }
  };

  return (
    <Modal
      open={visible}
      confirmLoading={isLoading}
      width="800px"
      onOk={() => form.submit()}
      onCancel={() => {
        setShowState(false);
        form.resetFields();
      }}
    >
      <Form
        form={form}
        labelAlign="right"
        layout="inline"
        scrollToFirstError
        onFinish={(vals) => _handlerSubmit(vals)}
        style={{ maxHeight: 500, overflow: 'auto', marginTop: 10 }}
        className={style.inline_form_item}
      >
        {type ? (
          <Form.Item name="breakdown_id" label="事件唯一ID" rules={[{ required: true }]}>
            <Input placeholder="请输入" style={{ width: 200 }} disabled={true} />
          </Form.Item>
        ) : null}
        <Form.Item name="event_type" label="事件名称" rules={[{ required: true }]}>
          <Input placeholder="请输入" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="alarm_time" label="故障发生时间" rules={[{ required: true }]}>
          <DatePicker showTime />
        </Form.Item>
        <Form.Item name="status" label="事件状态" rules={[{ required: true }]}>
          <Select placeholder="请选择" style={{ width: 200 }} options={statusOptions} />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.alarm_time !== curValues.alarm_time || prevValues.status !== curValues.status
          }
        >
          {({ getFieldValue }) => {
            const alarmTime = dayjs(getFieldValue('alarm_time'));
            const status = getFieldValue('status');
            return (
              <Form.Item
                name="recover_time"
                label="故障恢复时间"
                rules={[
                  {
                    required: status === 1,
                    validator: (rule: any, value: string) => {
                      if (!value) {
                        return status === 1
                          ? Promise.reject(new Error('状态为已恢复时，恢复时间必填'))
                          : Promise.resolve();
                      }
                      return dayjs(value).isBefore(alarmTime)
                        ? Promise.reject(new Error('恢复时间不得小于故障发生时间'))
                        : Promise.resolve();
                    },
                  },
                ]}
              >
                <DatePicker showTime />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="region" label="地域信息" rules={[{ required: true }]}>
          <Select
            placeholder="请选择"
            style={{ width: 200 }}
            onChange={(value) => {
              form.setFieldsValue({ region: value, zone: undefined });
            }}
            options={smsRegion.map((v) => ({ label: v.title, value: v.value }))}
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.region !== curValues.region}
        >
          {({ getFieldValue }) => {
            const region = getFieldValue('region');
            return (
              <Form.Item name="zone" label="机房可用区信息">
                <Select
                  placeholder="请选择"
                  style={{ width: 200 }}
                  allowClear
                  options={getZoneOptions(region)}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="title" label="故障标题" rules={[{ required: true }]}>
          <Input placeholder="请输入" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="description" label="故障描述" rules={[{ required: true }]}>
          <Input placeholder="请输入" style={{ width: 200 }} maxLength={80} />
        </Form.Item>
        <Form.Item name="internal_reason" label="故障原因（对内）" rules={[{ required: true }]}>
          <Input.TextArea placeholder="请输入" autoSize style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="external_reason" label="故障原因（对外）" rules={[{ required: true }]}>
          <Input.TextArea placeholder="请输入" autoSize style={{ width: 200 }} maxLength={80} />
        </Form.Item>
        <Form.Item name="recover_advise" label="故障恢复建议">
          <Input.TextArea placeholder="请输入" autoSize rows={2} style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="follower" label="故障关注人">
          <Input placeholder="多个用英文逗号隔开" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="handler" label="故障当前处理人">
          <Input placeholder="多个用英文逗号隔开" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="owner" label="故障技术owner" rules={[{ required: true }]}>
          <Input placeholder="多个用英文逗号隔开" style={{ width: 200 }} />
        </Form.Item>
        <Form.Item
          name="instances"
          label="受故障影响qappid"
          extra={<div>换行输入多个qappid</div>}
          rules={[
            {
              required: true,
              validator: (rule: any, value: string) => {
                const reg = /^\d+$/;
                if (!value) {
                  return Promise.reject(new Error('必填'));
                }
                const infoArr = _.map(_.compact(value.split('\n')), _.trim);
                const unvalidIndex = _.findIndex(infoArr, (v) => !reg.test(v));
                return unvalidIndex === -1
                  ? Promise.resolve()
                  : Promise.reject(new Error(`第${unvalidIndex + 1}行格式不正确，请重新输入`));
              },
            },
          ]}
        >
          <Input.TextArea placeholder="换行输入多个qappid" style={{ width: 200 }} rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
