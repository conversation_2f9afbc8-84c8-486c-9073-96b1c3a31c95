import React, { useState } from 'react';
import PatternTable from '@/pages/component/PatternLayout';
import _ from 'lodash';
import { smsRegion, yesOrNo } from '@/const/const';
import { addReportEvents, editReportEvents, getReportEvents } from '@/services/ebReports';
import { Checkbox, Divider, Input, Radio, Tree, TreeSelect, message } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd/es/form/Form';

const statusOptions = [
  { label: '正常', value: 1 },
  { label: '通知/提示', value: 0 },
  { label: '故障/异常', value: 2 },
];

const areaOptions = [
  { label: '国内站', value: 0 },
  { label: '国际站', value: 1 },
  { label: '国内站/国际站', value: 2 },
];

const region_zones = _.reduce(
  smsRegion,
  (res: string[], v) => {
    const values = v.children.map((o) => o.value);
    res = res.concat(values);
    return res;
  },
  [],
);

const EventsOps = () => {
  const [changedFields, setChangedField] = useState<string[]>(['event_type']);

  const operateForm = [
    {
      showOnAdd: true,
      name: 'event_type',
      label: '事件名称(英文)',
      isRequired: true,
      disabled: true,
    },
    {
      showOnAdd: true,
      name: 'event_type_cn',
      label: '事件名称(中文)',
      isRequired: true,
      editOptinal: true,
    },
    {
      showOnAdd: true,
      name: 'status',
      label: '服务状态',
      renderType: 'select',
      options: statusOptions,
      isRequired: true,
      editOptinal: true,
    },
    {
      showOnAdd: true,
      name: 'area',
      label: '站点类型',
      renderType: 'select',
      options: areaOptions,
      editOptinal: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'region_zones',
      label: '地域-可用区',
      editOptinal: true,
      isRequired: true,
      render: (form: FormInstance<any>) => {
        const value = form.getFieldValue('region_zones');
        return (
          <TreeSelect
            dropdownRender={(originNode) => {
              return (
                <div>
                  <div style={{ padding: '4px 8px 8px 8px', cursor: 'pointer' }}>
                    <Checkbox
                      checked={value?.length === region_zones.length}
                      onChange={(e) => {
                        if (e.target.checked === true) {
                          form.setFieldsValue({
                            region_zones,
                          });
                        } else {
                          form.setFieldsValue({
                            region_zones: [],
                          });
                        }
                        setChangedField((prev: string[]) => {
                          const n: string[] = prev.concat('region_zones');
                          return [...new Set(n)];
                        });
                      }}
                    >
                      全选
                    </Checkbox>
                  </div>
                  <Divider style={{ margin: '0' }} />
                  {originNode}
                </div>
              );
            }}
            value={form.getFieldValue('region_zones')}
            onChange={(value) => {
              form.setFieldsValue({ region_zones: value });
            }}
            treeNodeFilterProp="title"
            treeCheckable
            treeData={smsRegion}
            allowClear
            style={{ width: 300 }}
            placeholder="请选择"
          />
        );
      },
    },
    {
      showOnAdd: true,
      name: 'is_report',
      editOptinal: true,
      label: '是否上报',
      isRequired: true,
      render: () => (
        <Radio.Group>
          <Radio value={0}>否</Radio>
          <Radio value={1}>是</Radio>
        </Radio.Group>
      ),
    },
    {
      showOnAdd: true,
      name: 'remark',
      editOptinal: true,
      label: '备注',
      isRequired: true,
      render: () => <Input.TextArea autoSize style={{ width: 200 }} />,
    },
  ];

  async function doEdit(_vals: any) {
    const vals = _.pickBy(_vals, (v, index) => !_.isNil(v) && changedFields.includes(index));
    if (changedFields.length === 1 && changedFields[0] === 'event_type') {
      message.info('与原始数据一致');
      throw new Error();
    }
    if (vals.region_zones) {
      vals.zones = _.groupBy(vals?.region_zones ?? [], (v) => v.slice(0, -2));
    }
    return await editReportEvents({ ..._.omit(vals, ['region_zones']) });
  }

  async function doAdd(vals: any) {
    setChangedField([]);
    return await addReportEvents({
      ..._.omit(vals, ['region_zones']),
      zones: _.groupBy(vals?.region_zones ?? [], (v) => v.slice(0, -2)),
    });
  }
  async function getList(vals?: any) {
    setChangedField([]);
    const res = await getReportEvents({ ...vals });
    _.forEach(
      res.data?.list ?? [],
      (v: { zones: Record<string, string[]>; region_zones: string[] }) => {
        // eslint-disable-next-line no-param-reassign
        v.region_zones = _.reduce(
          v.zones,
          (result: string[], value) => {
            result = result.concat(value);
            return result;
          },
          [],
        );
      },
    );
    return res;
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={[
        {
          title: 'id',
          dataIndex: 'id',
          key: 'id',
          align: 'center',
        },
        {
          title: '事件名称(英文)',
          dataIndex: 'event_type',
          key: 'event_type',
          align: 'center',
        },
        {
          title: '事件名称(中文)',
          dataIndex: 'event_type_cn',
          key: 'event_type_cn',
          align: 'center',
        },
        {
          title: '服务状态',
          dataIndex: 'status',
          key: 'status',
          align: 'center',
          render: (status: number) => _.find(statusOptions, (v) => v.value === status)?.label,
        },
        {
          title: '站点类型',
          dataIndex: 'area',
          key: 'area',
          align: 'center',
          render: (area: number) => _.find(areaOptions, (v) => v.value === area)?.label,
        },
        {
          title: '地域信息-机房可用区',
          key: 'region_zones',
          align: 'center',
          render: ({ zones }: { regions: string[]; zones: Record<string, string[]> }) => {
            const treeData = _.map(zones ?? {}, (v, index) => {
              return {
                title: _.find(smsRegion, (item) => item.value === index)?.title,
                key: index,
                children: _.map(v ?? [], (zone) => ({
                  title: zone,
                  key: zone,
                })),
              };
            });
            return (
              <Tree showLine defaultExpandAll switcherIcon={<DownOutlined />} treeData={treeData} />
            );
          },
        },
        {
          title: '是否上报',
          dataIndex: 'is_report',
          key: 'is_report',
          align: 'center',
          render: (is_report: number) => _.find(yesOrNo, (v) => v.value === is_report)?.label,
        },
        {
          title: '备注',
          dataIndex: 'remark',
          key: 'remark',
          align: 'center',
          render: (remark: string) => <pre>{remark}</pre>,
        },
      ]}
      searchKeys={[]}
      operateForm={operateForm}
      operType={0}
      formProps={{
        onFieldsChange: (changedFields: any) => {
          if (changedFields?.[0]?.name?.[0]) {
            setChangedField((prev: string[]) => {
              const n: string[] = prev.concat(changedFields?.[0]?.name?.[0]);
              return [...new Set(n)];
            });
          }
        },
      }}
    />
  );
};

export default EventsOps;
