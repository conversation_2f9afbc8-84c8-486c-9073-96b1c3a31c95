import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Button,
  Table,
  Form,
  Input,
  message,
  Col,
  Row,
  DatePicker,
  Modal,
  Typography,
  Tooltip,
} from 'antd';
import { history } from 'umi';
import { ExclamationCircleTwoTone, SearchOutlined } from '@ant-design/icons';
import { useAsyncFn, useSetState } from 'react-use';
import {
  getProviderGroupBindList,
  unbindProviderGroup,
  editProviderGroupWeight,
  freezeProviderGroup,
  unfreezeProviderGroup,
} from '@/services/channel';
import { useDialogRef } from '@/utils/react-use/useDialog';

import AddChannelGroupBind from './component/AddChannelGroupBind';
import EditChannelGroupWeight from './EditChannelGroupWeight';
import { ModalForm, ProFormRadio, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { addOfflineForTestFalied, getPriceSubStatus } from '@/services/channelOffline';
import { site } from '@/const/const';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
const { Text } = Typography;

const ChannelGroupBindList = () => {
  const { regionOptions = [] } = useFetchCountryInfo();

  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const [pagination, setPagination] = useSetState({
    page_index: 1,
    page_size: 50,
  });
  const {
    searchKeys: passSearchKeys,
    group_id,
    name,
    country_code,
    operator_name,
    mnc,
  } = history.location?.state as any;
  const [searchKeys, setSearchKeys] = useState({});
  const [selectedRowKeys, setSelectKeys] = useState<(string | number)[]>([]);
  const dialogRef = useDialogRef();
  const weightRef = useDialogRef();
  const [freezeVisible, setFreezeVisible] = useState(false);

  const { value: subStatus } = useAsyncRetryFunc(async () => {
    const result = await getPriceSubStatus({});
    return (
      result?.data
        .filter((el: any) => el.status === 10)
        ?.map((el: any) => ({ label: el.sub_status_name, value: el.sub_status })) ?? {}
    );
  }, []);

  const [state, getList] = useAsyncFn(async () => {
    const res = await getProviderGroupBindList({
      ...pagination,
      ...searchKeys,
      group_id,
    });
    return res?.data || {};
  }, [group_id, pagination, searchKeys]);

  async function createOffline(values: any) {
    const res: any = await addOfflineForTestFalied({
      country_code,
      mnc: values.assign_operator ? mnc : '000',
      assign_operator: values.assign_operator,
      provider_id: values.provider_id,
      price_sub_status: values.price_sub_status,
      msg: values.msg,
    });
    if (res.code === 0 && !res.data.errors.length) {
      message.success('操作成功');
      getList();
      return true;
    }
    return false;
  }

  function onSubmit(vals: any) {
    Object.keys(vals).forEach((k) => {
      if (!vals[k]) delete vals[k];
    });
    setSearchKeys({ ...vals, page_index: 1 });
  }

  async function handleUnbind(bind_ids: string) {
    Modal.confirm({
      title: '提示',
      content: '确认解绑吗?',
      onOk: async () => {
        try {
          const res = await unbindProviderGroup({
            bind_ids,
            group_id: state.value?.list[0]?.group_id,
          });
          if (res.code === 0) {
            message.success('解绑成功');
            setSelectKeys([]);
            getList();
          }
        } catch (err) {
          console.log(err);
        }
      },
    });
  }

  async function handleFreeze(values: { cooling_time: Date }) {
    try {
      const res = await freezeProviderGroup({
        conf_ids: selectedRowKeys.join(','),
        cooling_time: values.cooling_time,
      });
      if (res.code === 0) {
        setFreezeVisible(false);
        message.success('冻结成功');
        setSelectKeys([]);
        getList();
      }
    } catch (err) {
      console.log(err);
    }
  }

  async function handleUnfreeze(bind_id?: number) {
    Modal.confirm({
      title: '提示',
      content: `确认解冻【${bind_id ?? selectedRowKeys.join(',')}】吗?`,
      onOk: async () => {
        try {
          const res = await unfreezeProviderGroup({
            conf_ids: bind_id ? `${bind_id}` : selectedRowKeys.join(','),
          });
          if (res.code === 0) {
            setFreezeVisible(false);
            message.success('解冻成功');
            setSelectKeys([]);
            getList();
          }
        } catch (err) {
          console.log(err);
        }
      },
    });
  }

  async function editWeight({
    bind_id,
    weight,
    group_id,
    monitor_threshold,
  }: {
    bind_id: number;
    weight: number;
    group_id: number;
    monitor_threshold: number;
  }) {
    const res: any = await editProviderGroupWeight({
      bind_id,
      weight,
      group_id,
      monitor_threshold,
    });
    if (res.code === 0) {
      message.success('调整成功');
    }
  }

  useEffect(() => {
    getList();
  }, [pagination, searchKeys]);

  const columns: any = [
    {
      title: '绑定id',
      dataIndex: 'bind_id',
      key: 'bind_id',
      align: 'center',
    },
    {
      title: '通道id',
      dataIndex: 'provider_id',
      key: 'provider_id',
      align: 'center',
    },
    {
      title: '通道名称',
      dataIndex: 'provider_name',
      key: 'provider_name',
      align: 'center',
    },
    {
      title: '权重',
      dataIndex: 'weight',
      key: 'weight',
      align: 'center',
    },
    {
      title: '重新调度阈值',
      dataIndex: 'monitor_threshold',
      key: 'monitor_threshold',
      align: 'center',
    },
    {
      title: '价格',
      key: 'price',
      align: 'center',
      render: (row: any) => `${row.price}(${row.price_curr})`,
    },
    {
      title: '调度cr',
      // dataIndex: 'scheduler_cr',
      key: 'scheduler_cr',
      align: 'center',
      render: (row: any) => (
        <Tooltip
          title={
            <>
              <p>report: {row.scheduler_cr?.report}</p>
              <p>total: {row.scheduler_cr?.total}</p>
            </>
          }
        >
          {typeof row.scheduler_cr === 'string' ? row.scheduler_cr : `${row.scheduler_cr?.cr}`}
          <ExclamationCircleTwoTone style={{ marginLeft: 4 }} />
        </Tooltip>
      ),
    },

    {
      title: 'cr',
      key: 'cr',
      align: 'center',
      render: (row: any) => (typeof row.cr === 'string' ? row.cr : `${row.cr?.cr}`),
    },
    {
      title: '冻结状态',
      dataIndex: 'cooling_flag',
      key: 'cooling_flag',
      align: 'center',
      render: (val) => (val ? <Text style={{ color: '#6eb0cb' }}>冻结</Text> : '未冻结'),
    },
    {
      title: '冻结时间',
      dataIndex: 'cooling_time',
      key: 'cooling_time',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      render: (row: any) =>
        window.location.host !== site.china && (
          <>
            <Button type="link" onClick={() => handleUnbind(`${row.bind_id}`)}>
              解绑
            </Button>
            <Button onClick={() => weightRef.current.open({ initialValues: row })} type="link">
              权重调整
            </Button>
            <ModalForm
              title="发起停用下线"
              trigger={<Button type="link">发起停用下线单</Button>}
              form={addForm}
              labelCol={{ span: 7 }}
              width={500}
              layout="horizontal"
              modalProps={{
                destroyOnClose: true,
                maskClosable: false,
              }}
              onFinish={async (values) => {
                return await createOffline({ ...values, provider_id: row.provider_id });
              }}
              initialValues={{
                assign_operator: 0,
              }}
            >
              <Form.Item label="供应商id">{row.provider_id}</Form.Item>
              <Form.Item label="国家">
                {regionOptions.find((el) => el.value === country_code)?.label}
              </Form.Item>
              <ProFormRadio.Group
                name="assign_operator"
                label="是否全网下线"
                options={[
                  {
                    label: '是',
                    value: 0,
                  },
                  {
                    label: '否',
                    value: 1,
                  },
                ]}
                rules={[{ required: true }]}
              />
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, curValues) =>
                  prevValues.assign_operator !== curValues.assign_operator
                }
              >
                {({ getFieldValue }) => {
                  const assignOperator = getFieldValue('assign_operator');
                  return assignOperator ? (
                    <Form.Item label="运营商">{operator_name}</Form.Item>
                  ) : null;
                }}
              </Form.Item>

              <ProFormSelect
                name="price_sub_status"
                options={subStatus}
                showSearch
                label="停用子状态"
                rules={[{ required: true }]}
              ></ProFormSelect>
              <ProFormText name="msg" label="备注"></ProFormText>
            </ModalForm>
            {!row?.cooling_flag ? (
              <Button
                type="link"
                onClick={() => {
                  setSelectKeys([row.bind_id]);
                  setFreezeVisible(true);
                }}
              >
                冻结通道
              </Button>
            ) : (
              <Button
                type="link"
                onClick={() => {
                  handleUnfreeze(row.bind_id);
                }}
              >
                解冻通道
              </Button>
            )}
          </>
        ),
    },
  ];

  return (
    <PageContainer>
      <Row justify="space-between">
        <Col>
          {window.location.host !== site.china && (
            <>
              <Button
                type="primary"
                style={{ marginBottom: 10 }}
                onClick={() => {
                  dialogRef.current.open({ initialValues: history.location?.state });
                }}
              >
                新增绑定
              </Button>
              <Button
                type="primary"
                style={{ margin: '0 0 10px 10px' }}
                disabled={!selectedRowKeys.length}
                onClick={() => {
                  handleUnbind(selectedRowKeys.join(','));
                }}
              >
                批量解绑
              </Button>
              <Button
                type="primary"
                style={{ margin: '0 0 10px 10px' }}
                disabled={
                  !selectedRowKeys.length ||
                  !(state.value?.list ?? [])
                    ?.filter((el: Record<string, any>) => selectedRowKeys.includes(el.bind_id))
                    ?.some((el: Record<string, any>) => !el.cooling_flag)
                }
                onClick={() => {
                  setFreezeVisible(true);
                }}
              >
                批量冻结
              </Button>
              <Button
                type="primary"
                style={{ margin: '0 0 10px 10px' }}
                disabled={
                  !selectedRowKeys.length ||
                  !(state.value?.list ?? [])
                    ?.filter((el: Record<string, any>) => selectedRowKeys.includes(el.bind_id))
                    ?.some((el: Record<string, any>) => !!el.cooling_flag)
                }
                onClick={() => handleUnfreeze()}
              >
                批量解冻
              </Button>
            </>
          )}
        </Col>
        <Col>
          <Button
            onClick={() => {
              history.push('/channel/group/list', {
                searchKeys: passSearchKeys,
              });
            }}
          >
            返回
          </Button>
        </Col>
      </Row>
      <Form
        className="sender-search-form"
        form={form}
        // initialValues={{ ...formData }}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item label="通道组id">{group_id}</Form.Item>
        <Form.Item label="通道组名称">{name}</Form.Item>
        <Form.Item name="provider_id" label="通道id">
          <Input placeholder="通道id" />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={state.value?.list}
        rowKey={(record: any) => record.bind_id}
        loading={state.loading}
        pagination={{
          defaultCurrent: 1,
          total: state.value?.count,
          showSizeChanger: true,
          pageSize: pagination.page_size,
          onShowSizeChange: (current, page_size) => setPagination({ page_size }),
          onChange: (page) => {
            setPagination({ page_index: page });
          },
        }}
        rowSelection={{
          selectedRowKeys,
          type: 'checkbox',
          onChange: (selectedRowKeys: React.Key[]) => {
            setSelectKeys(selectedRowKeys);
          },
        }}
      />
      <AddChannelGroupBind dialogRef={dialogRef} reload={getList} />
      <EditChannelGroupWeight dialogRef={weightRef} onSubmit={editWeight} onSuccess={getList} />
      <ModalForm
        title="冻结通道"
        layout="horizontal"
        open={freezeVisible}
        onFinish={handleFreeze}
        width={500}
        onOpenChange={(val) => {
          setFreezeVisible(val);
        }}
      >
        <Form.Item label="id">{selectedRowKeys.join(',')}</Form.Item>
        <Form.Item name="cooling_time" label="冻结截止时间" rules={[{ required: true }]}>
          <DatePicker showTime></DatePicker>
        </Form.Item>
      </ModalForm>
    </PageContainer>
  );
};

export default ChannelGroupBindList;
