import React, { useEffect, useMemo, useState } from 'react';
import { history } from 'umi';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, message, Popconfirm, Select } from 'antd';
import { useAsyncFn } from 'react-use';
import { useDialogRef } from '@/utils/react-use/useDialog';
import {
  getForceChannelList,
  editForceChannel,
  deleteForceChannel,
  getMccMncList,
} from '@/services/channel';
import AddForceChannelConfig from './component/AddForceChannelConfig';

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: any;
  // inputType: 'number' | 'text';
  record: any;
  // index: number;
  children: React.ReactNode;
}

const ChannelForceConfigList = () => {
  const [tableForm] = Form.useForm();

  const [startEdit, setStartEdit] = useState(false);
  const dialogRef = useDialogRef();
  const [editingKey, setEditingKey] = useState<any[]>([]);
  const isEditing = (record: any) => {
    return startEdit && editingKey.includes(record.key.toString());
  };
  const [initialValues, setInitialValues] = useState({});
  const pathParams: any = history.location.state?.row;
  const [mncList, setMncList] = useState([]);

  const columns: any = [
    {
      title: '运营商',
      // dataIndex: 'mnc',
      // key: 'mnc',
      align: 'center',
      render: (row: any) => (row.isParent ? '' : <>{row.operator_name}</>),
      width: 350,
    },
    {
      title: '通道名称',
      // dataIndex: 'channel_name',
      // key: 'channel_name',
      align: 'center',
      render: (row: any) => {
        return row.isParent === 1
          ? `${row.channel_name}_${row.supplier_name}(${row.provider_id})`
          : '';
      },
    },
    {
      title: '是否强制',
      // dataIndex: 'is_forced',
      // key: 'is_forced',
      align: 'center',
      editable: true,
      render: (row: any) =>
        row.isParent === 0 && row.is_forced !== undefined ? <>{row.is_forced ? '是' : '否'}</> : '',
    },
    {
      title: '操作',
      align: 'center',
      render: (row: any) =>
        row.isParent === 1 ? (
          <>
            <Popconfirm
              title="确认删除此条通道配置?"
              onConfirm={() => handleDelete(row.conf_id.toString())}
              okText="Yes"
              cancelText="No"
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          </>
        ) : (
          <>
            <Button
              type="link"
              onClick={() => {
                dialogRef.current.open({
                  initialValues: {
                    ...pathParams,
                    ...row,
                    is_forced: row.children[0]?.is_forced,
                  },
                });
              }}
            >
              添加通道
            </Button>
          </>
        ),
    },
  ];

  const [state, getList] = useAsyncFn(async () => {
    const res = await getForceChannelList({
      page_size: 1000,
      page_index: 1,
      obj_type: pathParams.obj_type,
      obj_id: pathParams.obj_id,
      mcc: pathParams?.mcc,
    });
    return res.data;
  }, []);

  async function getMncList() {
    const res = await getMccMncList({
      page_index: 1,
      page_size: 1000,
      mcc: pathParams?.mcc,
    });
    setMncList(res.data.list);
  }

  useEffect(() => {
    getList();
    getMncList();
  }, []);

  const list = useMemo(() => {
    const formValues = state.value?.list?.reduce((pre = {}, cur: any) => {
      pre[`is_forced_${cur.mnc}`] = String(cur.is_forced);
      return pre;
    }, {});
    setInitialValues(formValues); // 编辑表单初始值
    return mncList?.map((el: any) => ({
      ...el,
      isParent: 0,
      key: el.mnc,
      is_forced: state.value?.list?.find((c: any) => c.mnc === el.mnc)?.is_forced,
      children:
        state.value?.list
          ?.filter((c: any) => c.mnc === el.mnc)
          .map((d: any) => ({ ...d, isParent: 1, key: `${d.mcc}_${d.mnc}_${d.conf_id}` })) || [],
    }));
  }, [state.value?.list, mncList]);

  async function handleDelete(conf_ids: string) {
    try {
      const params = {
        conf_ids,
      };
      const res = await deleteForceChannel(params);
      if (res.code === 0) {
        message.success('删除成功');
        getList();
        setStartEdit(false);
        setEditingKey([]);
      }
    } catch (err) {
      message.error('删除失败');
      console.log(err);
    }
  }

  useEffect(() => {
    getList();
  }, []);

  const rowSelection = {
    checkStrictly: false,
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
    selectedRowKeys: editingKey,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      const keys = selectedRows.map((e: any) => e.key);
      if (!keys.length) {
        setStartEdit(false);
      }
      setEditingKey(keys);
    },
  };

  const EditableCell: React.FC<EditableCellProps> = function ({
    editing,
    // dataIndex,
    title,
    // inputType,
    record,
    // index,
    children,
    ...restProps
  }) {
    return (
      <td {...restProps}>
        {editing && record ? (
          <Form.Item
            name={`is_forced_${record.mnc}`}
            style={{ margin: 0 }}
            rules={[
              {
                required: !record.isParent,
                message: `请输入 ${title}!`,
              },
            ]}
          >
            {!record.isParent ? (
              <Select
                style={{ width: 80 }}
                options={[
                  {
                    value: '1',
                    label: '是',
                  },
                  {
                    value: '0',
                    label: '否',
                  },
                ]}
              />
            ) : undefined}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const mergedColumns = columns.map((col: any) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: any) => ({
        record,
        inputType: 'number',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  async function submit() {
    const data = await doFormatData();
    if (!data.length) {
      return message.error('没有做任何改动');
    }
    try {
      const res = await editForceChannel({ params: data });
      if (res.code === 0 && !res.msg?.errors?.length) {
        getList();
        setEditingKey([]);
        return message.success('编辑成功');
      }
    } catch (err) {
      message.success('编辑失败');
    }
  }

  async function doFormatData() {
    if (!editingKey.length) {
      return message.error('请先选择编辑项');
    }
    const vals = await tableForm.validateFields();
    Object.keys(vals).forEach((k) => {
      if (vals[k] === undefined) {
        delete vals[k];
      }
    });
    // [k, v]
    let changed: any = [];
    Object.entries(vals).forEach((cur) => {
      const mnc = cur[0].split('_')[2];
      const is_forced = cur[1];
      changed = state.value?.list
        ?.filter((el: any) => el.mnc === mnc && String(el.is_forced) !== String(is_forced))
        .map((c: any) => ({ conf_id: c.conf_id, is_forced }));
    });
    return changed;
  }

  return (
    <PageContainer>
      <Button
        type="link"
        onClick={() => history.push(`/channel/force?from=1`, history.location.state)}
      >
        返回上一页
      </Button>
      <Form layout="inline" labelAlign="right">
        <Form.Item label="调度对象类型">
          <span>{pathParams.obj_type}</span>
        </Form.Item>
        <Form.Item label="调度对象">
          <span>{pathParams.obj_id}</span>
        </Form.Item>
        <Form.Item label="国家/地区">
          <span>{pathParams?.country_name}</span>
        </Form.Item>
      </Form>
      <div style={{ margin: '20px 0', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          {!startEdit || !editingKey.length ? (
            <Button
              disabled={editingKey.length === 0}
              onClick={() => {
                const conf_ids = editingKey
                  .map((el) => el.split('_')[2])
                  .filter((el) => !!el)
                  .join(',');
                if (!conf_ids) {
                  message.error('至少选择一项');
                  return;
                }
                setStartEdit(true);
                tableForm.setFieldsValue(initialValues);
              }}
            >
              批量编辑
            </Button>
          ) : (
            <Button onClick={submit} type="primary">
              提交编辑
            </Button>
          )}
          <Popconfirm
            title="确认清空此筛选条件下的配置吗，清空后不可恢复?"
            onConfirm={() => {
              const conf_ids = editingKey
                .map((el) => el.split('_')[2])
                .filter((el) => !!el)
                .join(',');
              if (!conf_ids) {
                message.error('至少选择一项');
                return;
              }
              handleDelete(conf_ids);
            }}
            okText="Yes"
            cancelText="No"
          >
            <Button disabled={editingKey.length === 0} danger style={{ marginLeft: 10 }}>
              批量删除
            </Button>
          </Popconfirm>
        </div>
        <div>
          <Button
            onClick={() => {
              dialogRef.current.open({
                initialValues: {
                  ...pathParams,
                  mnc: undefined,
                  is_forced: undefined,
                  addType: 'other',
                },
              });
            }}
          >
            新增配置
          </Button>
        </div>
      </div>
      <Form form={tableForm} component={false}>
        <Table
          columns={mergedColumns}
          dataSource={list}
          components={{
            body: {
              cell: EditableCell,
            },
          }}
          size="middle"
          rowKey={(record: any) => record.key}
          loading={state.loading}
          rowSelection={{
            type: 'checkbox',
            ...rowSelection,
          }}
          pagination={false}
          expandable={{
            expandedRowKeys: list?.map((el: any) => el.key),
          }}
        />
      </Form>
      <AddForceChannelConfig dialogRef={dialogRef} reload={getList} />
    </PageContainer>
  );
};

export default ChannelForceConfigList;
