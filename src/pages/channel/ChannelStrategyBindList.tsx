import React, { useMemo, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Button,
  Table,
  Form,
  message,
  Popconfirm,
  InputNumber,
  Col,
  Row,
  TableColumnsType,
  Tooltip,
} from 'antd';
import { Navigate, history } from 'umi';
import { ExclamationCircleTwoTone, SearchOutlined } from '@ant-design/icons';
import { useSetState } from 'react-use';
import {
  getProviderStrategyBindList,
  unbindProviderGroupStrategy,
} from '@/services/channelStrategy';
import { useDialogRef } from '@/utils/react-use/useDialog';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import AddChannelGroupStrategyBind from './component/AddChannelGroupStrategyBind';
import EditChannelStrategyWeight from './component/EditChannelStrategyWeight';
import _ from 'lodash';
import { isMobile } from '@/const/jadgeUserAgent';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { site } from '@/const/const';

const ChannelStrategyBindList = () => {
  const [form] = Form.useForm();
  const { searchKeys: passSearchKeys, row = {} } = history.location.state as {
    row: any;
    searchKeys: Record<string, any>;
  };
  const { tactic_id, name: tactic_name, category } = row;
  const [searchKeys, setSearchKeys] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [selectedRowKeys, setSelectKeys] = useState<(string | number)[]>([]);
  const dialogRef = useDialogRef();
  const weightRef = useDialogRef();
  const [mccMncInfo] = useMccMncInfo();

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const result = await getProviderStrategyBindList({
      ..._.pickBy(searchKeys, (v) => v !== null),
      tactic_id,
    });
    return result?.data ?? {};
  }, [searchKeys, tactic_id]);

  const list = useMemo(() => {
    return {
      parent: _.uniqBy(state?.list ?? [], 'mnc'),
      children: _.groupBy(state?.list ?? [], 'mnc'),
    };
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const firstColumns = useMemo(() => {
    const m =
      category === 1
        ? {
            title: '市占比',
            dataIndex: 'market_share_map',
            key: 'market_share_map',
          }
        : {};
    return [
      {
        title: '运营商',
        key: 'mnc',
        render: (row: any) => {
          const { operator_name, mnc } = _.find(
            mccMncInfo,
            (v) => v.country_code === row.country_code && v.mnc === row.mnc,
          ) ?? {
            operator_name: '',
            mnc: row.mnc,
          };
          return `${operator_name}(${mnc})`;
        },
      },
      m,
    ];
  }, [mccMncInfo, category]);

  function onSubmit(vals: any) {
    setSearchKeys({ ...vals, page_index: 1 });
  }

  async function handleUnbind(bind_ids: string) {
    try {
      const res = await unbindProviderGroupStrategy({
        bind_ids,
        tactic_id,
      });
      if (res.code === 0) {
        message.success('解绑成功');
        retry();
      }
    } catch (err) {
      message.error('解绑失败');
      console.log(err);
    }
  }

  if (!tactic_id) {
    return <Navigate to="channel-strategy-list" />;
  }

  const expandedRowRender = (record: Record<string, any>) => {
    const columns: TableColumnsType<any> = [
      {
        title: '绑定id',
        dataIndex: 'bind_id',
        key: 'bind_id',
        align: 'center',
      },
      {
        title: '通道id',
        dataIndex: 'provider_id',
        key: 'provider_id',
        align: 'center',
      },
      {
        title: '通道名称',
        dataIndex: 'account_name',
        key: 'account_name',
        align: 'center',
      },
      {
        title: '供应商名称',
        dataIndex: 'supplier_name',
        key: 'supplier_name',
        align: 'center',
      },
      {
        title: '调度cr',
        // dataIndex: 'scheduler_cr',
        key: 'scheduler_cr',
        align: 'center',
        render: (row: any) => (
          <Tooltip
            title={
              <>
                <p>report: {row.scheduler_cr?.report}</p>
                <p>total: {row.scheduler_cr?.total}</p>
              </>
            }
          >
            {typeof row.scheduler_cr === 'string' ? row.scheduler_cr : `${row.scheduler_cr?.cr}`}
            <ExclamationCircleTwoTone style={{ marginLeft: 4 }} />
          </Tooltip>
        ),
      },

      {
        title: 'cr',
        key: 'cr',
        align: 'center',
        render: (row: any) => (typeof row.cr === 'string' ? row.cr : `${row.cr?.cr}`),
      },
      {
        title: '价格',
        key: 'price',
        align: 'center',
        render: (row: any) => `${row.price}(${row.price_curr})`,
      },
      {
        title: '权重',
        dataIndex: 'weight',
        key: 'weight',
        align: 'center',
      },
      {
        title: '操作',
        align: 'center',
        render: (row: any) =>
          window.location.host !== site.china ? (
            <>
              <Popconfirm
                title="确认解绑吗？"
                onConfirm={() => handleUnbind(`${row.bind_id}`)}
                okText="Yes"
                cancelText="No"
              >
                <Button type="link">解绑</Button>
              </Popconfirm>
              <Button onClick={() => weightRef.current.open({ initialValues: row })}>
                权重调整
              </Button>
            </>
          ) : null,
      },
    ];

    return (
      <Table
        columns={columns}
        pagination={false}
        dataSource={list?.children?.[record.mnc]}
        rowKey={(record: any) => record.bind_id}
        rowSelection={{
          type: 'checkbox',
          onChange: (selectedRowKeys: React.Key[]) => {
            setSelectKeys(selectedRowKeys);
          },
        }}
      />
    );
  };

  return (
    <PageContainer>
      <Row justify="space-between">
        {window.location.host !== site.china ? (
          <Col>
            <Button
              type="primary"
              style={{ marginBottom: 10 }}
              onClick={() => {
                dialogRef.current.open({
                  initialValues: row,
                });
              }}
            >
              新增绑定
            </Button>
            <Button
              type="primary"
              style={{ margin: '0 0 10px 10px' }}
              disabled={!selectedRowKeys.length}
              onClick={() => {
                handleUnbind(selectedRowKeys.join(','));
              }}
            >
              批量解绑
            </Button>
          </Col>
        ) : (
          <Col></Col>
        )}
        <Col>
          <Button
            onClick={() => {
              history.push('/channel/strategy/list', { searchKeys: passSearchKeys });
            }}
          >
            返回
          </Button>
        </Col>
      </Row>

      <Form
        className="sender-search-form"
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item label="通道策略id">{tactic_id}</Form.Item>
        <Form.Item label="通道策略名称">{tactic_name}</Form.Item>
        <Form.Item name="bind_id" label="绑定id">
          <InputNumber />
        </Form.Item>
        <Form.Item name="provider_id" label="通道id">
          <InputNumber />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={firstColumns}
        dataSource={list.parent ?? []}
        rowKey="mnc"
        loading={loading}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        pagination={{
          defaultCurrent: 1,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
        expandable={{ expandedRowRender, expandedRowKeys: list?.parent.map((el: any) => el.mnc) }}
      />
      <AddChannelGroupStrategyBind dialogRef={dialogRef} reload={retry} />
      <EditChannelStrategyWeight dialogRef={weightRef} onSuccess={retry} />
    </PageContainer>
  );
};

export default ChannelStrategyBindList;
