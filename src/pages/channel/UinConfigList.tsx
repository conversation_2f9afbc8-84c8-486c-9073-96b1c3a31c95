import React from 'react';
import { history } from 'umi';
import { Form } from 'antd';

import {
  getUinChannelList,
  editUinChannel,
  deleteUinChannel,
  addUinChannel,
} from '@/services/channel';

import UinChannelConfigAdd from './component/AddUinChannelConfig';
import ChannelConfigList from './component/ConfigList';

const UinChannelConfig = () => {
  const pathParams: any = (history.location.state as { row?: Record<string, any> })?.row;

  return (
    <ChannelConfigList
      obj="uin"
      getListFn={(params) => getUinChannelList({ ...params, uin: pathParams.uin })}
      editFn={(params) => editUinChannel({ ...params, uin: pathParams.uin })}
      deleteFn={(params) => deleteUinChannel({ ...params, uin: pathParams.uin })}
      addFn={(params) => {
        const data = params.map((el: any) => ({ ...el, uin: pathParams.uin }));
        return addUinChannel({
          params: data,
        });
      }}
      addComponent={(dialogRef, getList) => (
        <UinChannelConfigAdd dialogRef={dialogRef} reload={getList} />
      )}
      formItem={
        <Form.Item label="uin">
          <span>{pathParams.uin}</span>
        </Form.Item>
      }
    />
  );
};

export default UinChannelConfig;
