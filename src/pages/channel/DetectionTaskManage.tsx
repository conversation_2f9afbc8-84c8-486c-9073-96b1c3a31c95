import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal, Tag, Typography } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import { isMobile } from '@/const/jadgeUserAgent';
import _, { isArray } from 'lodash';
import {
  addDetectionTask,
  deleteDetectionTask,
  getDetectionTaskList,
  getTelqMccMncList,
  setDetectionTask,
} from '@/services/channelDetectionTask';
import { findLabel } from '@/utils/utils';
import { channelType } from './component/const';
import ChannelSelect from './commonComponent/ChannelSelect';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { history } from 'umi';
import { useAsyncFn } from 'react-use';
import SelectAll from '@/components/SelectAll';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const { Text } = Typography;
const canEditKeys = ['task_name', 'start_time', 'frequency', 'tactic_type'];

export type DataItem = {
  name: string;
  state: string;
};

const detctionType = [
  { label: '滚动探测', value: 0 },
  { label: '全部探测', value: 1 },
];

const allFields = [
  'task_name',
  'channel_type',
  'account_id',
  'country_code',
  'mnc_info',
  'sender',
  'msg',
  'start_time',
  'frequency',
  'enable',
  'tactic_type',
];

function channelSelectRender(formRef: any) {
  return (
    <ChannelSelect
      mode="default"
      value={formRef.current?.getFieldValue('account_id')}
      reloadOn={
        formRef.current?.getFieldValue('channel_type') === 0
          ? ''
          : `${formRef.current?.getFieldValue('channel_type')}}`
      }
      type={formRef.current?.getFieldValue('channel_type')}
      onChange={(value) => {
        formRef.current?.setFieldsValue({ account_id: value });
      }}
      pageSize={1000}
      // placeholder={FlagType[typeValue]}
    />
  );
}

const DetectionTaskManage = () => {
  const { regionOptions = [], regionList = [] } = useFetchCountryInfo();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const [mccMncInfo] = useMccMncInfo();
  const [open, setOpen] = useState(false);
  const [type, setType] = useState('create');
  const [initialValues, setInitialValues] = useState<any>({});

  const [telqMccMnc, fetchTelqMccMnc] = useAsyncFn(async (mcc?: string) => {
    const res = await getTelqMccMncList({ mcc: mcc?.split('_')[1] });
    return isArray(res.data) ? res.data : [];
  }, []);

  useEffect(() => {
    fetchTelqMccMnc();
  }, [fetchTelqMccMnc]);

  function handleDelete(row: any) {
    Modal.confirm({
      title: '提示',
      content: '确定删除该任务吗?',
      onOk: async () => {
        const res = await deleteDetectionTask({
          task_id: row.task_id,
        });
        if (res.code === 0) {
          actionRef.current?.reload();
        }
      },
    });
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: '任务id',
        dataIndex: 'task_id',
        key: 'task_id',
      },
      {
        title: '任务名称',
        dataIndex: 'task_name',
        key: 'task_name',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        title: '通道类型',
        dataIndex: 'channel_type',
        key: 'channel_type',
        render: (text, row: any) => findLabel(channelType, row.channel_type),
        valueType: 'select',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          options: channelType,
        },
      },
      {
        title: '账号id',
        // dataIndex: 'account_id',
        key: 'account_id',
        valueType: 'select',
        render: (text, row: any) => `${row.account_name}(${row.account_id})`,
        renderFormItem: () => channelSelectRender(formRef),
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        title: '国家',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        render: (text, row: any) => findLabel(regionOptions, row.country_code),
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          options: regionOptions,
          showSearch: true,
        },
      },
      {
        title: '运营商信息',
        dataIndex: 'mnc_info',
        key: 'mnc_info',
        hideInSearch: true,
        valueType: 'select',
        renderFormItem: () => (
          <SelectAll
            options={telqMccMnc.value?.map((el) => ({
              label: `${el.operator}(${el.mnc})`,
              value: `${el.mcc}_${el.mnc}`,
            }))}
          ></SelectAll>
        ),
        render: (text, row: any) => {
          return (
            <Text
              // style={ellipsis ?  : undefined}
              ellipsis={{ tooltip: true }}
            >
              {_.map(row.mnc_info, (mnc) => {
                const name = _.find(mccMncInfo, (v) => v.mnc === mnc)?.operator_name;
                return `${name}(${mnc})`;
              }).join(', ')}
            </Text>
          );
        },
        formItemProps: {
          label: '运营商',
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          mode: 'multiple',
        },
      },
      {
        title: '探测sender',
        dataIndex: 'sender',
        key: 'sender',
        formItemProps: {
          rules: [{ required: true }],
        },
      },
      {
        title: '探测内容',
        dataIndex: 'msg',
        key: 'msg',
        formItemProps: {
          rules: [
            {
              required: true,
            },
            {
              validator: (_, value: string) => {
                if (!value?.includes('${testIdText}')) {
                  return Promise.reject('内容格式错误');
                }
                return Promise.resolve();
              },
            },
          ],
        },
      },

      {
        title: '开始时间',
        dataIndex: 'start_time',
        key: 'start_time',
        hideInSearch: true,
        valueType: 'dateTime',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          minuteStep: 10,
          secondStep: 60,
        },
      },
      {
        title: '探测频率(h/次)',
        dataIndex: 'frequency',
        key: 'frequency',
        hideInSearch: true,
        valueType: 'digit',
        formItemProps: {
          label: '探测频率',
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          controls: true,
          precision: 0,
        },
      },
      {
        title: '探测策略',
        dataIndex: 'tactic_type',
        key: 'tactic_type',
        hideInSearch: true,
        render: (text, row: any) => findLabel(detctionType, row.tactic_type),
        valueType: 'select',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: { options: detctionType },
      },
      {
        title: '创建人',
        dataIndex: 'create_rtx',
        key: 'create_rtx',
        hideInSearch: true,
      },
      {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time',
        hideInSearch: true,
      },
      {
        title: '更新时间',
        dataIndex: 'update_time',
        key: 'update_time',
        hideInSearch: true,
      },
      {
        title: '状态',
        dataIndex: 'enable',
        key: 'enable',
        hideInSearch: true,
        valueType: 'switch',
        render: (text, row: any) =>
          row.enable ? <Tag color="green">开启</Tag> : <Tag color="red">关闭</Tag>,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        width: 150,
        render: (text, row: any) => {
          return (
            <>
              <Button
                type="link"
                onClick={() => {
                  handleEnable(row);
                }}
              >
                {row.enable ? '关闭' : '开启'}任务
              </Button>

              <Button
                type="link"
                onClick={() => {
                  setOpen(true);
                  setType('edit');
                  // 弹窗表单未初始化，addFormRef为空
                  !addFormRef.current && setInitialValues(row);
                  const vals = _.pick(row, canEditKeys);
                  addFormRef.current?.setFieldsValue(vals);
                }}
              >
                编辑
              </Button>
              <Button
                type="link"
                onClick={() => {
                  handleDelete(row);
                }}
              >
                删除
              </Button>
              <Button
                type="link"
                onClick={() => {
                  history.push('/channel/detection/detail', {
                    row,
                  });
                }}
              >
                详情
              </Button>
            </>
          );
        },
      },
    ];
  }, [mccMncInfo, regionOptions, telqMccMnc.value]);

  const formItems: any = columns
    .filter((el: any) => allFields.includes(el.key))
    .map((el) => {
      if (el.key === 'msg') {
        return {
          ...el,
          formItemProps: {
            ...el.formItemProps,
            extra: '内容必须包含${testIdText}',
          },
        };
      }
      if (el.key === 'account_id') {
        return {
          ...el,
          renderFormItem: (text, row: any) => channelSelectRender(addFormRef),
        };
      }
      if (el.key === 'frequency') {
        return {
          ...el,
          formItemProps: {
            ...el.formItemProps,
            addonAfter: 'h/次',
          },
        };
      }
      return el;
    });

  const editFormItems: any = formItems.filter((el) => canEditKeys.includes(el.key));

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getDetectionTaskList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'edit'
          ? await setDetectionTask({ ...vals, task_id: initialValues?.task_id })
          : await addDetectionTask({
              ...vals,
              country_code: vals.country_code,
              mcc: regionList.find((v) => v.nation_code === vals.country_code)?.mcc,
              mnc_info: vals.mnc_info.map((el) => el.split('_')[1]),
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        setOpen(false);
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  async function handleEnable(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定${row.enable ? '关闭' : '开启'}该任务吗?`,
        onOk: async () => {
          const res = await setDetectionTask({
            task_id: row.task_id,
            enable: row.enable ? 0 : 1,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }

  function reset() {
    const vals = _.reduce(
      allFields,
      (acc: { [key: string]: any }, key: string) => ({
        ...acc,
        [key]: undefined,
      }),
      {},
    );
    addFormRef.current?.setFieldsValue({ ...vals });
  }

  return (
    <PageContainer>
      <BetaSchemaForm<DataItem>
        formRef={addFormRef}
        layoutType="ModalForm"
        open={open}
        onOpenChange={setOpen}
        title={type === 'edit' ? '编辑探测任务' : '新增探测任务'}
        layout="horizontal"
        labelCol={{ span: 4 }}
        onFinish={onFinish}
        columns={type === 'edit' ? editFormItems : formItems}
        width={600}
        onValuesChange={(changeValues) => {
          if (changeValues.country_code) {
            fetchTelqMccMnc(changeValues.country_code);
            addFormRef.current.setFieldsValue({ mnc_info: undefined });
          }
        }}
        initialValues={_.pick(initialValues, canEditKeys)}
      />
      <Button
        type="primary"
        onClick={() => {
          reset();
          setOpen(true);
          setType('create');
        }}
      >
        新增探测任务
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="task_id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 2000 }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default DetectionTaskManage;
