import React from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Form, Input, Select } from 'antd';
import { getForceConfigCountry, deleteForceChannel } from '@/services/channel';
import CountryList from './component/CountryList';

const ForceChannelCountryList = () => {
  // const [searchKeys, setSearchKeys] = useSetState({ sdkappid: '' });
  return (
    <PageContainer>
      <CountryList
        obj="force"
        getListFn={getForceConfigCountry}
        deleteFn={deleteForceChannel}
        searchFormItem={
          <>
            <Form.Item name="obj_type" rules={[{ required: true }]}>
              <Select
                placeholder="调度对象类型"
                options={[
                  {
                    label: 'uin',
                    value: 'uin',
                  },
                  {
                    label: 'sdkappid',
                    value: 'sdkappid',
                  },
                ]}
              />
            </Form.Item>
            <Form.Item name="obj_id" rules={[{ required: true }]}>
              <Input placeholder="uin/sdkappid" />
            </Form.Item>
          </>
        }
      />
    </PageContainer>
  );
};

export default ForceChannelCountryList;
