import React, { useEffect, useState } from 'react';
import { Modal, Button, InputNumber, Form, message } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog, useDialogRef } from '@/utils/react-use/useDialog';
import { history } from 'umi';
import { checkGroupPrice } from '@/services/channel';
import { bindProviderGroupStrategy } from '@/services/channelStrategy';
import CheckChannelAttributesOrPriceModal from './CheckChannelAttributesOrPriceModal';
import MncSelect from '../commonComponent/MncSelect';
import ChannelSelect from '../commonComponent/ChannelSelect';
import _ from 'lodash';
import { errorsObj } from '@/pages/global-components/BatchErrorModal';

interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
}

const AddChannelGroupStrategyBind = (props: DialogProps) => {
  const [form] = Form.useForm();
  const historyTacticId = (history.location.state as { tactic_id?: number })?.tactic_id;
  const { dialogRef, reload } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const tacticAttributesDialogRef = useDialogRef();
  const { initialValues } = defaultVal;
  const [loading, setLoading] = useState(false);
  const [accountNoCheck, setAccountNoCheck] = useState<any[number]>([]);

  async function onFinish(vals: any) {
    try {
      const tacticId = historyTacticId ?? initialValues?.tactic_id;
      const provider_id = vals.provider_id
        .map((account_id: number | string) => `${tacticId}_${account_id}`)
        .filter((c: number) => !accountNoCheck.includes(c));
      if (provider_id.length === 0) {
        return message.error('请至少选择一个通道');
      }
      setLoading(true);

      const res = await bindProviderGroupStrategy({
        params: provider_id.map((c: string) => ({
          ...vals,
          provider_id: Number(c.split('_')[1]),
          tactic_id: tacticId,
          mnc: vals.mnc.split('_')[1],
        })),
      });
      setLoading(false);
      setVisible(false);
      if (res.code === 0 && !res.msg?.errors?.length) {
        reload();
        tacticAttributesDialogRef.current.close();

        return message.success('绑定成功');
      }
    } catch (err) {
      setLoading(false);
      return message.error('绑定失败');
    }
  }

  async function checkPrice() {
    setLoading(true);
    try {
      const response = await checkGroupPrice({
        params: [
          {
            channel_id: historyTacticId ?? initialValues?.tactic_id,
            channel_type: 2,
            account_ids: form.getFieldValue('provider_id')?.join(','),
          },
        ],
      });
      const errorData = response.data.filter((el) => el.code !== 0);
      if (errorData.length) {
        errorsObj.setVisible(true, errorData);
        setLoading(false);
        return;
      }
      setLoading(false);
      // setCheckResult(res.data.check_infos);
      const checkInfos = _.flatMap(response.data, (res) =>
        Object.keys(res.data.check_infos).map((k) => ({
          group_id: res.param.channel_id,
          account_id: Number(k),
          ...res.data.check_infos[k],
        })),
      );
      const needCheck = checkInfos?.filter(
        (el: any) => el?.diff_keys?.length !== 0 || !el.price_id,
      );
      if (needCheck.length === 0) {
        return form.submit();
      }
      tacticAttributesDialogRef.current.open({
        initialValues: needCheck,
        type: 2,
      });
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
    // form.submit();
  }

  useEffect(() => {
    form.setFieldsValue({
      mnc: initialValues?.mnc === '000' ? undefined : `${initialValues?.mcc}_${initialValues?.mnc}`,
    });
  }, [form, initialValues]);

  return (
    <>
      <Modal
        title="绑定"
        open={visible}
        key={initialValues?.tactic_id}
        footer={null}
        onCancel={() => setVisible(false)}
        destroyOnClose={true}
      >
        <Form
          className="sender-search-form"
          labelCol={{ span: 7 }}
          form={form}
          labelAlign="left"
          onFinish={(vals) => onFinish(vals)}
          initialValues={{
            provider_id: initialValues?.provider_id,
          }}
        >
          <Form.Item label="通道策略id">
            <span>{historyTacticId ?? initialValues?.tactic_id}</span>
          </Form.Item>
          <Form.Item name="mnc" label="运营商" labelCol={{ span: 7 }} rules={[{ required: true }]}>
            <MncSelect
              initialValues={{ mcc: initialValues?.mcc }}
              onChange={(mnc: any) => form.setFieldsValue({ mnc })}
              value={form.getFieldValue('mnc')}
              disabled={initialValues?.mnc !== '000'}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.tactic_id !== curValues.tactic_id}
          >
            {({ getFieldValue, setFieldsValue }) => {
              return (
                <Form.Item name="provider_id" label="通道id" rules={[{ required: true }]}>
                  <ChannelSelect
                    value={getFieldValue('provider_id')}
                    type={0}
                    onChange={(value) => {
                      setFieldsValue({ provider_id: value });
                    }}
                    pageSize={1000}
                    reloadOn={getFieldValue('mnc')}
                    loadFnParams={{
                      0: {
                        route_country_mnc: `0-${initialValues?.country_code}-${
                          getFieldValue('mnc')?.split('_')[1] || '000'
                        }`,
                      },
                    }}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item name="weight" label="权重" rules={[{ required: true }]}>
            <InputNumber style={{ width: 320 }} />
          </Form.Item>
          <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
            <Button onClick={checkPrice} type="primary" loading={loading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      <CheckChannelAttributesOrPriceModal
        dialogRef={tacticAttributesDialogRef}
        continueSubmit={(noCheck: any) => {
          setAccountNoCheck(noCheck);
          form.submit();
        }}
        loading={loading}
      />
    </>
  );
};

export default AddChannelGroupStrategyBind;
