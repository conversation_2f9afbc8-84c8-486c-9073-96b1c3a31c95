import { ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { getGlobalChannelList, getSdkChannelList, getUinChannelList } from '@/services/channel';
import { channelType } from './const';
import { Button, Modal, Spin, Table, Tabs, TabsProps, Typography } from 'antd';
import { DialogRef, useDialog, useDialogRef } from '@/utils/react-use/useDialog';
import { useAsyncFn } from 'react-use';
import { getAppInfo } from '@/services/scdAPI';
import { getOperatorName } from '@/utils/utils';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import CpqTacticOperateDialog from './CpqTacticOperateDialog';
import { getProviderStrategyBindList } from '@/services/channelStrategy';
import { InfoCircleTwoTone } from '@ant-design/icons';
const { Title } = Typography;

interface Props {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export default function CpqTacticScheduleDialog(props: Props) {
  const { onSuccess, dialogRef } = props;
  const operateDialogRef = useDialogRef();

  const [mccMncInfo] = useMccMncInfo();
  const [visible, setVisible, defaultValue] = useDialog<{ initialValues: any }>(dialogRef);
  const { initialValues } = defaultValue;
  const [tabs, setTabs] = useState<any>([]);
  const [items, setItems] = useState<any>([]);

  const [tacticChannelList, fetchTacticChannelList] = useAsyncFn(async () => {
    const res = await getProviderStrategyBindList({
      page_index: 1,
      page_size: 1000,
      tactic_id: initialValues?.tactic_id,
    });
    return (
      res.data?.list.map((el) => ({
        ...el,
        tactic_price: initialValues?.price,
        tactic_cost_price: initialValues?.cost_price,
        tactic_price_curr: initialValues?.price_curr,
      })) ?? []
    );
  }, [initialValues]);

  const [sdkappidList, fetchSdkappidList] = useAsyncFn(async () => {
    const res = await getAppInfo({
      uin: initialValues?.uin,
      page_index: 1,
      page_size: 1000,
    });
    return res.data.list.map((el) => el.sdkappid) ?? [];
  }, [initialValues?.uin]);

  const groupTacticChannelList = useMemo(() => {
    return _.groupBy(tacticChannelList?.value, 'mnc');
  }, [tacticChannelList]);

  const columns: any = useMemo(() => {
    return [
      {
        title: '运营商',
        key: 'mnc',
        onCell: (row: any, index) => {
          const len = groupTacticChannelList[row.mnc]?.length;
          return {
            rowSpan: index % len === 0 ? len : 0,
          };
        },
        render: (row) => getOperatorName(mccMncInfo, row.mnc, { mcc: row.mcc }),
      },
      {
        title: '通道id',
        dataIndex: 'provider_id',
        key: 'provider_id',
      },
      {
        title: '通道名称',
        dataIndex: 'account_name',
        key: 'account_name',
      },
      {
        title: '供应商名称',
        dataIndex: 'supplier_name',
        key: 'supplier_name',
      },
      {
        title: '通道成本',
        key: 'price',
        render: (row: any) => `${row.price}(${row.price_curr})`,
      },
      {
        title: '通道权重',
        dataIndex: 'weight',
        key: 'weight',
      },
      {
        title: '标品成本',
        key: 'tactic_cost_price',
        render: (row: any) => `${Number(row.price).toFixed(6)}(${row.tactic_price_curr})`,
        onCell: (row: any, index: number) => ({
          rowSpan: index === 0 ? tacticChannelList?.value?.length : 0,
        }),
      },
      {
        title: '销售价格',
        key: 'tactic_price',
        render: (row: any) => `${Number(row.price).toFixed(6)}(${row.tactic_price_curr})`,
        onCell: (row: any, index: number) => ({
          rowSpan: index === 0 ? tacticChannelList?.value?.length : 0,
        }),
      },
    ];
  }, [groupTacticChannelList, mccMncInfo, tacticChannelList?.value?.length]);

  const getScheduleFn = useCallback(
    async (params: any, item: { id: number; type: string }) => {
      const { data } =
        item.type === 'sdkappid'
          ? await getSdkChannelList({
              sdkappid: item.id,
              mcc: initialValues?.mcc,
              sms_type: initialValues?.sms_type,
              page_index: 1,
              page_size: 1000,
            })
          : item.type === 'uin'
          ? await getUinChannelList({
              uin: item.id,
              mcc: initialValues?.mcc,
              sms_type: initialValues?.sms_type,
              page_index: 1,
              page_size: 1000,
            })
          : await getGlobalChannelList({
              mcc: initialValues?.mcc,
              page_index: 1,
              page_size: 1000,
            });
      return {
        data: data.list,
        success: true,
      };
    },
    [initialValues?.mcc, initialValues?.sms_type],
  );

  const handleConfirmBind = useCallback(
    (row: any[]) => {
      operateDialogRef.current.open({
        initialValues: row,
        status: 1,
      });
    },
    [operateDialogRef],
  );

  const handleChangeStatus = useCallback(
    (row: any[], status: number) => {
      operateDialogRef.current.open({
        initialValues: row,
        status,
      });
    },
    [operateDialogRef],
  );

  useEffect(() => {
    if (!visible) return;
    if (!initialValues?.sdkappids) {
      fetchSdkappidList();
    }
    if (initialValues?.tactic_id) {
      fetchTacticChannelList();
    }
  }, [fetchSdkappidList, fetchTacticChannelList, initialValues, visible]);

  useEffect(() => {
    const _tabs: any[] = [
      { id: initialValues?.uin, type: 'uin' },
      { id: 'global', type: 'global' },
    ];
    if (initialValues?.sdkappids) {
      _tabs.splice(
        1,
        0,
        ...initialValues?.sdkappids?.split(',')?.map((el) => ({ id: el, type: 'sdkappid' })),
      );
    } else {
      _tabs.splice(
        1,
        0,
        ...(sdkappidList?.value ?? []).map((el) => ({ id: el, type: 'sdkappid' })),
      );
    }
    const _items: TabsProps['items'] = _tabs.map((item: any) => ({
      key: item.id,
      label: item.type === 'uin' ? `uin:${item.id}` : item.type === 'sdkappid' ? item.id : '全局',
      children: (
        <ProTable
          columns={[
            {
              title: '运营商',
              dataIndex: 'operator_name',
              key: 'operator_name',
            },
            {
              title: '通道',
              key: 'channel_name',
              render: (row: any) => {
                return row.type === 0
                  ? `${row.channel_name}_${row.supplier_name}(${row.channel_id})`
                  : row.channel_name;
              },
            },
            {
              title: '通道类型',
              dataIndex: 'type',
              key: 'type',
              render: (val: any) =>
                channelType.find((el) => el.value.toString() === val?.toString())?.label,
            },
            {
              title: '权重',
              dataIndex: 'weight',
              key: 'weight',
            },
            {
              title: '优先级',
              dataIndex: 'level',
              key: 'level',
            },
          ]}
          request={(params: any) => getScheduleFn(params, item)}
          pagination={false}
          options={false}
          search={false}
        ></ProTable>
      ),
    }));
    setTabs(_tabs);
    setItems(_items);
  }, [getScheduleFn, initialValues, sdkappidList?.value]);

  return (
    <>
      <Modal
        title={
          <>
            <InfoCircleTwoTone />
            调度信息
          </>
        }
        open={visible}
        onCancel={() => {
          setVisible(false);
        }}
        width={1200}
        zIndex={100}
        destroyOnClose
        footer={[
          <Button type="primary" key="submit" onClick={() => handleConfirmBind([initialValues])}>
            自动调度
          </Button>,
          <Button
            type="primary"
            danger
            key="reject"
            onClick={() => handleChangeStatus([initialValues], 2)}
          >
            拒绝
          </Button>,
          <Button
            type="primary"
            ghost
            key="handle"
            onClick={() => handleChangeStatus([initialValues], 3)}
          >
            手动调度
          </Button>,
          <Button key="back" onClick={() => setVisible(false)}>
            知道了
          </Button>,
        ]}
      >
        <Title level={5}>自动调度更新标品</Title>
        {tacticChannelList?.loading ? (
          <>
            <Spin style={{ marginRight: 10 }}></Spin>
            标品通道信息加载中....
          </>
        ) : (
          <Table
            columns={columns}
            dataSource={tacticChannelList?.value ?? []}
            pagination={false}
          ></Table>
        )}
        <Title level={5} style={{ marginTop: 20 }}>
          现有标品
        </Title>
        <Tabs defaultActiveKey={tabs?.[0]?.id} items={items} />
      </Modal>
      <CpqTacticOperateDialog
        dialogRef={operateDialogRef}
        onSuccess={() => {
          onSuccess();
          setVisible(false);
        }}
      ></CpqTacticOperateDialog>
    </>
  );
}
