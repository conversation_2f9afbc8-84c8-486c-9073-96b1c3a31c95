import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Table, Alert } from 'antd';
import { useDialog } from '@/utils/react-use/useDialog';
import _ from 'lodash';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { findLabel } from '@/utils/utils';
import { smsType } from '@/const/const';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

interface Props {
  dialogRef: any;
  continueSubmit: (arg: any) => void;
  loading: boolean;
}

export default function AccountNoPriceModal(props: Props) {
  const [mccMncInfo] = useMccMncInfo();
  const { dialogRef, continueSubmit, loading } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const { initialValues: dataSource } = defaultVal;
  const { regionOptionsMcc = [] } = useFetchCountryInfo();
  const columns: any = useMemo(() => {
    return [
      {
        title: '国家',
        key: 'mcc',
        align: 'center',
        render: (row: any) => findLabel(regionOptionsMcc, row.mcc),
      },
      {
        title: '运营商',
        key: 'mnc',
        align: 'center',
        render: (row: any) => {
          const { operator_name, mcc, mnc } =
            _.find(
              mccMncInfo,
              (v) =>
                v.mcc.toString() === row.mcc.toString() && v.mnc.toString() === row.mnc.toString(),
            ) ?? {};
          return `${operator_name}(${mcc}_${mnc})`;
        },
      },
      {
        title: '短信类型',
        key: 'sms_type',
        align: 'center',
        render: (row: any) => findLabel(smsType, row.sms_type),
      },
      {
        title: '通道id',
        dataIndex: 'account_id',
        key: 'account_id',
        align: 'center',
      },
      {
        title: '通道',
        dataIndex: 'account',
        key: 'account',
        align: 'center',
      },
    ];
  }, [mccMncInfo, regionOptionsMcc]);

  const rowSelection = {
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      const keys = selectedRows.map(
        (record: any) => `${record.mnc}_${record.mcc}_${record.sms_type}_${record.account_id}`,
      );
      setSelectedRowKeys(keys);
      setSelectedRows(selectedRows);
    },
  };

  useEffect(() => {
    if (!visible) {
      setSelectedRowKeys([]);
      setSelectedRows([]);
    }
  }, [visible]);

  return (
    <Modal
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      onOk={() => {
        const noCheck = _.differenceWith(dataSource, selectedRows, _.isEqual);
        continueSubmit(noCheck);
      }}
      okText="继续提交"
      confirmLoading={loading}
      destroyOnClose={true}
      maskClosable={false}
      width={800}
    >
      <Alert message="以下通道无报价，请手动选择后提交" type="info" />
      <Table
        columns={columns}
        dataSource={dataSource}
        size="middle"
        rowKey={(record) => `${record.mnc}_${record.mcc}_${record.sms_type}_${record.account_id}`}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        pagination={false}
      />
    </Modal>
  );
}
