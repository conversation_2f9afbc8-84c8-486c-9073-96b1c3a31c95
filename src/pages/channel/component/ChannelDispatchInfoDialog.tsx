import React from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal } from 'antd';
import ChannelDispatchTabs from './ChannelDispatchTabs';

interface DialogProps {
  dialogRef: DialogRef;
}

export const ChannelDispatchInfoDialog = (props: DialogProps) => {
  const { dialogRef } = props;

  const [visible, setShowState, initVals] = useDialog<{
    country_code: string;
    mnc: string;
    channel_id: number;
  }>(dialogRef);

  return (
    <>
      <Modal
        open={visible}
        title="调度信息"
        width="80%"
        destroyOnClose
        footer={null}
        onCancel={() => {
          setShowState(false);
        }}
      >
        <ChannelDispatchTabs searchKeys={initVals} tableProps={{ scroll: { x: 'max-content' } }} />
      </Modal>
    </>
  );
};
