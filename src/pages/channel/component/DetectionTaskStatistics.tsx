import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ActionType, BetaSchemaForm, ProTable } from '@ant-design/pro-components';
import _, { isArray } from 'lodash';
import { getDetectionTaskStatistics } from '@/services/channelDetectionTask';
import { history } from 'umi';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { useAsyncFn } from 'react-use';
import { getProviderGroupBindList } from '@/services/channel';
import { getProviderStrategyBindList } from '@/services/channelStrategy';
import { DataItem } from '../DetectionTaskManage';

const type = [
  { label: 'ALL', value: 'all' },
  { label: '账号id', value: 'provider_id' },
  { label: '运营商', value: 'mnc' },
];

const DirectPortList = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const queryFormRef = useRef<any>();
  const { row: query } = history.location.state as {
    row: any;
  };
  const { task_id, account_id, channel_type } = query;
  const [mccMncInfo] = useMccMncInfo();
  const [searchKeys, setSearchKeys] = useState<Record<string, any>>({});
  const [list, setList] = useState<Record<string, any>[]>([]);

  const [providerIdList, fetchProviderIdList] = useAsyncFn(async () => {
    if (channel_type.toString() === '0')
      return [{ provider_id: account_id, provider_name: query.account_name }];
    const res =
      channel_type.toString() === '1'
        ? await getProviderGroupBindList({ group_id: account_id })
        : await getProviderStrategyBindList({ tactic_id: account_id });
    return isArray(res.data) ? res.data : [];
  }, [account_id, channel_type, query.account_name]);

  useEffect(() => {
    fetchProviderIdList();
  }, [fetchProviderIdList]);

  const columns: any = useMemo(() => {
    const allColumns = [
      {
        title: '账号id',
        key: 'provider_id',
        render: (text, row: any) => `${row.account_name}(${row.provider_id})`,
      },
      {
        title: '运营商',
        // dataIndex: 'mnc',
        key: 'mnc',
        width: 150,
        render: (text, row: any) => {
          const name = _.find(mccMncInfo, (v) => v.mnc === row.mnc)?.operator_name ?? '';
          return `${name}(${row.mnc})`;
        },
      },
      {
        title: '请求量',
        dataIndex: 'num_req',
        key: 'num_req',
        hideInSearch: true,
      },
      {
        title: '请求成功量',
        dataIndex: 'num_succ',
        key: 'num_succ',
        hideInSearch: true,
      },
      {
        title: '回执量',
        dataIndex: 'num_cb',
        key: 'num_cb',
        hideInSearch: true,
      },
      {
        title: '回执成功量',
        dataIndex: 'num_cb_succ',
        key: 'num_cb_succ',
        hideInSearch: true,
      },
      {
        title: '接收成功量',
        dataIndex: 'num_recv_succ',
        key: 'num_recv_succ',
        hideInSearch: true,
      },
      {
        title: '请求成功率',
        dataIndex: 'req_succ_rate',
        key: 'req_succ_rate',
        hideInSearch: true,
      },
      {
        title: 'DR（回执成功率）',
        dataIndex: 'cb_succ_rate',
        key: 'cb_succ_rate',
        hideInSearch: true,
      },
      {
        title: 'CR（转化率）',
        dataIndex: 'recv_succ_rate',
        key: 'recv_succ_rate',
        hideInSearch: true,
      },
      {
        title: '改写率',
        dataIndex: 'recv_diff_rate',
        key: 'recv_diff_rate',
        hideInSearch: true,
      },
    ];
    return allColumns.filter((el) => {
      const keys = _.keys(list[0]);
      return keys.length ? keys.includes(el.key) : allColumns;
    });
  }, [list, mccMncInfo]);

  const requestFn = useCallback(
    async (_params: any) => {
      const { data } = await getDetectionTaskStatistics({
        ..._.omit(
          _.pickBy(_params, (v) => !_.isNil(v) && v !== ''),
          ['pageSize', 'current'],
        ),
        task_id,
        account_id,
        ..._.pickBy(searchKeys, (v) => !_.isNil(v) && v !== ''),
      });
      setList(isArray(data) ? data : []);
      return {
        data: isArray(data) ? data : [],
        success: true,
        total: data.count,
      };
    },
    [account_id, searchKeys, task_id],
  );

  const formItems: any = useMemo(() => {
    const provider = providerIdList.value?.map((el) => ({
      label: `${el.provider_name}(${el.provider_id})`,
      value: el.provider_id,
    }));
    const operator = query.mnc_info.map((el) => {
      const name = _.find(mccMncInfo, (v) => v.mnc === el)?.operator_name;
      return {
        label: `${name}(${el})`,
        value: el,
      };
    });
    const providerField = {
      title: '账号id',
      key: 'provider_id',
      valueType: 'select',
      fieldProps: {
        options: provider,
        showSearch: true,
      },
    };
    const operatorField = {
      title: '运营商',
      key: 'mnc',
      valueType: 'select',
      fieldProps: {
        options: operator,
        showSearch: true,
      },
    };
    const fields = [
      {
        title: '查询类型',
        key: 'type',
        valueType: 'select',
        fieldProps: {
          options: type,
        },
      },
      providerField,
      operatorField,
    ];
    return fields;
  }, [mccMncInfo, providerIdList.value, query.mnc_info]);

  async function onFinish(vals) {
    setSearchKeys({ ...vals });
    actionRef.current?.reload();
  }

  return (
    <>
      <BetaSchemaForm<DataItem>
        formRef={queryFormRef}
        layoutType="QueryFilter"
        layout="horizontal"
        labelCol={{ span: 4 }}
        onFinish={onFinish}
        columns={formItems}
      />

      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={false}
        request={requestFn}
        options={false}
      />
    </>
  );
};
export default DirectPortList;
