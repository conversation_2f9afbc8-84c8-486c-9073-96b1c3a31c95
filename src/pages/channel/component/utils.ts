import type { InfoType } from '@/pages/global-state';
import _ from 'lodash';
import type { FormInstance } from 'antd';

export function getMncOptions(
  mccMncInfo: InfoType | undefined,
  value: string | string[] | undefined,
  key: 'country_code' | 'mcc' = 'country_code',
) {
  return (mccMncInfo ?? [])
    .filter((v) =>
      value ? (Array.isArray(value) ? value.includes(v[key]) : v[key] === value) : true,
    )
    .map((o) => ({
      label: `${o.operator_name}(${o.mcc}_${o.mnc})`,
      value: `${o.mcc}_${o.mnc}`,
    }));
}

// 取mnc*通道交叉集合
export function getSelectGather({
  initMns,
  form,
  idKey = 'channel_id',
}: {
  initMns?: { mcc?: string; mnc?: string };
  form: FormInstance;
  idKey?: string;
}) {
  const allIds = form.getFieldValue(idKey) ?? [];
  const allMnc = initMns?.mnc
    ? [initMns?.mnc]
    : (form.getFieldValue('mnc') ?? []).map((v: string) => v.split('_')?.[1]);
  const arr: string[] = [];
  _.forEach(allMnc, (mnc) => {
    _.forEach(allIds, (id) => {
      arr.push(`${mnc}_${id}`);
    });
  });
  return arr;
}

export const validatePhoneInputInProviderWhitelist = (inputText: string) => {
  return new Promise((resolve, reject) => {
    const lines = inputText.split('\n');
    if (lines.length > 200) {
      reject(''); // 超过200行，验证失败
    }

    const phoneRegex = /^\+\d{1,3}\s?\d{3,14}$/;

    for (let line of lines) {
      if (!phoneRegex.test(line.trim())) {
        reject(''); // 不符合手机号码格式，验证失败
      }
    }
    resolve(''); // 验证通过
  });
};
