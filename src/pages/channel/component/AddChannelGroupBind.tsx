import React, { useMemo, useState } from 'react';
import { Modal, Button, InputNumber, Form, message } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialogRef, useDialog } from '@/utils/react-use/useDialog';
import { history } from 'umi';

import { bindProviderGroup, checkGroupPrice } from '@/services/channel';
import ChannelSelect from '../commonComponent/ChannelSelect';
import CheckCheckChannelAttributesOrPriceModal from './CheckChannelAttributesOrPriceModal';
import { routeTypeByName, routeTypeMap } from './const';
import _ from 'lodash';
import { errorsObj } from '@/pages/global-components/BatchErrorModal';

interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
}

const AddChannelGroupBind = (props: DialogProps) => {
  const groupNoPriceDialogRef = useDialogRef();
  const historyGroupId = (history.location.state as { group_id?: number })?.group_id;
  const [form] = Form.useForm();
  const { dialogRef, reload } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const { initialValues = {} } = defaultVal;
  const [loading, setLoading] = useState(false);
  const [accountNoCheck, setAccountNoCheck] = useState<any[number]>([]);

  async function onFinish(vals: any) {
    const groupId = historyGroupId ?? initialValues?.group_id;
    try {
      const provider_id = vals.provider_id
        .map((account_id) => `${groupId}_${account_id}`)
        .filter((c: number) => !accountNoCheck.includes(c));
      if (provider_id.length === 0) {
        return message.error('请至少选择一个通道');
      }
      setLoading(true);
      const res = await bindProviderGroup({
        params: provider_id.map((c: string) => ({
          ...vals,
          provider_id: Number(c.split('_')[1]),
          group_id: groupId,
        })),
      });
      setLoading(false);
      setVisible(false);
      groupNoPriceDialogRef.current.close();
      if (res.code === 0 && !res.msg?.errors?.length) {
        reload();
        return message.success('绑定成功');
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
      return message.error('绑定失败');
    }
  }

  async function checkPrice() {
    setLoading(true);
    try {
      const response = await checkGroupPrice({
        params: [
          {
            channel_id: historyGroupId ?? initialValues?.group_id,
            channel_type: 1,
            account_ids: form.getFieldValue('provider_id')?.join(','),
          },
        ],
      });
      setLoading(false);
      const errorData = response.data.filter((el) => el.code !== 0);
      if (errorData.length) {
        errorsObj.setVisible(true, errorData);
        setLoading(false);
        return;
      }
      // setCheckResult(res.data.check_infos);
      const checkInfos = _.flatMap(response.data, (res) =>
        Object.keys(res.data.check_infos).map((k) => ({
          group_id: res.param.channel_id,
          account_id: Number(k),
          ...res.data.check_infos[k],
        })),
      );
      const needCheck = checkInfos?.filter(
        (el: any) => el?.diff_keys?.length !== 0 || !el.price_id,
      );
      if (needCheck.length === 0) {
        return form.submit();
      }
      groupNoPriceDialogRef.current.open({
        initialValues: needCheck,
        type: 1,
      });
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
    // form.submit();
  }

  const routeCountryMnc = useMemo(() => {
    const route_type = initialValues?.resource_type
      ? routeTypeMap.find((el: any) => el.id === initialValues?.resource_type)?.route_type ?? 0
      : routeTypeByName.find((el) => initialValues?.name?.includes(el.text))?.value;

    const { country_code } = initialValues;

    return `${route_type}-${country_code}`;
  }, [initialValues]);

  return (
    <>
      <Modal
        title="绑定"
        open={visible}
        footer={null}
        onCancel={() => setVisible(false)}
        destroyOnClose={true}
      >
        <Form
          className="sender-search-form"
          labelCol={{ span: 7 }}
          form={form}
          labelAlign="left"
          onFinish={(vals) => onFinish(vals)}
          initialValues={{ provider_id: initialValues?.provider_id }}
        >
          <Form.Item label="通道组id">
            <span>{historyGroupId ?? initialValues?.group_id}</span>
          </Form.Item>
          <Form.Item name="provider_id" label="通道" rules={[{ required: true }]}>
            <ChannelSelect
              value={form.getFieldValue('provider_id')}
              type={0}
              onChange={(value) => {
                form.setFieldsValue({ provider_id: value });
              }}
              pageSize={1000}
              loadFnParams={{
                0: {
                  route_country_mnc: routeCountryMnc,
                },
              }}
            />
          </Form.Item>
          <Form.Item name="weight" label="权重" rules={[{ required: true }]}>
            <InputNumber controls={false} style={{ width: 320 }} />
          </Form.Item>
          <Form.Item name="monitor_threshold" label="重新调度阈值" rules={[{ required: true }]}>
            <InputNumber controls={false} style={{ width: 320 }} />
          </Form.Item>
          <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
            <Button htmlType="button" onClick={checkPrice} type="primary" loading={loading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      <CheckCheckChannelAttributesOrPriceModal
        dialogRef={groupNoPriceDialogRef}
        continueSubmit={(noCheck: any) => {
          setAccountNoCheck(noCheck);
          form.submit();
        }}
        loading={loading}
      />
    </>
  );
};

export default AddChannelGroupBind;
