import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Table, Alert } from 'antd';
import { useDialog } from '@/utils/react-use/useDialog';
import _ from 'lodash';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { findLabel } from '@/utils/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

interface Props {
  dialogRef: any;
  continueSubmit: (arg: any) => void;
  loading: boolean;
}

export default function CheckIncomeCostDialog(props: Props) {
  const { regionOptionsMcc = [] } = useFetchCountryInfo();
  const [mccMncInfo] = useMccMncInfo();
  const { dialogRef, continueSubmit, loading } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    list: {
      price: string;
      price_curr: string;
      account_id: number;
      mnc: string;
      income_price: string;
      income_price_curr: string;
      mcc: string;
    }[];
  }>(dialogRef);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const { list } = defaultVal;
  const columns: any = useMemo(
    () => [
      {
        title: 'object',
        key: 'object',
        dataIndex: 'object',
        align: 'center',
      },
      {
        title: '国家',
        key: 'mcc',
        align: 'center',
        render: (row: any) => findLabel(regionOptionsMcc, row.mcc),
      },
      {
        title: '运营商',
        key: 'mnc',
        align: 'center',
        render: (row: any) => {
          const { operator_name, mcc, mnc } =
            _.find(mccMncInfo, (v) => v.mcc === row.mcc && v.mnc === row.mnc) ?? {};
          return `${operator_name}(${mcc}_${mnc})`;
        },
      },
      {
        title: '通道id',
        dataIndex: 'account_id',
        key: 'account_id',
        align: 'center',
      },
      {
        title: '通道',
        dataIndex: 'account',
        key: 'account',
        align: 'center',
      },
      {
        title: '销售价格',
        key: 'income',
        align: 'center',
        render: (row: any) => `${row.income_price}(${row.income_price_curr})`,
      },
      {
        title: '成本价格',
        key: 'price',
        align: 'center',
        render: (row: any) => `${row.price}(${row.price_curr})`,
      },
    ],
    [mccMncInfo, regionOptionsMcc],
  );

  const rowSelection = {
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      const keys = selectedRows.map((e: any) => `${e.mnc}_${e.mcc}_${e.account_id}`);
      setSelectedRowKeys(keys);
      setSelectedRows(selectedRows);
    },
  };

  useEffect(() => {
    if (!visible) {
      setSelectedRowKeys([]);
    }
  }, [visible]);

  return (
    <Modal
      open={visible}
      onCancel={() => {
        setSelectedRowKeys([]);
        setVisible(false);
      }}
      width={900}
      onOk={() => {
        const noCheck = _.differenceWith(list, selectedRows, _.isEqual);
        continueSubmit(noCheck);
      }}
      okText="继续提交"
      confirmLoading={loading}
      destroyOnClose={true}
      maskClosable={false}
    >
      <Alert message="以下通道销售价格低于成本价格，请手动勾选需要提交的通道" type="info" />
      <Table
        columns={columns}
        dataSource={list}
        size="middle"
        rowKey={(record) => `${record.mnc}_${record.mcc}_${record.account_id}`}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        pagination={false}
      />
    </Modal>
  );
}
