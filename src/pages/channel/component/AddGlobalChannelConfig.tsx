import React, { useState } from 'react';
import { Modal, Button, InputNumber, Form, Select, message } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog, useDialogRef } from '@/utils/react-use/useDialog';
import { channelType, level } from './const';
import { addGlobalChannel, checkAccountPrice } from '@/services/channel';
import AccountNoPriceModal from './CheckAcoountNoPriceModal';
import _ from 'lodash';
import { getSelectGather } from './utils';
import ChannelSelect from '../commonComponent/ChannelSelect';
import MncSelect from '../commonComponent/MncSelect';
import { errorsObj } from '@/pages/global-components/BatchErrorModal';
interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
}

const GlobalChannelConfigAdd = (props: DialogProps) => {
  const [form] = Form.useForm();
  const { dialogRef, reload } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const accountNoPriceDialogRef = useDialogRef();
  const { initialValues } = defaultVal;
  const [loading, setLoading] = useState(false);
  const [channelList, setChannelList] = useState<any[]>([]);
  const [accountNoCheck, setAccountNoCheck] = useState<string[]>([]); // 没有报价，不提交的通道列表
  const validateFields = async () => {
    try {
      await form.validateFields(['weight', 'type', 'channel_id', 'mnc', 'level']);
      return true;
    } catch (err) {
      return false;
    }
  };

  const checkAccounthHasPrice = async () => {
    const validateSuccess = await validateFields();
    if (!validateSuccess) {
      return;
    }
    if ([1, 2].includes(form.getFieldValue('type'))) {
      form.submit();
      return;
    }
    setLoading(true);
    const curMnc = initialValues?.mnc
      ? [initialValues.mnc]
      : (form.getFieldValue('mnc') ?? []).map((v: string) => v.split('_')[1]);
    const params = curMnc.map((mnc) => ({
      account_ids: form.getFieldValue('channel_id')?.join(','),
      mcc: initialValues.mcc,
      mnc,
      sms_type: initialValues?.sms_type,
    }));
    const response = await checkAccountPrice({ params });
    if (response.code !== 0) {
      setLoading(false);
    }
    const errorData = response.data.filter((el) => el.code !== 0);
    if (errorData.length) {
      errorsObj.setVisible(true, errorData);
      setLoading(false);
      return;
    }
    const res = _.flatMap(response.data, (el) => el.data).filter((el: any) => !el.price_exists);
    const noPriceList = _.flatten(res);
    if (noPriceList.length) {
      accountNoPriceDialogRef.current.open({
        initialValues: noPriceList.map((el: any) => ({
          ...el,
          account: channelList.find((c: any) => c.value.toString() === el.account_id.toString())
            ?.label,
        })),
      });
      setLoading(false);
    } else {
      form.submit();
      setLoading(false);
    }
  };
  async function onFinish(vals: any) {
    setLoading(true);
    try {
      const channelArr = getSelectGather({ initMns: initialValues, form }).filter(
        (c: string) => !accountNoCheck.includes(c.toString()),
      );
      if (!channelArr.length) {
        setLoading(false);
        return message.error('请至少选择一个通道');
      }
      const data: any = [];
      channelArr.forEach((c: string) => {
        const mnc = c.split('_')[0];
        const channel_id = c.split('_')[1];
        const obj = {
          ...vals,
          mcc_mnc: `${initialValues.mcc}_${mnc}`,
          channel_id,
          sms_type: initialValues?.sms_type,
          mnc: undefined,
        };
        data.push(obj);
      });
      const res = await addGlobalChannel({
        params: data,
      });
      setLoading(false);
      setVisible(false);
      accountNoPriceDialogRef.current.close();
      if (res.code === 0 && !res.msg?.errors?.length) {
        reload();
        form.resetFields();
        message.success('添加成功');
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
      return message.error('添加失败');
    }
  }

  return (
    <>
      <Modal
        title="添加"
        open={visible}
        footer={null}
        onCancel={() => {
          setVisible(false);
          form.resetFields();
        }}
        destroyOnClose={true}
        maskClosable={false}
      >
        <Form
          className="sender-search-form"
          labelCol={{ span: 7 }}
          form={form}
          labelAlign="left"
          initialValues={{ type: 0, level: 2, weight: 10 }}
          onFinish={(vals) => onFinish(vals)}
        >
          <Form.Item name="mnc" label="运营商" rules={[{ required: !initialValues?.mnc }]}>
            {initialValues?.mnc ? (
              initialValues?.operator_name
            ) : (
              <MncSelect
                mode="multiple"
                initialValues={{ mcc: initialValues?.mcc }}
                onChange={(mnc: any) => {
                  form.setFieldsValue({ mnc });
                  if (form.getFieldValue('type') > 0) {
                    form.setFieldsValue({ channel_id: undefined });
                  }
                }}
                value={form.getFieldValue('mnc')}
              />
            )}
          </Form.Item>
          <Form.Item name="type" label="通道类型" rules={[{ required: true }]}>
            <Select
              allowClear
              showSearch
              placeholder="请选择"
              options={channelType}
              onChange={(type) => {
                form.setFieldsValue({ type, channel_id: undefined });
              }}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) =>
              (curValues.type > 0 && prevValues.channel_id !== curValues.channel_id) ||
              prevValues.type !== curValues.type
            }
          >
            {({ getFieldValue }) => {
              const curMnc = (getFieldValue('mnc') ?? []).map((v: string) => v.split('_')[1]);
              const paramMnc = initialValues?.mnc
                ? initialValues?.mnc
                : curMnc.length > 1
                ? '000'
                : curMnc.join(',');

              return (
                <Form.Item
                  name="channel_id"
                  label="通道"
                  rules={[{ required: true }]}
                  labelCol={{ span: 7 }}
                >
                  <ChannelSelect
                    value={getFieldValue('channel_id')}
                    reloadOn={
                      getFieldValue('type') === 0
                        ? ''
                        : `${getFieldValue('type')}_${getFieldValue('mnc')}`
                    }
                    type={getFieldValue('type')}
                    onChange={(value) => {
                      form.setFieldsValue({ channel_id: value });
                    }}
                    pageSize={1000}
                    loadFnParams={{
                      1: {
                        country_code: initialValues?.country_code,
                        sms_type: initialValues?.sms_type,
                        mnc: paramMnc,
                      },
                      2: {
                        country_code: initialValues?.country_code,
                        sms_type: initialValues?.sms_type,
                        mnc: paramMnc,
                      },
                    }}
                    loadFnCallBack={({ params, options }) => {
                      !params.search_key && setChannelList(options);
                    }}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item name="weight" label="权重" rules={[{ required: true }]}>
            <InputNumber controls={false} style={{ width: 320 }} />
          </Form.Item>
          <Form.Item name="level" label="优先级" rules={[{ required: true }]}>
            <Select options={level} defaultValue="2" />
          </Form.Item>
          <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
            <Button onClick={() => checkAccounthHasPrice()} type="primary" loading={loading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      <AccountNoPriceModal
        dialogRef={accountNoPriceDialogRef}
        continueSubmit={(noCheck: any) => {
          setAccountNoCheck(noCheck.map((record: any) => `${record.mnc}_${record.account_id}`));
          form.submit();
        }}
        loading={loading}
      />
    </>
  );
};

export default GlobalChannelConfigAdd;
