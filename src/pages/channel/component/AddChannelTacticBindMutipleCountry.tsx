import React, { useEffect, useState } from 'react';
import { Modal, Button, InputNumber, Form, message, Select, Table } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialogRef, useDialog } from '@/utils/react-use/useDialog';
import { checkGroupPrice } from '@/services/channel';
import CheckCheckChannelAttributesOrPriceModal from './CheckChannelAttributesOrPriceModal';
import { tacticCategory } from './const';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getAccountList } from '@/services/smppAccount';
import { getProductType } from '@/services/tacticResources';
import { smsType, smsTypeValueMap, strategyTypes } from '@/const/const';
import _ from 'lodash';
import { bindProviderGroupStrategy, getProviderStrategyList } from '@/services/channelStrategy';
import { errorsObj } from '@/pages/global-components/BatchErrorModal';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { findLabel, getOperatorName } from '@/utils/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
}

const AddChannelTacticBindMutipleCountry = (props: DialogProps) => {
  const { regionOptions = [] } = useFetchCountryInfo();
  const groupNoPriceDialogRef = useDialogRef();
  const [form] = Form.useForm();
  const { dialogRef, reload } = props;
  const [visible, setVisible] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const [loading, setLoading] = useState(false);
  const [mccMncInfo] = useMccMncInfo();

  const { value: accountList } = useAsyncRetryFunc(async () => {
    const res = await getAccountList({ page_size: 2000, page_index: 1 });
    return res.data.list.map((el: any) => ({
      value: el.account_id,
      label: `${el.account_name}(${el.account_id})`,
    }));
  }, []);

  const { value: productType } = useAsyncRetryFunc(async () => {
    const res = await getProductType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);

  async function doBind(noCheck: any[]) {
    const vals = form.getFieldsValue();
    const checked = vals.channel_ids
      .map((c: number) => `${c}_${vals.account_id}`)
      .filter((c: number) => !noCheck.includes(c));
    if (checked.length === 0) {
      return message.error('请至少选择一个通道');
    }
    const res = await bindProviderGroupStrategy({
      params: checked.map((c: string) => ({
        weight: vals.weight,
        provider_id: vals.account_id,
        tactic_id: c.split('_')[0],
        monitor_threshold: vals.monitor_threshold,
        mnc: '000',
      })),
    });
    setLoading(false);
    setVisible(false);
    groupNoPriceDialogRef.current.close();
    if (res.code === 0 && !res.msg?.errors?.length) {
      reload();
      return message.success('绑定成功');
    }
  }

  async function getGroupIds(vals) {
    const res = await getProviderStrategyList({
      page_index: 1,
      page_size: 1000,
      country_codes: vals.country_codes,
      product_type: vals.product_type,
      category: vals.category,
      tactic_type: vals.tactic_type,
      sms_type: _.sum(vals.sms_type),
      mncs: ['000'],
    });
    return res.data?.list ?? [];
  }

  async function checkPrice(channelIds: number[]) {
    setLoading(true);
    const params = channelIds.map((id) => ({
      channel_id: id,
      channel_type: 2,
      account_ids: form.getFieldValue('account_id').toString(),
    }));
    try {
      const response = await checkGroupPrice({ params });
      const errorData = response.data.filter((el) => el.code !== 0);
      if (errorData.length) {
        errorsObj.setVisible(true, errorData);
        setLoading(false);
        return;
      }
      setLoading(false);
      const checkInfos = _.flatMap(response.data, (res) =>
        _.keys(res.data?.check_infos).map((k) => ({
          tactic_id: res.param.channel_id,
          account_id: Number(k),
          ...res.data?.check_infos[k],
        })),
      );
      const needCheck = checkInfos?.filter(
        (el: any) => el?.diff_keys?.length !== 0 || !el.price_id,
      );
      if (needCheck.length === 0) {
        doBind([]);
        return;
      }
      groupNoPriceDialogRef.current.open({
        initialValues: needCheck,
        type: 2,
      });
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  }

  async function onFinish(vals: any) {
    try {
      setLoading(true);
      const list = await getGroupIds(vals);
      const gropuIds = _.map(list, (el) => el.tactic_id);
      if (!gropuIds.length) {
        message.error('没有符合条件的标品');
        setLoading(false);
        return;
      }
      const allFilter = _.flatMap(vals.country_codes, (countryCode) =>
        _.flatMap(vals.sms_type, (smsType) => {
          const obj = {
            country_code: countryCode,
            product_type: vals.category === 0 ? vals.tactic_type : vals.product_type,
            category: vals.category,
            sms_type: smsType,
          };
          return vals.category === 1
            ? _.flatMap(
                mccMncInfo.filter((el: any) => el.country_code === countryCode),
                (mnc: any) => ({ ...obj, mnc: mnc.mnc }),
              )
            : obj;
        }),
      );
      const flatList = _.flatMap(list, (el) => {
        const smsTypes = _.find(
          smsTypeValueMap,
          (s) => s.value.toString() === el.sms_type.toString(),
        )?.valueMap;
        return _.flatMap(smsTypes, (smsType) => ({
          ...el,
          product_type: vals.category === 0 ? el.tactic_type : el.product_type,
          sms_type: smsType,
        }));
      });
      const difference = _.differenceWith(allFilter, flatList, (cur: any, other: any) => {
        const n =
          vals.category === 1
            ? ['country_code', 'product_type', 'category', 'sms_type', 'mnc']
            : ['country_code', 'product_type', 'category', 'sms_type'];
        return _.every(n, (k) => cur[k]?.toString() === other[k]?.toString());
      });
      if (difference.length > 0) {
        Modal.confirm({
          title: '以下标品缺失',
          content: (
            <>
              <Table
                dataSource={difference}
                columns={[
                  {
                    title: '国家',
                    // dataIndex: 'mcc',
                    key: 'mcc',
                    align: 'center',
                    render: (row: any) => findLabel(regionOptions, row.country_code),
                  },
                  {
                    title: '标品类别',
                    dataIndex: 'category',
                    key: 'category',
                    align: 'center',
                    width: 100,
                    render: (val: string) => findLabel(tacticCategory, val),
                  },
                  {
                    title: '运营商',
                    key: 'mnc',
                    align: 'center',
                    render: (row: any) =>
                      getOperatorName(mccMncInfo, row.mnc, { country_code: row.country_code }),
                  },
                  {
                    title: '标品类型',
                    dataIndex: 'product_type',
                    key: 'product_type',
                    align: 'center',
                    width: 100,
                    render: (val: number) => {
                      return _.find(
                        vals.category === 0 ? strategyTypes : productType,
                        (v) => v.value === val,
                      )?.label;
                    },
                  },
                  {
                    title: '短信类型',
                    dataIndex: 'sms_type',
                    key: 'sms_type',
                    align: 'center',
                    render: (val: number) =>
                      smsTypeValueMap.find((el) => el.value.toString() === val?.toString())?.text,
                  },
                ]}
                pagination={false}
              ></Table>
            </>
          ),
          width: 800,
          okText: '继续绑定',
          onOk: () => {
            form.setFieldValue('channel_ids', gropuIds);
            checkPrice(gropuIds);
          },
          onCancel: () => {
            setLoading(false);
          },
        });
      } else {
        form.setFieldValue('channel_ids', gropuIds);
        checkPrice(gropuIds);
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
      return message.error('绑定失败');
    }
  }

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [form, visible]);

  return (
    <>
      <Modal
        title="多国家配置"
        open={visible}
        footer={null}
        onCancel={() => setVisible(false)}
        destroyOnClose={true}
      >
        <Form
          className="sender-search-form"
          labelCol={{ span: 7 }}
          form={form}
          labelAlign="left"
          onFinish={(vals) => onFinish(vals)}
        >
          <Form.Item name="channel_ids" hidden></Form.Item>
          <Form.Item label="国家" name="country_codes" rules={[{ required: true }]}>
            <Select
              mode="multiple"
              options={regionOptions}
              placeholder="请选择"
              showSearch
              allowClear
              onChange={() => form.setFieldsValue({ mnc: undefined })}
              filterOption={(inputValue, option) =>
                !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
              }
            ></Select>
          </Form.Item>
          {/* <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) =>
              prevValues.country_codes !== curValues.country_codes
            }
          >
            {({ getFieldValue }) => {
              const countryCodes = getFieldValue('country_codes');
              return countryCodes?.length > 1 ? null : (
                <Form.Item name="mnc" label="运营商">
                  <MncSelect
                    initialValues={{ country_code: countryCodes?.[0] }}
                    onChange={(mnc: any) => form.setFieldsValue({ mnc })}
                    value={form.getFieldValue('mnc')}
                    allowClear
                    mode="multiple"
                    style={{ width: 180 }}
                    placeholder="请选择"
                  />
                </Form.Item>
              );
            }}
          </Form.Item> */}
          <Form.Item name="account_id" label="供应商账号ID" rules={[{ required: true }]}>
            <Select
              options={accountList}
              showSearch
              allowClear
              style={{ width: 200 }}
              placeholder="请选择"
              filterOption={(inputValue, option) =>
                !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
              }
            />
          </Form.Item>
          <Form.Item name="category" label="标品类别" rules={[{ required: true }]}>
            <Select options={tacticCategory} placeholder="请选择"></Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.category !== curValues.category}
          >
            {({ getFieldValue }) => {
              const category = getFieldValue('category');
              const old = category === 0;
              return (
                <>
                  {old ? (
                    <>
                      <Form.Item name="tactic_type" label="策略类型" rules={[{ required: true }]}>
                        <Select allowClear placeholder="请选择" options={strategyTypes} />
                      </Form.Item>
                    </>
                  ) : (
                    <Form.Item name="product_type" label="标品类型" rules={[{ required: true }]}>
                      <Select
                        allowClear
                        showSearch
                        placeholder="请选择"
                        options={productType}
                        filterOption={(inputValue, option) =>
                          !!option?.label.toLowerCase().includes(inputValue.toLowerCase())
                        }
                      />
                    </Form.Item>
                  )}
                </>
              );
            }}
          </Form.Item>
          <Form.Item name="sms_type" label="短信类型" rules={[{ required: true }]}>
            <Select mode="multiple" allowClear showSearch placeholder="请选择" options={smsType} />
          </Form.Item>
          <Form.Item name="weight" label="权重" rules={[{ required: true }]}>
            <InputNumber></InputNumber>
          </Form.Item>
          <Form.Item name="monitor_threshold" label="重新调度阈值" rules={[{ required: true }]}>
            <InputNumber />
          </Form.Item>
          <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
            <Button type="primary" loading={loading} htmlType="submit">
              提交
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      <CheckCheckChannelAttributesOrPriceModal
        dialogRef={groupNoPriceDialogRef}
        continueSubmit={(noCheck: any) => {
          doBind(noCheck);
        }}
        loading={loading}
      />
    </>
  );
};

export default AddChannelTacticBindMutipleCountry;
