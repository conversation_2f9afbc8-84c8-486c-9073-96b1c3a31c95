import type { ReactNode } from 'react';
import React, { useEffect, useMemo, useState } from 'react';
import { history } from 'umi';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, message, InputNumber, Select, Popconfirm, Popover } from 'antd';
import { useAsyncFn } from 'react-use';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { channelType, level } from './const';
import { smsType } from '@/const/const';
import { getMccMncList } from '@/services/channel';
import _ from 'lodash';

interface Props {
  obj: 'uin' | 'sdkappid' | 'force' | 'global';
  getListFn: (obj: Page & Record<string, any>) => Promise<any>;
  deleteFn: (obj: any) => Promise<any>;
  addFn: (obj: any) => Promise<any>;
  editFn: (obj: { params: any[] }) => Promise<any>;
  addComponent: (dialogRef: DialogRef, getList: () => any) => ReactNode;
  formItem?: ReactNode;
}

type Page = {
  page_index: number;
  page_size: number;
};

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: any;
  record: any;
  children: React.ReactNode;
}

const pagination = {
  page_index: 1,
  page_size: 10000,
};
const ChannelConfigList = (props: Props) => {
  const { getListFn, addComponent, deleteFn, editFn, addFn, formItem, obj } = props;
  const [tableForm] = Form.useForm();

  const [startEdit, setStartEdit] = useState(false);
  const dialogRef = useDialogRef();
  const [editingKey, setEditingKey] = useState<any[]>([]);
  const isEditing = (record: any) => {
    return startEdit && editingKey.includes(record.key);
  };
  const [initialValues, setInitialValues] = useState({});
  const [mncList, setMncList] = useState([]);
  const [synSmsType, setSyncSmsType] = useState([]);
  const [syncLoading, setSyncLoading] = useState(false);
  const pathParams: any = history.location.state?.row;

  const columns: any = [
    {
      title: '运营商',
      // dataIndex: 'mnc',
      // key: 'mnc',
      align: 'center',
      render: (row: any) => (row.isParent ? '' : <>{row.operator_name}</>),
      width: 350,
    },
    {
      title: '通道',
      // dataIndex: 'channel_name',
      // key: 'channel_name',
      align: 'center',
      render: (row: any) => {
        return row.type === 0
          ? `${row.channel_name}_${row.supplier_name}(${row.channel_id})`
          : row.isParent
          ? row.channel_name
          : '';
      },
    },
    {
      title: '通道类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (val: any) =>
        channelType.find((el) => el.value.toString() === val?.toString())?.label,
    },
    {
      title: '权重',
      dataIndex: 'weight',
      key: 'weight',
      align: 'center',
      editable: true,
    },
    {
      title: '优先级',
      dataIndex: 'level',
      key: 'level',
      align: 'center',
      editable: true,
    },
    {
      title: '操作',
      align: 'center',
      render: (row: any) =>
        row.isParent === 1 ? (
          <>
            <Popconfirm
              title="确认删除此条通道配置?"
              onConfirm={() => handleDelete(row.conf_id.toString())}
              okText="Yes"
              cancelText="No"
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          </>
        ) : (
          <>
            <Button
              type="link"
              onClick={() => {
                dialogRef.current.open({
                  initialValues: {
                    ...pathParams,
                    ...row,
                  },
                });
              }}
            >
              添加通道
            </Button>
          </>
        ),
    },
  ];

  const [state, getList] = useAsyncFn(async () => {
    const params = {
      ...pagination,
      mcc: pathParams?.mcc,
      sms_type: pathParams?.sms_type,
    };
    const res = await getListFn(params);
    return res.data;
  }, [pagination]);

  async function getMncList() {
    const res = await getMccMncList({
      page_index: 1,
      page_size: 1000,
      mcc: pathParams?.mcc,
    });
    setMncList(res.data.list);
  }

  useEffect(() => {
    getList();
    getMncList();
  }, [pagination]);

  const list = useMemo(() => {
    const formValues = state.value?.list?.reduce((pre = {}, cur: any) => {
      pre[`weight_${cur.conf_id}`] = cur.weight;
      pre[`level_${cur.conf_id}`] = cur.level;
      return pre;
    }, {});
    setInitialValues(formValues); // 编辑表单初始值
    return mncList?.map((el: any) => ({
      ...el,
      isParent: 0,
      key: el.mnc,
      children: state.value?.list
        ?.filter((c: any) => c.mnc === el.mnc)
        .map((d: any) => ({ ...d, isParent: 1, key: `${d.mcc}_${d.mnc}_${d.conf_id}` })),
    }));
  }, [state.value?.list, mncList]);

  async function handleDelete(conf_ids: string) {
    try {
      const params = {
        conf_ids,
      };
      const res = await deleteFn(params);
      if (res.code === 0) {
        message.success('删除成功');
        getList();
        setStartEdit(false);
        setEditingKey([]);
      }
    } catch (err) {
      message.error('删除失败');
      console.log(err);
    }
  }

  const rowSelection = {
    checkStrictly: false,
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
    selectedRowKeys: editingKey,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      const keys = selectedRows.map((e: any) => e.key);
      if (!keys.length) {
        setStartEdit(false);
      }
      setEditingKey(keys);
    },
  };

  const EditableCell: React.FC<EditableCellProps> = function ({
    editing,
    dataIndex,
    title,
    record,
    children,
    ...restProps
  }) {
    const inputNode =
      dataIndex === 'weight' ? <InputNumber /> : <Select style={{ width: 100 }} options={level} />;
    return (
      <td {...restProps}>
        {editing && record ? (
          <Form.Item
            name={`${dataIndex}_${record.conf_id}`}
            style={{ margin: 0 }}
            rules={[
              {
                required: !!record.isParent,
                message: `请输入 ${title}!`,
              },
            ]}
          >
            {record.isParent ? inputNode : undefined}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const mergedColumns = columns.map((col: any) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: any) => ({
        record,
        inputType: 'number',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  async function submit() {
    const data = await doFormatData();
    if (!data.length) {
      return message.error('没有做任何改动');
    }
    try {
      const res = await editFn({ params: data });
      if (res.code === 0 && !res.msg?.errors?.length) {
        getList();
        setEditingKey([]);
        message.success('编辑成功');
      }
    } catch (err) {
      message.success('编辑失败');
    }
  }

  async function handleSync() {
    const conf_ids = editingKey.map((el) => el.split('_')[2]).filter((el) => !!el);
    if (!conf_ids.length) {
      message.error('至少选择一项');
      return;
    }
    const filter = state.value?.list.filter((el: any) => conf_ids.includes(el.conf_id.toString()));
    const data = synSmsType.map((s: number) => {
      return filter.map((el: any) => {
        const obj = {
          mcc_mnc: `${el.mcc}_${el.mnc}`,
          sms_type: s,
          level: el.level,
          channel_id: el.channel_id,
          type: el.type,
          weight: el.weight,
        };
        return obj;
      });
    });
    setSyncLoading(true);
    Promise.all(
      data.map((el) => {
        return addFn(el).then((res) => {
          const smsTypeTxt = smsType.find((s) => s.value === el[0].sms_type)?.label;
          if (res.code === 0 && !res.msg?.errors?.length) {
            message.success(`${smsTypeTxt}同步成功`);
          }
        });
      }),
    ).finally(() => {
      setSyncLoading(false);
    });
  }

  async function doFormatData() {
    if (!editingKey.length) {
      return message.error('请先选择编辑项');
    }
    const vals = await tableForm.validateFields();
    Object.keys(vals).forEach((k) => {
      if (vals[k] === undefined) {
        delete vals[k];
      }
    });
    // [k, v]
    const editData = Object.entries(vals).reduce((pre = [], cur) => {
      const conf_id = Number(cur[0].split('_')[1]);
      const columnsName = cur[0].split('_')[0];
      const row = state.value?.list.find((el) => String(el.conf_id) === String(conf_id));
      const obj = { conf_id };
      obj[columnsName] = cur[1];
      const index = pre.findIndex((el: any) => String(el.conf_id) === String(conf_id));
      if (index !== -1) {
        // @ts-ignore
        pre[index] = { ...row, ...pre[index], ...obj };
      } else {
        // @ts-ignore
        pre.push({ ...row, ...obj });
      }
      return pre;
    }, []);
    const originData = [...state.value?.list];
    const data = editData
      .filter((e: any) => {
        const row = originData.find((o) => String(o.conf_id) === String(e?.conf_id));
        return !_.isEqual(e, row);
      })
      .map((el: any) => ({ conf_id: el.conf_id, weight: el.weight, level: el.level }));
    return data;
  }

  return (
    <PageContainer>
      <Button onClick={() => history.push(`/channel/${obj}?from=1`, history.location.state)}>
        返回上一页
      </Button>
      <Form layout="inline" labelAlign="right">
        {formItem}
        <Form.Item label="国家/地区">
          <span>{pathParams?.country_name}</span>
        </Form.Item>
        <Form.Item label="短信类型">
          <span>
            {smsType.find((el) => el.value?.toString() === pathParams?.sms_type?.toString())?.label}
          </span>
        </Form.Item>
      </Form>
      <div style={{ margin: '20px 0', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          {!startEdit || !editingKey.length ? (
            <Button
              disabled={editingKey.length === 0}
              onClick={() => {
                const conf_ids = editingKey
                  .map((el) => el.split('_')[2])
                  .filter((el) => !!el)
                  .join(',');
                if (!conf_ids) {
                  message.error('至少选择一项');
                  return;
                }
                setStartEdit(true);
                tableForm.setFieldsValue(initialValues);
              }}
            >
              批量编辑
            </Button>
          ) : (
            <Button onClick={submit} type="primary">
              提交编辑
            </Button>
          )}

          <Popconfirm
            title="确认清空此筛选条件下的配置吗，清空后不可恢复?"
            onConfirm={() => {
              const conf_ids = editingKey
                .map((el) => el.split('_')[2])
                .filter((el) => !!el)
                .join(',');
              if (!conf_ids) {
                message.error('至少选择一项');
                return;
              }
              handleDelete(conf_ids);
            }}
            okText="Yes"
            cancelText="No"
          >
            <Button disabled={editingKey.length === 0} danger style={{ marginLeft: 10 }}>
              批量删除
            </Button>
          </Popconfirm>
        </div>
        <div>
          <Popover
            content={
              <>
                <Select
                  style={{ width: 200 }}
                  value={synSmsType}
                  onChange={(val) => setSyncSmsType(val)}
                  options={smsType.filter(
                    (el) => el.value.toString() !== pathParams.sms_type?.toString(),
                  )}
                  placeholder="请选择短信类型"
                  mode="multiple"
                />
                <div style={{ textAlign: 'right' }}>
                  <Button type="link" onClick={handleSync} loading={syncLoading}>
                    确认
                  </Button>
                </div>
              </>
            }
            title="同步勾选数据"
            trigger="click"
          >
            <Button disabled={editingKey.length === 0}>同步至</Button>
          </Popover>
          <Button
            style={{ marginLeft: 10 }}
            onClick={() => {
              dialogRef.current.open({
                initialValues: {
                  ...pathParams,
                  mnc: undefined,
                  addType: 'other',
                },
              });
            }}
          >
            新增配置
          </Button>
        </div>
        {/* <div>
          <Button
            type="primary"
            onClick={() => {
              dialogRef.current.open({
                initialValues: {
                  ...pathParams,
                  mnc: undefined,
                  addType: 'default',
                },
              });
            }}
          >
            新增默认配置
          </Button>

        </div> */}
      </div>
      <Form form={tableForm} component={false}>
        <Table
          columns={mergedColumns}
          dataSource={list}
          components={{
            body: {
              cell: EditableCell,
            },
          }}
          size="middle"
          rowKey={(record: any) => record.key}
          loading={state.loading}
          rowSelection={{
            type: 'checkbox',
            ...rowSelection,
          }}
          pagination={false}
          expandable={{
            expandedRowKeys: list?.map((el: any) => el.key),
          }}
        />
      </Form>
      {addComponent(dialogRef, getList)}
    </PageContainer>
  );
};

export default ChannelConfigList;
