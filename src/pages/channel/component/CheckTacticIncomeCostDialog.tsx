import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Table, Alert } from 'antd';
import { useDialog } from '@/utils/react-use/useDialog';

interface Props {
  dialogRef: any;
  continueSubmit: (noCheck: any) => void;
  loading: boolean;
  allCountry?: any[];
}

export default function CheckTacticIncomeCostDialog(props: Props) {
  const { dialogRef, continueSubmit, loading, allCountry } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    list: {
      price: string;
      price_curr: string;
      account_id: number;
      mnc: string;
      income_price: string;
      income_price_curr: string;
    }[];
    obj_type: 'uin' | 'sdkappid' | 'overall';
  }>(dialogRef);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const { list, obj_type } = defaultVal;
  const columns: any = useMemo(() => {
    const cols = [
      {
        title: '国家',
        key: 'country_code',
        align: 'center',
        render: (row: any) => {
          return allCountry?.find((el) => el.value === row.country_code)?.label;
        },
      },
      {
        title: '策略id',
        dataIndex: 'tactic_id',
        key: 'tactic_id',
        align: 'center',
      },
      {
        title: '销售价格',
        key: 'income',
        align: 'center',
        render: (row: any) => `${row.income_price}(${row.income_price_curr})`,
      },
      {
        title: '成本价格',
        key: 'price',
        align: 'center',
        render: (row: any) => `${row.price}(${row.price_curr})`,
      },
    ];
    if (obj_type !== 'overall') {
      cols.unshift({
        title: obj_type,
        dataIndex: 'object',
        key: 'object',
        align: 'center',
      });
    }
    return cols;
  }, [allCountry, obj_type]);

  const rowSelection = {
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(selectedRowKeys);
    },
  };

  useEffect(() => {
    !visible && setSelectedRowKeys([]);
  }, [visible]);

  return (
    <Modal
      open={visible}
      onCancel={() => {
        setSelectedRowKeys([]);
        setVisible(false);
      }}
      width={900}
      onOk={() => {
        const noCheck = list
          .map((v: any) => `${v.country_code}_${v.tactic_id}_${v.object}`)
          .filter((el: any) => !selectedRowKeys.includes(el));
        continueSubmit(noCheck);
      }}
      okText="继续提交"
      confirmLoading={loading}
      destroyOnClose={true}
      maskClosable={false}
    >
      <Alert message="以下策略销售价格低于成本价格，请手动勾选需要提交的策略" type="info" />
      <Table
        columns={columns}
        dataSource={list}
        size="middle"
        rowKey={(record: any) => `${record.country_code}_${record.tactic_id}_${record.object}`}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        pagination={false}
      />
    </Modal>
  );
}
