import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Table, Alert, Typography } from 'antd';
import { useDialog } from '@/utils/react-use/useDialog';
import { smsTypeValueMap } from '@/const/const';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

interface Props {
  dialogRef: any;
  continueSubmit: (arg: any) => void;
  loading: boolean;
}

const { Text } = Typography;

export default function CheckChannelAttributesOrPriceModal(props: Props) {
  const { regionOptions = [] } = useFetchCountryInfo();

  const { dialogRef, continueSubmit, loading } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
    type: number;
  }>(dialogRef);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const { initialValues, type } = defaultVal;

  const [idKey, setIdKey] = useState('group_id');

  useEffect(() => {
    setIdKey(type === 1 ? 'group_id' : 'tactic_id');
  }, [type]);

  const columns: any = useMemo(() => {
    return [
      {
        title: idKey === 'group_id' ? '通道组id' : '通道策略id',
        dataIndex: idKey,
        key: idKey,
        align: 'center',
      },
      {
        title: '通道id',
        dataIndex: 'account_id',
        key: 'account_id',
        align: 'center',
      },
      {
        title: '短信类型',
        // dataIndex: 'sms_type',
        key: 'sms_type',
        align: 'center',
        render: (row: any) => (
          <Text type={row.diff_keys?.includes('sms_type') ? 'danger' : undefined}>
            {smsTypeValueMap.find((el) => el.value === row.sms_type)?.text}
          </Text>
        ),
      },
      {
        title: '国家码',
        // dataIndex: 'country_code',
        key: 'country_code',
        align: 'center',
        render: (row: any) => (
          <Text type={row.diff_keys?.includes('country_code') ? 'danger' : undefined}>
            {regionOptions.find((el) => el.value === row.country_code)?.label}
          </Text>
        ),
      },
      {
        title: '报价id',
        dataIndex: 'price_id',
        key: 'price_id',
        align: 'center',
      },
      {
        title: '是否卡发',
        // dataIndex: 'is_sim_farm',
        key: 'is_sim_farm',
        align: 'center',
        render: (row: any) => (
          <Text type={row.diff_keys?.includes('is_sim_farm') ? 'danger' : undefined}>
            {row.is_sim_farm !== undefined ? (row.is_sim_farm ? '是' : '否') : ''}
          </Text>
        ),
      },
      {
        title: '是否本地发国际',
        // dataIndex: 'is_local_to_oversea',
        key: 'is_local_to_oversea',
        align: 'center',
        render: (row: any) => (
          <Text type={row.diff_keys?.includes('is_local_to_oversea') ? 'danger' : undefined}>
            {row.is_local_to_oversea !== undefined ? (row.is_local_to_oversea ? '是' : '否') : ''}
          </Text>
        ),
      },
      {
        title: '是否语音',
        // dataIndex: 'is_voice',
        key: 'is_voice',
        align: 'center',
        render: (row: any) => (
          <Text type={row.diff_keys?.includes('is_voice') ? 'danger' : undefined}>
            {row.is_voice !== undefined ? (row.is_voice ? '是' : '否') : ''}
          </Text>
        ),
      },
      {
        title: '是否OTT',
        // dataIndex: 'is_ott',
        key: 'is_ott',
        align: 'center',
        render: (row: any) => (
          <Text type={row.diff_keys?.includes('is_ott') ? 'danger' : undefined}>
            {row.is_ott !== undefined ? (row.is_ott ? '是' : '否') : ''}
          </Text>
        ),
      },
      {
        title: '提示',
        key: 'notice',
        align: 'center',
        render: (row: any) => (
          <Text type="danger">{!row.price_id ? '无报价' : '通道属性不匹配'}</Text>
        ),
      },
    ];
  }, [idKey, regionOptions]);

  const rowSelection = {
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      const keys = selectedRows.map((e: any) => `${e[idKey]}_${e.account_id}`);
      setSelectedRowKeys(keys);
    },
  };

  useEffect(() => {
    !visible && setSelectedRowKeys([]);
  }, [visible]);

  return (
    <Modal
      open={visible}
      onCancel={() => {
        setVisible(false);
      }}
      width={900}
      onOk={async () => {
        const noCheck = initialValues?.filter(
          (el: any) => !selectedRowKeys.includes(`${el[idKey]}_${el.account_id}`),
        );
        continueSubmit(noCheck.map((el: any) => `${el[idKey]}_${el.account_id}`));
      }}
      okText="继续提交"
      confirmLoading={loading}
      destroyOnClose={true}
      maskClosable={false}
    >
      <Alert message="以下通道因无报价/通道属性不匹配无法被提交，请知悉" type="info" />
      <Table
        columns={columns}
        dataSource={initialValues}
        size="middle"
        rowKey={(record: any) => `${record[idKey]}_${record.account_id}`}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        pagination={false}
      />
    </Modal>
  );
}
