import type { ReactNode } from 'react';
import { history } from 'umi';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Table, Form, message, Popconfirm, Select, Modal } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useList } from 'react-use';
import { getCountryList } from '@/services/api';

import _, { unionBy, differenceBy } from 'lodash';
import { useQuery } from '@/utils/react-use/useQuery';
import { deleteRessiueConfiguration } from '@/services/ressiue';

interface Props {
  showBatchDelete?: boolean;
  obj: 'uin' | 'sdkappid' | 'force' | 'global';
  getListFn: (obj: Page & Record<string, any>) => Promise<any>;
  deleteFn: (obj: { mcc?: string } & Record<string, any>) => Promise<any>;
  searchFormItem?: ReactNode;
}

type Page = {
  page_index: number;
  page_size: number;
};

const ChannelCountryList = (props: Props) => {
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [form] = Form.useForm();
  const { from: queryFrom } = useQuery();
  const [countryList, { set, insertAt, removeAt }] = useList<any[]>([]);
  const [allCountry, setAllCountry] = useState<
    {
      country_code: string;
      value: string;
      label: string;
    }[]
  >([]);
  const [selectedCountry, setSelectedCountry] = useState([]);
  const [visible, setVisible] = useState(false);
  const { getListFn, searchFormItem, obj, deleteFn, showBatchDelete = false } = props;
  const [loading, setLoading] = useState(false);

  async function fetchCountryList() {
    const res = await getCountryList();
    setAllCountry(
      res.data.map((el: any) => ({
        label: `${el.nation_name}_${el.nation_code}(${el.mcc})`,
        value: el.mcc,
        country_code: el.nation_code,
      })),
    );
    return res.data || [];
  }

  const getList = useCallback(
    async (vals?: any, from?: string) => {
      setLoading(true);
      const res = await getListFn({
        page_index: 1,
        page_size: 10000,
        ...vals,
      });
      const list = res.data || [];
      const union =
        _.unionBy(from ? [...history.location.state?.countryListNew, ...list] : list, 'mcc') || [];
      const arr = union.map((el: any) => {
        const item = {
          ...el,
          country_name: allCountry.find((c: any) => {
            return String(el.mcc) === String(c.value);
          })?.label,
          country_code: allCountry.find((c: any) => {
            return String(el.mcc) === String(c.value);
          })?.country_code,
        };
        if (obj === 'sdkappid' || obj === 'uin') {
          item[obj] = form.getFieldValue(obj);
        } else if (obj === 'force') {
          item.obj_type = form.getFieldValue('obj_type');
          item.obj_id = form.getFieldValue('obj_id');
        }
        return item;
      });
      set(arr);
      setLoading(false);
      // return arr || [];
    },
    [allCountry, form, getListFn, obj, set],
  );

  const handleDelete = useCallback(
    async (row: any, action?: 1 | 2) => {
      const vals = form.getFieldsValue();
      const params =
        obj === 'sdkappid' || obj === 'uin'
          ? {
              id: vals[obj],
              mcc: row.mcc,
              action,
            }
          : {
              ...vals,
              mcc: row.mcc,
            };
      try {
        const res = await deleteFn(params);
        if (res.code === 0) {
          message.success('删除成功');
          const vals = form.getFieldsValue();
          Object.keys(vals).forEach((k) => {
            if (!vals[k]) delete vals[k];
          });
          getList(vals);
        }
      } catch (err) {
        message.error('删除失败');
        console.log(err);
      }
    },
    [deleteFn, form, getList, obj],
  );

  async function handleDeleteRessiue(row: any) {
    try {
      const vals = form.getFieldsValue();
      const res = await deleteRessiueConfiguration({
        id: vals[obj],
        mcc: row.mcc,
        mnc: '000',
        type: obj,
      });
      if (res.code === 0) {
        message.success('删除成功');
        const vals = form.getFieldsValue();
        Object.keys(vals).forEach((k) => {
          if (!vals[k]) delete vals[k];
        });
        getList(vals);
      }
    } catch (err) {
      message.error('删除失败');
      console.log(err);
    }
  }

  const columns: any = useMemo(() => {
    const showSupply = obj === 'sdkappid' || obj === 'uin';
    const supplyColumn = showSupply
      ? [
          {
            title: '补发通道',
            align: 'center',
            render: (row: any) => (
              <>
                <Button
                  type="link"
                  onClick={() =>
                    history.push(`/channel/${obj}/ressiue`, {
                      row: { ...row, sms_type: '1' },
                      searchVals: form.getFieldsValue(),
                      countryListNew: countryList.filter((el: any) => !!el.is_new), // 新增的国家
                    })
                  }
                >
                  编辑补发
                </Button>
                <Button
                  type="link"
                  onClick={() =>
                    history.push(`/channel/${obj}/ressiue-view`, {
                      row: { ...row, sms_type: '1' },
                      searchVals: form.getFieldsValue(),
                      countryListNew: countryList.filter((el: any) => !!el.is_new), // 新增的国家
                    })
                  }
                >
                  查看补发
                </Button>
                <Popconfirm
                  title={
                    <>
                      <h4>确认删除该补发配置吗，删除后无法恢复？</h4>
                    </>
                  }
                  onConfirm={() => handleDeleteRessiue(row)}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button type="link">删除补发</Button>
                </Popconfirm>
              </>
            ),
          },
        ]
      : [];
    return [
      {
        title: '序号',
        render: (row: any, t: any, i: any) => <>{i + 1}</>,
        align: 'center',
      },
      {
        title: '国家/地区',
        dataIndex: 'country_name',
        key: 'country_name',
        align: 'center',
        filters: countryList.map((c: any) => ({ text: c.country_name, value: c.country_name })),
        onFilter: (value: string, record: any) => record.country_name.indexOf(value) === 0,
      },
      {
        title: showSupply ? '主通道' : '操作',
        align: 'center',
        render: (row: any) => (
          <>
            {obj === 'force' ? (
              <Button
                type="link"
                onClick={() =>
                  history.push(`/channel/${obj}/config`, {
                    row: { ...row, sms_type: '1' },
                    searchVals: form.getFieldsValue(),
                    countryListNew: countryList.filter((el: any) => !!el.is_new), // 新增的国家
                  })
                }
              >
                编辑通道
              </Button>
            ) : (
              <>
                <Button
                  type="link"
                  onClick={() =>
                    history.push(`/channel/${obj}/config`, {
                      row: { ...row, sms_type: '1' },
                      searchVals: form.getFieldsValue(),
                      countryListNew: countryList.filter((el: any) => !!el.is_new), // 新增的国家
                    })
                  }
                >
                  编辑OTP通道
                </Button>
                <Button
                  type="link"
                  onClick={() =>
                    history.push(`/channel/${obj}/config`, {
                      row: { ...row, sms_type: '2' },
                      searchVals: form.getFieldsValue(),
                      countryListNew: countryList.filter((el: any) => !!el.is_new), // 新增的国家
                    })
                  }
                >
                  编辑通知通道
                </Button>
                <Button
                  type="link"
                  onClick={() =>
                    history.push(`/channel/${obj}/config`, {
                      row: { ...row, sms_type: '4' },
                      searchVals: form.getFieldsValue(),
                      countryListNew: countryList.filter((el: any) => !!el.is_new), // 新增的国家
                    })
                  }
                >
                  编辑营销通道
                </Button>
              </>
            )}
            <Popconfirm
              title={
                <>
                  <h4>
                    {showSupply
                      ? '此操作将同步删除主通道和补发通道，删除后无法恢复，请确认'
                      : '确认删除此条数据吗，请确认？'}
                  </h4>
                </>
              }
              onConfirm={() => handleDelete(row, 2)}
              okText="Yes"
              cancelText="No"
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          </>
        ),
      },
      ...supplyColumn,
    ];
  }, [countryList, form, handleDelete, obj]);

  useEffect(() => {
    fetchCountryList();
  }, []);

  useEffect(() => {
    const searchVals = history.location.state?.searchVals;
    if (!queryFrom || !searchVals) {
      return;
    }
    form.setFieldsValue({ ...searchVals });
    getList(searchVals, '1');
  }, [allCountry, form, getList, queryFrom]);

  function onSubmit(vals: any) {
    Object.keys(vals).forEach((k) => {
      if (!vals[k]) delete vals[k];
    });
    getList(vals);
  }

  function handleOk() {
    const addItems = selectedCountry.map((v: string) => {
      const row: any = {
        mcc: v,
        country_name: allCountry.find((el: any) => el.value === v)?.label,
        country_code: allCountry.find((el: any) => el.value === v)?.country_code,
        is_new: 1, // 此次新增
      };
      if (obj === 'sdkappid' || obj === 'uin') {
        row[obj] = form.getFieldValue(obj);
      } else if (obj === 'force') {
        row.obj_type = form.getFieldValue('obj_type');
        row.obj_id = form.getFieldValue('obj_id');
      }
      row.sms_type = form.getFieldValue('sms_type');
      return row;
    });
    //  @ts-ignore
    addItems.forEach((el) => {
      insertAt(0, el);
    });
    setVisible(false);
  }

  async function handleBatchDelete() {
    // 因为存在临时添加的数据，所以直接使用useList中的数据进行删除 并且不拉取数据库数据（因为会覆盖掉）
    const vals = form.getFieldsValue();
    try {
      // 删除数据库中的数据
      const res = await deleteFn({
        ...vals,
        mcc_list: selectedKeys.join(','),
      });
      if (res.code === 0) {
        console.log(vals);
        Object.keys(vals).forEach((k) => {
          if (!vals[k]) delete vals[k];
        });
        getList(vals);
        message.success('删除成功');
      }
    } catch (err) {
      message.error('数据库删除失败，已自动刷新');
      console.log(err);
    } finally {
      setSelectedKeys([]);
    }
  }

  const disabledBatchDelete = selectedKeys.length === 0;

  return (
    <>
      <Button
        type="primary"
        style={{ marginBottom: 10 }}
        onClick={() => {
          if ((obj === 'sdkappid' || obj === 'uin') && !form.getFieldValue(obj)) {
            message.error(`请先输入${obj}`);
            return;
          }
          if (obj === 'force' && !form.getFieldValue('obj_id')) {
            message.error(`请先输入调度对象`);
            return;
          }
          setVisible(true);
        }}
      >
        添加国家/地区
      </Button>
      <Form
        className="sender-search-form"
        labelCol={{ span: 6 }}
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        {searchFormItem}
        <Form.Item name="mcc">
          <Select
            showSearch
            allowClear
            placeholder="国家/地区"
            filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
            options={allCountry}
            style={{ width: 250 }}
          />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
        {showBatchDelete && (
          <Button
            type="primary"
            htmlType="button"
            disabled={disabledBatchDelete}
            onClick={handleBatchDelete}
          >
            批量删除
          </Button>
        )}
      </Form>

      <Table
        rowSelection={
          showBatchDelete
            ? {
                selectedRowKeys: selectedKeys,
                onChange(selectedRowKeys) {
                  setSelectedKeys(selectedRowKeys as string[]);
                },
              }
            : undefined
        }
        columns={columns}
        pagination={{
          onChange() {
            setSelectedKeys([]);
          },
        }}
        dataSource={countryList}
        rowKey={(record: any) => record.mcc}
        loading={loading}
      />
      <Modal
        title="添加国家/地区"
        open={visible}
        onOk={handleOk}
        onCancel={() => setVisible(false)}
        destroyOnClose
      >
        <Form>
          <Form.Item name="country_name" label="国家/地区：">
            <Select
              showSearch
              mode="multiple"
              allowClear
              placeholder="请选择"
              options={differenceBy(
                allCountry,
                countryList.map((el: any) => ({ label: el.mcc, value: el.mcc })),
                'value',
              )}
              filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
              style={{ width: 250 }}
              onChange={(val) => setSelectedCountry(val)}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ChannelCountryList;
