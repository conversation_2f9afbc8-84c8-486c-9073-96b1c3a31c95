import React, { useEffect, useState } from 'react';
import { Modal, Button, Input, Form, Select, InputNumber, Alert, Table, Switch, Radio } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import _ from 'lodash';
import { getMccMncList } from '@/services/channel';
import { smsType, smsTypeValueMap, strategyTypes } from '@/const/const';
import ScrollLoadMore from '@/components/ScrollLoadMore';
import { addProviderGroupStrategy, editProviderGroupStrategy } from '@/services/channelStrategy';
import { _useMccMncInfo } from '@/pages/global-state';
import { tacticCategory } from './const';
import { findLabel } from '@/utils/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { yesOrNO } from '@/pages/dispatch/component/const';
interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
}

const AddChannelGroupStrategy = (props: DialogProps) => {
  const { regionOptions = [] } = useFetchCountryInfo();
  const [form] = Form.useForm();
  const [mccMncInfo] = _useMccMncInfo();
  const { dialogRef, reload } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
    productType: any[];
  }>(dialogRef);
  const { initialValues, productType } = defaultVal;
  const [loading, setLoading] = useState(false);
  const rules = [{ required: !initialValues?.tactic_id }];
  const [changedFields, setChangedField] = useState(['tactic_id', 'mnc', 'mcc']);

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      setChangedField(['tactic_id']);
      return;
    }
    form.setFieldsValue({
      ...initialValues,
      is_auto: initialValues?.is_auto === 0,
      operator: `${initialValues?.mcc}_${initialValues?.mnc}`,
    });
  }, [form, initialValues, visible]);

  async function onFinish(vals: any) {
    try {
      setLoading(true);
      const isEdit = !!initialValues?.tactic_id;
      let _vals = _.cloneDeep(vals);
      if (isEdit) {
        _vals = _.pickBy(_vals, (v, index) => !_.isNil(v) && changedFields.includes(index));
      }
      if (vals.operator) {
        _vals.mnc = vals.operator.split('_')?.[1];
      }

      const productTypeLabel = findLabel(productType, vals.product_type);
      const smsTypeLabel = findLabel(smsType, vals.sms_type);
      const countryCodes = _.isArray(vals.country_code) ? vals.country_code : [vals.country_code];
      const names: any = _.reduce(
        countryCodes,
        (res, c) => {
          const countryName = regionOptions.find((v) => v.value === c)?.label?.split('_')?.[0];
          const name = `${countryName}-${productTypeLabel}-${smsTypeLabel}-标品${
            vals.name_remark ? `-${vals.name_remark}` : ''
          }`;
          return { ...res, [c]: vals.category === 0 ? vals.name : name };
        },
        {},
      );
      try {
        const res = isEdit
          ? await editProviderGroupStrategy({
              ..._.omit(_vals, ['operator']),
              name: vals.category === 0 ? vals?.name : names[countryCodes?.[0]],
              tactic_id: initialValues?.tactic_id,
              sms_type: _.isArray(vals.sms_type) ? _.sum(vals.sms_type) : vals.sms_type,
              is_ott: 0,
              is_voice: 0,
              is_local_to_oversea: 0,
              is_sim_farm: 0,
              tactic_type: vals.category === 0 ? vals.tactic_type : 7,
              mnc: _vals.mnc ?? '000',
              mcc: _.find(mccMncInfo, (v) => v.country_code === countryCodes?.[0])?.mcc,
              is_auto: !vals.is_auto ? 1 : 0,
              inquiry_status: vals.inquiry_status ? 1 : 0,
            })
          : await addProviderGroupStrategy({
              ..._.omit(_vals, ['operator', 'country_code', 'name']),
              names,
              sms_type: _.isArray(vals.sms_type) ? _.sum(vals.sms_type) : vals.sms_type,
              is_ott: 0,
              is_voice: 0,
              is_local_to_oversea: 0,
              is_sim_farm: 0,
              tactic_type: vals.category === 0 ? vals.tactic_type : 7,
              mnc: _vals.mnc ?? '000',
              countries: countryCodes.map((el: any) => ({
                country_code: el,
                mcc: _.find(mccMncInfo, (v) => v.country_code === el)?.mcc,
              })),
              is_auto: !vals.is_auto ? 1 : 0,
              inquiry_status: vals.inquiry_status ? 1 : 0,
            });
        setLoading(false);
        reload();
        setVisible(false);
        if (res.code === 90015 && !isEdit) {
          Modal.error({
            title: '重复添加',
            width: 600,
            content: (
              <>
                <Table
                  dataSource={res.data}
                  columns={[
                    {
                      title: '国家码',
                      dataIndex: 'country_code',
                      key: 'country_code',
                      align: 'center',
                      render: (val: string) => regionOptions.find((el) => el.value === val)?.label,
                    },
                    {
                      title: '短信类型',
                      dataIndex: 'sms_type',
                      key: 'sms_type',
                      align: 'center',
                      render: (val: number) =>
                        smsTypeValueMap.find((el) => el.value.toString() === val?.toString())?.text,
                    },
                    {
                      title: '标品类型',
                      dataIndex: 'product_type',
                      key: 'product_type',
                      align: 'center',
                      render: (val: string) =>
                        productType?.find((el) => el.value === val)?.label ?? (val || '-'),
                    },
                    {
                      title: '运营商',
                      key: 'mnc',
                      align: 'center',
                      render: (row: any) => {
                        const { operator_name, mcc, mnc } = _.find(
                          mccMncInfo,
                          (v) => v.mcc === row.mcc && v.mnc === row.mnc,
                        ) ?? {
                          operator_name: '',
                          mcc: row.mcc,
                          mnc: row.mnc,
                        };
                        return `${operator_name}(${mcc}_${mnc})`;
                      },
                    },
                  ]}
                ></Table>
              </>
            ),
          });
        }
      } catch (err) {
        console.log(err);
        setLoading(false);
      }
    } catch (err) {
      console.log(err);
    }
  }
  function onFieldsChange(changedFields: any) {
    if (changedFields?.[0]?.name?.[0]) {
      setChangedField((prev: string[]) => {
        const n: string[] = prev.concat(changedFields?.[0]?.name?.[0]);
        return [...new Set(n), 'mnc', 'mcc'];
      });
    }
    if (changedFields?.[0]?.name?.includes('sms_type')) {
      !initialValues?.tactic_id && form.setFieldValue('is_auto', changedFields?.[0]?.value === 1);
    }
  }

  return (
    <Modal
      title={initialValues?.tactic_id ? '编辑' : '添加'}
      open={visible}
      footer={null}
      onCancel={() => setVisible(false)}
      destroyOnClose={true}
    >
      <Form
        className="sender-search-form"
        labelCol={{ span: 7 }}
        form={form}
        labelAlign="left"
        onFinish={(vals) => onFinish(vals)}
        onFieldsChange={onFieldsChange}
        initialValues={{ inquiry_status: 1 }}
      >
        <Alert
          message="选择分运营商标品，会自动生成通道策略名称，可添加名称备注"
          style={{ marginBottom: 20 }}
        ></Alert>
        <Form.Item name="category" label="标品类别" rules={rules}>
          <Select
            options={tacticCategory}
            placeholder="请选择"
            onChange={(val) => {
              if (val === 2) {
                form.setFieldsValue({
                  country_code: undefined,
                  sms_type: 1,
                  recalculate_threshold: 20,
                  min_cr: 70,
                  highest_cost: 0.2,
                  is_auto: true,
                });
              } else if (val === 1) {
                form.setFieldsValue({
                  country_code: undefined,
                  sms_type: undefined,
                  recalculate_threshold: 20,
                  min_cr: 70,
                  highest_cost: 0.2,
                });
              } else {
                form.setFieldsValue({
                  country_code: undefined,
                  sms_type: undefined,
                  recalculate_threshold: undefined,
                  min_cr: undefined,
                  highest_cost: undefined,
                });
              }
            }}
          ></Select>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.category !== curValues.category}
        >
          {({ getFieldValue }) => {
            const category = getFieldValue('category');
            const old = category === 0;
            return (
              <>
                {old && (
                  <Form.Item name="name" label="策略名称" rules={rules}>
                    <Input placeholder="请输入"></Input>
                  </Form.Item>
                )}
                <Form.Item name="sms_type" label="短信类型" rules={rules}>
                  <Select
                    mode={old ? 'multiple' : undefined}
                    allowClear
                    showSearch
                    placeholder="请选择"
                    options={smsType}
                  />
                </Form.Item>
                {old ? (
                  <>
                    <Form.Item name="tactic_type" label="策略类型" rules={rules}>
                      <Select allowClear placeholder="请选择" options={strategyTypes} />
                    </Form.Item>
                  </>
                ) : null}
                <Form.Item name="country_code" label="国家码" rules={rules}>
                  <Select
                    options={regionOptions}
                    placeholder="请选择"
                    value={form.getFieldValue('country_code')}
                    mode={category === 2 ? 'multiple' : undefined}
                    onChange={(value) => {
                      form.setFieldsValue({
                        country_code: value,
                        operator: undefined,
                      });
                    }}
                    filterOption={(inputValue, option) => !!option?.label.includes(inputValue)}
                    showSearch
                    allowClear
                  />
                </Form.Item>
              </>
            );
          }}
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.country_code !== curValues.country_code ||
            prevValues.category !== curValues.category
          }
        >
          {({ getFieldValue }) => {
            const countryCode = getFieldValue('country_code');
            const operator = getFieldValue('operator');
            const category = getFieldValue('category');
            return category !== 2 ? (
              <Form.Item name="operator" label="运营商" labelCol={{ span: 7 }}>
                <ScrollLoadMore
                  value={operator}
                  reloadOn={countryCode}
                  onChange={(value) => {
                    if (!value) {
                      form.setFieldsValue({
                        operator: undefined,
                      });
                      return;
                    }
                    form.setFieldsValue({
                      operator: value,
                    });
                  }}
                  loadFn={async (params) => {
                    const pageParams = {
                      page_index: params.page_index,
                      page_size: params.page_size,
                      search_key: params.search_key,
                      country_code: countryCode,
                    };
                    const res = await getMccMncList({
                      ...pageParams,
                    });
                    const options = res?.data?.list?.map(
                      (item: {
                        country_code: string;
                        operator_name: string;
                        mcc: string;
                        mnc: string;
                      }) => {
                        return {
                          value: `${item.mcc}_${item.mnc}`,
                          label: `${item.operator_name}(${item.mnc})`,
                        };
                      },
                    );
                    if (countryCode) {
                      form.setFieldsValue({ mcc: res?.data?.list?.[0].mcc });
                    }
                    return {
                      options,
                      total: res.data.count,
                    };
                  }}
                />
              </Form.Item>
            ) : null;
          }}
        </Form.Item>
        <Form.Item name="product_type" label="标品类型" rules={rules}>
          <Select
            options={productType}
            showSearch
            placeholder="请选择"
            filterOption={(inputValue, option) =>
              !!option?.label.toLowerCase().includes(inputValue.toLowerCase())
            }
          ></Select>
        </Form.Item>
        <Form.Item name="highest_cost" label="标品支持最高成本" rules={rules}>
          <InputNumber addonAfter="USD"></InputNumber>
        </Form.Item>
        <Form.Item name="min_cr" label="标品最低cr" rules={rules}>
          <InputNumber></InputNumber>
        </Form.Item>
        <Form.Item name="recalculate_threshold" label="重新调度阈值" rules={rules}>
          <InputNumber></InputNumber>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.category !== curValues.category}
        >
          {({ getFieldValue }) => {
            const category = getFieldValue('category');
            return (
              category !== 0 && (
                <Form.Item name="is_auto" label="自动调度" rules={rules}>
                  <Switch></Switch>
                </Form.Item>
              )
            );
          }}
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.category !== curValues.category}
        >
          {({ getFieldValue }) => {
            const category = getFieldValue('category');
            return (
              category !== 0 && (
                <Form.Item name="inquiry_status" label="参与询价" rules={rules}>
                  <Radio.Group options={yesOrNO}></Radio.Group>
                </Form.Item>
              )
            );
          }}
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.category !== curValues.category}
        >
          {({ getFieldValue }) => {
            const old = getFieldValue('category') === 0;
            return (
              <>
                {!old && (
                  <Form.Item name="name_remark" label="名称备注">
                    <Input placeholder="请输入名称备注" />
                  </Form.Item>
                )}
              </>
            );
          }}
        </Form.Item>
        {/* <Form.Item name="is_sim_farm" label="是否卡发" rules={rules}>
          <RadioGroup>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item name="is_local_to_oversea" label="是否本地发国际" rules={rules}>
          <RadioGroup>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item name="is_voice" label="是否语音" rules={rules}>
          <RadioGroup>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item name="is_ott" label="是否OTT" rules={rules}>
          <RadioGroup>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </RadioGroup>
        </Form.Item> */}
        <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
          <Button htmlType="submit" type="primary" loading={loading}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddChannelGroupStrategy;
