import React, { useMemo } from 'react';
import { isMobile } from '@/const/jadgeUserAgent';
import { Table, Tabs } from 'antd';
import { _useMccMncInfo } from '@/pages/global-state';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getChannleDispatchInfo } from '@/services/channelDispatchInfo';
import _ from 'lodash';
import { site, smsTypeValueMap } from '@/const/const';
import { channelType } from './const';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import { probeConfTypeOptions } from '@/pages/tactic-resources/probeTraficTestingWeightConf';
import { getProviderAccountList } from '@/services/channel';
import { findLabel } from '@/utils/utils';

const siteMaps = [
  {
    site: site.china,
    affix: 'hk',
    text: '香港',
  },
  {
    site: site.singapore,
    affix: 'sg',
    text: '新加坡',
  },
  {
    site: site.germany,
    affix: 'gm',
    text: '法兰克福',
  },
];

const ChannelDispatchTabs = ({
  searchKeys,
  tableProps,
}: {
  searchKeys: Record<string, any>;
  tableProps?: Record<string, any>;
}) => {
  const [mccMncInfo] = _useMccMncInfo();
  const { regionOptions = [], regionOptionsMcc = [] } = useFetchCountryInfo();
  const { value: state, loading } = useAsyncRetryFunc(async () => {
    if (!searchKeys.channel_id || _.isNil(searchKeys.type)) return {};
    const result = await getChannleDispatchInfo({ ...searchKeys });
    return _.isEmpty(result?.data) ? {} : result.data;
  }, [searchKeys]);

  const { value: accountList } = useAsyncRetryFunc(async () => {
    const result = await getProviderAccountList();
    return result.data?.list ?? [];
  }, []);

  const infos = useMemo(() => {
    return {
      uin_conf_hk: state?.uin_conf_hk ?? [],
      uin_conf_sg: state?.uin_conf_sg ?? [],
      uin_conf_gm: state?.uin_conf_gm ?? [],
      sdk_conf_hk: state?.sdk_conf_hk ?? [],
      sdk_conf_sg: state?.sdk_conf_sg ?? [],
      sdk_conf_gm: state?.sdk_conf_gm ?? [],
      overall_conf_hk: state?.overall_conf_hk ?? [],
      overall_conf_sg: state?.overall_conf_sg ?? [],
      overall_conf_gm: state?.overall_conf_gm ?? [],
      force_conf_hk: state?.force_conf_hk ?? [],
      force_conf_sg: state?.force_conf_sg ?? [],
      force_conf_gm: state?.force_conf_gm ?? [],
      bind_group_conf: state?.bind_group_conf ?? [],
      bind_tactic_conf: state?.bind_tactic_conf ?? [],
      probe_conf: state?.probe_conf?.country.concat(state?.probe_conf?.operater ?? []) ?? [],
      provider_white_list_hk: state?.provider_white_list_hk ?? [],
      provider_white_list_sg: state?.provider_white_list_sg ?? [],
      provider_white_list_gm: state?.provider_white_list_gm ?? [],
      uin_reissue_channel_conf_sg: state?.uin_reissue_channel_conf_sg ?? [],
      sdk_reissue_channel_conf_sg: state?.sdk_reissue_channel_conf_sg ?? [],
      tactic_reissue_channel_conf_sg: state?.tactic_reissue_channel_conf_sg ?? [],
      uin_reissue_channel_conf_hk: state?.uin_reissue_channel_conf_hk ?? [],
      sdk_reissue_channel_conf_hk: state?.sdk_reissue_channel_conf_hk ?? [],
      tactic_reissue_channel_conf_hk: state?.tactic_reissue_channel_conf_hk ?? [],
      uin_reissue_channel_conf_gm: state?.uin_reissue_channel_conf_gm ?? [],
      sdk_reissue_channel_conf_gm: state?.sdk_reissue_channel_conf_gm ?? [],
      tactic_reissue_channel_conf_gm: state?.tactic_reissue_channel_conf_gm ?? [],
    };
  }, [state]);

  const tabs = useMemo(() => {
    const columns = [
      {
        title: 'uin',
        dataIndex: 'uin',
        key: 'uin',
        align: 'center',
      },
      {
        title: 'sdkappid',
        dataIndex: 'sdkappid',
        key: 'sdkappid',
        align: 'center',
      },
      {
        title: '国家/地区',
        key: 'mcc',
        align: 'center',
        render: (row: any) => findLabel(regionOptionsMcc, row.mcc),
      },
      {
        title: '运营商',
        key: 'mnc',
        align: 'center',
        render: (row: any) => {
          const { operator_name, mcc, mnc } = _.find(
            mccMncInfo,
            (v) => v.mcc === row.mcc && v.mnc === row.mnc,
          ) ?? {
            operator_name: row.operator_name,
            mcc: row.mcc,
            mnc: row.mnc,
          };
          return row.mnc ? `${operator_name}(${mcc}_${mnc})` : '-';
        },
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        key: 'sms_type',
        align: 'center',
        render: (val: number) =>
          _.find(smsTypeValueMap, (el) => el.value.toString() === val?.toString())?.text,
      },
      {
        title: '通道类型',
        dataIndex: 'type',
        key: 'type',
        align: 'center',
        render: (val: number) =>
          _.find(channelType, (el) => el.value.toString() === val?.toString())?.label,
      },
      {
        title: '权重',
        dataIndex: 'weight',
        key: 'weight',
        align: 'center',
      },
      {
        title: '优先级（数值越大优先级越高）',
        dataIndex: 'level',
        key: 'level',
        align: 'center',
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        align: 'center',
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        align: 'center',
      },
      {
        title: '是否强制',
        dataIndex: 'is_forced',
        key: 'is_forced',
        align: 'center',
        editable: true,
        render: (is_forced: number) => (is_forced ? '是' : '否'),
      },
      {
        title: '国家/地区',
        dataIndex: 'country_code',
        key: 'country_code',
        align: 'center',
        render: (val: string) => _.find(regionOptions, (el) => el.value === val)?.label || val,
      },
      {
        title: '绑定配置id',
        dataIndex: 'bind_id',
        key: 'bind_id',
        align: 'center',
      },
      {
        title: '供应商账号',
        dataIndex: 'provider_id',
        key: 'provider_id',
        align: 'center',
        render: (provider_id: number | string) => {
          const account = _.find(accountList, (el) => el.provider_id === provider_id) ?? {};
          return `${account?.provider_name}_${account.supplier_name}(${account.provider_id})`;
        },
      },
      {
        title: '通道组id',
        dataIndex: 'group_id',
        key: 'group_id',
        align: 'center',
      },
      {
        title: '通道组名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
      },
      {
        title: '通道策略id',
        dataIndex: 'tactic_id',
        key: 'tactic_id',
        align: 'center',
      },
      {
        title: '探测流量',
        dataIndex: 'testing_weight',
        key: 'testing_weight',
        align: 'center',
      },
      {
        title: '配置类型',
        key: 'probe_conf_type',
        align: 'center',
        render: (row) =>
          _.find(probeConfTypeOptions, (el) => el.value.toString() === row.type.toString())?.label,
      },
      {
        title: '号码',
        key: 'mobile',
        dataIndex: 'mobile',
      },
      {
        title: '补发ID',
        key: 'reissue_id',
        dataIndex: 'reissue_id',
      },
    ];

    function findCols(cols: string[]) {
      return cols.map((v) => _.find(columns, (col) => col.key === v));
    }

    return [
      ...siteMaps.map((el: any) => {
        return {
          title: `uin调度信息(${el?.text})`,
          key: `uin_conf_${el?.affix}`,
          columns: findCols([
            'uin',
            'sms_type',
            'type',
            'mcc',
            'mnc',
            'level',
            'weight',
            'created_at',
            'updated_at',
          ]),
        };
      }),
      ...siteMaps.map((el: any) => {
        return {
          title: `应用调度信息(${el?.text})`,
          key: `sdk_conf_${el?.affix}`,
          columns: [
            {
              title: '应用id',
              dataIndex: 'sdkappid',
              key: 'sdkappid',
              align: 'center',
            },
            ...findCols([
              'mnc',
              'sms_type',
              'mcc',
              'weight',
              'type',
              'level',
              'created_at',
              'updated_at',
            ]),
          ],
        };
      }),
      ...siteMaps.map((el: any) => {
        return {
          title: `全局调度信息(${el?.text})`,
          key: `overall_conf_${el?.affix}`,
          columns: findCols([
            'mcc',
            'mnc',
            'sms_type',
            'type',
            'weight',
            'level',
            'created_at',
            'updated_at',
          ]),
        };
      }),
      ...siteMaps.map((el: any) => {
        return {
          title: `强制调度信息(${el?.text})`,
          key: `force_conf_${el?.affix}`,
          columns: [
            {
              title: '绑定对象（uin/sdkappid）',
              dataIndex: 'obj_id',
              key: 'obj_id',
              align: 'center',
            },
            {
              title: '调度对象类型（uin/sdkappid）',
              dataIndex: 'obj_type',
              key: 'obj_type',
              align: 'center',
            },
            ...findCols(['mcc', 'mnc', 'is_forced', 'created_at', 'updated_at']),
          ],
        };
      }),
      {
        title: '所属通道组信息',
        key: 'bind_group_conf',
        columns: findCols([
          'bind_id',
          'group_id',
          'name',
          'sms_type',
          'country_code',
          'weight',
          'created_at',
          'updated_at',
        ]),
      },
      {
        title: '所属通道策略信息',
        key: 'bind_tactic_conf',
        columns: findCols([
          'bind_id',
          'tactic_id',
          'name',
          'sms_type',
          'country_code',
          'mnc',
          'weight',
          'created_at',
          'updated_at',
        ]),
      },
      {
        title: '通道探测流量',
        key: 'probe_conf',
        columns: findCols([
          'uin',
          'mnc',
          'provider_id',
          'probe_conf_type',
          'testing_weight',
          'created_at',
          'updated_at',
        ]),
      },
      ...siteMaps.map((el: any) => {
        return {
          title: `号码加白配置(${el?.text})`,
          key: `provider_white_list_${el?.affix}`,
          columns: findCols([
            'uin',
            'mobile',
            'country_code',
            'sms_type',
            'provider_id',
            'created_at',
            'updated_at',
          ]),
        };
      }),
      ...siteMaps.map((el: any) => {
        return {
          title: `uin补发调度信息(${el?.text})`,
          key: `uin_reissue_channel_conf_${el?.affix}`,
          columns: findCols([
            'uin',
            'mcc',
            'mnc',
            'sms_type',
            'reissue_id',
            'weight',
            'created_at',
            'updated_at',
          ]),
        };
      }),
      ...siteMaps.map((el: any) => {
        return {
          title: `应用补发调度信息(${el?.text})`,
          key: `sdk_reissue_channel_conf_${el?.affix}`,
          columns: findCols([
            'sdkappid',
            'mcc',
            'mnc',
            'sms_type',
            'reissue_id',
            'weight',
            'created_at',
            'updated_at',
          ]),
        };
      }),
      ...siteMaps.map((el: any) => {
        return {
          title: `标品补发调度信息(${el?.text})`,
          key: `tactic_reissue_channel_conf_${el?.affix}`,
          columns: findCols([
            'tactic_id',
            'mcc',
            'mnc',
            'sms_type',
            'reissue_id',
            'weight',
            'created_at',
            'updated_at',
          ]),
        };
      }),
    ].filter((tab) => infos[tab.key]?.length > 0);
  }, [accountList, infos, mccMncInfo, regionOptions, regionOptionsMcc]);

  const defaultValue = _.find(siteMaps, (el) => el.site === window.location.host)?.affix;

  return (
    <>
      {loading && <p>加载中...</p>}
      {!loading && !tabs.length && searchKeys.channel_id && <p>暂无数据</p>}
      {tabs.length ? (
        <Tabs defaultValue={`uin_conf_${defaultValue}`}>
          {tabs.map((tab) => {
            return (
              <Tabs.TabPane tab={tab.title} key={tab.key}>
                <Table
                  size="middle"
                  dataSource={infos[tab.key]}
                  columns={tab.columns as any}
                  loading={loading}
                  scroll={isMobile() ? { x: 'max-content' } : undefined}
                  style={{ marginTop: 20 }}
                  {...tableProps}
                />
              </Tabs.TabPane>
            );
          })}
        </Tabs>
      ) : null}
    </>
  );
};
export default ChannelDispatchTabs;
