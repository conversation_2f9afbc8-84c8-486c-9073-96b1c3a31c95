import React, { useEffect, useState } from 'react';
import { Button, Form, Input, Modal, Select, Table, message } from 'antd';
import { batchChannelConf, checkExistsTactic, checkIncomeCostPrice } from '@/services/channel';
import _ from 'lodash';

import { getCountryList } from '@/services/api';
import { smsType, strategyTypes } from '@/const/const';
import { isMatchCountry, level, tacticCategory } from './const';
import { useDialogRef } from '@/utils/react-use/useDialog';
import CheckTacticIncomeCostDialog from './CheckTacticIncomeCostDialog';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { filterStringSpace } from '@/utils/utils';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getProductType } from '@/services/tacticResources';
import { errorsObj } from '@/pages/global-components/BatchErrorModal';
import SelectOptionsByInput from '@/components/SelectOptionsByInput';

const MutipleCountryBindChannelByTactic = () => {
  const [mccMncInfo] = useMccMncInfo();

  const [form] = Form.useForm();
  const [allCountry, setAllCountry] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const checkDialogRef = useDialogRef();
  const [existTacticArr, setExistTacticArr] = useState<any[]>([]);

  const { value: productType } = useAsyncRetryFunc(async () => {
    const res = await getProductType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);

  async function fetchCountryList() {
    const res = await getCountryList();
    setAllCountry(
      res.data.map((el: any) => ({
        label: `${el.nation_name}_${el.nation_code}(${el.mcc})`,
        value: el.nation_code,
      })),
    );
    return res.data || [];
  }

  async function batchConfig(params: any, vals: any) {
    try {
      if (params.length === 0) return message.error('请至少选择一条数据');
      setLoading(true);
      const { code } = await batchChannelConf({
        exist_tactic_arr: params,
        obj_type: vals.obj_type,
        // objects: vals.objects,
        weight: vals.weight,
        level: vals.level,
      });
      setLoading(false);
      // checkDialogRef.current.close();
      if (code === 0) {
        message.success('批量绑定成功');
      }
    } catch (error) {
      setLoading(false);
    }
  }

  async function checkIncome(data: any, vals: any) {
    if (data.length === 0) {
      return message.error('所选策略均未通过校验');
    }
    if (vals.obj_type === 'overall') {
      return batchConfig(data, vals);
    }
    const allExistTacticArr: any[] = [];
    const objectsArr = filterStringSpace(vals.objects)?.split(',') ?? [];
    objectsArr.forEach((object: string) => {
      const arr = data.map((el: any) => ({ ...el, object }));
      allExistTacticArr.push(...arr);
    });
    Modal.confirm({
      title: '是否需要进行损益校验',
      content: '损益校验会耗费较大的时间，大批量操作可在人工确认后跳过此步骤',
      okText: '继续校验',
      cancelText: '跳过并绑定',
      onOk: async () => {
        try {
          setLoading(true);
          const uniqByCountry = _.reduce(
            data,
            (result: any[], el: any) => {
              const index = result.findIndex((c: any) => c.country_code === el.country_code);
              if (index === -1) {
                result.push({ ...el, tactic_id: [el.tactic_id], sms_type: [el.sms_type] });
              } else {
                if (
                  el.tactic_id !== result[index].tactic_id ||
                  el.sms_type !== result[index].sms_type
                ) {
                  result[index]?.tactic_id.push(el.tactic_id);
                  result[index]?.sms_type.push(el.sms_type);
                }
              }
              return result;
            },
            [],
          );
          const objects = filterStringSpace(vals.objects).split(',');
          const allRequest = _.flatten(
            objects.map((object) => {
              return uniqByCountry.map((el: any) => ({
                ...el,
                object,
              }));
            }) ?? [],
          );
          const params = allRequest.map((el) => ({
            channel_ids: _.uniq(el.tactic_id).join(','),
            mcc: el.mcc,
            // sdkappid: initialValues?.sdkappid,
            [vals.obj_type]: el.object,
            type: 'tactic',
            mnc: '000',
          }));
          const res = await checkIncomeCostPrice({ params });
          if (res.code !== 0) {
            setLoading(false);
          }
          const errorData = res.data.filter((el) => el.code !== 0);
          if (errorData.length) {
            errorsObj.setVisible(true, errorData);
            setLoading(false);
            return;
          }
          const _data = _.flatMap(res.data, (el) => el.data);
          const isEmpty = _data.filter((v) => !_.isEmpty(v.cost_price_info))?.length === 0;
          if (!isEmpty) {
            const arr: any[] = [];
            _data
              .filter((o) => Object.keys(o.cost_price_info).length)
              .forEach((v) => {
                Object.entries(v.cost_price_info).forEach(([key, value]: any[]) => {
                  arr.push({
                    ...value,
                    object: v.object,
                    country_code: v?.country_code,
                    tactic_id: key,
                    income_price: v.income_price_info.price,
                    income_price_curr: v.income_price_info.price_curr,
                  });
                });
              });
            checkDialogRef.current.open({
              list: arr,
              obj_type: vals.obj_type,
            });
            setLoading(false);
          } else {
            batchConfig(allExistTacticArr, vals);
          }
        } catch (error) {
          console.log(error);
          setLoading(false);
        }
      },
      onCancel: () => {
        batchConfig(allExistTacticArr, vals);
      },
    });
  }

  async function onSubmit(vals: any) {
    try {
      setLoading(true);
      const params = {
        sms_type_arr: vals.sms_type,
        tactic_type: vals.tactic_type,
        country_arr: vals.country_code,
        category: vals.category,
        product_type: vals.product_type ?? 0,
      };
      const { code, data } = await checkExistsTactic(params);
      const { exists_tactics, not_exists_tactics } = data;
      setExistTacticArr(exists_tactics);
      setLoading(false);
      if (code === 0) {
        if (not_exists_tactics.length > 0) {
          const dataSource: any[] = not_exists_tactics.map((el: any) => {
            const country = allCountry?.find((c: any) => c.value === el.country_code)?.label;
            return {
              ...el,
              country,
            };
          });
          Modal.confirm({
            title: '提示',
            width: '50%',
            content: (
              <>
                <p>以下策略缺失，请确认</p>
                <Table
                  dataSource={dataSource}
                  scroll={{ x: 'max-content' }}
                  rowKey={(el) => `${el.mnc}_${el.country_code}_${el.sms_type}`}
                  columns={[
                    {
                      title: '国家',
                      dataIndex: 'country',
                      key: 'country',
                    },
                    {
                      title: '运营商',
                      key: 'mnc',
                      render: (row) => {
                        const item: any = mccMncInfo?.find((el) => {
                          return el.mnc.toString() === row.mnc.toString();
                        });
                        return `${item?.operator_name}(${item?.mnc})`;
                      },
                    },
                    {
                      title: '短信类型',
                      key: 'sms_type',
                      render: (row) => smsType.find((el) => el.value === row.sms_type)?.label,
                    },
                    {
                      title: '策略类型',
                      key: 'tactic_type',
                      render: (row) =>
                        strategyTypes.find((el) => el.value === row.tactic_type)?.label,
                    },
                    {
                      title: '原因',
                      dataIndex: 'err_msg',
                      key: 'err_msg',
                    },
                  ]}
                />
              </>
            ),
            onOk: () => {
              checkIncome(exists_tactics, vals);
            },
            onCancel() {},
          });
        } else {
          checkIncome(exists_tactics, vals);
        }
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchCountryList();
  }, []);

  return (
    <>
      <div style={{ width: 700 }}>
        <Form
          form={form}
          onFinish={(vals) => onSubmit(vals)}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 16 }}
        >
          <Form.Item name="obj_type" rules={[{ required: true }]} label="调度对象类型">
            <Select
              placeholder="调度对象类型"
              options={[
                {
                  label: 'uin',
                  value: 'uin',
                },
                {
                  label: 'sdkappid',
                  value: 'sdkappid',
                },
                {
                  label: '全局',
                  value: 'overall',
                },
              ]}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.obj_type !== curValues.obj_type}
          >
            {({ getFieldValue }) => {
              const objType = getFieldValue('obj_type');
              return objType === 'overall' ? null : (
                <Form.Item name="objects" rules={[{ required: true }]} label="调度对象">
                  <Input placeholder="支持多个，英文逗号分隔" />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item name="country_code" label="国家/地区" rules={[{ required: true }]}>
            <SelectOptionsByInput
              mode="multiple"
              showSearch
              allowClear
              placeholder="国家/地区"
              filterOption={(val: string, opt: any) =>
                opt.label.toLowerCase().includes(val.toLowerCase())
              }
              options={allCountry}
              maxTagCount={30}
            />
          </Form.Item>
          <Form.Item name="sms_type" rules={[{ required: true }]} label="短信类型">
            <Select
              mode="multiple"
              showSearch
              allowClear
              placeholder="请选择短信类型"
              filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
              options={smsType}
            />
          </Form.Item>
          <Form.Item name="category" label="标品类别" rules={[{ required: true }]}>
            <Select options={tacticCategory} placeholder="请选择"></Select>
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.category !== curValues.category}
          >
            {({ getFieldValue }) => {
              const category = getFieldValue('category');
              const old = category === 0;
              return (
                <>
                  {old ? (
                    <>
                      <Form.Item name="tactic_type" rules={[{ required: true }]} label="策略类型">
                        <Select
                          showSearch
                          allowClear
                          placeholder="请选择策略类型"
                          filterOption={(val, opt: any) =>
                            opt.label.toLowerCase().includes(val.toLowerCase())
                          }
                          options={strategyTypes}
                        />
                      </Form.Item>
                    </>
                  ) : (
                    <Form.Item name="product_type" label="标品类型" rules={[{ required: true }]}>
                      <Select
                        options={productType}
                        showSearch
                        placeholder="请选择"
                        filterOption={(inputValue, option) =>
                          !!option?.label.toLowerCase().includes(inputValue.toLowerCase())
                        }
                      ></Select>
                    </Form.Item>
                  )}
                </>
              );
            }}
          </Form.Item>
          <Form.Item name="weight" rules={[{ required: true }]} label="权重">
            <Input placeholder="请输入权重" />
          </Form.Item>
          <Form.Item name="level" rules={[{ required: true }]} label="优先级">
            <Select
              showSearch
              allowClear
              placeholder="请选择优先级"
              filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
              options={level}
              defaultValue="2"
            />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Button htmlType="submit" type="primary" loading={loading}>
              提交
            </Button>
            <Button htmlType="button" style={{ marginLeft: 10 }} onClick={() => form.resetFields()}>
              重置
            </Button>
          </Form.Item>
        </Form>
      </div>
      <CheckTacticIncomeCostDialog
        dialogRef={checkDialogRef}
        continueSubmit={(noCheck: any) => {
          const vals = form.getFieldsValue();
          const allExistTacticArr: any[] = [];
          const objectsArr = filterStringSpace(vals.objects).split(',');
          objectsArr.forEach((object: string) => {
            const arr = existTacticArr.map((el) => ({
              ...el,
              object,
              category: form.getFieldValue('category'),
              tactic_type: form.getFieldValue('tactic_type'),
              product_type: form.getFieldValue('product_type'),
            }));
            allExistTacticArr.push(...arr);
          });
          const data = allExistTacticArr.filter((el: any) => {
            return !noCheck.includes(`${el.country_code}_${el.tactic_id}_${el.object}`);
          });
          batchConfig(data, vals);
        }}
        loading={loading}
        allCountry={allCountry}
      />
    </>
  );
};

export default MutipleCountryBindChannelByTactic;
