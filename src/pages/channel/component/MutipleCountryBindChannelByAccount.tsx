import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Form, Input, Modal, Select, Table, message } from 'antd';
import {
  addGlobalChannel,
  addSdkChannel,
  addUinChannel,
  checkAccountPrice,
  checkIncomeCostPrice,
} from '@/services/channel';
import _ from 'lodash';

import { getCountryList } from '@/services/api';
import { smsType } from '@/const/const';
import { isMatchCountry, level } from './const';
import { useDialogRef } from '@/utils/react-use/useDialog';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getAccountList } from '@/services/smppAccount';
import AccountNoPriceModal from './CheckAcoountNoPriceModal';
import CheckIncomeCostDialog from './CheckIncomeCostDialog';
import { errorsObj } from '@/pages/global-components/BatchErrorModal';
import SelectOptionsByInput from '@/components/SelectOptionsByInput';

const MutipleCountryBindChannelByAccount = () => {
  const [mccMncInfo] = useMccMncInfo();

  const [form] = Form.useForm();
  const [allCountry, setAllCountry] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const accountNoPriceDialogRef = useDialogRef();
  const checkDialogRef = useDialogRef();
  const [accountNoCheck, setAccountNoCheck] = useState<any[]>([]); // 没有报价，不提交的通道列表
  const [priceNoCheck, setPriceNoCheck] = useState<any[]>([]); // 有报价，不提交的通道列表

  const { value: accountList } = useAsyncRetryFunc(async () => {
    const res = await getAccountList({ page_size: 2000, page_index: 1, status: 7 });
    return res.data.list.map((el: any) => ({
      value: el.account_id,
      label: `${el.account_name}(${el.account_id})`,
    }));
  }, []);

  async function fetchCountryList() {
    const res = await getCountryList();
    setAllCountry(
      res.data.map((el: any) => ({
        label: `${el.nation_name}_${el.nation_code}(${el.mcc})`,
        value: el.nation_code,
      })),
    );
    return res.data || [];
  }

  const getAllChannels = useCallback(() => {
    const vals = form.getFieldsValue();
    const accountsData = _.flatMap(vals.country_code, (countryCode) => {
      const mcc = mccMncInfo.find((el) => el.country_code.toString() === countryCode.toString())
        ?.mcc;
      return _.flatMap(vals.sms_type, (smsType) => {
        const mncs = mccMncInfo
          .filter((v) => v.mcc.toString() === mcc?.toString())
          .map((el) => el.mnc);
        return _.map(mncs, (mnc) => ({
          mcc,
          sms_type: smsType,
          mnc,
          account_ids: _.isArray(vals.channel_id)
            ? vals.channel_id.join(',')
            : vals.channel_id.toString(),
        }));
      });
    });
    return {
      accountsData,
      allChannels: _.flatMap(
        _.isArray(vals.channel_id) ? vals.channel_id : [vals.channel_id],
        (channel_id) =>
          _.flatMap(accountsData, (el) => ({
            mcc: el.mcc,
            sms_type: el.sms_type,
            mnc: el.mnc,
            account_id: channel_id,
          })),
      ),
    };
  }, [form, mccMncInfo]);

  async function doBind() {
    setLoading(true);
    const { allChannels } = getAllChannels();
    const vals = form.getFieldsValue();
    try {
      const channelArr = _.differenceWith(
        allChannels,
        [
          ...accountNoCheck,
          ..._.flatMap(vals.sms_type, (smsType) =>
            _.flatMap(priceNoCheck, (el) => ({
              ...el,
              sms_type: smsType,
            })),
          ),
        ],
        (cur: any, other: any) => {
          const n = ['mcc', 'mnc', 'sms_type', 'account_id'];
          return _.every(n, (k) => cur[k].toString() === other[k].toString());
        },
      );
      if (!channelArr.length) {
        setLoading(false);
        return message.error('请至少选择一个通道');
      }
      const data: any = vals.objects
        ? _.flatMap(vals.objects.split(','), (object) =>
            _.map(channelArr, (c) => ({
              mcc_mnc: `${c.mcc}_${c.mnc}`,
              channel_id: c.account_id,
              [vals.obj_type]: object,
              sms_type: c.sms_type,
              type: 0,
              level: vals.level,
              weight: vals.weight,
            })),
          )
        : _.map(channelArr, (c) => ({
            mcc_mnc: `${c.mcc}_${c.mnc}`,
            channel_id: c.account_id,
            sms_type: c.sms_type,
            type: 0,
            level: vals.level,
            weight: vals.weight,
          }));
      const apiName: { [key: string]: (data: any) => Promise<any> } = {
        uin: addUinChannel,
        sdkappid: addSdkChannel,
        overall: addGlobalChannel,
      };
      const res = await apiName[vals.obj_type]({
        params: data,
      });
      setLoading(false);
      accountNoPriceDialogRef.current.close();
      checkDialogRef.current.close();
      if (res.code === 0 && !res.msg?.errors?.length) {
        message.success('添加成功');
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
      return message.error('添加失败');
    }
  }

  const checkIsPriceNormal = async (accountNoCheck: string[]) => {
    try {
      const vals = form.getFieldsValue();
      if (vals.obj_type === 'overall') {
        doBind();
        return;
      }
      const { allChannels } = getAllChannels();
      setLoading(true);
      // 当前所选的通道
      const selectedAccount = _.differenceWith(
        allChannels,
        accountNoCheck,
        (cur: any, other: any) => {
          const n = ['mcc', 'mnc', 'sms_type', 'account_id'];
          return _.every(n, (k) => cur[k].toString() === other[k].toString());
        },
      );
      if (!selectedAccount.length) {
        doBind();
        setLoading(false);
        return;
      }
      const params = _.flatMap(vals.objects.split(','), (object) =>
        _.map(selectedAccount, (account) => ({
          [vals.obj_type]: object,
          channel_ids: _.isArray(vals.channel_id)
            ? vals.channel_id.join(',')
            : vals.channel_id.toString(),
          mcc: account.mcc,
          mnc: account.mnc,
          type: 'single',
        })),
      );
      const res = await checkIncomeCostPrice({
        params,
      });
      const errorData = res.data.filter((el) => el.code !== 0);
      if (errorData.length) {
        errorsObj.setVisible(true, errorData);
        setLoading(false);
        return;
      }
      const data = _.flatMap(res.data, (el) => el.data);
      const isEmpty = data.filter((v) => !_.isEmpty(v.cost_price_info))?.length === 0;
      if (!isEmpty) {
        const arr: any[] = [];
        data
          .filter((o) => Object.keys(o.cost_price_info).length)
          .forEach((v) => {
            Object.entries(v.cost_price_info).forEach(([key, value]: any[]) => {
              arr.push({
                ...value,
                account_id: key,
                mnc: v?.mnc,
                mcc: v?.mcc,
                income_price: v.income_price_info.price,
                income_price_curr: v.income_price_info.price_curr,
                account: accountList.find((c: any) => c.value.toString() === key.toString())?.label,
              });
            });
          });
        checkDialogRef.current.open({
          list: arr,
        });
        setLoading(false);
      } else {
        doBind();
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  const onFinish = async (vals) => {
    setLoading(true);
    const { accountsData } = getAllChannels();
    const response = await checkAccountPrice({ params: accountsData });
    if (response.code !== 0) {
      setLoading(false);
    }
    const errorData = response.data.filter((el) => el.code !== 0);
    if (errorData.length) {
      errorsObj.setVisible(true, errorData);
      setLoading(false);
      return;
    }
    const res = _.flatMap(response.data, (el) => el.data).filter((el: any) => !el.price_exists);
    const noPriceList = _.flatten(res);
    if (noPriceList.length) {
      accountNoPriceDialogRef.current.open({
        initialValues: noPriceList.map((el: any) => ({
          ...el,
          account: accountList.find((c: any) => c.value.toString() === el.account_id.toString())
            ?.label,
        })),
      });
      setLoading(false);
    } else {
      checkIsPriceNormal([]);
    }
  };

  useEffect(() => {
    fetchCountryList();
  }, []);

  return (
    <>
      <div style={{ width: 700 }}>
        <Form
          form={form}
          onFinish={(vals) => onFinish(vals)}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 16 }}
        >
          <Form.Item name="obj_type" rules={[{ required: true }]} label="调度对象类型">
            <Select
              placeholder="调度对象类型"
              options={[
                {
                  label: 'uin',
                  value: 'uin',
                },
                {
                  label: 'sdkappid',
                  value: 'sdkappid',
                },
                {
                  label: '全局',
                  value: 'overall',
                },
              ]}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.obj_type !== curValues.obj_type}
          >
            {({ getFieldValue }) => {
              const objType = getFieldValue('obj_type');
              return objType === 'overall' ? null : (
                <Form.Item name="objects" rules={[{ required: true }]} label="调度对象">
                  <Input placeholder="支持多个，英文逗号分隔" />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item name="country_code" label="国家/地区" rules={[{ required: true }]}>
            <SelectOptionsByInput
              mode="multiple"
              showSearch
              allowClear
              placeholder="国家/地区"
              filterOption={(val: string, opt: any) =>
                opt.label.toLowerCase().includes(val.toLowerCase())
              }
              options={allCountry}
              maxTagCount={30}
            />
          </Form.Item>
          <Form.Item name="sms_type" rules={[{ required: true }]} label="短信类型">
            <Select
              mode="multiple"
              showSearch
              allowClear
              placeholder="请选择短信类型"
              filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
              options={smsType}
            />
          </Form.Item>
          <Form.Item name="channel_id" label="供应商账号ID" rules={[{ required: true }]}>
            <Select
              options={accountList}
              showSearch
              placeholder="请选择"
              filterOption={(inputValue, option) =>
                !!option?.label.toLowerCase().includes(inputValue.toLowerCase())
              }
              allowClear
            ></Select>
          </Form.Item>
          <Form.Item name="weight" rules={[{ required: true }]} label="权重">
            <Input placeholder="请输入权重" />
          </Form.Item>
          <Form.Item name="level" rules={[{ required: true }]} label="优先级">
            <Select
              showSearch
              allowClear
              placeholder="请选择优先级"
              filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
              options={level}
              defaultValue="2"
            />
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Button htmlType="submit" type="primary" loading={loading}>
              提交
            </Button>
            <Button htmlType="button" style={{ marginLeft: 10 }} onClick={() => form.resetFields()}>
              重置
            </Button>
          </Form.Item>
        </Form>
      </div>
      <AccountNoPriceModal
        dialogRef={accountNoPriceDialogRef}
        continueSubmit={(noCheck: any) => {
          setAccountNoCheck(noCheck);
          checkIsPriceNormal(noCheck);
        }}
        loading={loading}
      />
      <CheckIncomeCostDialog
        dialogRef={checkDialogRef}
        continueSubmit={(selected: any) => {
          setPriceNoCheck(selected);
          doBind();
        }}
        loading={loading}
      />
    </>
  );
};

export default MutipleCountryBindChannelByAccount;
