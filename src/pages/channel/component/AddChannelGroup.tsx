import React, { useEffect, useState } from 'react';
import { Modal, Button, Input, Form, Select, Alert } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';

import { addProviderGroup, editProviderGroup } from '@/services/channel';
import { smsType } from '@/const/const';
import { _useMccMncInfo } from '@/pages/global-state';
import _ from 'lodash';
import { findLabel } from '@/utils/utils';
import { groupCategory } from './const';
import MncSelect from '../commonComponent/MncSelect';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
  resourceType: any[];
}

const AddChannelGroup = (props: DialogProps) => {
  const [form] = Form.useForm();
  const { dialogRef, reload, resourceType } = props;
  const [mccMncInfo] = _useMccMncInfo();
  const { regionOptions = [], regionList = [] } = useFetchCountryInfo();

  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const { initialValues } = defaultVal;
  const isEdit = !!initialValues?.group_id;
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue(initialValues);
    } else {
      form.resetFields();
    }
  }, [form, initialValues]);

  async function onFinish(vals: any) {
    setLoading(true);
    const mcc = regionList.find((v) => v.nation_code === vals.country_code)?.mcc;
    const _mnc = _.isArray(vals.mnc) ? vals.mnc : [vals.mnc ?? `${mcc}_000`];
    const mnc = _mnc?.map((mnc: string) => mnc?.split('_')?.[1]);

    const resourceTypeLabel = findLabel(resourceType, vals.resource_type);
    const smsTypeLabel = findLabel(smsType, vals.sms_type);
    const names: any = _.reduce(
      _mnc,
      (res, c) => {
        const mnc = c?.split('_')?.[1];
        const operatorName =
          _.find(mccMncInfo, (v) => v.mnc === mnc && v.country_code === vals.country_code)
            ?.operator_name ?? '';
        const name = `${operatorName}-${resourceTypeLabel}-${smsTypeLabel}-资源池${
          vals.name_remark ? `-${vals.name_remark}` : ''
        }`;
        return { ...res, [mnc]: vals.category === 0 ? vals.name : name };
      },
      {},
    );

    try {
      isEdit
        ? await editProviderGroup({
            name: vals.category === 0 ? vals.name : names[mnc?.[0]],
            group_id: initialValues?.group_id,
            sms_type: _.isArray(vals.sms_type) ? _.sum(vals.sms_type) : vals.sms_type,
            resource_type: vals.resource_type,
            country_code: vals.country_code,
            mnc: mnc?.[0] ?? '000',
            category: vals.category,
            cr: '0',
            dr: '0',
            is_ott: 0,
            is_voice: 0,
            is_local_to_oversea: 0,
            is_sim_farm: 0,
          })
        : await addProviderGroup({
            names,
            sms_type: _.isArray(vals.sms_type) ? _.sum(vals.sms_type) : vals.sms_type,
            resource_type: vals.resource_type ?? 0,
            country_code: vals.country_code,
            mncs: mnc,
            category: vals.category,
            cr: '0',
            dr: '0',
            is_ott: 0,
            is_voice: 0,
            is_local_to_oversea: 0,
            is_sim_farm: 0,
          });
      setLoading(false);
      reload();
      setVisible(false);
    } catch (err) {
      console.log(err);
      setLoading(false);
    }
  }

  return (
    <Modal
      title={initialValues?.group_id ? '编辑' : '添加'}
      open={visible}
      footer={null}
      onCancel={() => setVisible(false)}
      destroyOnClose={true}
      maskClosable={false}
    >
      <Alert
        message="选择新资源组，会自动生成通道组名称，可添加名称备注"
        style={{ marginBottom: 20 }}
      ></Alert>
      <Form
        className="sender-search-form"
        labelCol={{ span: 7 }}
        form={form}
        labelAlign="left"
        onFinish={(vals) => onFinish(vals)}
      >
        <Form.Item name="category" label="资源类别" rules={[{ required: true }]}>
          <Select options={groupCategory} placeholder="请选择"></Select>
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.category !== curValues.category}
        >
          {({ getFieldValue }) => {
            const old = getFieldValue('category') === 0;
            return (
              <>
                {old && (
                  <Form.Item name="name" label="通道组名称" rules={[{ required: true }]}>
                    <Input placeholder="请输入"></Input>
                  </Form.Item>
                )}
                <Form.Item name="sms_type" label="短信类型" rules={[{ required: true }]}>
                  <Select
                    mode={old ? 'multiple' : undefined}
                    allowClear
                    showSearch
                    placeholder="请选择"
                    options={smsType}
                  />
                </Form.Item>
              </>
            );
          }}
        </Form.Item>
        <Form.Item name="country_code" label="国家" rules={[{ required: true }]}>
          <Select
            options={regionOptions}
            placeholder="请选择"
            onChange={(value) => {
              form.setFieldsValue({ mnc: undefined, country_code: value });
            }}
            filterOption={(inputValue, option) => !!option?.label.includes(inputValue)}
            showSearch
            allowClear
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.country_code !== curValues.country_code ||
            prevValues.category !== curValues.category
          }
        >
          {({ getFieldValue }) => {
            const countryCode = getFieldValue('country_code');
            const category = getFieldValue('category');
            return (
              <Form.Item
                name="mnc"
                label="运营商"
                labelCol={{ span: 7 }}
                rules={[{ required: category === 1 }]}
              >
                <MncSelect
                  mode={category === 1 && !isEdit ? 'multiple' : undefined}
                  initialValues={{ country_code: countryCode }}
                  onChange={(mnc: any) => form.setFieldsValue({ mnc })}
                  value={form.getFieldValue('mnc')}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) => prevValues.category !== curValues.category}
        >
          {({ getFieldValue }) => {
            const old = getFieldValue('category') === 0;
            return (
              <>
                {!old && (
                  <>
                    <Form.Item name="resource_type" label="资源类型" rules={[{ required: true }]}>
                      <Select
                        allowClear
                        showSearch
                        placeholder="请选择"
                        options={resourceType}
                        filterOption={(inputValue, option) =>
                          !!option?.label.toLowerCase().includes(inputValue.toLowerCase())
                        }
                      />
                    </Form.Item>
                    <Form.Item name="name_remark" label="名称备注">
                      <Input placeholder="请输入名称备注" />
                    </Form.Item>
                  </>
                )}
              </>
            );
          }}
        </Form.Item>

        {/* <Form.Item name="is_sim_farm" label="是否卡发" rules={[{ required: true }]}>
          <RadioGroup>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item name="is_local_to_oversea" label="是否本地发国际" rules={[{ required: true }]}>
          <RadioGroup>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item name="is_voice" label="是否语音" rules={[{ required: true }]}>
          <RadioGroup>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item name="is_ott" label="是否OTT" rules={[{ required: true }]}>
          <RadioGroup>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </RadioGroup>
        </Form.Item> */}
        <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
          <Button htmlType="submit" type="primary" loading={loading}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddChannelGroup;
