import React, { useState } from 'react';
import { Modal, Button, InputNumber, Form, message } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { editProviderStrategyWeight } from '@/services/channelStrategy';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

const EditChannelStrategyWeight = (props: DialogProps) => {
  const [form] = Form.useForm();
  const { dialogRef, onSuccess } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const { initialValues } = defaultVal;
  const [loading, setLoading] = useState(false);

  async function onFinish(vals: { weight: number }) {
    setLoading(true);
    try {
      const res: any = await editProviderStrategyWeight({
        weight: vals.weight,
        bind_id: initialValues?.bind_id,
        tactic_id: initialValues?.tactic_id,
      });
      if (res.code === 0) {
        message.success('调整成功');
      }
      setLoading(false);
      onSuccess();
      setVisible(false);
    } catch (err) {
      setLoading(false);
    }
  }

  return (
    <Modal
      title="权重调整"
      open={visible}
      footer={null}
      onCancel={() => setVisible(false)}
      destroyOnClose={true}
    >
      <Form
        className="sender-search-form"
        labelCol={{ span: 7 }}
        form={form}
        labelAlign="left"
        onFinish={(vals) => onFinish(vals)}
        initialValues={{ weight: initialValues?.weight }}
      >
        <Form.Item label="绑定id">
          <span>{initialValues?.bind_id}</span>
        </Form.Item>
        <Form.Item name="weight" label="权重" rules={[{ required: true }]}>
          <InputNumber />
        </Form.Item>
        <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
          <Button htmlType="submit" type="primary" loading={loading}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditChannelStrategyWeight;
