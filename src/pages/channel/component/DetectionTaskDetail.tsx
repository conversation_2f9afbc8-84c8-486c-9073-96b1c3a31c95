import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ActionType, BetaSchemaForm, ProTable } from '@ant-design/pro-components';
import { isMobile } from '@/const/jadgeUserAgent';
import _, { isArray } from 'lodash';
import { findLabel } from '@/utils/utils';
import dayjs from 'dayjs';
import { getDetectionTaskDetails } from '@/services/channelDetectionTask';
import { history } from 'umi';

import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { useAsyncFn } from 'react-use';
import { getProviderGroupBindList } from '@/services/channel';
import { getProviderStrategyBindList } from '@/services/channelStrategy';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

type DataItem = {
  name: string;
  state: string;
};

const DirectPortList = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const { regionOptionsMcc = [] } = useFetchCountryInfo();
  const [searchKeys, setSearchKeys] = useState<{ [key: string]: any }>({
    time: [dayjs().subtract(1, 'month'), dayjs()],
  });
  const { row: query = {} } = history.location.state as {
    row: any;
  };
  const { task_id, channel_type, account_id, mnc_info } = query;
  const [mccMncInfo] = useMccMncInfo();

  const [providerIdList, fetchProviderIdList] = useAsyncFn(async () => {
    if (channel_type.toString() === '0')
      return [{ provider_id: account_id, provider_name: query.account_name }];
    const res =
      channel_type.toString() === '1'
        ? await getProviderGroupBindList({ group_id: account_id })
        : await getProviderStrategyBindList({ tactic_id: account_id });
    return isArray(res.data) ? res.data : [];
  }, [account_id, channel_type, query.account_name]);

  useEffect(() => {
    fetchProviderIdList();
  }, [fetchProviderIdList]);

  const columns: any = useMemo(() => {
    return [
      {
        title: '账号id',
        key: 'provider_id',
        valueType: 'select',
        fieldProps: {
          options: providerIdList.value?.map((el) => ({
            label: `${el.provider_name}(${el.provider_id})`,
            value: el.provider_id,
          })),
          showSearch: true,
        },
        render: (text, row: any) => `${row.account_name}(${row.provider_id})`,
      },
      {
        title: '运营商',
        dataIndex: 'mnc',
        key: 'mnc',
        width: 150,
        // hideInSearch: true,
        valueType: 'select',
        render: (text, row: any) => {
          const name = _.find(mccMncInfo, (v) => v.mnc === row.mnc)?.operator_name ?? '';
          return `${name}(${row.mnc})`;
        },
        fieldProps: {
          options: mnc_info.map((el) => {
            const name = _.find(mccMncInfo, (v) => v.mnc === el)?.operator_name;
            return {
              label: `${name}(${el})`,
              value: el,
            };
          }),
          showSearch: true,
        },
      },
      {
        title: '手机号',
        dataIndex: 'phone',
        key: 'phone',
      },
      {
        title: '标识',
        dataIndex: 'serial_no',
        key: 'serial_no',
      },
      {
        title: '消息ID',
        dataIndex: 'message_id',
        key: 'message_id',
      },
      {
        title: '国家',
        // dataIndex: 'mcc',
        key: 'mcc',
        valueType: 'select',
        render: (text, row: any) => `${findLabel(regionOptionsMcc, row.mcc)}(${row.mcc})`,
      },
      {
        title: '提交的短信内容',
        dataIndex: 'msg',
        key: 'msg',
      },
      {
        title: '接收的短信内容',
        dataIndex: 'recv_msg',
        key: 'recv_msg',
      },
      {
        title: '提交的sender',
        dataIndex: 'sender',
        key: 'sender',
      },
      {
        title: '接收的sender',
        dataIndex: 'recv_sender',
        key: 'recv_sender',
      },
      {
        title: '请求状态',
        // dataIndex: 'req_result',
        key: 'req_result',
        render: (text, row: any) => `${row.req_result_text}`,
      },
      {
        title: '回执状态',
        dataIndex: 'cb_status',
        key: 'cb_status',
      },
      {
        title: '接收状态',
        // dataIndex: 'recv_status_text',
        key: 'recv_status_text',
        render: (text, row: any) => row.recv_status_text || row.recv_status,
      },
      {
        title: '请求时间',
        dataIndex: 'req_time',
        key: 'req_time',
      },
      {
        title: '回执时间',
        dataIndex: 'cb_time',
        key: 'cb_time',
      },
      {
        title: '接收时间',
        dataIndex: 'recv_time',
        key: 'recv_time',
      },
    ];
  }, [mccMncInfo, mnc_info, providerIdList.value]);

  const requestFn = useCallback(
    async (_params: any) => {
      const { data } = await getDetectionTaskDetails({
        ..._.omit(
          _.pickBy(_params, (v) => !_.isNil(v) && v !== ''),
          ['pageSize', 'current'],
        ),
        task_id,
        channel_type,
        ..._.pickBy(_.omit(searchKeys, ['time']), (v, k) => !_.isNil(v) && v !== ''),
        from_time: searchKeys.time
          ? dayjs(searchKeys.time?.[0])
              .startOf('day')
              .format('YYYY-MM-DD HH:mm:ss')
          : undefined,
        to_time: searchKeys.time
          ? dayjs(searchKeys.time?.[1])
              .endOf('day')
              .format('YYYY-MM-DD HH:mm:ss')
          : undefined,
      });
      return {
        data: data?.list || [],
        success: true,
        total: data.count,
      };
    },
    [searchKeys, task_id, channel_type],
  );

  useEffect(() => {
    actionRef.current?.reload();
  }, [searchKeys]);

  const searchColumns = useMemo(() => {
    const f = columns.filter((el) => ['phone', 'mnc', 'provider_id'].includes(el.key));
    const time = [
      {
        title: '起止时间',
        key: 'time',
        valueType: 'dateRange',
      },
    ];
    return [...f, ...time];
  }, [columns]);

  return (
    <>
      <BetaSchemaForm<DataItem>
        layoutType="QueryFilter"
        onFinish={async (values) => {
          actionRef.current?.setPageInfo?.({ current: 1 });
          setSearchKeys({ ...values });
        }}
        columns={searchColumns}
        initialValues={{
          time: [dayjs().subtract(1, 'month'), dayjs()],
        }}
        collapseRender={() => null}
        collapsed={false}
      />
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={false}
        scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 2000 }}
        request={requestFn}
        options={false}
      />
    </>
  );
};
export default DirectPortList;
