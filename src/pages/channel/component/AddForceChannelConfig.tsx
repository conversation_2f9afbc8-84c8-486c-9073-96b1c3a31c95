import React, { useState } from 'react';
import { Modal, Button, Form, Radio, message } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog, useDialogRef } from '@/utils/react-use/useDialog';

import { addForceChannel, checkAccountPrice } from '@/services/channel';
import AccountNoPriceModal from './CheckAcoountNoPriceModal';
import _ from 'lodash';
import { getSelectGather } from './utils';
import ChannelSelect from '../commonComponent/ChannelSelect';
import MncSelect from '../commonComponent/MncSelect';
import { errorsObj } from '@/pages/global-components/BatchErrorModal';
interface DialogProps {
  dialogRef: DialogRef;
  reload: () => void;
}

const ForceChannelConfigAdd = (props: DialogProps) => {
  const [form] = Form.useForm();
  const { dialogRef, reload } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const accountNoPriceDialogRef = useDialogRef();
  const { initialValues } = defaultVal;
  const [loading, setLoading] = useState(false);
  const [channelList, setChannelList] = useState<any[]>([]);

  const [accountNoCheck, setAccountNoCheck] = useState<string[]>([]); // 没有报价，不提交的通道列表
  const validateFields = async () => {
    try {
      await form.validateFields(['provider_id', 'mnc', 'is_forced']);
      return true;
    } catch (err) {
      return false;
    }
  };

  const checkAccounthHasPrice = async () => {
    const validateSuccess = await validateFields();
    if (!validateSuccess) {
      return;
    }
    setLoading(true);
    const curMnc = initialValues?.mnc
      ? [initialValues.mnc]
      : (form.getFieldValue('mnc') ?? []).map((v: string) => v.split('_')[1]);
    const params = curMnc.map((mnc) => ({
      account_ids: form.getFieldValue('provider_id')?.join(','),
      mcc: initialValues.mcc,
      mnc,
    }));
    const response = await checkAccountPrice({ params });
    if (response.code !== 0) {
      setLoading(false);
    }
    const errorData = response.data.filter((el) => el.code !== 0);
    if (errorData.length) {
      errorsObj.setVisible(true, errorData);
      setLoading(false);
      return;
    }
    const res = _.flatMap(response.data, (el) => el.data).filter((el: any) => !el.price_exists);
    const noPriceList = _.flatten(res);
    if (noPriceList.length) {
      accountNoPriceDialogRef.current.open({
        initialValues: noPriceList.map((el: any) => ({
          ...el,
          account: channelList.find((c: any) => c.value.toString() === el.account_id.toString())
            ?.label,
        })),
      });
      setLoading(false);
    } else {
      form.submit();
      setLoading(false);
    }
  };

  async function onFinish(vals: any) {
    setLoading(true);
    try {
      const channelArr = getSelectGather({
        initMns: initialValues,
        form,
        idKey: 'provider_id',
      }).filter((c: string) => !accountNoCheck.includes(c.toString()));
      if (!channelArr.length) {
        setLoading(false);
        return message.error('请至少选择一个通道');
      }
      const data: any = [];
      channelArr.forEach((c: string) => {
        const mnc = c.split('_')[0];
        const provider_id = c.split('_')[1];
        const obj = {
          ...vals,
          mcc_mnc: `${initialValues.mcc}_${mnc}`,
          provider_id,
          obj_type: initialValues.obj_type,
          obj_id: initialValues.obj_id,
          is_forced: initialValues.is_forced || vals.is_forced,
          mnc: undefined,
        };
        data.push(obj);
      });
      const res = await addForceChannel({
        params: data,
      });
      setLoading(false);
      setVisible(false);
      accountNoPriceDialogRef.current.close();
      if (res.code === 0 && !res.msg?.errors?.length) {
        reload();
        form.resetFields();
        message.success('添加成功');
      }
    } catch (err) {
      console.log(err);
      setLoading(false);
      return message.error('添加失败');
    }
  }

  return (
    <>
      <Modal
        title={initialValues?.conf_id ? '编辑' : '添加'}
        open={visible}
        footer={null}
        onCancel={() => {
          setVisible(false);
          form.resetFields();
        }}
        destroyOnClose={true}
        maskClosable={false}
      >
        <Form
          className="sender-search-form"
          labelCol={{ span: 7 }}
          preserve={false}
          form={form}
          labelAlign="left"
          onFinish={(vals) => onFinish(vals)}
        >
          <Form.Item name="mnc" label="运营商" rules={[{ required: !initialValues?.mnc }]}>
            {initialValues?.mnc ? (
              initialValues?.operator_name
            ) : (
              <MncSelect
                mode="multiple"
                initialValues={{ mcc: initialValues?.mcc }}
                onChange={(mnc: any) => form.setFieldsValue({ mnc })}
                value={form.getFieldValue('mnc')}
              />
            )}
          </Form.Item>
          <Form.Item
            name="provider_id"
            label="通道"
            rules={[{ required: initialValues?.addType !== 'default' }]}
          >
            <ChannelSelect
              value={form.getFieldValue('provider_id')}
              type={0}
              onChange={(value) => {
                form.setFieldsValue({ provider_id: value });
              }}
              pageSize={1000}
              loadFnCallBack={({ params, options }) => {
                !params.search_key && setChannelList(options);
              }}
            />
          </Form.Item>
          <Form.Item
            name="is_forced"
            label="是否强制调度"
            rules={[{ required: initialValues?.is_forced === undefined }]}
          >
            {initialValues?.is_forced !== undefined ? (
              String(initialValues?.is_forced) === '1' ? (
                '是'
              ) : (
                '否'
              )
            ) : (
              <Radio.Group>
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            )}
          </Form.Item>
          <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
            <Button onClick={() => checkAccounthHasPrice()} type="primary" loading={loading}>
              提交
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      <AccountNoPriceModal
        dialogRef={accountNoPriceDialogRef}
        continueSubmit={(noCheck: any) => {
          setAccountNoCheck(noCheck.map((record: any) => `${record.mnc}_${record.account_id}`));
          form.submit();
        }}
        loading={loading}
      />
    </>
  );
};

export default ForceChannelConfigAdd;
