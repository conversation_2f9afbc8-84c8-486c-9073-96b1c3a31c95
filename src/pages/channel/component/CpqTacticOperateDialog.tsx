import { BetaSchemaForm } from '@ant-design/pro-components';
import _ from 'lodash';
import React, { useEffect, useMemo, useRef } from 'react';
import { statusOptions } from '../CpqTacticScheduler';
import { bindTacticCpqRecord, changeStatusTacticCpqRecord } from '@/services/channel';
import { level } from './const';
import { DialogRef, useDialog } from '@/utils/react-use/useDialog';
import { message } from 'antd';
import { errorsObj } from '@/pages/global-components/BatchErrorModal';

interface Props {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export default function CpqTacticOperateDialog(props: Props) {
  const { onSuccess, dialogRef } = props;
  const [visible, setVisible, defaultValue] = useDialog<{ initialValues: any; status: number }>(
    dialogRef,
  );
  const { initialValues, status } = defaultValue;
  const addFormRef = useRef<any>();

  const title = useMemo(() => {
    const item = _.find(statusOptions, (el) => el.value === status);
    return item?.title || item?.label;
  }, [status]);

  async function onFinish(vals) {
    try {
      const res = await Promise.all(
        initialValues.map(async (el) =>
          status === 1
            ? await bindTacticCpqRecord({
                ...vals,
                record_id: el?.record_id,
                client_id: el?.sdkappids.toString() || el?.uin.toString(),
                bind_type: el?.sdkappids ? 1 : 0,
                mcc_mnc: `${el?.mcc}_${el?.mnc}`,
                tactic_id: el?.tactic_id,
                sms_type: el?.sms_type,
              })
            : await changeStatusTacticCpqRecord({
                ...vals,
                status,
                record_id: el?.record_id,
              }),
        ),
      );
      let isClose = true;
      const results: any[] = [];
      res.forEach((el: any) => {
        if (el.code === 0) {
          results.push(el?.data || []);
        } else {
          isClose = false;
        }
      });
      console.log(results);
      if (!_.flatten(results).filter((el) => el.code !== 0)?.length && isClose) {
        message.success('操作成功');
        isClose = true;
      } else {
        isClose = false;
        const _errors = _.flatMap(results, (err: any, index) =>
          err.map((s) => ({
            ...s,
            msg: `${s?.msg}【record_id: ${initialValues[index].record_id}】`,
          })),
        )?.filter((el) => el.code !== 0);
        _errors.length && errorsObj.setVisible(true, _errors);
      }
      onSuccess();
      return isClose;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const allFields = [
      {
        title: '权重',
        dataIndex: 'weight',
        valueType: 'text',
      },
      {
        title: '优先级',
        dataIndex: 'level',
        valueType: 'select',
        fieldProps: {
          options: level,
        },
      },
      {
        title: '备注',
        dataIndex: 'remark',
        valueType: 'text',
      },
    ];
    return status === 1 ? allFields : allFields.slice(2, 3);
  }, [status]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      weight: 100,
      level: 2,
      remark: undefined,
    });
  }

  useEffect(() => {
    !visible && reset();
  }, [visible]);

  return (
    <BetaSchemaForm
      title={title}
      formRef={addFormRef}
      open={visible}
      initialValues={{ weight: 100, level: 2 }}
      onOpenChange={setVisible}
      layoutType="ModalForm"
      layout="horizontal"
      labelCol={{ span: 6 }}
      onFinish={onFinish}
      modalProps={{ maskClosable: false, zIndex: 1000 }}
      columns={formItems}
      width={500}
    />
  );
}
