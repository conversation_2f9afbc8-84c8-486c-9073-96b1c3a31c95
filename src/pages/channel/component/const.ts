export const channelType = [
  {
    label: '单通道',
    type: 'single',
    value: 0,
  },
  {
    label: '通道组',
    type: 'group',
    value: 1,
  },
  {
    label: '通道策略',
    type: 'tactic',
    value: 2,
  },
];

export const level = [
  {
    label: '2',
    value: 2,
  },
  {
    label: '1',
    value: 1,
  },

  {
    label: '0',
    value: 0,
  },
];

// 根据通道组名称匹配router_type
export const routeTypeByName = [
  { value: 9, text: '混合' }, // 1混合通道+8纯直连扣量
  { value: 2, text: '卡发' },
  { value: 4, text: '语音' },
  { value: 8, text: '直连扣量' },
  { value: 16, text: 'OTT' },
  { value: 32, text: 'SMS' },
  { value: 64, text: '本地发国际' },
  { value: 128, text: '纯本地' },
  { value: 256, text: '直连' },
];

export const routeTypeMap: any = [
  {
    id: 13,
    name: '混合',
    route_type: 9,
  },
  {
    id: 12,
    name: '上行短信',
    route_type: 0,
  },
  {
    id: 11,
    name: 'VLN',
    route_type: 256,
  },
  {
    id: 10,
    name: 'TFN',
    route_type: 256,
  },
  {
    id: 9,
    name: '10DLC',
    route_type: 256,
  },
  {
    id: 8,
    name: 'LC',
    route_type: 256,
  },
  {
    id: 7,
    name: 'SC',
    route_type: 256,
  },
  {
    id: 6,
    name: '本地直连',
    route_type: 128,
  },
  {
    id: 5,
    name: 'WA绿标',
    route_type: 16,
  },
  {
    id: 4,
    name: '语音',
    route_type: 4,
  },
  {
    id: 3,
    name: '本地发国际',
    route_type: 64,
  },
  {
    id: 2,
    name: 'SIM',
    route_type: 2,
  },
  {
    id: 1,
    name: '国际直连',
    route_type: 256,
  },
];

export const tacticCategory = [
  {
    label: '旧标品',
    value: 0,
  },
  {
    label: '分运营商标品',
    value: 1,
  },
  {
    label: '新国家类型标品',
    value: 2,
  },
];

export const groupCategory = [
  {
    label: '旧资源组',
    value: 0,
  },
  {
    label: '分运营商资源组',
    value: 1,
  },
  {
    label: '新国家类型资源组',
    value: 2,
  },
];

const regex = /^(.*?)\((.*?)\)?$/;
export const isMatchCountry = (
  item: { label: string; value: string | number },
  inputData: string[],
) => {
  const match = regex.exec(item.label);
  return !!match && inputData.some((v: string) => match.slice(1, 3).includes(String(v)));
};
