import React, { useState } from 'react';
import { Modal, Button, InputNumber, Form } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';

interface DialogProps {
  dialogRef: DialogRef;
  onSubmit: (params: any) => Promise<any>;
  onSuccess: () => void;
}

const AddChannelGroupBind = (props: DialogProps) => {
  const [form] = Form.useForm();
  const { dialogRef, onSubmit, onSuccess } = props;
  const [visible, setVisible, defaultVal] = useDialog<{
    initialValues: any;
  }>(dialogRef);
  const { initialValues } = defaultVal;
  const [loading, setLoading] = useState(false);

  async function onFinish(vals: { weight: number; monitor_threshold: number }) {
    setLoading(true);
    try {
      await onSubmit({
        weight: vals.weight,
        bind_id: initialValues?.bind_id,
        group_id: initialValues?.group_id,
        monitor_threshold: vals.monitor_threshold,
      });
      setLoading(false);
      onSuccess();
      setVisible(false);
    } catch (err) {
      setLoading(false);
    }
  }

  return (
    <Modal
      title="权重调整"
      open={visible}
      footer={null}
      onCancel={() => setVisible(false)}
      destroyOnClose={true}
    >
      <Form
        className="sender-search-form"
        labelCol={{ span: 7 }}
        form={form}
        labelAlign="left"
        onFinish={(vals) => onFinish(vals)}
        initialValues={{ weight: initialValues?.weight }}
      >
        <Form.Item label="绑定id">
          <span>{initialValues?.bind_id}</span>
        </Form.Item>
        <Form.Item name="weight" label="权重" rules={[{ required: true }]}>
          <InputNumber controls={false} style={{ width: 320 }} />
        </Form.Item>
        <Form.Item name="monitor_threshold" label="重新调度阈值" rules={[{ required: true }]}>
          <InputNumber controls={false} style={{ width: 320 }} />
        </Form.Item>
        <Form.Item style={{ textAlign: 'center', marginTop: 20 }}>
          <Button htmlType="submit" type="primary" loading={loading}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddChannelGroupBind;
