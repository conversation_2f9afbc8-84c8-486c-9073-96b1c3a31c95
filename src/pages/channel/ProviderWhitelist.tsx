import React, { useCallback, useMemo, useRef, useState } from 'react';
import { Button, message, Modal } from 'antd';
import {
  ActionType,
  BetaSchemaForm,
  PageContainer,
  ProFormColumnsType,
  ProTable,
} from '@ant-design/pro-components';
import _ from 'lodash';
import {
  getProviderWhitelistList,
  deletetProviderWhitelist,
  editProviderWhitelist,
  addProviderWhitelist,
} from '@/services/channelProviderWhitelist';
import { smsType } from '@/const/const';
import ChannelSelect from './commonComponent/ChannelSelect';
import { validatePhoneInputInProviderWhitelist } from './component/utils';
import { findLabel } from '@/utils/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import SelectAll from '@/components/SelectAll';
import ProviderCascader from '../global-components/ProviderCascader';

export type DataItem = {
  name: string;
  state: string;
};

const allFields = [
  'id',
  'uin',
  'sms_type',
  'provider_id',
  'mobile',
  'mcc',
  'created_at',
  'updated_at',
  'provider_name',
  'datas',
];
const addFormFields = ['mobile', 'uin'];
const ProviderWhitelist = () => {
  const actionRef = useRef<ActionType>();
  const addFormRef = useRef<any>();
  const formRef = useRef<any>();
  const editRef = useRef<any>();
  const currentRowRef = useRef<any>();
  const [, forceUpdate] = useState({});
  const [open, setOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [type, setType] = useState('create');
  const [initialValues, setInitialValues] = useState<any>({});
  const [selectKeys, setSelectKeys] = useState<(string | number)[]>([]);
  const { regionOptionsMcc = [] } = useFetchCountryInfo();

  const columns: ProFormColumnsType[] = useMemo(
    () => [
      {
        title: '配置ID',
        dataIndex: 'id',
        key: 'id',
        hideInSearch: true,
        fieldProps: { disabled: true },
      },
      {
        title: 'UIN',
        dataIndex: 'uin',
        key: 'uin',
        valueType: 'digit',
        hideInSearch: true,
        formItemProps: { rules: [{ required: true }], width: 300 },
        fieldProps: { controls: false, min: 0, precision: 0 },
        render: (_, { uin }: any) => `${uin}`,
      },
      {
        title: '号码',
        dataIndex: 'mobile',
        key: 'mobile',
        valueType: 'textarea',
        formItemProps: {
          rules: [
            {
              required: true,
              message: '输入号码不规范，必须携带国家代码并且不超过两百行',
              validator(_, value) {
                return validatePhoneInputInProviderWhitelist(value);
              },
            },
          ],
        },
      },
      {
        title: '国家地区码',
        dataIndex: 'mcc',
        key: 'mcc',
        valueType: 'select',
        formItemProps: { rules: [{ required: true }] },
        fieldProps: {
          options: regionOptionsMcc,
          placeholder: '请选择国家',
          showSearch: true,
        },
        render: (_, { mcc }: any) => findLabel(regionOptionsMcc, mcc),
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        key: 'sms_type',
        valueType: 'select',
        hideInSearch: true,
        formItemProps: { rules: [{ required: true }] },
        fieldProps: { options: smsType, placeholder: '请选择短信类型' },
      },
      {
        title: '加白通道',
        dataIndex: 'provider_id',
        key: 'provider_id',
        valueType: 'text',
        render: (_, { provider_name, provider_id }) => `${provider_name}(${provider_id})`,
        transform: (value) => {
          if (value.length === 1) {
            return { supplier_id: value[0] };
          }
          return { supplier_id: value[0], provider_id: value[value.length - 1] };
        },
        renderFormItem() {
          return <ProviderCascader />;
        },
        formItemProps: { rules: [{ required: true }] },
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        valueType: 'text',
        hideInSearch: true,
        formItemProps: { rules: [{ required: true }] },
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        valueType: 'text',
        hideInSearch: true,
        formItemProps: { rules: [{ required: true }] },
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        width: 150,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button
                type="link"
                onClick={() => {
                  setType('edit');
                  setEditOpen(true);
                  currentRowRef.current = row;
                  // 弹窗表单未初始化，addFormRef为空
                  !editRef.current && setInitialValues(row);
                  editRef.current?.setFieldsValue(row);
                }}
              >
                编辑
              </Button>
              <Button
                type="link"
                onClick={() => {
                  handleDelete(row);
                }}
              >
                删除
              </Button>
            </>
          );
        },
      },
    ],
    [regionOptionsMcc],
  );
  const formItems = columns
    .filter((item) => addFormFields.includes(item.key))
    .map((item) => {
      if (['mobile'].includes(item.key)) {
        return { ...item, width: undefined }; // 直接在这里处理宽度属性
      }
      return item;
    })
    .concat([
      {
        dataIndex: 'datas',
        key: 'datas',
        valueType: 'formList',
        hideInSearch: true,
        formItemProps: {
          rules: [{ required: true, message: '请至少添加一行数据' }],
        },
        fieldProps: { min: 1 },
        columns: [
          {
            valueType: 'group',
            key: 'group',
            columns: [
              {
                title: '短信类型',
                dataIndex: 'sms_types',
                key: 'sms_types',
                valueType: 'select',
                hideInSearch: true,
                formItemProps: { rules: [{ required: true }] },
                fieldProps: { options: smsType, style: { width: 210 }, mode: 'multiple' },
                renderFormItem: () => <SelectAll />,
              },
              {
                title: '加白通道',
                dataIndex: 'provider_id',
                key: 'provider_id',
                formItemProps: { rules: [{ required: true }] },
                fieldProps: { style: { width: 360 } },
                renderFormItem: () => {
                  return (
                    <ChannelSelect
                      mode="default"
                      type={0}
                      placeholder="请选择供应商账号"
                      pageSize={1000}
                    />
                  );
                },
              },
            ],
          },
        ],
      },
    ]);
  const requestFn = useCallback(async (params: any) => {
    const { data } = await getProviderWhitelistList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);
  function handleDelete(row: any) {
    Modal.confirm({
      title: '提示',
      content: '确定删除该加白号码配置吗?',
      onOk: async () => {
        const res = await deletetProviderWhitelist({
          id: row.id,
        });
        if (res.code === 0) {
          actionRef.current?.reload();
        }
      },
    });
  }
  async function onFinish({ uin, mobile, datas }: any) {
    const submitData = datas.map(({ provider_id, sms_types }: any) => {
      return {
        provider_id,
        sms_types,
        uin,
        mobiles: mobile.split('\n'),
      };
    });
    try {
      const res = await addProviderWhitelist(submitData);
      if (res.code === 0) {
        actionRef.current?.reload();
        message.success('操作成功');
        setOpen(false);
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }
  function reset() {
    const vals = _.reduce(
      allFields,
      (acc: { [key: string]: any }, key: string) => ({
        ...acc,
        [key]: undefined,
      }),
      {},
    );
    addFormRef.current?.setFieldsValue({ ...vals });
  }
  const handleBatchEdit = async () => {
    const isBatch = type === 'batchEdit';
    try {
      const res = await editProviderWhitelist({
        provider_id: editRef.current?.getFieldValue('provider_id'),
        ids: isBatch ? selectKeys : [currentRowRef.current?.id],
      });
      if (res.code === 0) {
        actionRef.current?.reload();
        message.success('操作成功');
        setEditOpen(false);
        isBatch && setSelectKeys([]);
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  };
  const showBatchSelect = !formRef.current?.getFieldValue('provider_id');
  const disableBatchEdit =
    !formRef.current?.getFieldValue('provider_id') || selectKeys.length === 0;
  return (
    <PageContainer>
      <BetaSchemaForm<DataItem>
        formRef={addFormRef}
        layoutType="ModalForm"
        open={open}
        onOpenChange={setOpen}
        title="号码加白配置"
        layout="vertical"
        labelCol={{ span: 5 }}
        onFinish={onFinish}
        columns={formItems}
        width={700}
        trigger={
          <Button
            type="primary"
            onClick={() => {
              reset();
              setType('create');
            }}
          >
            号码加白配置
          </Button>
        }
      />
      <BetaSchemaForm
        layoutType="ModalForm"
        formRef={editRef}
        open={editOpen}
        onOpenChange={setEditOpen}
        columns={[
          {
            title: '新加白通道',
            dataIndex: 'provider_id',
            key: 'provider_id',
            formItemProps: { rules: [{ required: true }] },
            fieldProps: { style: { width: 360 } },
            renderFormItem: () => {
              return (
                <ChannelSelect mode="default" type={0} placeholder="请选择通道" pageSize={1000} />
              );
            },
          },
        ]}
        initialValues={type === 'edit' ? initialValues : undefined}
        layout="vertical"
        width={450}
        labelCol={{ span: 8 }}
        title={type == 'edit' ? '编辑号码加白配置' : '批量修改加白通道'}
        onFinish={handleBatchEdit}
        trigger={
          <Button
            style={{ marginLeft: 10 }}
            disabled={disableBatchEdit}
            type="default"
            onClick={() => {
              setType('batchEdit');
            }}
          >
            批量修改通道
          </Button>
        }
      ></BetaSchemaForm>
      <ProTable
        onLoad={() => {
          // 强制触发更新disableBatchEdit
          forceUpdate({});
        }}
        actionRef={actionRef}
        formRef={formRef}
        columns={columns as any}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
        rowSelection={
          showBatchSelect
            ? undefined
            : {
                selectedRowKeys: selectKeys,
                onChange: (selectedRowKeys: (number | string)[]) => {
                  setSelectKeys(selectedRowKeys);
                },
              }
        }
      />
    </PageContainer>
  );
};
export default ProviderWhitelist;
