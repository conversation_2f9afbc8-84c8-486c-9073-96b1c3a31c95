import React, { useState } from 'react';
import { Tabs, TabsProps } from 'antd';
import { PageContainer } from '@ant-design/pro-components';

import DetectionTaskDetail from './component/DetectionTaskDetail';
import DetectionTaskStatistics from './component/DetectionTaskStatistics';

const items: TabsProps['items'] = [
  {
    key: 'detail',
    label: '短信明细',
    children: <DetectionTaskDetail></DetectionTaskDetail>,
  },
  {
    key: 'statistics',
    label: '统计查询',
    children: <DetectionTaskStatistics></DetectionTaskStatistics>,
  },
];

const DetectionTaskDetailAndStatistics = () => {
  const [activeKeys, setActiveKeys] = useState('detail');

  return (
    <PageContainer>
      <Tabs
        defaultActiveKey="detail"
        activeKey={activeKeys}
        items={items}
        onChange={(val) => setActiveKeys(val)}
      />
    </PageContainer>
  );
};
export default DetectionTaskDetailAndStatistics;
