import React from 'react';
import { PageContainer } from '@ant-design/pro-layout';

import { getOverallConfigCountry, deleteGlobalChannel } from '@/services/channel';

import CountryList from './component/CountryList';

const UinChannelCountryList = () => {
  return (
    <PageContainer>
      <CountryList
        obj="global"
        getListFn={getOverallConfigCountry}
        deleteFn={deleteGlobalChannel}
      />
    </PageContainer>
  );
};

export default UinChannelCountryList;
