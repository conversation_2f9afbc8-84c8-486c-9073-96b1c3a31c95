import React from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Form, Input } from 'antd';
import { getSdkConfigCountry, deleteSdkRessiueChannel } from '@/services/channel';
import CountryList from './component/CountryList';

const SdkappidChannelCountryList = () => {
  // const [searchKeys, setSearchKeys] = useSetState({ sdkappid: '' });
  return (
    <PageContainer>
      <CountryList
        obj="sdkappid"
        getListFn={getSdkConfigCountry}
        deleteFn={deleteSdkRessiueChannel}
        searchFormItem={
          <>
            <Form.Item name="sdkappid" rules={[{ required: true }]}>
              <Input placeholder="sdkappid" />
            </Form.Item>
          </>
        }
      />
    </PageContainer>
  );
};

export default SdkappidChannelCountryList;
