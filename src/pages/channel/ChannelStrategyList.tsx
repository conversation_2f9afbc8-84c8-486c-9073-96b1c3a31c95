import React, { useMemo, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Button,
  Table,
  Form,
  Input,
  message,
  Popconfirm,
  Select,
  InputNumber,
  Tooltip,
  Tag,
  Modal,
} from 'antd';
import { LoadingOutlined, RedoOutlined, SearchOutlined } from '@ant-design/icons';
import { useSetState } from 'react-use';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { history } from 'umi';
import { site, smsType, smsTypeValueMap, strategyTypes } from '@/const/const';
import {
  deleteProviderStrategy,
  getProviderStrategyList,
  refreshStrategyPrice,
} from '@/services/channelStrategy';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import AddChannelGroupStrategy from './component/AddChannelGroupStrategy';
import AddChannelGroupStrategyBind from './component/AddChannelGroupStrategyBind';
import _ from 'lodash';
import { isMobile } from '@/const/jadgeUserAgent';
import { getMncOptions } from './component/utils';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { getProductType } from '@/services/tacticResources';
import { tacticCategory } from './component/const';
import { findLabel, renderYesOrNO } from '@/utils/utils';
import AddChannelTacticBindMutipleCountry from './component/AddChannelTacticBindMutipleCountry';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';
import ViewSupplyConfig from '../channelRessiue/ViewSupplyConfig';
import { deleteRessiueConfiguration } from '@/services/ressiue';

const ChannelStrategyList = () => {
  const { regionOptions = [] } = useFetchCountryInfo();

  const [form] = Form.useForm();
  const initSearchKeys = history.location.state?.searchVals ?? {};
  const dialogRefBindMutipleCountry = useDialogRef();

  const [searchKeys, setSearchKeys] = useSetState<{
    page_index: number;
    page_size: number;
    sms_type: number[];
    [key: string]: any;
  }>({
    page_index: 1,
    page_size: 10,
    sms_type: [],
    ...initSearchKeys,
  });
  const dialogRef = useDialogRef();
  const dialogBindRef = useDialogRef();
  const [priceValue, setPriceValue] = useSetState<Record<string, any>>({});
  const [loadingRecord, setLoadingRecord] = useSetState<Record<string, boolean>>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const { value: productType } = useAsyncRetryFunc(async () => {
    const res = await getProductType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    if (res.code !== 0) {
      message.error(res?.msg);
    }
    return options;
  }, []);

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    let sms_type;
    if (searchKeys.sms_type?.length) {
      sms_type = searchKeys.sms_type?.reduce((pre: number, cur: number) => {
        return pre + cur;
      }, 0);
    }
    const result = await getProviderStrategyList({
      ..._.omit(searchKeys, ['country_code']),
      sms_type,
      country_codes: searchKeys.country_code ? [searchKeys.country_code?.split('_')[0]] : undefined,
    });
    return result?.data ?? {};
  }, [searchKeys]);

  const [mccMncInfo] = useMccMncInfo();

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  async function handlerCount(tactic_id: number[]) {
    setLoadingRecord({ [`${tactic_id}`]: true });
    try {
      const res = await refreshStrategyPrice({ tactic_ids: tactic_id.join(',') });
      if (res?.code === 0) {
        setPriceValue(
          tactic_id.reduce((obj: Record<string, any>, id) => {
            return {
              ...obj,
              [id]: res.data?.price_infos?.[id] || {
                price: '0.000000',
                price_curr: '',
              },
            };
          }, {}),
        );
      }
    } catch (error) {
    } finally {
      setLoadingRecord({
        [`${tactic_id}`]: false,
      });
    }
  }

  async function handleDeleteRessiue(row: any) {
    Modal.confirm({
      title: '删除补发配置',
      content: '确定删除该补发策略吗？删除后无法恢复，请确认。',
      onOk: async () => {
        try {
          const res = await deleteRessiueConfiguration({
            id: row.tactic_id,
            mcc: row.mcc,
            mnc: '000',
            type: 'product',
          });
          if (res.code === 0) {
            message.success('删除补发配置成功');
          }
        } catch (err) {
          message.error('删除补发配置失败');
          console.log(err);
        }
      },
    });
  }

  function viewSupplyCondition(row: any) {
    Modal.info({
      title: '补发场景及条件',
      width: '90%',
      content: (
        <ViewSupplyConfig _smsType={row.sms_type} from="dialog" row={row}></ViewSupplyConfig>
      ),
    });
  }

  const columns: any[] = useMemo(() => {
    async function handleDelete(row: any) {
      try {
        const res = await deleteProviderStrategy({ tactic_id: row.tactic_id });
        if (res.code === 0) {
          message.success('删除成功');
          retry();
        }
      } catch (err) {
        message.error('删除失败');
      }
    }
    return [
      {
        title: '通道策略id',
        dataIndex: 'tactic_id',
        key: 'tactic_id',
        align: 'center',
        width: 60,
      },
      {
        title: '通道策略名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
      },
      {
        title: '策略类型',
        dataIndex: 'tactic_type',
        key: 'sms_type',
        align: 'center',
        render: (val: number) =>
          strategyTypes.find((el) => el.value.toString() === val?.toString())?.label ?? '-',
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        key: 'sms_type',
        align: 'center',
        render: (val: number) =>
          smsTypeValueMap.find((el) => el.value.toString() === val?.toString())?.text,
      },
      {
        title: '国家码',
        dataIndex: 'country_code',
        key: 'country_code',
        align: 'center',
        render: (val: string) => regionOptions.find((el) => el.value === val)?.label,
      },
      {
        title: '运营商',
        key: 'mnc',
        align: 'center',
        render: (row: any) => {
          const { operator_name, mcc, mnc } = _.find(
            mccMncInfo,
            (v) => v.mcc === row.mcc && v.mnc === row.mnc,
          ) ?? {
            operator_name: '',
            mcc: row.mcc,
            mnc: row.mnc,
          };
          return `${operator_name}(${mcc}_${mnc})`;
        },
      },
      {
        title: '标品类型',
        dataIndex: 'product_type',
        key: 'product_type',
        align: 'center',
        render: (val: string) => productType?.find((el) => el.value === val)?.label ?? (val || '-'),
      },
      {
        title: '支持最高成本',
        dataIndex: 'highest_cost',
        key: 'highest_cost',
        align: 'center',
      },
      {
        title: '最低cr',
        dataIndex: 'min_cr',
        key: 'min_cr',
        align: 'center',
      },
      {
        title: '重新调度阈值',
        dataIndex: 'recalculate_threshold',
        key: 'recalculate_threshold',
        align: 'center',
        width: 60,
      },
      {
        title: '标品类别',
        dataIndex: 'category',
        key: 'category',
        align: 'center',
        render: (val) => findLabel(tacticCategory, val),
      },
      {
        title: '是否自动调度',
        dataIndex: 'is_auto',
        key: 'is_auto',
        align: 'center',
        render: (val) => renderYesOrNO(!val),
      },
      {
        title: '是否参与询价',
        dataIndex: 'inquiry_status',
        key: 'inquiry_status',
        align: 'center',
        render: (val) => renderYesOrNO(val),
      },
      // {
      //   title: '是否',
      //   // dataIndex: 'is_sim_farm',
      //   key: 'is_sim_farm',
      //   align: 'center',
      //   render: (row: any) => {
      //     return (
      //       <>
      //         <Tag color={row.is_sim_farm ? 'green' : 'red'}>卡发</Tag>
      //         <Tag color={row.is_local_to_oversea ? 'green' : 'red'}>本地发国际</Tag>
      //         <Tag color={row.is_voice ? 'green' : 'red'}>语音</Tag>
      //         <Tag color={row.is_ott ? 'green' : 'red'}>OTT</Tag>
      //       </>
      //     );
      //   },
      // },
      {
        title: '价格',
        key: 'price',
        align: 'center',
        render: (row: any) => {
          const newPrice =
            priceValue[row.tactic_id] !== undefined ? priceValue[row.tactic_id]?.price : row.price;
          const curr = priceValue[row.tactic_id]
            ? priceValue[row.tactic_id]?.price_curr
            : row.price_curr;
          const newPriceCurr = curr ? `(${curr})` : '';
          return (
            <div style={{ whiteSpace: 'nowrap' }}>
              <span>{`${Number(newPrice).toFixed(6)}${newPriceCurr}`}</span>
              <span style={{ marginLeft: 10 }}>
                {loadingRecord[row.tactic_id] ? (
                  <LoadingOutlined style={{ cursor: 'pointer' }} />
                ) : (
                  <Tooltip title="刷新">
                    <RedoOutlined
                      onClick={() => {
                        handlerCount([row.tactic_id]);
                      }}
                      style={{
                        cursor: 'pointer',
                        color: '#006eff',
                      }}
                    />
                  </Tooltip>
                )}
              </span>
            </div>
          );
        },
      },
      {
        title: '绑定列表',
        align: 'center',
        key: 'bind_num',
        render: (row: any) => (
          <>
            <Button
              type="link"
              onClick={() => {
                history.push('/channel/strategy/bind', {
                  searchKeys,
                  row,
                });
              }}
            >
              点击查看({`共${row.bind_num}条`})
            </Button>
          </>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        align: 'center',
        width: 60,
      },
      {
        title: '操作',
        align: 'center',
        width: 300,
        render: (row: any) =>
          window.location.host !== site.china ? (
            <>
              <Button
                type="link"
                onClick={() => {
                  dialogBindRef.current.open({ initialValues: row, type: 2 });
                }}
              >
                绑定通道
              </Button>
              <Popconfirm
                title="确认删除此条数据吗？"
                onConfirm={() => handleDelete(row)}
                okText="Yes"
                cancelText="No"
              >
                <Button type="link">删除</Button>
              </Popconfirm>
              <Button
                type="link"
                onClick={() => {
                  dialogRef.current.open({
                    initialValues: {
                      ...row,
                      sms_type: smsTypeValueMap
                        .find((el) => el.value === row.sms_type)
                        ?.text?.split('，')
                        .map((v) => smsType.find((s) => s.label === v)?.value),
                      operator: `${row.mcc}_${row.mnc}`,
                      highest_cost: Number(row.highest_cost),
                    },
                    productType,
                  });
                }}
              >
                编辑
              </Button>
              <br />
              <Button
                type="link"
                onClick={() => {
                  history.push('/resources/operator-tactic/ressiue', {
                    row,
                    from: 'strategy',
                    searchVals: form.getFieldsValue(),
                  });
                }}
              >
                配置补发
              </Button>
              <Button
                type="link"
                onClick={() => {
                  viewSupplyCondition(row);
                }}
              >
                查看补发
              </Button>
              <Button
                type="link"
                onClick={() => {
                  handleDeleteRessiue(row);
                }}
              >
                删除补发
              </Button>
            </>
          ) : null,
      },
    ];
  }, [
    dialogBindRef,
    dialogRef,
    loadingRecord,
    mccMncInfo,
    priceValue,
    productType,
    regionOptions,
    retry,
    searchKeys,
  ]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  function onSubmit(vals: any) {
    setSearchKeys({
      ...vals,
      mnc: vals.mnc?.split('_')?.[1],
      mcc: vals.mnc?.split('_')?.[0],
      page_index: 1,
    });
  }

  return (
    <PageContainer>
      {window.location.host !== site.china && (
        <>
          <Button
            type="primary"
            style={{ marginBottom: 10 }}
            onClick={() => {
              dialogRef.current.open({ initialValues: null, productType });
            }}
          >
            添加
          </Button>
          <Button
            type="primary"
            style={{ marginLeft: 10 }}
            onClick={() => {
              dialogRefBindMutipleCountry.current.open();
            }}
          >
            多国家配置
          </Button>
        </>
      )}
      <Button
        style={{ marginLeft: 10, marginBottom: 10 }}
        loading={loadingRecord[`${selectedRowKeys}`]}
        disabled={!selectedRowKeys?.length}
        onClick={() => {
          handlerCount(selectedRowKeys.map((el) => Number(el)));
        }}
      >
        <RedoOutlined
          style={{
            cursor: 'pointer',
          }}
        />
        批量刷新价格
      </Button>
      <Form
        className="sender-search-form"
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
        initialValues={{ ...searchKeys }}
      >
        <Form.Item name="tactic_id" label="策略id">
          <InputNumber placeholder="策略id" controls={false} />
        </Form.Item>
        <Form.Item name="name" label="通道策略名称">
          <Input placeholder="通道策略名称" />
        </Form.Item>
        <Form.Item name="tactic_type" label="策略类型">
          <Select placeholder="策略类型" options={strategyTypes} allowClear />
        </Form.Item>
        <Form.Item name="country_code" label="国家/地区码">
          <Select
            placeholder="国家/地区码"
            options={regionOptions}
            value={form.getFieldValue('country_code')}
            onChange={(value) => {
              form.setFieldsValue({ country_code: value, mnc: undefined });
            }}
            allowClear
            showSearch
            filterOption={(inputValue, option) =>
              !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
            }
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.country_code !== curValues.country_code
          }
        >
          {({ getFieldValue }) => {
            const countryCode = getFieldValue('country_code');
            return (
              <Form.Item name="mnc" label="运营商">
                <Select
                  style={{ width: 150 }}
                  placeholder="mnc"
                  options={getMncOptions(mccMncInfo, countryCode)}
                  allowClear
                  showSearch
                  filterOption={(inputValue, option) => !!option?.label.includes(inputValue)}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="sms_type" label="短信类型">
          <Select
            allowClear
            placeholder="短信类型"
            options={smsType}
            mode="multiple"
            style={{ minWidth: 100 }}
          />
        </Form.Item>
        <Form.Item name="product_type" label="标品类型">
          <Select
            options={productType}
            showSearch
            placeholder="请选择"
            filterOption={(inputValue, option) =>
              !!option?.label.toLowerCase().includes(inputValue.toLowerCase())
            }
            style={{ width: 180 }}
            allowClear
          ></Select>
        </Form.Item>
        <Form.Item name="category" label="标品类别">
          <Select
            options={tacticCategory}
            placeholder="请选择"
            allowClear
            style={{ width: 180 }}
          ></Select>
        </Form.Item>
        {/* <Form.Item name="is_sim_farm" label="是否卡发">
          <Select allowClear options={yesOrNo} style={{ width: 60 }} />
        </Form.Item>
        <Form.Item name="is_local_to_oversea" label="是否本地发国际">
          <Select allowClear options={yesOrNo} style={{ width: 60 }} />
        </Form.Item>
        <Form.Item name="is_voice" label="是否语音">
          <Select allowClear options={yesOrNo} style={{ width: 60 }} />
        </Form.Item>
        <Form.Item name="is_ott" label="是否OTT ">
          <Select allowClear options={yesOrNo} style={{ width: 60 }} />
        </Form.Item> */}
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        rowKey="tactic_id"
        columns={columns}
        dataSource={list}
        loading={loading}
        pagination={{
          defaultCurrent: searchKeys.page_index,
          pageSize: searchKeys.page_size,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys,
          onChange: (selectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
        scroll={isMobile() ? { x: 'max-content' } : { x: 1900 }}
      />
      <AddChannelGroupStrategy dialogRef={dialogRef} reload={retry} />
      <AddChannelGroupStrategyBind dialogRef={dialogBindRef} reload={retry} />
      <AddChannelTacticBindMutipleCountry
        dialogRef={dialogRefBindMutipleCountry}
        reload={retry}
      ></AddChannelTacticBindMutipleCountry>
    </PageContainer>
  );
};

export default ChannelStrategyList;
