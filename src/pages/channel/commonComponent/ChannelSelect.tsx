import ScrollLoadMore from '@/components/ScrollLoadMore';
import { getProviderAccountList, getProviderGroupList } from '@/services/channel';
import { getProviderStrategyList } from '@/services/channelStrategy';
import React from 'react';

type loadFnParams = {
  0?: any;
  1?: any;
  2?: any;
};

export default function ChannelSelect({
  value,
  onChange,
  reloadOn,
  type,
  mode = 'multiple',
  placeholder = '请选择',
  loadFnParams = {
    0: {}, // 单通道
    1: {}, // 通道组
    2: {}, // 通道策略
  },
  loadFnCallBack,
  ...restProps
}: {
  value: any;
  onChange: (value: any, context?: any) => void;
  reloadOn?: string;
  type: 0 | 1 | 2;
  mode?: 'multiple' | 'tags' | 'default';
  placeholder?: string;
  loadFnParams?: loadFnParams;
  loadFnCallBack?: (params: any) => void;
  [key: string]: any;
}) {
  return (
    <ScrollLoadMore
      mode={mode}
      value={value}
      reloadOn={reloadOn}
      onChange={onChange}
      placeholder={placeholder}
      loadFn={async (params) => {
        const pageParams = {
          page_index: params.page_index,
          page_size: params.page_size,
        };
        let res;
        let options = [];
        if (type === 1) {
          res = await getProviderGroupList({
            ...pageParams,
            name: params.search_key,
            ...loadFnParams[1],
          });
          options = res?.data?.list
            ?.filter((el: any) => el.bind_num)
            .map((item: { group_id: number; name: number }) => {
              return {
                value: item.group_id,
                label: item.name,
              };
            });
        } else if (type === 2) {
          res = await getProviderStrategyList({
            ...pageParams,
            name: params.search_key,
            ...loadFnParams[2],
          });
          options = res?.data?.list
            ?.filter((el: any) => el.bind_num)
            .map((item: { tactic_id: number; name: number }) => {
              return {
                value: item.tactic_id,
                label: `${item.name}(${item.tactic_id})`,
              };
            });
        } else if (type === 0) {
          res = await getProviderAccountList({
            ...pageParams,
            search_key: params.search_key,
            status: 7,
            // tencent_node: window.location.host !== site.china ? 2 : 1,
            ...loadFnParams[0],
          });
          options = res?.data?.list?.map(
            (item: {
              provider_id: number;
              supplier_id: number;
              provider_name: string;
              supplier_name: string;
            }) => {
              return {
                value: item.provider_id,
                label: `${item.provider_name}_${item.supplier_name}(${item.provider_id})`,
              };
            },
          );
        }
        loadFnCallBack?.({ params, options });
        return {
          options,
          total: res.data.count,
        };
      }}
      {...restProps}
    />
  );
}
