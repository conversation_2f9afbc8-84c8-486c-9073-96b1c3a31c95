import React, { useMemo } from 'react';
import { getMncOptions } from '../component/utils';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import SelectAll from '@/components/SelectAll';
import { Select } from 'antd';

export default function MncSelect({
  value,
  onChange,
  initialValues,
  mode,
  ...restProps
}: {
  value?: any;
  onChange?: (value: any) => void;
  initialValues?: {
    mcc?: string;
    country_code?: string;
    mnc?: string[] | undefined;
  };
  showAll?: boolean;
  mode?: 'multiple' | 'tags' | undefined;

  [key: string]: any;
}) {
  const [mccMncInfo] = useMccMncInfo();

  const mncOptions = useMemo(() => {
    const country_code =
      initialValues?.country_code ??
      mccMncInfo?.find((item: any) => item.mcc === initialValues?.mcc)?.country_code;
    const mncList = initialValues?.mnc
      ? mccMncInfo.filter((item: any) => initialValues.mnc?.includes(item.mnc))
      : mccMncInfo;
    return getMncOptions(mncList, country_code);
  }, [initialValues, mccMncInfo]);

  return mode === 'multiple' ? (
    <SelectAll
      value={value}
      onChange={onChange}
      mode={mode || 'multiple'}
      placeholder="请选择运营商"
      options={mncOptions}
      {...restProps}
    />
  ) : (
    <Select
      value={value}
      onChange={onChange}
      mode={mode}
      placeholder="请选择运营商"
      options={mncOptions}
      {...restProps}
    />
  );
}
