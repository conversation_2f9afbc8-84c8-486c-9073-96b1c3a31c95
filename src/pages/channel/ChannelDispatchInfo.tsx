import React from 'react';
import { Button, Form, Input, Select } from 'antd';
import { useSetState } from 'react-use';
import { PageContainer } from '@ant-design/pro-layout';
import _ from 'lodash';
import { SearchOutlined } from '@ant-design/icons';
import { channelType } from './component/const';
import ChannelDispatchTabs from './component/ChannelDispatchTabs';
import { getMncOptions } from './component/utils';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import ChannelSelect from './commonComponent/ChannelSelect';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

enum FlagType {
  '请选择通道',
  '请选择通道组',
  '请选择通道策略',
}

const ChannelDispatchInfo = () => {
  const { regionOptions = [] } = useFetchCountryInfo();

  const [form] = Form.useForm();
  const [mccMncInfo] = useMccMncInfo();

  const [searchKeys, setSearchKeys] = useSetState<{
    channel_id?: number;
  }>({});

  return (
    <PageContainer>
      <div>
        <Form
          form={form}
          layout="inline"
          labelAlign="right"
          onFinish={(vals) =>
            setSearchKeys({
              ...vals,
              mnc: vals.mnc?.split('_')?.[1],
            })
          }
          initialValues={{ type: 0, assign_operator: 0 }}
        >
          <Form.Item name="type" noStyle>
            <Select
              options={channelType}
              onChange={() => form.setFieldsValue({ channel_id: undefined })}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.type !== curValues.type}
          >
            {({ getFieldValue }) => {
              const typeValue = getFieldValue('type');
              return (
                <Form.Item style={{ width: 250 }} name="channel_id" rules={[{ required: true }]}>
                  <ChannelSelect
                    mode="default"
                    value={getFieldValue('channel_id')}
                    reloadOn={
                      getFieldValue('type') === 0
                        ? ''
                        : `${getFieldValue('type')}_${getFieldValue('mnc')}`
                    }
                    type={getFieldValue('type')}
                    onChange={(value) => {
                      form.setFieldsValue({ channel_id: value });
                    }}
                    pageSize={1000}
                    placeholder={FlagType[typeValue]}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item name="country_code" label="国家/地区">
            <Select
              showSearch
              allowClear
              placeholder="国家/地区"
              options={regionOptions}
              value={form.getFieldValue('country_code')}
              onChange={(value) => {
                form.setFieldsValue({
                  country_code: value,
                  mnc: undefined,
                  mcc: _.find(mccMncInfo, (v) => v.country_code === value)?.mcc,
                });
              }}
              filterOption={(input, option) => {
                return (
                  option?.label.includes(input) ||
                  option?.value.includes(input.toLocaleUpperCase()) ||
                  false
                );
              }}
              style={{ width: 250 }}
            />
          </Form.Item>
          <Form.Item name="mcc" hidden>
            <Input />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) =>
              prevValues.country_code !== curValues.country_code ||
              prevValues.assign_operator !== curValues.assign_operator
            }
          >
            {({ getFieldValue }) => {
              const countryCode = getFieldValue('country_code');
              const assignOperator = getFieldValue('assign_operator');
              return (
                <>
                  <Form.Item name="assign_operator" label="是否指定运营商">
                    <Select
                      options={[
                        { label: '是', value: 1 },
                        { label: '否', value: 0 },
                      ]}
                      style={{ width: 100 }}
                    ></Select>
                  </Form.Item>
                  {assignOperator ? (
                    <Form.Item name="mnc" label="运营商" rules={[{ required: true }]}>
                      <Select
                        style={{ width: 150 }}
                        placeholder="mnc"
                        options={getMncOptions(mccMncInfo, countryCode)}
                        allowClear
                        showSearch
                        filterOption={(inputValue, option) =>
                          !!option?.label.toUpperCase().includes(inputValue.toLowerCase())
                        }
                      />
                    </Form.Item>
                  ) : null}
                </>
              );
            }}
          </Form.Item>
          <Form.Item>
            <Button htmlType="submit" type="primary" icon={<SearchOutlined />}>
              查询
            </Button>
          </Form.Item>
        </Form>
      </div>
      <hr />
      <div style={{ marginTop: 15 }}>
        {!searchKeys.channel_id ? <p>暂无数据</p> : <ChannelDispatchTabs searchKeys={searchKeys} />}
      </div>
    </PageContainer>
  );
};
export default ChannelDispatchInfo;
