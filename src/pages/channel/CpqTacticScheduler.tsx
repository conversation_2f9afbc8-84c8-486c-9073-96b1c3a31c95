import React, { useCallback, useMemo, useRef } from 'react';
import { Button, Typography } from 'antd';
import { ActionType, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import { getTacticCpqRecord } from '@/services/channel';
import { findLabel, getOperatorName } from '@/utils/utils';
import { smsType } from '@/const/const';
import { saleType } from '../tactic-resources/tacticSaleProduct';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { tacticCategory } from './component/const';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getProductType } from '@/services/tacticResources';
import { useDialogRef } from '@/utils/react-use/useDialog';
import CpqTacticOperateDialog from './component/CpqTacticOperateDialog';
import CpqTacticScheduleDialog from './component/CpqTacticScheduleDialog';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const { Text } = Typography;

export const statusOptions = [
  { value: 0, label: '待处理' },
  { value: 1, label: '已调度', title: '确认调度' },
  { value: 2, label: '已拒绝', title: '拒绝' },
  { value: 3, label: '手动调度中', title: '手动调度' },
  { value: 4, label: '已手动调度' },
];

const CpqTacticScheduler = () => {
  const { regionOptions = [] } = useFetchCountryInfo();
  const [mccMncInfo] = useMccMncInfo();
  const actionRef = useRef<ActionType>();
  const operateDialogRef = useDialogRef();
  const schedlueDialogRef = useDialogRef();
  const formRef = useRef<any>();
  const [selectedRows, setSelectedRows] = React.useState<React.Key[]>([]);

  const { value: productType } = useAsyncRetryFunc(async () => {
    const res = await getProductType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);

  const handleConfirmBind = useCallback(
    (row: any[]) => {
      operateDialogRef.current.open({
        initialValues: row,
        status: 1,
      });
    },
    [operateDialogRef],
  );

  const handleChangeStatus = useCallback(
    (row: any[], status: number) => {
      operateDialogRef.current.open({
        initialValues: row,
        status,
      });
    },
    [operateDialogRef],
  );

  const columns: any = useMemo(() => {
    return [
      {
        title: '记录ID',
        dataIndex: 'record_id',
        key: 'record_id',
        hideInSearch: true,
      },
      {
        title: '测算单id',
        dataIndex: 'cpq_id',
        key: 'cpq_id',
      },
      {
        title: 'uin',
        dataIndex: 'uin',
        key: 'uin',
        width: 120,
      },
      {
        title: 'qappid',
        dataIndex: 'qappid',
        key: 'qappid',
      },
      {
        title: 'sdkappid',
        dataIndex: 'sdkappids',
        key: 'sdkappids',
        width: 100,
      },
      {
        title: '国家',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        fieldProps: {
          options: regionOptions,
          showSearch: true,
        },
        render: (text, row) => findLabel(regionOptions, row.country_code),
      },
      {
        title: '运营商',
        dataIndex: 'mnc',
        key: 'mnc',
        hideInSearch: true,
        render: (text, row) => getOperatorName(mccMncInfo, row.mnc, { mcc: row.mcc }),
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        key: 'sms_type',
        valueType: 'select',
        fieldProps: {
          options: smsType,
          showSearch: true,
        },
        render: (text, row) => findLabel(smsType, row.sms_type),
      },
      {
        title: '售卖类型',
        dataIndex: 'sale_type',
        key: 'sale_type',
        valueType: 'select',
        fieldProps: {
          options: saleType,
          showSearch: true,
        },
        render: (text, row) => findLabel(saleType, row.sale_type),
      },
      {
        title: '标品id',
        dataIndex: 'tactic_id',
        key: 'tactic_id',
        hideInSearch: true,
        render: (text, row) => (row.sale_type === 2 ? '-' : `${row.tactic_name}(${row.tactic_id})`),
      },
      {
        title: '标品类别',
        dataIndex: 'category',
        key: 'category',
        hideInSearch: true,
        width: 120,
        render: (text, row) =>
          row.sale_type === 2 ? '-' : findLabel(tacticCategory, row.category),
      },
      {
        title: '标品类型',
        dataIndex: 'product_type',
        key: 'product_type',
        hideInSearch: true,
        render: (text, row) =>
          row.sale_type === 2 ? '-' : findLabel(productType, row.product_type),
      },

      {
        title: '销售价格',
        dataIndex: 'price',
        key: 'price',
        width: 80,
        render: (text, row) => `${Number(row.price).toFixed(6)}(${row.price_curr})`,
        hideInSearch: true,
      },
      {
        title: '成本价格',
        dataIndex: 'cost_price',
        key: 'cost_price',
        hideInSearch: true,
        width: 80,
        render: (text, row) =>
          row.sale_type === 2
            ? '-'
            : `${Number(row.cost_price).toFixed(6)}(${row.cost_price_curr})`,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        valueType: 'select',
        fieldProps: {
          options: statusOptions,
          showSearch: true,
        },
        render: (text, row) => findLabel(statusOptions, row.status),
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        hideInSearch: true,
      },
      {
        title: '调度信息',
        dataIndex: 'schedule',
        key: 'schedule',
        hideInSearch: true,
        render: (text, row) => (
          <Button
            type="link"
            onClick={() => schedlueDialogRef.current.open({ initialValues: { ...row } })}
          >
            点击查看
          </Button>
        ),
      },
      {
        title: '更新时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        hideInSearch: true,
      },

      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              {row.status === 0 && (
                <>
                  <Button type="link" onClick={() => handleConfirmBind([row])}>
                    <Text type="success">自动调度</Text>
                  </Button>
                  <Button type="link" onClick={() => handleChangeStatus([row], 2)}>
                    <Text type="danger">拒绝</Text>
                  </Button>
                  <Button type="link" onClick={() => handleChangeStatus([row], 3)}>
                    手动调度
                  </Button>
                </>
              )}

              {row.status === 3 && (
                <Button type="link" onClick={() => handleChangeStatus([row], 4)}>
                  完成
                </Button>
              )}
            </>
          );
        },
      },
    ];
  }, [
    handleChangeStatus,
    handleConfirmBind,
    mccMncInfo,
    productType,
    regionOptions,
    schedlueDialogRef,
  ]);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getTacticCpqRecord({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list,
      total: data.count,
      success: true,
    };
  }, []);

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any) => {
      setSelectedRows(selectedRows);
    },
  };

  return (
    <PageContainer>
      <CpqTacticOperateDialog
        dialogRef={operateDialogRef}
        onSuccess={() => actionRef.current?.reload()}
      ></CpqTacticOperateDialog>
      <CpqTacticScheduleDialog
        dialogRef={schedlueDialogRef}
        onSuccess={() => actionRef.current?.reload()}
      ></CpqTacticScheduleDialog>
      <Button onClick={() => handleConfirmBind(selectedRows)} style={{ marginRight: 10 }}>
        <Text type="success">自动调度</Text>
      </Button>
      <Button
        danger
        onClick={() => handleChangeStatus(selectedRows, 2)}
        style={{ marginRight: 10 }}
      >
        <Text type="danger">拒绝</Text>
      </Button>
      <Button onClick={() => handleChangeStatus(selectedRows, 3)} style={{ marginRight: 10 }}>
        <Text type="success">手动调度</Text>
      </Button>
      <Button onClick={() => handleChangeStatus(selectedRows, 4)}>
        <Text>完成</Text>
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="record_id"
        search={{
          collapseRender: false,
          collapsed: false,
          span: 5,
        }}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
        request={requestFn}
        options={false}
        scroll={{ x: 2000 }}
      />
    </PageContainer>
  );
};
export default CpqTacticScheduler;
