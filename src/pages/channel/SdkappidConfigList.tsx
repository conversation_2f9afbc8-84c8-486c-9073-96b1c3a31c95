import React from 'react';
import { history } from 'umi';
import { Form } from 'antd';
import {
  getSdkChannelList,
  editSdkChannel,
  deleteSdkChannel,
  addSdkChannel,
} from '@/services/channel';

import SdkChannelConfigAdd from './component/AddSdkChannelConfig';
import ChannelConfigList from './component/ConfigList';

const SdkChannelConfig = () => {
  const pathParams: any = (history.location.state as { row?: Record<string, any> })?.row;

  return (
    <ChannelConfigList
      obj="sdkappid"
      getListFn={(params) => getSdkChannelList({ ...params, sdkappid: pathParams.sdkappid })}
      editFn={(params) => editSdkChannel({ ...params, sdkappid: pathParams.sdkappid })}
      deleteFn={(params) => deleteSdkChannel({ ...params, sdkappid: pathParams.sdkappid })}
      addFn={(params) => {
        const data = params.map((el: any) => ({ ...el, sdkappid: pathParams.sdkappid }));
        return addSdkChannel({
          params: data,
        });
      }}
      addComponent={(dialogRef, getList) => (
        <SdkChannelConfigAdd dialogRef={dialogRef} reload={getList} />
      )}
      formItem={
        <Form.Item label="sdkappid">
          <span>{pathParams.sdkappid}</span>
        </Form.Item>
      }
    />
  );
};

export default SdkChannelConfig;
