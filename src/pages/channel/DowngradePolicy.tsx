import React, { useCallback, useMemo, useRef, useState } from 'react';
import { But<PERSON>, Modal } from 'antd';
import {
  ActionType,
  BetaSchemaForm,
  PageContainer,
  ProFormColumnsType,
  ProTable,
} from '@ant-design/pro-components';
import { isMobile } from '@/const/jadgeUserAgent';
import _ from 'lodash';
import {
  getDowngradePolicyList,
  deletetDowngradePolicy,
  editDowngradePolicy,
  addDowngradePolicy,
} from '@/services/channelDowngradePolicy';
import { findLabel } from '@/utils/utils';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

export type DataItem = {
  name: string;
  state: string;
};

const policyTypeOptions = [
  { label: 'sdkappid+国家配置', value: 1 },
  { label: 'uin+国家配置', value: 2 },
  { label: 'uin触发策略', value: 3 },
];

const allFields = [
  'sdkappid',
  'uin',
  'mcc',
  'mccs',
  'policy_type',
  'send_check_range',
  'send_check_count',
  'cbfail_check_range',
  'cbfail_check_count',
  'downgrade_percent',
];
const initZeroValsKeys = [
  'send_check_range',
  'send_check_count',
  'cbfail_check_range',
  'cbfail_check_count',
  'downgrade_percent',
];
const addFormFields = [
  'policy_type',
  'send_check_range',
  'send_check_count',
  'cbfail_check_range',
  'cbfail_check_count',
  'downgrade_percent',
];
const editFormFields = ['id', ...addFormFields];
const formItemProps = { rules: [{ required: true }] };
const commonCountryColumn: ProFormColumnsType = {
  title: '国家',
  dataIndex: 'mcc',
  key: 'mcc',
  valueType: 'select',
  formItemProps,
};

const uinColumn = {
  title: 'UIN',
  dataIndex: 'uin',
  key: 'uin',
  valueType: 'digit',
  formItemProps,
  fieldProps: { controls: false, min: 0, precision: 0 },
  render: (_, { uin }: any) => uin || '-',
};
const sdkColumn = {
  title: 'sdkappid',
  dataIndex: 'sdkappid',
  key: 'sdkappid',
  valueType: 'digit',
  formItemProps,
  fieldProps: { precision: 0, min: 0, controls: false },
  render: (_, { sdkappid }: any) => sdkappid || '-',
  width: 200,
};
const idColumn = {
  title: '配置ID',
  dataIndex: 'id',
  key: 'id',
  width: 70,
  hideInSearch: true,
  fieldProps: { disabled: true },
};
const policyTypeColumn = {
  title: '配置层级策略',
  dataIndex: 'policy_type',
  key: 'policy_type',
  valueType: 'select',
  hideInSearch: true,
  hideInTable: true,
  formItemProps,
  fieldProps: { options: policyTypeOptions, placeholder: '请选择策略' },
};
const generateFormItems = (
  columns: any[],
  formFields: string[],
  commonCountryColumn: any,
  regionOptionsMcc: any,
  isEditForm: boolean,
): any => {
  return columns
    .filter((el) => formFields.includes(el.key))
    .map((el) => {
      const formCommonCountryColumn = {
        ...commonCountryColumn,
        fieldProps: {
          options: regionOptionsMcc,
          placeholder: '请选择国家',
          mode: isEditForm ? '' : 'multiple',
          showSearch: true,
        },
        transform: (value: any) => ({ [isEditForm ? 'mcc' : 'mccs']: value }),
      };
      if (el.key === 'policy_type') {
        const withWitdhUinColumn = {
          ...uinColumn,
          width: 320,
        };
        return [
          ...(isEditForm ? [{ ...el, fieldProps: { ...el.fieldProps, disabled: true } }] : [el]),
          //   el,
          {
            valueType: 'dependency',
            name: ['policy_type'],
            key: 'policy_type_dependency',
            columns: ({ policy_type }) => {
              switch (policy_type) {
                case 1:
                  return [sdkColumn, formCommonCountryColumn];
                case 2:
                  return [withWitdhUinColumn, formCommonCountryColumn];
                case 3:
                  return [withWitdhUinColumn];
                default:
                  return [];
              }
            },
          },
        ];
      } else {
        return el;
      }
    })
    .flat();
};
const DowngradePolicy = () => {
  const actionRef = useRef<ActionType>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const [type, setType] = useState('create');
  const [initialValues, setInitialValues] = useState<any>({});
  const { regionOptionsMcc = [] } = useFetchCountryInfo();

  function handleDelete(row: any) {
    Modal.confirm({
      title: '提示',
      content: '确定删除该补发策略吗?',
      onOk: async () => {
        const res = await deletetDowngradePolicy({
          id: row.id,
        });
        if (res.code === 0) {
          actionRef.current?.reload();
        }
      },
    });
  }

  const columns: any = useMemo(() => {
    return [
      idColumn,
      policyTypeColumn,
      uinColumn,
      sdkColumn,
      {
        ...commonCountryColumn,
        fieldProps: {
          options: regionOptionsMcc,
          placeholder: '请选择国家',
          showSearch: true,
        },
        transform: (val: string) => ({ mcc: val }),
        render: (_, { mcc }: any) => (mcc ? findLabel(regionOptionsMcc, mcc) : ''),
      },
      {
        title: '发送检查时间范围(分钟)',
        dataIndex: 'send_check_range',
        key: 'send_check_range',
        hideInSearch: true,
        valueType: 'digit',
        formItemProps,
        fieldProps: { controls: true, precision: 0, max: 999, min: 0, defaultValue: 0 },
      },
      {
        title: '下发一次就触发(次)',
        dataIndex: 'send_check_count',
        key: 'send_check_count',
        hideInSearch: true,
        formItemProps,
        valueType: 'digit',
        fieldProps: { controls: true, precision: 0, max: 10, min: 0, defaultValue: 0 },
      },
      {
        title: '回执失败检查时间范围设置(分钟)',
        dataIndex: 'cbfail_check_range',
        key: 'cbfail_check_range',
        hideInSearch: true,
        formItemProps,
        valueType: 'digit',
        fieldProps: { controls: true, precision: 0, max: 999, min: 0, defaultValue: 0 },
      },
      {
        title: '失败一次就触发(次)',
        dataIndex: 'cbfail_check_count',
        key: 'cbfail_check_count',
        hideInSearch: true,
        formItemProps,
        valueType: 'digit',
        fieldProps: { controls: true, precision: 0, max: 10, min: 0, defaultValue: 0 },
      },
      {
        title: '降级比例(%)',
        dataIndex: 'downgrade_percent',
        key: 'downgrade_percent',
        hideInSearch: true,
        valueType: 'digit',
        formItemProps,
        fieldProps: { controls: true, precision: 0, max: 100, min: 0, defaultValue: 0 },
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        width: 150,
        render: (text: string, row: any) => {
          return (
            <>
              <Button
                type="link"
                onClick={() => {
                  setOpen(true);
                  setType('edit');
                  // 弹窗表单未初始化，addFormRef为空
                  !addFormRef.current && setInitialValues(row);
                  addFormRef.current?.setFieldsValue(row);
                }}
              >
                编辑
              </Button>
              <Button
                type="link"
                onClick={() => {
                  handleDelete(row);
                }}
              >
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, [addFormRef, regionOptionsMcc, type]);

  const formItems = generateFormItems(
    columns,
    addFormFields,
    commonCountryColumn,
    regionOptionsMcc,
    false,
  );
  const editFormItems = generateFormItems(
    columns,
    editFormFields,
    commonCountryColumn,
    regionOptionsMcc,
    true,
  );

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getDowngradePolicyList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals: any) {
    try {
      const res =
        type === 'edit'
          ? await editDowngradePolicy({
              ...vals,
              sdkappid: addFormRef.current?.getFieldValue('sdkappid'),
            })
          : await addDowngradePolicy({ ...vals });
      if (res.code === 0) {
        actionRef.current?.reload();
        setOpen(false);
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  function reset() {
    const vals = _.reduce(
      allFields,
      (acc: { [key: string]: any }, key: string) => ({
        ...acc,
        [key]: undefined,
      }),
      {},
    );
    addFormRef.current?.setFieldsValue({ ...vals });
  }

  function setInit() {
    const vals = _.reduce(
      initZeroValsKeys,
      (acc: { [key: string]: any }, key: string) => ({
        ...acc,
        [key]: 0,
      }),
      {},
    );
    !addFormRef.current && setInitialValues({ ...vals });
    addFormRef.current?.setFieldsValue({ ...vals });
  }
  return (
    <PageContainer>
      <BetaSchemaForm<DataItem>
        formRef={addFormRef}
        layoutType="ModalForm"
        open={open}
        onOpenChange={setOpen}
        title={type === 'edit' ? '编辑补发策略配置' : '补发策略配置'}
        layout="horizontal"
        labelCol={{ span: 10 }}
        onFinish={onFinish}
        columns={type === 'edit' ? editFormItems : formItems}
        width={600}
        initialValues={initialValues}
      />
      <Button
        type="primary"
        onClick={() => {
          reset();
          setInit();
          setOpen(true);
          setType('create');
        }}
      >
        补发策略配置
      </Button>
      <ProTable
        actionRef={actionRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 2000 }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default DowngradePolicy;
