import React from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Tabs, TabsProps } from 'antd';
import MutipleCountryBindChannelByTactic from './component/MutipleCountryBindChannelByTactic';
import MutipleCountryBindChannelByAccount from './component/MutipleCountryBindChannelByAccount';

const MutipleCountryBindChannel = () => {
  const items: TabsProps['items'] = [
    {
      key: 'tactic',
      label: '绑定策略',
      children: <MutipleCountryBindChannelByTactic></MutipleCountryBindChannelByTactic>,
    },
    {
      key: 'account',
      label: '绑定单通道',
      children: <MutipleCountryBindChannelByAccount></MutipleCountryBindChannelByAccount>,
    },
  ];

  const onChange = (key: string) => {
    console.log(key);
  };

  return (
    <PageContainer>
      <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
    </PageContainer>
  );
};

export default MutipleCountryBindChannel;
