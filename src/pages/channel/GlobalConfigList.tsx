import React from 'react';
import {
  getGlobalChannelList,
  editGlobalChannel,
  deleteGlobalChannel,
  addGlobalChannel,
} from '@/services/channel';

import GlobalChannelConfigAdd from './component/AddGlobalChannelConfig';
import ChannelConfigList from './component/ConfigList';

const GlobalChannelConfig = () => {
  return (
    <ChannelConfigList
      obj="global"
      getListFn={(params) => getGlobalChannelList({ ...params })}
      editFn={(params) => editGlobalChannel({ ...params })}
      deleteFn={(params) => deleteGlobalChannel({ ...params })}
      addFn={(params) => {
        return addGlobalChannel({
          params,
        });
      }}
      addComponent={(dialogRef, getList) => (
        <GlobalChannelConfigAdd dialogRef={dialogRef} reload={getList} />
      )}
    />
  );
};

export default GlobalChannelConfig;
