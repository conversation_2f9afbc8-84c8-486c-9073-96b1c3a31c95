import React from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Form, Input } from 'antd';

import { getUinConfigCountry, deleteUinRessiueChannel } from '@/services/channel';

import CountryList from './component/CountryList';

const UinChannelCountryList = () => {
  return (
    <PageContainer>
      <CountryList
        obj="uin"
        getListFn={getUinConfigCountry}
        deleteFn={deleteUinRessiueChannel}
        searchFormItem={
          <>
            <Form.Item name="uin" rules={[{ required: true }]}>
              <Input placeholder="uin" />
            </Form.Item>
          </>
        }
      />
    </PageContainer>
  );
};

export default UinChannelCountryList;
