import React, { useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, message, Popconfirm, Select, Tooltip } from 'antd';
import { LoadingOutlined, RedoOutlined, SearchOutlined } from '@ant-design/icons';
import { useSetState } from 'react-use';
import { getProviderGroupList, deleteProviderGroup, refreshGroupPrice } from '@/services/channel';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { history } from 'umi';
import { site, smsType, smsTypeValueMap } from '@/const/const';
import _ from 'lodash';
import ChannelGroupAdd from './component/AddChannelGroup';
import AddChannelGroupBind from './component/AddChannelGroupBind';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getMncOptions } from './component/utils';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { findLabel } from '@/utils/utils';
import { getResourceType } from '@/services/tacticResources';
import { groupCategory } from './component/const';
import AddChannelGroupBindMutipleCountry from './component/AddChannelGroupBindMutipleCountry';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const ChannelGroup = () => {
  const [form] = Form.useForm();
  const initSearchKeys =
    (history.location.state as { searchKeys?: Record<string, any> })?.searchKeys ?? {};
  const [searchKeys, setSearchKeys] = useSetState<{
    page_index: number;
    page_size: number;
    sms_type: number[];
    mnc?: string;
    [key: string]: any;
  }>({
    page_index: 1,
    page_size: 10,
    sms_type: [],
    ...initSearchKeys,
  });

  const [priceValue, setPriceValue] = useSetState<Record<string, any>>({});
  const [loadingRecord, setLoadingRecord] = useSetState<Record<string, boolean>>({});
  const dialogRef = useDialogRef();
  const dialogRefBind = useDialogRef();
  const dialogRefBindMutipleCountry = useDialogRef();
  const [mccMncInfo] = useMccMncInfo();
  const { regionOptions = [] } = useFetchCountryInfo();

  const { value: resourceType } = useAsyncRetryFunc(async () => {
    const res = await getResourceType({
      page_index: 1,
      page_size: 1000,
    });
    const options = res?.data?.list?.map((item: { name: string; id: number }) => {
      return {
        value: item.id,
        label: item.name,
      };
    });
    return options;
  }, []);

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    let sms_type;
    if (searchKeys.sms_type?.length) {
      sms_type = searchKeys.sms_type?.reduce((pre: number, cur: number) => {
        return pre + cur;
      }, 0);
    }
    const res = await getProviderGroupList({
      ..._.omit(searchKeys, ['country_code']),
      sms_type,
      mnc: searchKeys?.mnc?.split('_')?.[1],
      country_codes: searchKeys.country_code ? [searchKeys.country_code?.split('_')[0]] : undefined,
    });
    return res.data;
  }, [searchKeys]);

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? [];
  }, [state]);

  const columns: any = useMemo(() => {
    async function handlerCount(group_id: number) {
      setLoadingRecord({ [group_id]: true });
      try {
        const res = await refreshGroupPrice({ group_id });
        if (res?.code === 0) {
          setPriceValue({
            [group_id]: {
              ...(res.data?.price_info || {
                price: '0.000000',
                price_curr: '',
              }),
            },
          });
        }
      } catch (error) {
      } finally {
        setLoadingRecord({ [group_id]: false });
      }
    }
    async function handleDelete(row: any) {
      try {
        const res = await deleteProviderGroup({ group_id: row.group_id });
        if (res.code === 0) {
          message.success('删除成功');
          retry();
        } else {
          message.error('删除失败');
        }
      } catch (err) {
        message.error('删除失败');
        console.log(err);
      }
    }
    return [
      {
        title: '通道组id',
        dataIndex: 'group_id',
        key: 'group_id',
        align: 'center',
      },
      {
        title: '通道组名称',
        dataIndex: 'name',
        key: 'name',
        align: 'center',
      },
      {
        title: '短信类型',
        dataIndex: 'sms_type',
        key: 'sms_type',
        align: 'center',
        render: (val: number) =>
          smsTypeValueMap.find((el) => el.value.toString() === val?.toString())?.text,
      },
      {
        title: '国家',
        // dataIndex: 'mcc',
        key: 'mcc',
        align: 'center',
        render: (row: any) => regionOptions.find((el) => el.value === row.country_code)?.label,
      },
      {
        title: '运营商',
        key: 'mnc',
        align: 'center',
        render: (row: any) => {
          const { operator_name, mnc } = _.find(
            mccMncInfo,
            (v) => v.country_code === row.country_code && v.mnc === row.mnc,
          ) ?? {
            operator_name: '',
            mnc: row.mnc,
          };
          row.operator_name = `${operator_name}(${mnc})`;
          return `${operator_name}(${mnc})`;
        },
      },
      // {
      //   title: '转化率(%)',
      //   dataIndex: 'cr',
      //   key: 'cr',
      //   align: 'center',
      // },
      // {
      //   title: '回执成功率(%)',
      //   dataIndex: 'dr',
      //   key: 'dr',
      //   align: 'center',
      // },
      {
        title: '资源类型',
        dataIndex: 'resource_type',
        key: 'resource_type',
        align: 'center',
        width: 100,
        render: (val: number) => resourceType?.find((el) => el.value === val)?.label ?? '-',
      },
      {
        title: '资源类别',
        dataIndex: 'category',
        key: 'category',
        align: 'center',
        width: 100,
        render: (val: string) => findLabel(groupCategory, val),
      },
      // {
      //   title: '是否',
      //   // dataIndex: 'is_sim_farm',
      //   key: 'is_sim_farm',
      //   align: 'center',
      //   render: (row: any) => {
      //     return (
      //       <>
      //         <Tag color={row.is_sim_farm ? 'green' : 'red'}>卡发</Tag>
      //         <Tag color={row.is_local_to_oversea ? 'green' : 'red'}>本地发国际</Tag>
      //         <Tag color={row.is_voice ? 'green' : 'red'}>语音</Tag>
      //         <Tag color={row.is_ott ? 'green' : 'red'}>OTT</Tag>
      //       </>
      //     );
      //   },
      // },
      {
        title: '价格',
        key: 'bind_num',
        align: 'center',
        render: (row: any) => {
          const newPrice =
            priceValue[row.group_id] !== undefined ? priceValue[row.group_id]?.price : row.price;
          const curr = priceValue[row.group_id]
            ? priceValue[row.group_id]?.price_curr
            : row.price_curr;
          const newPriceCurr = curr ? `(${curr})` : '';
          return (
            <div style={{ whiteSpace: 'nowrap' }}>
              <span>{`${Number(newPrice).toFixed(6)}${newPriceCurr}`}</span>
              <span style={{ marginLeft: 10 }}>
                {loadingRecord[row.group_id] ? (
                  <LoadingOutlined style={{ cursor: 'pointer' }} />
                ) : (
                  <Tooltip title="刷新">
                    <RedoOutlined
                      onClick={() => {
                        handlerCount(row.group_id);
                      }}
                      style={{
                        cursor: 'pointer',
                        color: '#006eff',
                      }}
                    />
                  </Tooltip>
                )}
              </span>
            </div>
          );
        },
      },
      {
        title: '绑定列表',
        align: 'center',
        render: (row: any) => (
          <>
            <Button
              type="link"
              onClick={() => {
                history.push('/channel/group/bind', {
                  ...row,
                  searchKeys,
                });
              }}
            >
              点击查看(共{row.bind_num}条)
            </Button>
          </>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        align: 'center',
      },
      {
        title: '操作',
        align: 'center',
        render: (row: any) =>
          window.location.host !== site.china && (
            <>
              <Button
                type="link"
                onClick={() => {
                  dialogRefBind.current.open({ initialValues: row, type: 1 });
                }}
              >
                绑定通道
              </Button>
              <Popconfirm
                title="确认删除此条数据吗？"
                onConfirm={() => handleDelete(row)}
                okText="Yes"
                cancelText="No"
              >
                <Button type="link">删除</Button>
              </Popconfirm>
              <Button
                type="link"
                onClick={() => {
                  dialogRef.current.open({
                    initialValues: {
                      ...row,
                      mnc: `${_.find(mccMncInfo, (v) => v.country_code === row.country_code)
                        ?.mcc}_${row.mnc}`,
                      sms_type: smsTypeValueMap
                        .find((el) => el.value === row.sms_type)
                        ?.text?.split('，')
                        .map((v) => smsType.find((s) => s.label === v)?.value),
                      country_code: `${row.country_code}_${row.mcc}`,
                    },
                  });
                }}
              >
                编辑
              </Button>
            </>
          ),
      },
    ];
  }, [
    dialogRef,
    dialogRefBind,
    loadingRecord,
    mccMncInfo,
    priceValue,
    regionOptions,
    resourceType,
    retry,
    searchKeys,
    setLoadingRecord,
    setPriceValue,
  ]);

  function onSubmit(vals: any) {
    setSearchKeys({
      ...vals,
      page_index: 1,
    });
  }

  return (
    <PageContainer>
      {window.location.host !== site.china && (
        <>
          <Button
            type="primary"
            style={{ marginBottom: 10 }}
            onClick={() => {
              dialogRef.current.open({ initialValues: null });
            }}
          >
            添加
          </Button>
          <Button
            type="primary"
            style={{ marginLeft: 10 }}
            onClick={() => {
              dialogRefBindMutipleCountry.current.open();
            }}
          >
            多国家配置
          </Button>
        </>
      )}
      <Form
        className="sender-search-form"
        form={form}
        initialValues={{ ...searchKeys }}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="group_id" label="通道组id">
          <Input placeholder="通道组id" />
        </Form.Item>
        <Form.Item name="name" label="通道组名称">
          <Input placeholder="通道组名称" />
        </Form.Item>
        <Form.Item name="sms_type" label="短信类型">
          <Select
            allowClear
            placeholder="短信类型"
            options={smsType}
            mode="multiple"
            style={{ minWidth: 100 }}
          />
        </Form.Item>
        <Form.Item name="country_code" label="国家/地区">
          <Select
            showSearch
            allowClear
            placeholder="国家/地区"
            options={regionOptions}
            onChange={(value) => {
              form.setFieldsValue({ mnc: undefined, country_code: value });
            }}
            filterOption={(input, option) => {
              return (
                option?.label.includes(input) ||
                option?.value.includes(input.toLocaleUpperCase()) ||
                false
              );
            }}
            style={{ width: 250 }}
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.country_code !== curValues.country_code
          }
        >
          {({ getFieldValue }) => {
            const countryCode = getFieldValue('country_code')?.split('_')[0];
            return (
              <Form.Item name="mnc" label="运营商" labelCol={{ span: 7 }}>
                <Select
                  style={{ width: 150 }}
                  placeholder="mnc"
                  options={getMncOptions(mccMncInfo, countryCode)}
                  allowClear
                  showSearch
                  filterOption={(inputValue, option) =>
                    !!option?.label.toUpperCase().includes(inputValue.toLowerCase())
                  }
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="resource_type" label="资源类型">
          <Select
            allowClear
            showSearch
            placeholder="请选择"
            options={resourceType}
            style={{ width: 150 }}
            filterOption={(inputValue, option) =>
              !!option?.label.toLowerCase().includes(inputValue.toLowerCase())
            }
          />
        </Form.Item>
        <Form.Item name="cr" label="cr">
          <Input style={{ width: 100 }} addonAfter="%" />
        </Form.Item>
        <Form.Item name="dr" label="dr">
          <Input style={{ width: 100 }} addonAfter="%" />
        </Form.Item>

        {/* <Form.Item name="is_sim_farm" label="是否卡发">
          <Select options={yesOrNo} allowClear style={{ width: 60 }} />
        </Form.Item>
        <Form.Item name="is_local_to_oversea" label="是否本地发国际">
          <Select options={yesOrNo} allowClear style={{ width: 60 }} />
        </Form.Item>
        <Form.Item name="is_voice" label="是否语音">
          <Select options={yesOrNo} allowClear style={{ width: 60 }} />
        </Form.Item>
        <Form.Item name="is_ott" label="是否OTT">
          <Select options={yesOrNo} allowClear style={{ width: 60 }} />
        </Form.Item> */}
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={list}
        rowKey={(record: any) => record.group_id}
        loading={loading}
        pagination={{
          defaultCurrent: searchKeys.page_index,
          pageSize: searchKeys.page_size,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
      />
      <ChannelGroupAdd dialogRef={dialogRef} reload={retry} resourceType={resourceType} />
      <AddChannelGroupBind dialogRef={dialogRefBind} reload={retry} />
      <AddChannelGroupBindMutipleCountry
        dialogRef={dialogRefBindMutipleCountry}
        reload={retry}
      ></AddChannelGroupBindMutipleCountry>
    </PageContainer>
  );
};

export default ChannelGroup;
