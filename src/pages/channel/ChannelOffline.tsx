import React, { useMemo } from 'react';
import { isMobile } from '@/const/jadgeUserAgent';
import {
  addOfflineForTestFalied,
  getChannleOfflineList,
  getPriceSubStatus,
  quickOfflineConfig,
  restoreOffline,
} from '@/services/channelOffline';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import {
  Button,
  Form,
  Input,
  InputNumber,
  Modal,
  Select,
  Table,
  Tag,
  Tooltip,
  Typography,
  message,
} from 'antd';
import { useSetState } from 'react-use';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { PageContainer } from '@ant-design/pro-layout';
import _ from 'lodash';
import { CheckOutlined, ClockCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { ChannelDispatchInfoDialog } from './component/ChannelDispatchInfoDialog';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { getMncOptions } from './component/utils';
import {
  ModalForm,
  ProFormCheckbox,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import ChannelSelect from './commonComponent/ChannelSelect';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const { Text } = Typography;

const offlineStatus = [
  { value: 0, label: '待处理' },
  { value: 1, label: '已下线' },
  { value: 2, label: '已恢复' },
  { value: 3, label: '待自动调度' },
  { value: 4, label: '研发驳回' },
];
const offlineType = [
  { value: 1, label: '单通道' },
  { value: 2, label: '通道组' },
  { value: 4, label: '通道策略' },
  { value: 8, label: '探测流量' },
  { value: 16, label: '号码加白配置' },
  { value: 32, label: '补发通道' },
];

const rtxType = [
  { value: 1, label: '研发发起' },
  { value: 0, label: '采购发起' },
];

const getItemByTargetNum = (totalValue: number) => {
  return offlineType.filter((item) => {
    return (totalValue & item.value) === item.value;
  });
};

const ChannelOffline = () => {
  const { regionOptions = [] } = useFetchCountryInfo();
  const [mccMncInfo] = useMccMncInfo();
  const [form] = Form.useForm();
  const [addForm] = Form.useForm();
  const dialogRef = useDialogRef();
  const [searchKeys, setSearchKeys] = useSetState<{
    page_index: number;
    page_size: number;
  }>({
    page_index: 1,
    page_size: 10,
  });
  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const result = await getChannleOfflineList({ ...searchKeys });
    return result?.data ?? {};
  }, [searchKeys]);

  const { value: subStatus } = useAsyncRetryFunc(async () => {
    const result = await getPriceSubStatus({});
    return (
      result?.data
        .filter((el: any) => el.status === 10)
        ?.map((el: any) => ({ label: el.sub_status_name, value: el.sub_status })) ?? {}
    );
  }, []);

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  async function handleQuickOffline({
    offline_id,
    offline_type,
  }: {
    offline_id: number;
    offline_type: number[];
  }) {
    const res = await quickOfflineConfig({ offline_id, offline_type: _.sum(offline_type) });
    if (res.code === 0) {
      message.success('操作成功');
      retry();
      return true;
    }
    return false;
  }

  async function createOffline(values: any) {
    const res: any = await addOfflineForTestFalied({
      ...values,
      mnc: values.assign_operator ? values.mnc.split('_')[1] : '000',
    });
    if (res.code === 0 && !res.data.errors.length) {
      message.success('操作成功');
      retry();
      return true;
    }
    return false;
  }

  async function _restoreOffline(values: any) {
    const res: any = await restoreOffline(values);
    if (res.code === 0) {
      message.success('操作成功');
      retry();
      return true;
    }
    return false;
  }

  function openHistoryDialog(apply_ext: any[]) {
    Modal.info({
      title: '操作历史',
      width: '50%',
      icon: false,
      closable: true,
      content: (
        <Table
          dataSource={apply_ext}
          pagination={false}
          columns={[
            {
              title: '操作人',
              dataIndex: 'rtx_name',
              key: 'rtx_name',
              align: 'center',
            },
            {
              title: '操作时间',
              dataIndex: 'time',
              key: 'time',
              align: 'center',
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              align: 'center',
              render: (status) => _.find(offlineStatus, (v) => v.value === status)?.label,
            },
            {
              title: '下线类型',
              dataIndex: 'schedule',
              key: 'schedule',
              align: 'center',
              render: (schedule) =>
                getItemByTargetNum(schedule)
                  .map((item: any) => item.label)
                  .join(','),
            },
            {
              title: '备注',
              dataIndex: 'msg',
              key: 'msg',
              align: 'center',
            },
          ]}
        />
      ),
    });
  }

  return (
    <PageContainer>
      <ModalForm
        title="发起停用下线"
        trigger={<Button type="primary">发起停用下线</Button>}
        form={addForm}
        labelCol={{ span: 7 }}
        width={500}
        layout="horizontal"
        modalProps={{
          destroyOnClose: true,
          maskClosable: false,
        }}
        onFinish={async (values) => {
          return await createOffline(values);
        }}
        initialValues={{
          assign_operator: 0,
        }}
      >
        <Form.Item label="供应商账号id" name="provider_id" rules={[{ required: true }]}>
          <ChannelSelect
            mode="default"
            type={0}
            onChange={(value) => {
              addForm.setFieldsValue({ provider_id: value });
            }}
            pageSize={1000}
            placeholder="请选择供应商账号"
            value={addForm.getFieldValue('provider_id')}
          />
        </Form.Item>
        <ProFormSelect
          name="country_code"
          options={regionOptions}
          showSearch
          label="国家/地区"
          rules={[{ required: true }]}
          onChange={(value) => {
            addForm.setFieldsValue({ mnc: undefined, country_code: value });
          }}
        ></ProFormSelect>
        <ProFormRadio.Group
          name="assign_operator"
          label="是否全网下线"
          options={[
            {
              label: '是',
              value: 0,
            },
            {
              label: '否',
              value: 1,
            },
          ]}
        />
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.country_code !== curValues.country_code ||
            prevValues.assign_operator !== curValues.assign_operator
          }
        >
          {({ getFieldValue }) => {
            const countryCode = getFieldValue('country_code');
            const assignOperator = getFieldValue('assign_operator');
            return assignOperator ? (
              <Form.Item name="mnc" label="运营商" rules={[{ required: true }]}>
                <Select
                  placeholder="mnc"
                  options={getMncOptions(mccMncInfo, countryCode)}
                  allowClear
                  showSearch
                  filterOption={(inputValue, option) =>
                    !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
                  }
                />
              </Form.Item>
            ) : null;
          }}
        </Form.Item>
        <ProFormSelect
          name="price_sub_status"
          options={subStatus}
          showSearch
          label="停用子状态"
          rules={[{ required: true }]}
        ></ProFormSelect>
        <ProFormText name="msg" label="备注"></ProFormText>
      </ModalForm>
      <Form
        form={form}
        className="sender-search-form"
        layout="inline"
        labelAlign="right"
        onFinish={(vals) =>
          setSearchKeys({
            ...vals,
            mnc: vals.mnc?.split('_')?.[1],
            mcc: vals.mnc?.split('_')?.[0],
          })
        }
        style={{ maxHeight: 500, marginTop: 20, overflow: 'auto' }}
      >
        <Form.Item name="for_price_id" label="关联报价id">
          <InputNumber controls={false} />
        </Form.Item>
        <Form.Item name="account_id" label="供应商账号id">
          <InputNumber controls={false} />
        </Form.Item>
        <Form.Item name="status" label="通道下线状态">
          <Select
            allowClear
            placeholder="短信类型"
            options={offlineStatus}
            style={{ width: 120 }}
          />
        </Form.Item>
        <Form.Item name="country_code" label="国家/地区">
          <Select
            showSearch
            allowClear
            placeholder="国家/地区"
            value={form.getFieldValue('country_code')}
            onChange={(value) => {
              form.setFieldsValue({ country_code: value, mnc: undefined });
            }}
            options={regionOptions}
            filterOption={(input, option) => {
              return (
                option?.label.includes(input) ||
                option?.value.includes(input.toLocaleUpperCase()) ||
                false
              );
            }}
            style={{ width: 250 }}
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.country_code !== curValues.country_code
          }
        >
          {({ getFieldValue }) => {
            const countryCode = getFieldValue('country_code');
            return (
              <Form.Item name="mnc" label="运营商">
                <Select
                  style={{ width: 150 }}
                  placeholder="mnc"
                  options={getMncOptions(mccMncInfo, countryCode)}
                  allowClear
                  showSearch
                  filterOption={(inputValue, option) =>
                    !!option?.label.toUpperCase().includes(inputValue.toUpperCase())
                  }
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item name="buyer_rtx" label="提单人（采购）">
          <Input style={{ width: 100 }} />
        </Form.Item>
        <Form.Item name="dev_rtx" label="处理人（研发）">
          <Input style={{ width: 100 }} />
        </Form.Item>
        <Form.Item name="type" label="下线单类型">
          <Select options={rtxType} allowClear style={{ width: 100 }}></Select>
        </Form.Item>
        <Form.Item>
          <Button htmlType="submit" type="primary" loading={loading} icon={<SearchOutlined />}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        size="middle"
        dataSource={list}
        rowKey="offline_id"
        columns={[
          {
            title: '下线单id',
            dataIndex: 'offline_id',
            key: 'offline_id',
            align: 'center',
          },
          {
            title: '关联报价id',
            dataIndex: 'for_price_id',
            key: 'for_price_id',
            align: 'center',
          },
          {
            title: '供应商账号id',
            dataIndex: 'account_id',
            key: 'account_id',
            align: 'center',
          },
          {
            title: '国家码',
            dataIndex: 'country_code',
            key: 'country_code',
            align: 'center',
            render: (val: string) => regionOptions.find((el) => el.value === val)?.label,
          },
          {
            title: '是否指定运营商',
            dataIndex: 'assign_operator',
            key: 'assign_operator',
            align: 'center',
            render: (val: string) => (val ? '是' : '否'),
          },
          {
            title: '运营商',
            key: 'mnc',
            align: 'center',
            render: (row: any) => {
              const { operator_name, mcc, mnc } = _.find(
                mccMncInfo,
                (v) => v.mcc === row.mcc && v.mnc === row.mnc,
              ) ?? {
                operator_name: '',
                mcc: row.mcc,
                mnc: row.mnc,
              };
              return `${operator_name}(${mcc}_${mnc})`;
            },
          },
          {
            title: '通道下线状态',
            key: 'status',
            align: 'center',
            render: (row) => {
              const scheduleVal = getItemByTargetNum(row.schedule).map((o: any) => o.value);
              const text = _.find(offlineStatus, (v) => v.value === row.status)?.label;
              const tips = _.map(offlineType, (o: any) => (
                <p>
                  <Tag color={scheduleVal?.includes(o.value) ? 'green' : 'orange'}>
                    {scheduleVal?.includes(o.value) ? <CheckOutlined /> : <ClockCircleOutlined />}
                    {o.label}
                  </Tag>
                </p>
              ));
              return row.status === 0 ? (
                <Tooltip title={tips} placement="right" color="#fff">
                  <Text>{text}</Text>
                  <ClockCircleOutlined style={{ marginLeft: 5, color: 'orange' }} />
                </Tooltip>
              ) : (
                <Text>{text}</Text>
              );
            },
          },
          {
            title: '提单人（采购）',
            dataIndex: 'buyer_rtx',
            key: 'buyer_rtx',
            align: 'center',
          },
          {
            title: '处理人（研发）',
            dataIndex: 'dev_rtx',
            key: 'dev_rtx',
            align: 'center',
          },
          {
            title: '下线单类型',
            dataIndex: 'type',
            key: 'type',
            align: 'center',
            render: (type) => _.find(rtxType, (v) => v.value === type)?.label,
          },
          {
            title: '创建时间',
            dataIndex: 'created_at',
            key: 'created_at',
            align: 'center',
          },
          {
            title: '更新时间',
            dataIndex: 'updated_at',
            key: 'updated_at',
            align: 'center',
          },
          {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
          },
          {
            title: '操作历史',
            dataIndex: 'ext',
            key: 'ext',
            align: 'center',
            render: (ext) => {
              const _ext = JSON.parse(ext || '{}');
              const apply_ext = _ext.apply_ext || [];
              return (
                <Button type="link" onClick={() => openHistoryDialog(apply_ext)}>
                  操作历史
                </Button>
              );
            },
          },
          {
            title: '操作',
            align: 'center',
            width: 200,
            render: (row: any) => {
              const defaultScheduleVal = offlineType.map((e) => e.value);
              const scheduleVal = getItemByTargetNum(row.schedule).map((e) => e.value);
              const _offlineType = offlineType.map((o) => {
                if (scheduleVal?.includes(o.value)) {
                  return {
                    ...o,
                    disabled: true,
                  };
                }
                return o;
              });
              return (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      dialogRef.current.open({
                        country_code: row.country_code,
                        channel_id: row.account_id,
                        mnc: row.assign_operator ? row.mnc : '000',
                        mcc: row.mcc,
                        assign_operator: row.assign_operator,
                        type: 0,
                      });
                    }}
                  >
                    查看调度配置
                  </Button>
                  {row.status === 0 && (
                    <ModalForm
                      title="一键下线"
                      trigger={<Button type="link">一键下线</Button>}
                      width={400}
                      layout="horizontal"
                      modalProps={{
                        destroyOnClose: true,
                        maskClosable: false,
                      }}
                      onFinish={async (values) => {
                        return await handleQuickOffline({
                          offline_type: values.offline_type,
                          offline_id: row.offline_id,
                        });
                      }}
                      initialValues={{
                        offline_type: defaultScheduleVal,
                      }}
                    >
                      <Form.Item label="下线单id">{row.offline_id}</Form.Item>
                      <ProFormCheckbox.Group
                        name="offline_type"
                        options={_offlineType}
                        label="下线类型"
                        rules={[{ required: true }]}
                      ></ProFormCheckbox.Group>
                    </ModalForm>
                  )}
                  {[0, 1].includes(row.status) && row.type === 1 && (
                    <ModalForm
                      title="恢复"
                      trigger={<Button type="link">恢复</Button>}
                      width={400}
                      layout="horizontal"
                      modalProps={{
                        destroyOnClose: true,
                        maskClosable: false,
                      }}
                      onFinish={async (values) => {
                        return await _restoreOffline({
                          msg: values.msg,
                          offline_id: row.offline_id,
                        });
                      }}
                    >
                      <Form.Item label="下线单id">{row.offline_id}</Form.Item>
                      <ProFormText name="msg" label="备注"></ProFormText>
                    </ModalForm>
                  )}
                </>
              );
            },
          },
        ]}
        loading={loading}
        pagination={{
          defaultCurrent: 1,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
        style={{ marginTop: 20 }}
      />
      <ChannelDispatchInfoDialog dialogRef={dialogRef} />
    </PageContainer>
  );
};
export default ChannelOffline;
