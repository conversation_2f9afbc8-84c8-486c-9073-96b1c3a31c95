import React, { useState } from 'react';
import { Table, Space, message, Button, Select, Input, InputNumber } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import { SearchOutlined } from '@ant-design/icons';
import { querySendFreqList, setAppFreqLimit, setCountryLimit } from '@/services/api';
import _ from 'lodash';

const SendSettingList = () => {
  const [list, setList] = useState<any>([]);
  const [countryList, setCountryList] = useState<any>([]);
  const [count, setCount] = useState<number>(0);
  const [searchId, setSearchId] = useState('');
  const [isLoading, setLoading] = useState<boolean>(false);

  const columns: any = [
    {
      title: '应用国内日下发限制数',
      align: 'center',
      render: (row: any) => {
        return (
          <InputNumber
            style={{ width: '30%' }}
            defaultValue={row.app_china_daily_max_limit}
            onChange={(value) => {
              row.app_china_daily_max_limit = value;
            }}
          />
        );
      },
    },
    {
      title: '应用海外日下发限制数',
      align: 'center',
      render: (row: any) => {
        return (
          <InputNumber
            style={{ width: '30%' }}
            defaultValue={row.app_abroad_daily_max_limit}
            onChange={(value) => {
              row.app_abroad_daily_max_limit = value;
            }}
          />
        );
      },
    },
    {
      title: '应用开启地区限制白名单',
      align: 'center',
      render: (row: any) => {
        return (
          <Select
            defaultValue={row.enable_appcountry_freq_white}
            onChange={(value) => (row.enable_appcountry_freq_white = value)}
            style={{ width: 100 }}
          >
            <Select.Option value={0}>{'未开启'}</Select.Option>
            <Select.Option value={1}>{'开启'}</Select.Option>
          </Select>
        );
      },
    },
    {
      title: '操作',
      render: (row: any) => {
        return (
          <Space>
            <a
              onClick={() => {
                doEdit('app', row);
              }}
            >
              保存
            </a>
          </Space>
        );
      },
      align: 'center',
    },
  ];

  const expandedRowRender = () => {
    const cols = [
      { title: '序号', dataIndex: 'country_freq_limit_id', key: 'country_freq_limit_id' },
      { title: '地区/国家码', dataIndex: 'isocode', key: 'isocode' },
      {
        title: '地区/国家名',
        dataIndex: 'country_name',
        key: 'country_name',
      },
      {
        title: '日下发限制数',
        render: (row: any) => {
          return (
            <InputNumber
              style={{ width: '30%' }}
              defaultValue={row.daily_max_limit}
              onChange={(value) => {
                row.daily_max_limit = value;
              }}
            />
          );
        },
      },
      {
        title: '操作',
        render: (row: any) => (
          <Space size="middle">
            <a
              onClick={() => {
                doEdit('country', row);
              }}
            >
              保存
            </a>
          </Space>
        ),
      },
    ];
    return (
      <Table
        rowKey="country_freq_limit_id"
        columns={cols}
        dataSource={countryList}
        pagination={
          count > 10
            ? {
                defaultCurrent: 1,
                total: count,
                showSizeChanger: true,
              }
            : false
        }
      />
    );
  };

  async function getList() {
    setLoading(true);
    const info = [];
    const res = await querySendFreqList({ sdkappid: searchId });
    if (Object.keys(res?.data).length === 0) {
      setList([]);
      setLoading(false);
      return;
    }
    res?.data && info.push(res?.data);
    setList(info || []);
    setCountryList(res?.data?.country_freq_limit || []);
    setCount(res?.data?.country_freq_limit?.length || 0);
    setLoading(false);
  }

  async function doEdit(type: string, row: any) {
    const isValid = _.every(row, (val) => {
      return val !== null;
    });
    if (!isValid) {
      message.error('限制数不能为空');
      return;
    }
    let res;
    setLoading(true);
    if (type === 'app') {
      const {
        sdkappid,
        app_china_daily_max_limit,
        app_abroad_daily_max_limit,
        enable_appcountry_freq_white,
      } = row;
      res = await setAppFreqLimit({
        sdkappid,
        app_china_daily_max_limit,
        app_abroad_daily_max_limit,
        enable_appcountry_freq_white,
      });
    } else if (type === 'country') {
      const { sdkappid, country_freq_limit_id, daily_max_limit } = row;
      res = await setCountryLimit({ sdkappid, country_freq_limit_id, daily_max_limit });
    }
    setLoading(false);
    res?.code === 0 && getList();
  }

  return (
    <>
      <PageContainer title="发送配置">
        <Input
          placeholder="请输入应用id"
          style={{ width: 250, marginRight: 10 }}
          onChange={(e) => {
            setSearchId(e.target.value.trim());
          }}
        />
        <Button
          icon={<SearchOutlined />}
          type="primary"
          onClick={() => {
            if (searchId === '') {
              setList([]);
              return;
            }
            getList();
          }}
        >
          查询
        </Button>
        <Table
          rowKey="sdkappid"
          dataSource={list}
          columns={columns}
          loading={isLoading}
          expandable={{
            expandedRowRender,
            rowExpandable: (record) => record?.enable_appcountry_freq_white === 1,
          }}
          style={{ marginTop: 20 }}
          pagination={false}
        />
      </PageContainer>
    </>
  );
};

export default SendSettingList;
