import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { SearchOutlined } from '@ant-design/icons';
import { Table, Space, message, Button, Select, Input, InputNumber, Modal, Form } from 'antd';
import { getMaxNumber, setMaxNumber } from '@/services/api';

const LimitSetting = () => {
  const [form] = Form.useForm();
  const [searchId, setSearchId] = useState('');
  const [list, setList] = useState<any>([]);
  const [type, setType] = useState<string>(''); // 1客户分组上限：mobile_group_number, 2短信模版上限：template_number, 3 短链创建次数能力上限：surl_number
  const [isLoading, setLoading] = useState<boolean>(false);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [initForm, setInitForm] = useState<any>({});
  const defaultValMap: Record<string, any> = {
    mobile_group_number: 10,
    template_number: 200,
    surl_number: 10,
    sign_number: 500,
    china_sign_len: 12,
    oversea_sign_len: 15,
    max_send_process: 15,
    freq_limit_set_times: 10,
    max_receiver_number: 5,
    frq_white_number: 300,
  };

  const columns: any = [
    {
      title: 'qappid',
      dataIndex: 'qappid',
      key: 'qappid',
      align: 'center',
    },
    {
      title: '上限数',
      align: 'center',
      dataIndex: 'max_number',
      key: 'max_number',
    },
    {
      title: '操作',
      render: (row: any) => {
        return (
          <Space>
            <a
              onClick={() => {
                setInitForm({ ...row });
                setIsVisible(true);
              }}
            >
              编辑
            </a>
          </Space>
        );
      },
      align: 'center',
    },
  ];

  useEffect(() => {
    form.setFieldsValue({ ...initForm });
  }, [initForm]);

  async function doEdit(row: any) {
    if (row.max_number === null) {
      message.error('上限数不能为空');
      return;
    }
    setIsVisible(false);
    form.resetFields();
    setLoading(true);
    const { qappid, max_number, value_type } = row;
    const res = await setMaxNumber({ value_type, qappid, max_number });
    setLoading(false);
    if (res?.code === 0) {
      message.success('操作成功');
      getList();
    }
  }

  async function getList() {
    setLoading(true);
    const info = [];
    const defaultVal = defaultValMap[type];
    const res = await getMaxNumber({ value_type: type, qappid: searchId });
    if (Object.keys(res?.data).length === 0) {
      res?.code === 0
        ? setList([{ qappid: searchId, max_number: defaultVal, value_type: type }])
        : setList([]);
      setLoading(false);
      return;
    }
    res?.data && info.push(res?.data);
    setList(info || []);
    setLoading(false);
  }

  return (
    <>
      <PageContainer title="上限配置">
        <Input
          placeholder="请输入qappid"
          style={{ width: 250, marginRight: 10 }}
          onChange={(e) => {
            setSearchId(e.target.value.trim());
          }}
        />
        <Select
          placeholder="请选择类型"
          style={{ width: 250, marginRight: 10 }}
          onChange={(val: string) => {
            setType(val);
          }}
        >
          <Select.Option value={'mobile_group_number'}>客户分组上限</Select.Option>
          <Select.Option value={'template_number'}>短信模版上限</Select.Option>
          <Select.Option value={'surl_number'}>短链每日创建次数能力</Select.Option>
          <Select.Option value={'sign_number'}>短信签名数量上限</Select.Option>
          <Select.Option value={'china_sign_len'}>国内短信签名长度上限</Select.Option>
          <Select.Option value={'oversea_sign_len'}>国际短信签名长度上限</Select.Option>
          <Select.Option value={'frq_white_number'}>频率限制白名单数量上限</Select.Option>
          <Select.Option value={'max_receiver_number'}>告警联系人数量上限</Select.Option>
          <Select.Option value={'freq_limit_set_times'}>限额超量提醒设置最大次数</Select.Option>
          <Select.Option value={'max_send_process'}>群发最大进程数量</Select.Option>
        </Select>
        <Button
          icon={<SearchOutlined />}
          type="primary"
          onClick={() => {
            if (searchId === '' || !type) {
              setList([]);
              return;
            }
            getList();
          }}
        >
          查询
        </Button>
        <Table
          rowKey="qappid"
          dataSource={list}
          columns={columns}
          loading={isLoading}
          pagination={false}
          style={{ marginTop: 20 }}
        />
      </PageContainer>
      <Modal
        title="编辑上限配置"
        open={isVisible}
        destroyOnClose
        onOk={() => form.submit()}
        onCancel={() => {
          form.resetFields();
          setIsVisible(false);
        }}
      >
        <Form
          form={form}
          labelCol={{ span: 4 }}
          labelAlign="right"
          initialValues={{ ...initForm }}
          onFinish={(vals) => doEdit(vals)}
        >
          <Form.Item name="qappid" label="qappid" rules={[{ required: true }]}>
            <Input style={{ width: 150 }} disabled />
          </Form.Item>
          <Form.Item name="max_number" label="上限数" rules={[{ required: true }]}>
            <InputNumber style={{ width: '35%' }} />
          </Form.Item>
          <Form.Item name="value_type" hidden />
        </Form>
      </Modal>
    </>
  );
};

export default LimitSetting;
