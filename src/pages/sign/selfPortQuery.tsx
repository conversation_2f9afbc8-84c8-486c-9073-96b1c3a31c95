import React, { useMemo } from 'react';
import { Button, Form, Input, Select, Table } from 'antd';
import { useCss, useSetState } from 'react-use';
import { PageContainer } from '@ant-design/pro-layout';
import _ from 'lodash';
import { SearchOutlined } from '@ant-design/icons';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { querySelfPort } from '@/services/querySelfPort';

const types = [
  { value: 'uin' },
  { value: 'sdkappid' },
  { value: 'qappid' },
  { value: 'port', label: '端口' },
  { value: 'sign', label: '签名' },
];

const columns: any[] = [
  {
    title: 'uin',
    dataIndex: 'uin',
    key: 'uin',
    align: 'center',
  },
  {
    title: 'appid',
    dataIndex: 'qappid',
    key: 'qappid',
    align: 'center',
  },
  {
    title: '签名',
    dataIndex: 'sign',
    key: 'sign',
    align: 'center',
  },
  {
    title: '调度ID',
    dataIndex: 'provider_id',
    key: 'provider_id',
    align: 'center',
  },
  {
    title: '通道名称',
    dataIndex: 'provider',
    key: 'provider',
    align: 'center',
  },
  {
    title: '签名子码',
    dataIndex: 'subcode',
    key: 'subcode',
    align: 'center',
  },
  {
    title: '完整端口',
    dataIndex: 'port',
    key: 'port',
    align: 'center',
  },
];

const SelfPortQuery = () => {
  const [form] = Form.useForm();

  const [searchKeys, setSearchKeys] = useSetState<{
    type: string;
    value: string;
  }>({
    type: 'uin',
    value: '',
  });

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    if (!searchKeys.value) return;
    const res = await querySelfPort({
      ..._.pickBy(searchKeys, (v) => !!v),
    });
    return res;
  }, [searchKeys]);

  const list = useMemo(() => {
    return state?.data ?? [];
  }, [state]);

  const typeClass = useCss({
    'div.type_margin_0': {
      marginRight: 0,
    },
  });

  return (
    <PageContainer>
      <div>
        <Form
          form={form}
          layout="inline"
          labelAlign="right"
          onFinish={(vals) => setSearchKeys({ ...vals })}
          initialValues={{ type: 'uin' }}
          className={typeClass}
        >
          <Form.Item name="type" rules={[{ required: true }]} className="type_margin_0">
            <Select
              options={types}
              onChange={() => form.setFieldsValue({ value: undefined })}
              style={{ width: 120 }}
            />
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, curValues) => prevValues.type !== curValues.type}
          >
            <Form.Item label="" name="value" rules={[{ required: true }]}>
              <Input style={{ width: 160 }} placeholder="请输入" />
            </Form.Item>
          </Form.Item>
          <Form.Item>
            <Button htmlType="submit" type="primary" icon={<SearchOutlined />}>
              查询
            </Button>
          </Form.Item>
        </Form>
      </div>
      <div style={{ marginTop: 15 }}>
        <Table
          columns={columns}
          dataSource={list}
          rowKey={(record: any) => record.id}
          loading={loading}
          pagination={false}
        />
      </div>
    </PageContainer>
  );
};
export default SelfPortQuery;
