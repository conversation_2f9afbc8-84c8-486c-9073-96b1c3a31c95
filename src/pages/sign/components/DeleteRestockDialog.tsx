import React, { useEffect, useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, Input, InputNumber, Select } from 'antd';
import { operateSignUnsub } from '@/services/scdAPI';
import _ from 'lodash';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export const DeleteRestockDialog = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [form] = Form.useForm();
  const [isConfirmLoading, setConfirmLoading] = useState(false);
  const [visible, setShowState, initVals] = useDialog(dialogRef);

  const handlerSubmit = async (vals: any) => {
    setConfirmLoading(true);
    try {
      const res = await operateSignUnsub({
        ...vals,
        tels: _.compact(vals.tels.split('\n')),
        operate: 'del',
      });
      setConfirmLoading(false);
      setShowState(false);
      if (res?.code === 0) {
        onSuccess();
      }
    } catch (error) {
      setConfirmLoading(false);
    }
  };

  useEffect(() => {
    visible && form.setFieldsValue(initVals);
    !visible && form.setFieldsValue({});
  }, [form, initVals, visible]);

  return (
    <>
      <Modal
        open={visible}
        title="删除"
        destroyOnClose
        confirmLoading={isConfirmLoading}
        onOk={() => form.submit()}
        onCancel={() => {
          setShowState(false);
        }}
      >
        <Form
          form={form}
          labelAlign="right"
          onFinish={(vals) => handlerSubmit(vals)}
          initialValues={{ ...initVals }}
        >
          <Form.Item
            name="smstype"
            label="短信类型"
            rules={[{ required: true, message: '请选择' }]}
          >
            <Select style={{ width: 200 }}>
              <Select.Option value={0}>普通</Select.Option>
              <Select.Option value={1}>营销</Select.Option>
              <Select.Option value={2}>全局</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="tels"
            rules={[{ required: true, message: '请输入手机号码' }]}
            label="手机号"
          >
            <Input.TextArea style={{ width: 200 }} rows={3} />
          </Form.Item>
          <Form.Item name="sign" label="签名">
            <Input style={{ width: 180 }} />
          </Form.Item>
          <Form.Item name="appid" label="appid">
            <InputNumber style={{ width: 180 }} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
