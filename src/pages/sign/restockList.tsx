import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Input, Select, Form, Modal, message, List, InputNumber, Space } from 'antd';
import { operateSignUnsub, querySignUnsub } from '@/services/scdAPI';
import { DeleteRestockDialog } from './components/DeleteRestockDialog';
import { useDialogRef } from '@/utils/react-use/useDialog';

interface ItemProps {
  msg: string;
  data?: {
    tel?: string;
    smstype: number;
    sign?: string;
  };
  appid?: string | number;
}

const RestockList = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const [visible, setVisible] = useState(false);
  const [searchKey, setSearchKey] = useState({ tels: '', sign: '' });
  const dialogRef = useDialogRef();
  const [initForm] = useState<any>({
    operate: 'query',
    reason: '2',
    smstype: '2',
    fordden_del: '0',
  });

  async function addUnsub(vals: any) {
    vals.tels = vals?.tels?.split('\n');
    const res = await operateSignUnsub({ ...vals, operate: 'add' });
    setVisible(false);
    if (res?.code === 0) {
      message.success('操作成功');
      form.resetFields();
    }
  }

  function getDescItem(item: ItemProps) {
    return (
      <>
        <List.Item>
          {item.msg}
          {item?.data ? (
            <Button
              type="link"
              onClick={() =>
                dialogRef.current.open({
                  appid: item?.appid,
                  tels: item?.data?.tel,
                  sign: item.data?.sign,
                  smstype: item.data?.smstype,
                })
              }
            >
              删除
            </Button>
          ) : null}
        </List.Item>
        <br />
      </>
    );
  }

  async function getList(vals: any) {
    const params = {
      ...vals,
      tels: vals.tels.split('\n'),
    };
    try {
      setLoading(true);
      const res = await querySignUnsub({ ...params });
      setList(res?.data || []);
      setLoading(false);
    } catch (error) {
      setList([]);
      setLoading(false);
    }
  }

  return (
    <>
      <PageContainer>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>
            <Form
              labelAlign="right"
              onFinish={(vals) => {
                setSearchKey(vals);
                getList(vals);
              }}
              layout="inline"
              requiredMark={false}
            >
              <Form.Item
                name="tels"
                rules={[{ required: true, message: '请输入手机号码' }]}
                label="手机号"
              >
                <Input.TextArea placeholder="换行输入多个" autoSize />
              </Form.Item>
              <Form.Item name="sign" label="签名">
                <Input style={{ width: 180 }} />
              </Form.Item>
              <Form.Item name="appid" label="appid">
                <InputNumber style={{ width: 180 }} />
              </Form.Item>
              <Form.Item>
                <Button htmlType="submit" type="primary" loading={loading}>
                  查询
                </Button>
              </Form.Item>
            </Form>
          </div>
          <Space>
            <Button type="primary" onClick={() => setVisible(true)}>
              添加
            </Button>
            <Button
              type="primary"
              onClick={() => {
                dialogRef.current.open({
                  appid: '',
                  tels: '',
                  sign: '',
                  smstype: '',
                });
              }}
            >
              删除
            </Button>
          </Space>
        </div>
        <div style={{ marginTop: 20 }}>
          <List
            header="查询结果"
            bordered
            dataSource={list}
            renderItem={(item) => getDescItem(item)}
          />
        </div>

        <Modal
          open={visible}
          title="添加"
          destroyOnClose
          onOk={() => form.submit()}
          onCancel={() => {
            setVisible(false);
            form.resetFields();
          }}
        >
          <Form
            form={form}
            labelAlign="right"
            onFinish={(vals) => addUnsub(vals)}
            initialValues={{ ...initForm }}
          >
            {/* <Form.Item name="operate" label="操作">
            <Select style={{ width: 200 }}>
              <Select.Option value="add">添加</Select.Option>
              <Select.Option value="del">删除</Select.Option>
              <Select.Option value="query">查询</Select.Option>
            </Select>
          </Form.Item> */}
            <Form.Item name="reason" label="原因">
              <Select style={{ width: 200 }}>
                <Select.Option value="0">未拉黑</Select.Option>
                <Select.Option value="1">回t退定</Select.Option>
                <Select.Option value="2">用户投诉</Select.Option>
                <Select.Option value="3">运营商投诉</Select.Option>
                <Select.Option value="4">运营商提供的网管黑名单永久拉黑</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item name="smstype" label="短信类型">
              <Select style={{ width: 200 }}>
                <Select.Option value="0">普通</Select.Option>
                <Select.Option value="1">营销</Select.Option>
                <Select.Option value="2">全局</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="fordden_del"
              label="删除状态"
              help="禁止客户在控制台上删除，内部配置页面仍然可以删除"
            >
              <Select style={{ width: 200 }}>
                <Select.Option value="0">允许删除</Select.Option>
                <Select.Option value="1">禁止删除</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="tels"
              rules={[{ required: true, message: '请输入手机号码' }]}
              label="手机号"
              help="一行一个手机号码,最多一次200个，国内的忽略86直接输入手机号码,海外短信国家码和手机号放一起前面带+号"
            >
              <Input.TextArea style={{ width: 200 }} rows={3} />
            </Form.Item>
            <Form.Item name="sign" label="签名">
              <Input style={{ width: 180 }} />
            </Form.Item>
            <Form.Item name="appid" label="appid">
              <InputNumber style={{ width: 180 }} />
            </Form.Item>
          </Form>
        </Modal>
        <DeleteRestockDialog
          dialogRef={dialogRef}
          onSuccess={() => {
            getList(searchKey);
          }}
        />
      </PageContainer>
    </>
  );
};

export default RestockList;
