export const priorityStatus = [
  {
    text: '0级(低)',
    value: 0,
  },
  {
    text: '1级(中)',
    value: 1,
  },
  {
    text: '2级(高)',
    value: 2,
  },
];

export const cardType = [
  { value: 1, label: '身份证' },
  { value: 2, label: '护照' },
];

export const smsTemplateType = [
  { value: 0, label: '行业' },
  { value: 1, label: '营销' },
];

// 签名巡检状态
export const signInspectStatusOptions = [
  {
    value: 0,
    label: '未巡检',
    disabled: true,
  },
  {
    value: 1,
    label: '正常',
    disabled: true,
  },
  {
    value: 3,
    label: '即将过期',
    disabled: true,
  },
  {
    value: 2,
    label: '已失效',
    disabled: true,
  },
  {
    value: 4,
    label: '授权异常-待补充授权',
    disabled: true,
  },
  {
    value: 5,
    label: '授权异常-更新被授权方',
    disabled: true,
  },
  {
    value: 6,
    label: '待更新资质',
  },
];

// 签名类型
export const signRemarkTypeOptions = [
  {
    value: '公司名',
    label: '公司名',
  },
  {
    value: '网站',
    label: '网站',
  },
  { value: 'APP', label: 'APP' },
  { value: '公众号', label: '公众号' },
  {
    value: '小程序',
    label: '小程序',
  },
  { value: '商标', label: '商标' },
  { value: '政府/机关事业单位/其他机构', label: '政府/机关事业单位/其他机构' },
];
