import React from 'react';
import { getSignForce, insertSignForce, delSignForce } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const ForceDispatch = () => {
  const columns: any = [
    {
      title: '签名',
      dataIndex: 'sign',
      key: 'sign',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'sign',
      label: '签名',
      disabled: false,
      isRequired: true,
    },
  ];

  async function doAdd(vals: any) {
    return await insertSignForce({ ...vals });
  }
  async function doDel(vals: any) {
    return await delSignForce({ sign: vals.sign });
  }
  async function getList(vals?: any) {
    return await getSignForce({ ...vals });
  }

  return (
    <PatternTable
      rowKey={'sign'}
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'search_key',
          placeholder: '签名',
        },
      ]}
      operateForm={operateForm}
      operType={2}
      initialValues={{ type: '0' }}
    />
  );
};

export default ForceDispatch;
