import React, { useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Input, Form, Button, Transfer, message, Flex } from 'antd';
import _ from 'lodash';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getDirectGroup, getGloablProviders } from '@/services/api';
import { useSetState } from 'react-use';
import { editSdkappidProviderReport } from '@/services/sdkappidProviderReport';
const SdkappidProviderReport = () => {
  const [configKeys, setConfigKeys] = useSetState<{ [K in string]: string[] }>({
    provider_ids: [],
    group_ids: [],
    sdkappids: [],
  });
  const { value: providerList } = useAsyncRetryFunc(async () => {
    const res = await getGloablProviders();
    return (res?.data ?? []).map((v: { provider_id: number }) => ({
      ...v,
      key: `${v.provider_id}`,
    }));
  }, []);

  const { value: groupList } = useAsyncRetryFunc(async () => {
    const res = await getDirectGroup({
      page_size: 10000,
      page_index: 1,
    });
    return (res?.data?.list ?? []).map((v: { group_id: number }) => ({
      ...v,
      key: `${v.group_id}`,
    }));
  }, []);

  const handlerSubmit = async () => {
    const res = await editSdkappidProviderReport({
      ..._.mapValues(configKeys, (values) => {
        return values.map(Number); // 将每个值转换为数字
      }),
    });
    if (res.code === 0) {
      message.success('配置成功');
    }
  };

  const onChange = (provider_ids: any) => {
    setConfigKeys((pre) => ({ ...pre, ...provider_ids }));
  };

  return (
    <PageContainer>
      <div style={{ width: 'fit-content' }}>
        <Flex gap="middle" vertical={false} style={{ width: 'fit-content' }}>
          <div>
            <Form layout="inline" labelAlign="right" style={{ marginBottom: 15 }}>
              <Form.Item style={{ marginBottom: 5 }} label="sdkappid" required name="sdkappid">
                <Input.TextArea
                  placeholder="换行输入多个"
                  rows={6}
                  cols={30}
                  onChange={(e) =>
                    setConfigKeys({
                      sdkappids: _.uniq(_.compact((e.target.value ?? '').split('\n'))),
                    })
                  }
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 5 }} label="provider_ids">
                <Input.TextArea
                  cols={30}
                  placeholder="换行输入搜索多个provider_id"
                  rows={6}
                  onChange={(e) => {
                    const value = _.uniq(_.compact((e.target.value ?? '').split('\n')));
                    const provider_ids = _.filter(value, (v) => {
                      return !!_.find(providerList, (item) => item.key === v);
                    });
                    setConfigKeys((pre) => ({ ...pre, provider_ids }));
                  }}
                />
              </Form.Item>
            </Form>
            <Transfer
              filterOption={(inputValue: string, option: { provider_name: string }) => {
                return option.provider_name.indexOf(inputValue) > -1;
              }}
              showSearch
              dataSource={providerList}
              titles={[
                '全部通道',
                <>
                  <span
                    style={{ color: '#ff4d4f', marginRight: 4, fontFamily: 'SimSun, sans-serif' }}
                  >
                    *
                  </span>
                  已选通道
                </>,
              ]}
              targetKeys={configKeys.provider_ids}
              onChange={(val) => onChange({ provider_ids: val })}
              listStyle={{ height: 420, width: 360 }}
              render={(item) => item.provider_name}
              pagination
            />
          </div>
          <div>
            <Form layout="inline" labelAlign="right" style={{ marginBottom: 15 }}>
              <Form.Item style={{ marginBottom: 5 }} label="签名子码通道组">
                <Input.TextArea
                  cols={81}
                  placeholder="换行输入搜索多个签名子码通道组"
                  rows={6}
                  onChange={(e) => {
                    const value = _.uniq(_.compact((e.target.value ?? '').split('\n')));
                    const group_ids = _.filter(value, (v) => {
                      return !!_.find(groupList, (item) => item.key == v);
                    });
                    setConfigKeys((pre) => ({ ...pre, group_ids }));
                  }}
                />
              </Form.Item>
            </Form>
            <Transfer
              filterOption={(inputValue: string, option: { desc: string }) => {
                return option.desc.indexOf(inputValue) > -1;
              }}
              showSearch
              dataSource={groupList}
              titles={[
                '全部签名子码通道组',
                <>
                  <span
                    style={{ color: '#ff4d4f', marginRight: 4, fontFamily: 'SimSun, sans-serif' }}
                  >
                    *
                  </span>
                  已选签名子码通道组
                </>,
              ]}
              targetKeys={configKeys.group_ids}
              onChange={(val) => onChange({ group_ids: val })}
              listStyle={{ height: 420, width: 360 }}
              render={(item) => item.desc}
              pagination
            />
          </div>
        </Flex>
        <Flex justify="center">
          <Button
            type="primary"
            onClick={handlerSubmit}
            style={{ marginTop: 15 }}
            disabled={
              !configKeys.sdkappids.length ||
              (!configKeys.provider_ids.length && !configKeys.group_ids.length)
            }
          >
            提交进行迁移报备
          </Button>
        </Flex>
      </div>
    </PageContainer>
  );
};
export default SdkappidProviderReport;
