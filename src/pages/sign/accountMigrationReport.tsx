import React, { useMemo } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Input, Form, Button, Transfer, message, Flex } from 'antd';
import _ from 'lodash';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getGloablProviders } from '@/services/api';
import { useSetState } from 'react-use';
import { editAccountMigrationReport } from '@/services/accountMigrationReport';
const AccountMigrationReport = () => {
  const [configKeys, setConfigKeys] = useSetState<{ [K in string]: string[] }>({
    old_provider_ids: [],
    new_provider_ids: [],
  });
  const { value: state } = useAsyncRetryFunc(async () => {
    const res = await getGloablProviders();
    return res || {};
  }, []);

  const list = useMemo(() => {
    return (state?.data ?? []).map((v: { provider_id: number }) => ({
      ...v,
      key: `${v.provider_id}`,
    }));
  }, [state]);

  const handlerSubmit = async () => {
    if (configKeys.old_provider_ids.length !== configKeys.new_provider_ids.length) {
      message.error('新旧provider_ids数量不一致');
    } else {
      const res = await editAccountMigrationReport({
        ..._.mapValues(configKeys, (values) => {
          return values.map(Number); // 将每个值转换为数字
        }),
      });
      if (res.code === 0) {
        message.success('配置成功');
      }
    }
  };

  const onChange = (provider_ids: any) => {
    setConfigKeys((pre) => ({ ...pre, ...provider_ids }));
  };

  const filterOption = (inputValue: string, option: { provider_name: string }) => {
    return option.provider_name.indexOf(inputValue) > -1;
  };
  return (
    <PageContainer>
      <div style={{ width: 'fit-content' }}>
        {' '}
        <Flex gap="middle" vertical={false}>
          <div>
            <Form layout="inline" labelAlign="right" style={{ marginBottom: 15 }}>
              <Form.Item style={{ marginBottom: 5 }} label="旧provider_ids">
                <Input.TextArea
                  cols={82}
                  placeholder="换行输入搜索多个provider_id"
                  rows={6}
                  onChange={(e) => {
                    const value = _.uniq(_.compact((e.target.value ?? '').split('\n')));
                    const old_provider_ids = _.filter(value, (v) => {
                      return !!_.find(list, (item) => item.key === v);
                    });
                    setConfigKeys((pre) => ({ ...pre, old_provider_ids }));
                  }}
                />
              </Form.Item>
            </Form>
            <Transfer
              filterOption={filterOption}
              showSearch
              dataSource={list}
              titles={[
                '全部通道',
                <>
                  <span
                    style={{ color: '#ff4d4f', marginRight: 4, fontFamily: 'SimSun, sans-serif' }}
                  >
                    *
                  </span>
                  已选旧通道
                </>,
              ]}
              targetKeys={configKeys.old_provider_ids}
              onChange={(val) => onChange({ old_provider_ids: val })}
              listStyle={{ height: 420, width: 360 }}
              render={(item) => item.provider_name}
              pagination
            />
          </div>
          <div>
            <Form layout="inline" labelAlign="right" style={{ marginBottom: 15 }}>
              <Form.Item style={{ marginBottom: 5 }} label="新provider_ids">
                <Input.TextArea
                  cols={82}
                  placeholder="换行输入搜索多个provider_id"
                  rows={6}
                  onChange={(e) => {
                    const value = _.uniq(_.compact((e.target.value ?? '').split('\n')));
                    const new_provider_ids = _.filter(value, (v) => {
                      return !!_.find(list, (item) => item.key === v);
                    });
                    setConfigKeys((pre) => ({ ...pre, new_provider_ids }));
                  }}
                />
              </Form.Item>
            </Form>
            <Transfer
              filterOption={filterOption}
              showSearch
              dataSource={list}
              titles={[
                '全部通道',
                <>
                  <span
                    style={{ color: '#ff4d4f', marginRight: 4, fontFamily: 'SimSun, sans-serif' }}
                  >
                    *
                  </span>
                  已选新通道
                </>,
              ]}
              targetKeys={configKeys.new_provider_ids}
              onChange={(val) => onChange({ new_provider_ids: val })}
              listStyle={{ height: 420, width: 360 }}
              render={(item) => item.provider_name}
              pagination
            />
          </div>
        </Flex>{' '}
        <Flex justify="center">
          <Button
            type="primary"
            onClick={handlerSubmit}
            style={{ marginTop: 15 }}
            disabled={!configKeys.old_provider_ids.length || !configKeys.new_provider_ids.length}
          >
            提交进行迁移报备
          </Button>
        </Flex>
      </div>
    </PageContainer>
  );
};
export default AccountMigrationReport;
