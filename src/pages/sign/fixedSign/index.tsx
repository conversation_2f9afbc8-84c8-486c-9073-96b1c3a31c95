import React from 'react';
import { Tabs } from 'antd';
import { PageContainer } from '@ant-design/pro-components';
import FixedSign from './fixedSign';
import ToolList from './toolList';
const Index = () => {
  const tabKey = '0';
  return (
    <PageContainer>
      <Tabs
        defaultValue={tabKey}
        items={[
          {
            key: '0',
            label: '固定签名列表',
            children: <FixedSign />,
          },
          {
            key: '1',
            label: '工具列表',
            children: <ToolList />,
          },
        ]}
      ></Tabs>
    </PageContainer>
  );
};
export default Index;
