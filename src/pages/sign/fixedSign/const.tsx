import { addFixedSignature } from '@/services/fixedSignature';
import { message } from 'antd';

function fix(num: number, length: number): string {
  const numStr = String(num); // 将数字转换为字符串
  const padding = length - numStr.length; // 计算需要填充的零的数量

  // 如果需要填充零，则返回填充后的字符串，否则返回原字符串
  return padding > 0 ? '0'.repeat(padding) + numStr : numStr;
}

export const outputSignAndSubCodeList = (data: Data): string => {
  const subCodeStart = parseInt(data.subCodeStart, 10);
  const subCodeLength = data.subCodeStart.trim().length;
  const signs = data.signList.split('\n');

  const signAndSubCodes = signs
    .filter((sign) => sign.trim().length > 0) // 过滤掉空行
    .map((sign, index) => {
      const subCodeStr = fix(subCodeStart + index, subCodeLength); // 计算当前的 subCode
      return `${sign},${subCodeStr}`; // 使用模板字符串
    });

  return signAndSubCodes.join('\n'); // 连接结果
};

export const multipleFormConfig = {
  add: {
    title: '新增',
    columnsFilters: ['channel_id', 'sign_and_subcode'],
    onFinish: (values: any, _: unknown, { actionRef }: any) => {
      return addFixedSignature(values)
        .then((res) => {
          if (res.code === 0) {
            message.success('新增成功');
            actionRef.current.reload();
            return true;
          }
          return false;
        })
        .catch((err) => {
          message.error(err.message);
          return false;
        });
    },
  },
};

interface Data {
  subCodeStart: string;
  signList: string;
}
