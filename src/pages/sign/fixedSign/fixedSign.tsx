import React, { useCallback, useMemo, useRef } from 'react';
import { Button, message, Modal, Switch } from 'antd';
import { ActionType, BetaSchemaForm, ProTable } from '@ant-design/pro-components';
import { multipleFormConfig } from './const';
import { useMultipleConfigWithExtraDataModal } from '@/hooks/useMultipleConfigWithExtraDataModal';
import {
  deleteFixedSignature,
  getFixedSignature,
  getProviderChannelList,
  disabledFixedSignature,
} from '@/services/fixedSignature';
import _ from 'lodash';
import { useAsync } from 'react-use';

const FixedSign = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const { value: providerOptions } = useAsync(async () => {
    const res = await getProviderChannelList();
    return res.data.map(({ channel_id, provider }: any) => ({
      label: provider,
      value: channel_id,
    }));
  });
  function handleDelete(row: any) {
    Modal.confirm({
      title: '确认删除吗?',
      onOk: async () => {
        const res = await deleteFixedSignature({
          channel_id: String(row.channel_id),
          signature: row.signature,
        });
        if (res.code === 0) {
          message.success('删除成功');
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }
      },
    });
  }
  const handleDisabledChange = (row: any) => {
    Modal.confirm({
      title: (
        <>
          <p>{`请确定要${row.disabled === 0 ? '禁用' : '启用'}以下通道签名：`}</p>
          <p>{`通道名称：${row.provider}(${row.channel_id}`}</p>
          <p>{`签名：${row.signature}(${row.subcode})`}</p>
        </>
      ),
      onOk: async () => {
        const res = await disabledFixedSignature({
          channel_id: row.channel_id,
          signature: row.signature,
          disabled: row.disabled === 0 ? 1 : 0,
        });
        if (res.code === 0) {
          message.success('切换成功！');
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }
      },
    });
  };
  const columns: any = useMemo(() => {
    return [
      {
        title: '供应商ID',
        dataIndex: 'channel_id',
        key: 'channel_id',
        valueType: 'select',
        fieldProps: {
          options: providerOptions,
          showSearch: true,
        },
        render: (text: string, { channel_id }: any) => channel_id,
        formItemProps: { rules: [{ required: true }] },
        hideInSearch: true,
        transform: (value: string) => ({ channel_id: String(value) }),
      },
      {
        title: '供应商名称',
        dataIndex: 'provider',
        key: 'provider',
        formItemProps: { rules: [{ required: true }] },
        transform: (value: string) => ({
          provider: value.trim(),
        }),
      },
      {
        title: '接入码',
        dataIndex: 'access_code',
        key: 'access_code',
        hideInSearch: true,
      },
      {
        title: '签名',
        dataIndex: 'signature',
        key: 'signature',
        transform: (value: string) => ({
          signature: value.trim(),
        }),
      },
      {
        title: '子码',
        dataIndex: 'subcode',
        key: 'subcode',
        transform: (value: string) => ({
          subcode: value.trim(),
        }),
      },
      {
        title: '状态',
        dataIndex: 'disabled',
        key: 'disabled',
        hideInSearch: true,
        render: (disabled: number, row: any) => {
          return (
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
              checked={!disabled}
              onChange={() => {
                handleDisabledChange(row);
              }}
            />
          );
        },
      },
      {
        title: '签名与子码',
        dataIndex: 'sign_and_subcode',
        key: 'sign_and_subcode',
        hideInSearch: true,
        hideInTable: true,
        valueType: 'textarea',
        fieldProps: { rows: 6, placeholder: '签名与子码，每行一个，格式：签名,子码' },
        formItemProps: { rules: [{ required: true }] },
        transform: (text: string) => {
          return {
            sign_and_subcode: text
              .split('\n')
              .filter((item) => item.trim() !== '')
              .map((line) => {
                const [signature, subcode] = line.split(',');
                return { signature, subcode };
              }),
          };
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        key: 'action',
        hideInSearch: true,
        render(_: unknown, row: any) {
          return (
            <>
              <Button
                type="link"
                onClick={() => {
                  handleDelete(row);
                }}
              >
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, [providerOptions]);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getFixedSignature({
      ..._.omit(params, ['current', 'pageSize']),
      page_index: params.current,
      page_size: params.pageSize,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  const [open, formProps, openModal] = useMultipleConfigWithExtraDataModal(
    columns,
    multipleFormConfig,
    {
      actionRef,
    },
  );

  return (
    <>
      <Button
        type="primary"
        style={{ marginLeft: 10 }}
        onClick={() => {
          openModal('add');
        }}
      >
        添加
      </Button>
      {open && (
        <BetaSchemaForm
          layout="horizontal"
          labelCol={{ span: 6 }}
          width={550}
          formRef={addFormRef}
          modalProps={{ maskClosable: false }}
          {...formProps}
        />
      )}
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        form={{ span: 4 }}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        request={requestFn}
        options={false}
      />
    </>
  );
};
export default FixedSign;
