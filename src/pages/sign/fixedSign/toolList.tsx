import { BetaSchemaForm } from '@ant-design/pro-components';
import React, { useRef } from 'react';
import { outputSignAndSubCodeList } from './const';
import { copy, findLabel } from '@/utils/utils';
import { Button, message } from 'antd';
import { getAllSignature, getProviderChannelList, judgeSignature } from '@/services/fixedSignature';
import { useAsync } from 'react-use';
import _ from 'lodash';
const ToolList = () => {
  const toolFormRef = useRef<any>(null);
  const searchSdkFormRef = useRef<any>(null);
  const searchHasFormRef = useRef<any>(null);
  const { value: providerOptions } = useAsync(async () => {
    const res = await getProviderChannelList();
    return res.data.map(({ channel_id, provider }: any) => ({
      label: provider,
      value: channel_id,
    }));
  });

  return (
    <div
      style={{
        display: 'flex',
        gap: 100,
        justifyContent: 'center',
      }}
    >
      <div>
        <h2>签名子码辅助工具</h2>
        <BetaSchemaForm
          layout="vertical"
          layoutType="Form"
          formRef={toolFormRef}
          columns={[
            {
              title: '子码起始',
              dataIndex: 'subCodeStart',
              key: 'subCodeStart',
              formItemProps: { rules: [{ required: true }] },
            },
            {
              title: '签名',
              dataIndex: 'signList',
              key: 'signList',
              valueType: 'textarea',
              formItemProps: { rules: [{ required: true }] },
              fieldProps: { rows: 8 },
            },
            {
              title: '签名与子码',
              dataIndex: 'signAndSubCodeList',
              key: 'signAndSubCodeList',
              valueType: 'textarea',
              fieldProps: { rows: 8 },
            },
          ]}
          title="签名子码辅助工具"
          submitter={{
            render() {
              return (
                <>
                  <Button
                    onClick={() => {
                      copy(toolFormRef.current?.getFieldValue('signAndSubCodeList'));
                    }}
                  >
                    复制结果
                  </Button>
                  <Button
                    style={{ marginLeft: 10 }}
                    type="primary"
                    onClick={() => {
                      const data = toolFormRef.current?.getFieldsValue();
                      if (!data.signList || !data.subCodeStart) {
                        return;
                      }
                      toolFormRef.current?.setFieldValue(
                        'signAndSubCodeList',
                        outputSignAndSubCodeList(data as any),
                      );
                    }}
                  >
                    生成
                  </Button>
                </>
              );
            },
          }}
        />
      </div>
      <div>
        <h2>查询sdkappid对应的签名</h2>
        <BetaSchemaForm
          layout="vertical"
          layoutType="Form"
          formRef={searchSdkFormRef}
          columns={[
            {
              title: 'sdkappid',
              dataIndex: 'sdkappid',
              key: 'sdkappid',
              valueType: 'textarea',
              fieldProps: { rows: 8 },
              formItemProps: { rules: [{ required: true }] },
            },
            {
              title: '签名',
              dataIndex: 'signResult',
              key: 'signResult',
              valueType: 'textarea',
              fieldProps: { rows: 8 },
            },
          ]}
          title="查询sdkappid对应的签名"
          submitter={{
            searchConfig: {
              resetText: '复制结果',
              submitText: '搜索',
            },
            render(props, dom) {
              return dom[1];
            },
          }}
          onFinish={async (values: any) => {
            const { sdkappid } = values;
            const data = sdkappid
              .split('\n')
              .map((item: string) => item.trim())
              .filter((item: string) => !!item);
            getAllSignature({ sdkappid: data }).then((res: any) => {
              searchSdkFormRef.current?.setFieldValue(
                'signResult',
                res.data.map((v: any) => v.sign).join('\n'),
              );
            });
          }}
        />
      </div>
      <div>
        <h2>查询sdkappid是否都录入签名</h2>
        <BetaSchemaForm
          layout="vertical"
          layoutType="Form"
          formRef={searchHasFormRef}
          columns={[
            {
              title: '供应商名称',
              dataIndex: 'channel_id',
              key: 'channel_id',
              valueType: 'select',
              fieldProps: {
                options: providerOptions,
              },
              formItemProps: { rules: [{ required: true }] },
            },
            {
              title: 'sdkappid',
              dataIndex: 'sdkappid',
              key: 'sdkappid',
              valueType: 'textarea',
              fieldProps: { rows: 8 },
              formItemProps: { rules: [{ required: true }] },
            },
            {
              title: '签名',
              dataIndex: 'signResult',
              key: 'signResult',
              valueType: 'textarea',
              fieldProps: { rows: 8 },
            },
          ]}
          title="查询sdkappid是否都录入签名"
          submitter={{
            searchConfig: {
              submitText: '搜索',
            },
            render(props, dom) {
              return dom[1];
            },
          }}
          onFinish={async (values: any) => {
            const { sdkappid, channel_id } = values;
            const data = sdkappid
              .split('\n')
              .map((item: string) => item.trim())
              .filter((item: string) => !!item);
            judgeSignature({ sdkappid: data, channel_id }).then((res: any) => {
              if (0 === res.data.length) {
                message.success('没有签名报备问题');
              } else {
                const signResult = _.groupBy(res.data, (v: any) => v.channel_id);
                searchHasFormRef.current?.setFieldValue(
                  'signResult',
                  _.entries(signResult).map(([key, signs]) => {
                    return `${findLabel(providerOptions, key)}\n${signs
                      .map((sign) => `  签名:${sign.signature}|子码:${sign.subcode}`)
                      .join('\n')}`;
                  }),
                );
              }
            });
          }}
        />
      </div>
    </div>
  );
};
export default ToolList;
