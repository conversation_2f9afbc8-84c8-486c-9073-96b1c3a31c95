import React, { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { Button, message, Modal, Switch } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';

import {
  addFixedSignature,
  deleteFixedSignature,
  getFixedSignature,
  getProviderChannelList,
  getSchedulerApiConfigList,
  disabledFixedSignature,
} from '@/services/fixedSignature';
import { useAsync } from 'react-use';
import { outputSignAndSubCodeList } from './fixedSign/const';
import { processRowsData, saveCSV } from '../global-components/saveCsv';
import { findLabel } from '@/utils/utils';

const DevFixedSign = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const initAddFormDataRef = useRef<any>({});
  const [open, setOpen] = useState<any>(false);
  const [, forceUpdate] = useState<any>(null);
  const [providerOptionsMap, setProviderOptionsMap] = useState<any>({});

  const searchProviderOptions = providerOptionsMap[formRef.current?.getFieldValue('cooked_desc')];
  const addProviderOptions =
    providerOptionsMap[
      addFormRef.current?.getFieldValue('cooked_desc') || initAddFormDataRef.current.cooked_desc
    ];

  const getProviderList = async (params: any) => {
    let data: any = [];
    if (params.callback_provider_id !== 'undefined') {
      const res = await getProviderChannelList(params);
      data = res.data.map(({ channel_id, provider }: any) => ({
        label: provider,
        value: channel_id,
      }));
      if (data.length === 0) {
        message.warning('该配置下无固签账号！');
      }
      setProviderOptionsMap((pre: any) => ({ ...pre, [params.callback_provider_id]: data }));
    }
  };

  const { value: configList } = useAsync(async () => {
    const res = await getSchedulerApiConfigList();
    return res.data.map(({ cooked_desc, id }: any) => ({
      label: cooked_desc,
      value: id,
    }));
  });
  function handleDelete(row: any) {
    Modal.confirm({
      title: '确认删除吗?',
      onOk: async () => {
        const res = await deleteFixedSignature({
          channel_id: String(row.channel_id),
          signature: row.signature,
        });
        if (res.code === 0) {
          message.success('删除成功');
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }
      },
    });
  }
  const handleDisabledChange = (row: any) => {
    Modal.confirm({
      title: (
        <>
          <p>{`请确定要${row.disabled === 0 ? '禁用' : '启用'}以下通道签名：`}</p>
          <p>{`通道名称：${row.provider}(${row.channel_id}`}</p>
          <p>{`签名：${row.signature}(${row.subcode})`}</p>
        </>
      ),
      onOk: async () => {
        const res = await disabledFixedSignature({
          channel_id: row.channel_id,
          signature: row.signature,
          disabled: row.disabled === 0 ? 1 : 0,
        });
        if (res.code === 0) {
          message.success('切换成功！');
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }
      },
    });
  };

  const handleInputDataChange = () => {
    const { signList, subCodeStart } = addFormRef.current?.getFieldsValue();

    if (signList && subCodeStart) {
      addFormRef.current?.setFieldValue(
        'sign_and_subcode',
        outputSignAndSubCodeList({ signList, subCodeStart }),
      );
    }
  };

  const addColumns = [
    {
      title: '配置',
      dataIndex: 'cooked_desc',
      key: 'cooked_desc',
      valueType: 'select',
      fieldProps: {
        options: configList,
        showSearch: true,
        onChange: (value: string) => {
          addFormRef.current?.setFieldValue('channel_id');
          getProviderList({
            callback_provider_id: String(value),
          });
        },
      },
      transform: () => ({}),
    },
    {
      title: '供应商ID',
      dataIndex: 'channel_id',
      key: 'channel_id',
      valueType: 'select',
      fieldProps: {
        disabled: !addProviderOptions?.length,
        showSearch: true,
        options: addProviderOptions,
        onChange(value: string) {
          forceUpdate({});
          const cookedDesc = addFormRef.current?.getFieldValue('cooked_desc');
          formRef.current.setFieldValue('cooked_desc', cookedDesc);
          formRef.current.setFieldValue('channel_id', value);
        },
      },
      formItemProps: { rules: [{ required: true }] },
      transform: (value: number) => ({ channel_id: String(value) }),
    },
  ];

  const searchColumns = [
    {
      title: '配置',
      dataIndex: 'cooked_desc',
      key: 'cooked_desc',
      valueType: 'select',
      fieldProps: {
        options: configList,
        showSearch: true,
        onChange: (value: string) => {
          formRef.current?.setFieldValue('channel_id');
          getProviderList({
            callback_provider_id: String(value),
          });
        },
      },
      hideInTable: true,
      transform: () => ({}),
    },
    {
      title: '供应商ID',
      dataIndex: 'channel_id',
      key: 'channel_id',
      valueType: 'select',
      fieldProps: {
        disabled: !searchProviderOptions?.length || !formRef.current?.getFieldValue('cooked_desc'),
        showSearch: true,
        options: searchProviderOptions,
        onChange(value: string) {
          forceUpdate({});
          const cookedDesc = formRef.current?.getFieldValue('cooked_desc');
          // 如果 addFormRef.current 存在，设置其值
          if (addFormRef.current) {
            addFormRef.current.setFieldValue('cooked_desc', cookedDesc);
            addFormRef.current.setFieldValue('channel_id', value);
          } else {
            // 否则初始化 addFormDataRef
            initAddFormDataRef.current = {
              cooked_desc: cookedDesc,
              channel_id: value,
            };
          }
        },
      },
      render: (_: unknown, { channel_id }: { channel_id: string }) => channel_id,
      transform: (value: string) => ({ provider: findLabel(searchProviderOptions, value) }),
    },
  ];
  useLayoutEffect(() => {
    // 产品要求查询表单添加表单供应商ID变化时触发查询
    formRef.current?.submit();
  }, [
    formRef.current?.getFieldValue('channel_id'),
    addFormRef.current?.getFieldValue('channel_id'),
  ]);
  const columns: any = useMemo(() => {
    return [
      {
        title: '供应商名称',
        dataIndex: 'provider',
        key: 'provider',
        hideInSearch: true,
      },
      {
        title: '接入码',
        dataIndex: 'access_code',
        key: 'access_code',
        hideInSearch: true,
      },
      {
        title: '签名',
        dataIndex: 'signature',
        key: 'signature',
        transform: (value: string) => ({
          signature: value.trim(),
        }),
      },
      {
        title: '子码',
        dataIndex: 'subcode',
        key: 'subcode',
        transform: (value: string) => ({
          subcode: value.trim(),
        }),
      },
      {
        title: '状态',
        dataIndex: 'disabled',
        key: 'disabled',
        hideInSearch: true,
        render: (disabled: number, row: any) => {
          return (
            <Switch
              checkedChildren="启用"
              unCheckedChildren="禁用"
              checked={!disabled}
              onChange={() => {
                handleDisabledChange(row);
              }}
            />
          );
        },
      },
      {
        title: '子码起始',
        dataIndex: 'subCodeStart',
        key: 'subCodeStart',
        hideInTable: true,
        hideInSearch: true,
        fieldProps: {
          onChange: handleInputDataChange,
        },
      },
      {
        title: '签名',
        dataIndex: 'signList',
        key: 'signList',
        valueType: 'textarea',
        hideInTable: true,
        hideInSearch: true,
        fieldProps: {
          onChange: handleInputDataChange,
        },
      },
      {
        title: '签名与子码',
        dataIndex: 'sign_and_subcode',
        key: 'sign_and_subcode',
        hideInSearch: true,
        hideInTable: true,
        valueType: 'textarea',
        fieldProps: {
          rows: 6,
          placeholder: '签名与子码，每行一个，格式：签名,子码。可由填写子码起始与签名进行生成',
        },
        formItemProps: { rules: [{ required: true }] },
        transform: (text: string) => {
          return {
            sign_and_subcode: text
              .split('\n')
              .filter((item) => item.trim() !== '')
              .map((line) => {
                const [signature, subcode] = line.split(',');
                return { signature, subcode };
              }),
          };
        },
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        hideInSearch: true,
        render(_: unknown, row: any) {
          return (
            <>
              <Button
                type="link"
                onClick={() => {
                  handleDelete(row);
                }}
              >
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, []);

  // 导出 CSV
  const exportCsv = async () => {
    try {
      const searchCondition = formRef.current?.getFieldsValue() || {};
      const { channel_id } = searchCondition;
      const params = {
        ...searchCondition,
        provider: findLabel(searchProviderOptions, channel_id),
      };
      const { data } = await getFixedSignature(_.pickBy(params, _.identity));
      const processedData = data.map((item: any) => ({
        ...item,
        subcode: `="${item.subcode}"`, // 解决导出子码为字符串的问题
      }));
      const { headerText, exportData } = processRowsData(processedData, [
        ...searchColumns,
        ...columns,
      ]);

      const fileName = `${
        channel_id ? `${channel_id}账号固定签名明细` : '固定签名明细'
      }_${Date.now()}`;

      await saveCSV(fileName, headerText, exportData, {
        route: '/sign/dev-fixed-sign',
        params: {},
      });

      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getFixedSignature({
      ..._.omit(params, ['current', 'pageSize']),
      page_index: params.current,
      page_size: params.pageSize,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  return (
    <PageContainer>
      <Button
        type="primary"
        style={{ marginLeft: 10 }}
        onClick={() => {
          setOpen(true);
          addFormRef.current?.resetFields(['sign_and_subcode', 'signList', 'subCodeStart']);
        }}
      >
        添加
      </Button>
      <Button
        type="default"
        style={{ marginLeft: 10 }}
        onClick={() => {
          exportCsv();
        }}
      >
        导出
      </Button>
      <BetaSchemaForm
        title={'添加签名'}
        columns={[...addColumns, ...columns].filter((column: any) =>
          ['cooked_desc', 'channel_id', 'sign_and_subcode', 'subCodeStart', 'signList'].includes(
            column.key,
          ),
        )}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 6 }}
        width={550}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        modalProps={{ maskClosable: false }}
        initialValues={initAddFormDataRef.current}
        onFinish={({ channel_id, sign_and_subcode }: any) => {
          return addFixedSignature({ channel_id, sign_and_subcode })
            .then((res) => {
              if (res.code === 0) {
                message.success('新增成功');
                actionRef.current?.reload();
                return true;
              }
              return false;
            })
            .catch((err) => {
              message.error(err.message);
              return false;
            });
        }}
      />
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={[...searchColumns, ...columns]}
        form={{ span: 5 }}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        request={requestFn}
        onReset={() => {
          forceUpdate({});
        }}
        search={{
          labelWidth: 'auto',
        }}
        options={false}
      />
    </PageContainer>
  );
};
export default DevFixedSign;
