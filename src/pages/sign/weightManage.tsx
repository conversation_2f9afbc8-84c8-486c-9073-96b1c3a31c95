import React, { useState, useEffect } from 'react';
import { Select } from 'antd';
import { getWeightInfo, insertWeightInfo, updateWeightInfo } from '@/services/scdAPI';
import { getGloablProviders } from '@/services/api';
import { priorityStatus } from '../sign/const';
import PatternTable from '@/pages/component/PatternLayout';
import { serialize } from '@/utils/serialize';
import { dumpwarning } from '@/services/dumpWarning';

const WeightManage = () => {
  const [providers, setProviders] = useState<any>([]);

  const columns: any = [
    {
      title: '签名',
      dataIndex: 'sign',
      key: 'sign',
      align: 'center',
    },
    {
      title: '强制调度',
      dataIndex: 'force_sign_result',
      key: 'force_sign_result',
      align: 'center',
      render: (force: number) => {
        return <span>{force === 0 ? '否' : '是'}</span>;
      },
    },
    {
      title: 'provider',
      dataIndex: 'provider',
      key: 'provider',
      align: 'center',
    },
    {
      title: '单发普通',
      dataIndex: 'single',
      key: 'single',
      align: 'center',
    },
    {
      title: '单发营销',
      dataIndex: 'single_b',
      key: 'single_b',
      align: 'center',
    },
    {
      title: '群发2普通',
      dataIndex: 'multi2',
      key: 'multi2',
      align: 'center',
    },
    {
      title: '群发2营销',
      dataIndex: 'multi2_b',
      key: 'multi2_b',
      align: 'center',
    },
    {
      title: '优先级',
      dataIndex: 'hot_backup_level',
      key: 'hot_backup_level',
      align: 'center',
    },
  ];

  useEffect(() => {
    getGloablProviders().then((res) => {
      const providersList = (res?.data ?? []).map(
        (item: { provider_id: number; provider_name: string }) => {
          return {
            value: item.provider_id,
            label: item.provider_name,
          };
        },
      );
      setProviders(providersList);
    });
  }, []);

  const operateForm = [
    {
      showOnAdd: true,
      name: 'sign',
      label: '签名',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'provider_id',
      label: 'provider',
      isRequired: true,
      disabled: true,
      renderType: 'select',
      options: providers,
    },
    {
      showOnAdd: false,
      name: 'single',
      label: '单发普通',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: false,
      name: 'single_b',
      label: '单发营销',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: false,
      name: 'multi2',
      label: '群发2普通',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: false,
      name: 'multi2_b',
      label: '群发2营销',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: false,
      name: 'hot_backup',
      label: '优先级',
      disabled: false,
      isRequired: true,
      render: () => {
        return (
          <Select style={{ width: 120 }}>
            {priorityStatus.map((item) => {
              return (
                <Select.Option value={item.value} key={item.value}>
                  {item.text}
                </Select.Option>
              );
            })}
          </Select>
        );
      },
    },
  ];

  async function doEdit(vals: any) {
    const res = await updateWeightInfo({ ...vals });
    return res;
  }
  async function doAdd(vals: any) {
    const res = await insertWeightInfo({ ...vals });
    return res;
  }
  async function getList(vals?: any) {
    return await getWeightInfo({ ...vals });
  }

  async function onExport(vals: any) {
    const str = serialize(vals);
    const a = document.createElement('a');
    a.href = `/apis/sign/weight/export-weight?${str}`;
    a.download = '';
    a.click();
    dumpwarning({ route: '/sign/weight/export-weight', params: { ...vals } });
  }

  return (
    <PatternTable
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'search_key',
          placeholder: 'sign',
        },
        {
          label: '',
          name: 'provider_id',
          renderType: 'select',
          options: providers,
          placeholder: 'provider',
        },
      ]}
      operateForm={operateForm}
      operType={0}
      exportFile={onExport}
    />
  );
};

export default WeightManage;
