import React from 'react';
import PatternTable from '@/pages/component/PatternLayout';
import { editSignList, getSignList, insertSignList } from '@/services/scdAPI';
import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { BaseType } from 'antd/es/typography/Base';
import { findLabelWithType } from '@/utils/utils';
import { signRemarkTypeOptions } from './const';

const { RangePicker } = DatePicker;

const statusOptions: { value: any; label: string; type: BaseType }[] = [
  { label: '待审核', value: 1, type: 'warning' },
  { label: '已通过', value: 0, type: 'success' },
  { label: '已拒绝', value: 2, type: 'danger' },
  { label: '已删除', value: 3, type: 'secondary' },
];

const Manage = () => {
  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '法人姓名',
      dataIndex: 'corp_name',
      key: 'corp_name',
      align: 'center',
    },
    {
      title: '证件号码',
      dataIndex: 'corp_cr_num',
      key: 'corp_cr_num',
      align: 'center',
    },
    {
      title: '证件类型',
      dataIndex: 'corp_cr_type',
      key: 'corp_cr_type',
      align: 'center',
      render: (type: number) => (
        <span>{type === 1 ? '身份证' : type === 2 ? '护照' : type === 0 ? '不存在' : '--'}</span>
      ),
    },
    {
      title: 'sign',
      dataIndex: 'sign',
      key: 'sign',
      align: 'center',
    },
    {
      title: '签名类型',
      dataIndex: 'remark_type',
      key: 'remark_type',
      align: 'center',
      render: (remark_type: string) =>
        signRemarkTypeOptions.find((item) => item.value === remark_type)?.label,
    },
    {
      title: '签名状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status: number) => findLabelWithType(statusOptions, status),
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'sign',
      label: 'sign',
      isRequired: true,
      disabled: false,
    },
    {
      showOnAdd: true,
      name: 'remark_type',
      label: 'remark_type',
      isRequired: true,
      disabled: false,
      renderType: 'select',
      options: signRemarkTypeOptions.filter(
        (item) => !['网站', '公众号', '小程序'].includes(item.value),
      ),
    },
  ];

  async function doEdit(vals: any) {
    return await editSignList({ ...vals });
  }
  async function doAdd(vals: any) {
    return await insertSignList({ ...vals });
  }
  async function getList(vals?: any) {
    const params = _.cloneDeep(vals);
    if (vals?.time) {
      params.from_time = dayjs(params.time[0]).format('YYYY-MM-DD HH:mm:ss');
      params.to_time = dayjs(params.time[1]).format('YYYY-MM-DD HH:mm:ss');
    }
    return await getSignList({ ..._.omit(params, 'time') });
  }

  return (
    <PatternTable
      rowKey={(row: any) => `${row.id}-${row.status}-${row.remark_type}`}
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: 'appid',
          name: 'appid',
        },
        {
          label: 'sign',
          name: 'sign',
        },
        {
          label: '法人姓名',
          name: 'user_name',
          placeholder: '法人姓名',
        },
        {
          label: '证件号码',
          name: 'idcard',
          placeholder: '证件号码',
        },
        {
          label: '',
          name: 'time',
          render: () => (
            <RangePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              placeholder={['默认查询最近一个月数据', '结束时间']}
              presets={[
                { label: 'Today', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
                {
                  label: 'Yesterday',
                  value: [
                    dayjs().subtract(1, 'days').startOf('day'),
                    dayjs().subtract(1, 'days').endOf('day'),
                  ],
                },
                {
                  label: 'Week',
                  value: [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
                },
                {
                  label: 'Month',
                  value: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
                },
              ]}
            />
          ),
        },
      ]}
      operateForm={operateForm}
      operType={0}
      searchInitials={{ time: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')] }}
    />
  );
};

export default Manage;
