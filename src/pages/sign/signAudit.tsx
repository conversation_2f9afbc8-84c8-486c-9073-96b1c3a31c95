import React, { useRef } from 'react';
import { findAuditSign, editSignStatus } from '@/services/scdAPI';
import { InputNumber, message } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import _ from 'lodash';
import { signInspectStatusOptions } from './const';

const SignAudit = () => {
  const ref = useRef<ActionType>();

  async function editStatus(data: any) {
    const res = await editSignStatus({ ...data });
    if (res?.code === 0) {
      message.success('编辑成功');
      ref?.current?.reload();
    }
  }

  return (
    <PageContainer>
      <ProTable
        onRequestError={() => {
          message.error('查询失败');
        }}
        actionRef={ref}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          optionRender: (row, config, defaultDom) => {
            return [defaultDom[1]];
          },
          collapseRender: false,
        }}
        options={false}
        columns={[
          {
            title: '申请单ID',
            key: 'id',
            dataIndex: 'id',
            align: 'center',
            search: false,
            editable: false,
          },
          {
            title: '签名ID',
            key: 'sign_id',
            align: 'center',
            dataIndex: 'sign_id',
            editable: false,
            renderFormItem: () => (
              <InputNumber style={{ width: 180 }} placeholder="输入签名ID开始查询" />
            ),
          },
          {
            title: '签名内容',
            key: 'sign',
            align: 'center',
            dataIndex: 'sign',
            search: false,
            editable: false,
          },
          {
            title: '申请时间',
            key: 'apply_time',
            align: 'center',
            dataIndex: 'apply_time',
            search: false,
            editable: false,
          },
          {
            title: '审批时间',
            key: 'reply_time',
            align: 'center',
            dataIndex: 'reply_time',
            search: false,
            editable: false,
          },
          {
            title: '审核状态',
            key: 'apply_status',
            align: 'center',
            valueType: 'select',
            dataIndex: 'apply_status',
            search: false,
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
            fieldProps: {
              options: [
                {
                  label: '待审核',
                  value: 0,
                },
                {
                  label: '已通过',
                  value: 1,
                },
                {
                  label: '已拒绝',
                  value: 2,
                },
                {
                  label: '已删除',
                  value: 3,
                  disabled: true,
                },
                {
                  label: '更新后待审核',
                  value: 4,
                },
                {
                  label: '更新后已拒绝',
                  value: 5,
                },
              ],
            },
          },
          {
            title: '巡检状态',
            key: 'inspection_status',
            align: 'center',
            valueType: 'select',
            dataIndex: 'inspection_status',
            search: false,
            width: 120,
            fieldProps: {
              options: signInspectStatusOptions,
            },
          },
          {
            title: '操作',
            valueType: 'option',
            align: 'center',
            render: (text, record, _, action) => [
              <a
                key="editable"
                onClick={() => {
                  action?.startEditable?.(record.id);
                }}
              >
                编辑
              </a>,
            ],
          },
        ]}
        request={async (params: { sign_id?: string }) => {
          if (!params.sign_id) return { data: [], success: true };
          return await findAuditSign({ sign_id: params.sign_id });
        }}
        style={{ padding: 0 }}
        editable={{
          type: 'single',
          actionRender: (row, config, defaultDom) => [defaultDom.save, defaultDom.cancel],
          onSave: async (rowKey, data: any) => {
            await editStatus({
              ..._.pick(data, ['id', 'sign_id', 'appid', 'inspection_status', 'apply_status']),
            });
          },
        }}
        pagination={false}
      />
    </PageContainer>
  );
};
export default SignAudit;
