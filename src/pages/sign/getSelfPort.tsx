import React, { useMemo } from 'react';
import { Button, Form, Input, Table } from 'antd';
import { useSetState } from 'react-use';
import { PageContainer } from '@ant-design/pro-layout';
import _ from 'lodash';
import { SearchOutlined } from '@ant-design/icons';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { getSelfPort } from '@/services/querySelfPort';

const columns: any[] = [
  {
    title: 'uin',
    dataIndex: 'uin',
    key: 'uin',
    align: 'center',
  },
  {
    title: 'appid',
    dataIndex: 'qappid',
    key: 'qappid',
    align: 'center',
  },
  {
    title: '签名',
    dataIndex: 'sign',
    key: 'sign',
    align: 'center',
  },
  {
    title: '调度ID',
    dataIndex: 'provider_id',
    key: 'provider_id',
    align: 'center',
  },
  {
    title: '通道名称',
    dataIndex: 'provider',
    key: 'provider',
    align: 'center',
  },
  {
    title: '主码号',
    dataIndex: 'access_code',
    key: 'access_code',
    align: 'center',
  },
  {
    title: '混发子码',
    dataIndex: 'subcode',
    key: 'subcode',
    align: 'center',
  },
  {
    title: '签名子码',
    dataIndex: 'sign_subcode',
    key: 'sign_subcode',
    align: 'center',
  },
  {
    title: '完整端口',
    dataIndex: 'port',
    key: 'port',
    align: 'center',
  },
];

const SelfPort = () => {
  const [form] = Form.useForm();
  const [searchKeys, setSearchKeys] = useSetState<{
    page_index: number;
    page_size: number;
  }>({
    page_index: 1,
    page_size: 10,
  });

  const { value: state, loading } = useAsyncRetryFunc(async () => {
    const res = await getSelfPort({
      ..._.pickBy(searchKeys, (v) => !!v),
    });
    return res?.data ?? {};
  }, [searchKeys]);

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  return (
    <PageContainer>
      <div>
        <Form
          form={form}
          layout="inline"
          labelAlign="right"
          onFinish={(vals) => setSearchKeys({ ...vals, page_index: 1 })}
          initialValues={{ type: 'uin' }}
        >
          <Form.Item name="uin" label="uin">
            <Input allowClear style={{ width: 150 }} />
          </Form.Item>
          <Form.Item name="qappid" label="qappid">
            <Input allowClear style={{ width: 150 }} />
          </Form.Item>
          <Form.Item name="sign" label="签名">
            <Input allowClear style={{ width: 150 }} />
          </Form.Item>
          <Form.Item name="port" label="端口">
            <Input allowClear style={{ width: 150 }} />
          </Form.Item>
          <Form.Item name="provider_id" label="调度ID">
            <Input allowClear style={{ width: 150 }} />
          </Form.Item>
          <Form.Item>
            <Button htmlType="submit" type="primary" icon={<SearchOutlined />}>
              查询
            </Button>
          </Form.Item>
        </Form>
      </div>
      <div style={{ marginTop: 15 }}>
        <Table
          columns={columns}
          dataSource={list}
          rowKey={(record: any) => record.id}
          loading={loading}
          pagination={{
            current: searchKeys.page_index,
            total,
            showSizeChanger: true,
            onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
            onChange: (page_index) => {
              setSearchKeys({ page_index });
            },
          }}
        />
      </div>
    </PageContainer>
  );
};
export default SelfPort;
