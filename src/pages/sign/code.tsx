import React from 'react';
import { getAppSign, insertAppSign, updateAppSign } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const SignCode = () => {
  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '签名',
      dataIndex: 'sign',
      key: 'sign',
      align: 'center',
    },
    {
      title: '扩展码',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
    },
    {
      title: '扩展长度',
      dataIndex: 'len',
      key: 'len',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'sign',
      label: 'sign',
      isRequired: true,
      disabled: true,
    },
    {
      showOnAdd: true,
      name: 'code',
      label: 'code',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'len',
      label: 'len',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
  ];

  async function doEdit(vals: any) {
    const res = await updateAppSign({ ...vals });
    return res;
  }
  async function doAdd(vals: any) {
    const res = await insertAppSign({ ...vals });
    return res;
  }
  async function getList(vals?: any) {
    return await getAppSign({ ...vals });
  }

  return (
    <PatternTable
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: '签名/appid',
          name: 'search_key',
          placeholder: '签名/appid',
        },
      ]}
      operateForm={operateForm}
      operType={0}
    />
  );
};

export default SignCode;
