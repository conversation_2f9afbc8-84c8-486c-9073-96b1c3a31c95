import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import { isMobile } from '@/const/jadgeUserAgent';
import _ from 'lodash';
import { exportDirectPortList, getDirectPortList } from '@/services/directPort';
import { findLabel } from '@/utils/utils';
import { cardType, smsTemplateType } from './const';
import qs from 'query-string';
import dayjs from 'dayjs';
import { dumpwarning } from '@/services/dumpWarning';

type DataItem = {
  name: string;
  state: string;
};

const DirectPortList = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const searchFormRef = useRef<any>();
  const [exportLoading, setExportLoading] = useState(false);
  const [isFirst, setIsFirst] = useState<boolean>(true);
  const [searchKeys, setSearchKeys] = useState<{ [key: string]: any }>({
    time: [dayjs().subtract(7, 'day'), dayjs()],
  });

  const columns: any = useMemo(() => {
    return [
      {
        title: 'sdkappid',
        dataIndex: 'sdkappid',
        key: 'sdkappid',
        width: 100,
      },
      {
        title: '供应商ID',
        dataIndex: 'provider_id',
        key: 'provider_id',
        hideInSearch: true,
      },
      {
        title: 'provider_str',
        dataIndex: 'provider_str',
        key: 'provider_str',
        hideInSearch: true,
        width: 100,
      },
      {
        title: '模版类型',
        dataIndex: 'template_type',
        key: 'template_type',
        hideInSearch: true,
        render: (text, row: any) => {
          const f = row.template_type.split(',');
          return smsTemplateType
            .filter((el) => f.includes(el.value))
            .map((el) => el.label)
            .join(',');
        },
      },
      {
        title: '签名短信内容',
        dataIndex: 'msg',
        key: 'msg',
        hideInSearch: true,
        width: 100,
      },
      {
        title: '签名',
        dataIndex: 'sign',
        key: 'sign',
        hideInSearch: true,
      },
      {
        title: '短信主端口号',
        dataIndex: 'access_code',
        key: 'access_code',
        hideInSearch: true,
        width: 100,
      },
      {
        title: '短信子端口号',
        dataIndex: 'cb_extend',
        key: 'cb_extend',
        width: 100,
      },
      {
        title: '子端口新增时间',
        dataIndex: 'add_time',
        key: 'add_time',
        hideInSearch: true,
      },
      {
        title: 'qappid',
        dataIndex: 'qappid',
        key: 'qappid',
        hideInSearch: true,
      },
      {
        title: 'uin',
        dataIndex: 'uin',
        key: 'uin',
        hideInSearch: true,
      },
      {
        title: '企业名称',
        dataIndex: 'company_name',
        key: 'company_name',
        hideInSearch: true,
      },
      {
        title: '企业统一社会信用代码',
        dataIndex: 'company_number',
        key: 'company_number',
        hideInSearch: true,
      },
      {
        title: '责任人名称',
        dataIndex: 'corp_name',
        key: 'corp_name',
        hideInSearch: true,
      },
      {
        title: '责任人证件类型',
        dataIndex: 'corp_cr_type',
        key: 'corp_cr_type',
        hideInSearch: true,
        render: (text, record: Record<string, any>) => findLabel(cardType, record.corp_cr_type),
      },
      {
        title: '责任人证件号',
        dataIndex: 'corp_cr_num',
        key: 'corp_cr_num',
        hideInSearch: true,
      },
      {
        title: '经办人名称',
        dataIndex: 'transactor_name',
        key: 'transactor_name',
        hideInSearch: true,
      },
      {
        title: '经办人证件类型',
        dataIndex: 'transactor_cr_type',
        key: 'transactor_cr_type',
        hideInSearch: true,
        render: (text, record: Record<string, any>) =>
          findLabel(cardType, record.transactor_cr_type),
      },
      {
        title: '经办人证件号',
        dataIndex: 'transactor_cr_num',
        key: 'transactor_cr_num',
        hideInSearch: true,
      },
      {
        title: '经办人手机号',
        dataIndex: 'transactor_phone',
        key: 'transactor_phone',
        hideInSearch: true,
      },
      {
        title: '调度ID',
        dataIndex: 'scheduler_id',
        key: 'scheduler_id',
      },
    ];
  }, []);

  const requestFn = useCallback(
    async (params: any) => {
      if (isFirst) {
        setIsFirst(false);
        return {
          data: [],
          success: true,
          total: 0,
        };
      }
      const { data } = await getDirectPortList({
        ..._.omit(
          _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
          ['pageSize', 'current'],
        ),
        ..._.pickBy(searchKeys, (v, k) => k !== 'time' && !_.isNil(v) && v !== ''),
        start_time: searchKeys.time
          ? dayjs(searchKeys.time?.[0])
              .startOf('day')
              .format('YYYY-MM-DD HH:mm:ss')
          : undefined,
        end_time: searchKeys.time
          ? dayjs(searchKeys.time?.[1])
              .endOf('day')
              .format('YYYY-MM-DD HH:mm:ss')
          : undefined,
        page_index: params.current,
        page_size: params.pageSize,
      });
      return {
        data: data.list ?? [],
        success: true,
        total: data.count,
      };
    },
    [isFirst, searchKeys],
  );

  useEffect(() => {
    actionRef.current?.reload();
  }, [searchKeys]);

  async function handleExport() {
    try {
      const formVals = searchFormRef.current?.getFieldsValue();
      setExportLoading(true);
      const params = {
        ..._.pickBy(formVals, (v, k) => k !== 'time' && !_.isNil(v) && v !== ''),
        start_time: searchKeys.time
          ? dayjs(searchKeys.time?.[0])
              .startOf('day')
              .format('YYYY-MM-DD HH:mm:ss')
          : undefined,
        end_time: searchKeys.time
          ? dayjs(searchKeys.time?.[1])
              .endOf('day')
              .format('YYYY-MM-DD HH:mm:ss')
          : undefined,
      };
      const res = await exportDirectPortList({ ...params });
      if (res.code === 0) {
        dumpwarning({ route: 'sign/direct-port', params });
        Modal.success({
          content: '导出请求已提交，结果稍后将发送到您的邮箱，请注意查收',
        });
      }
      setExportLoading(false);
    } catch (error) {
      console.log(error);
      setExportLoading(false);
    }
  }

  const searchColumns = useMemo(() => {
    const f = columns.filter((el) => ['sdkappid', 'scheduler_id', 'cb_extend'].includes(el.key));
    const time = [
      {
        title: '起止时间',
        key: 'time',
        valueType: 'dateRange',
        fieldProps: {
          style: {
            width: 250,
          },
        },
      },
    ];
    return [...f, ...time];
  }, [columns]);

  return (
    <PageContainer>
      <Button
        type="primary"
        style={{ marginLeft: 10 }}
        onClick={handleExport}
        loading={exportLoading}
      >
        导出
      </Button>
      <BetaSchemaForm<DataItem>
        formRef={searchFormRef}
        layoutType="QueryFilter"
        labelWidth={100}
        onFinish={async (values) => {
          actionRef.current?.setPageInfo?.({ current: 1 });
          setSearchKeys({ ...values });
        }}
        columns={searchColumns}
        collapsed={false}
        collapseRender={() => null}
        initialValues={{
          time: [dayjs().subtract(7, 'day'), dayjs()],
        }}
      />
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={false}
        scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 2000 }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default DirectPortList;
