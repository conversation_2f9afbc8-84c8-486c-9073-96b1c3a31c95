import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ActionType, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import { getSignCredentialReportList } from '@/services/signCredentialReport';

const statusOptions = [
  {
    label: '待补全资料',
    value: 0,
  },
  {
    label: '待报备',
    value: 1,
  },
  {
    label: '资料不全',
    value: 2,
  },
  {
    label: '已报备',
    value: 4,
  },
];

const SignCredentialReport = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);

  const columns: any = useMemo(() => {
    return [
      {
        title: '报备时间',
        dataIndex: 'updated_at',
        key: 'updated_at',
        width: 120,
        hideInSearch: true,
      },
      {
        title: '签名ID',
        dataIndex: 'sign_id',
        key: 'sign_id',
        width: 100,
        order: 2,
      },
      // {
      //   title: 'ddate',
      //   dataIndex: 'ddate',
      //   key: 'ddate',
      //   hideInSearch: true,
      // },
      // {
      //   title: 'comp_id',
      //   dataIndex: 'comp_id',
      //   key: 'comp_id',
      //   hideInSearch: true,
      // },
      {
        title: '供应商',
        dataIndex: 'comp_alias',
        key: 'comp_alias',
        order: 3,
      },
      {
        title: '通道ID',
        dataIndex: 'provider_id',
        key: 'provider_id',
        width: 100,
        order: 1,
      },
      {
        title: 'account',
        dataIndex: 'account',
        key: 'account',
        hideInSearch: true,
      },

      {
        title: '签名',
        dataIndex: 'sign',
        key: 'sign',
        order: 4,
      },
      {
        title: 'subcode',
        dataIndex: 'subcode',
        key: 'subcode',
        hideInSearch: true,
      },
      {
        title: '公司名称',
        dataIndex: 'company_name',
        key: 'company_name',
        hideInSearch: true,
        width: 100,
      },
      {
        title: '统一社会信用代码',
        dataIndex: 'unisc_id',
        key: 'unisc_id',
        hideInSearch: true,
        width: 150,
      },
      {
        title: '法人姓名',
        dataIndex: 'corp_name',
        key: 'corp_name',
        hideInSearch: true,
        width: 120,
      },
      {
        title: '经办人姓名',
        dataIndex: 'transactor_name',
        key: 'transactor_name',
        hideInSearch: true,
        width: 120,
      },
      {
        title: '经办人证件号码',
        dataIndex: 'transactor_cr_num',
        key: 'transactor_cr_num',
        hideInSearch: true,
        width: 150,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        hideInSearch: true,
        render: (status: number) => _.find(statusOptions, (v) => v.value === status)?.label,
      },
    ];
  }, []);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getSignCredentialReportList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  function reset() {
    addFormRef.current?.setFieldsValue({
      name: '',
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
    <PageContainer>
      {/* <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 4 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={450}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setOpen(true);
        }}
      >
        新增
      </Button> */}
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default SignCredentialReport;
