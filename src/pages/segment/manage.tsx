import React from 'react';
import {
  getNumberSegment,
  editNumberSegment,
  delNumberSegment,
  addNumberSegment,
} from '@/services/numberSegment';
import PatternTable from '@/pages/component/PatternLayout';
import _ from 'lodash';
import { Select } from 'antd';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const phoneTypeOpts = [
  { value: 0, label: '固线' },
  { value: 1, label: '移动手机号' },
];
const sourceTypeOpts = [
  { value: 0, label: '历史IBG数据' },
  { value: 1, label: 'google开源库' },
  { value: 2, label: '手工录入' },
];

const Manage = () => {
  const { regionOptions = [], regionOptionsNationCode = [] } = useFetchCountryInfo();

  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '数据源类型',
      dataIndex: 'datasourceType',
      key: 'datasourceType',
      align: 'center',
      render: (sourceType: number) => _.find(sourceTypeOpts, (v) => v.value === sourceType)?.label,
    },
    {
      title: '号码类型',
      dataIndex: 'phoneType',
      key: 'phoneType',
      align: 'center',
      render: (phoneType: number) => _.find(phoneTypeOpts, (v) => v.value === phoneType)?.label,
    },
    {
      title: '国家码',
      dataIndex: 'countryCode',
      key: 'countryCode',
      align: 'center',
    },
    {
      title: '国家区域ID',
      dataIndex: 'nationCode',
      key: 'nationCode',
      align: 'center',
    },
    {
      title: '数据版本',
      dataIndex: 'version',
      key: 'version',
      align: 'center',
    },
    {
      title: '号段',
      dataIndex: 'prefix',
      key: 'prefix',
      align: 'center',
    },
    {
      title: '运营商',
      dataIndex: 'carrier',
      key: 'carrier',
      align: 'center',
    },
    {
      title: '最小合法号码长度',
      dataIndex: 'minValidLen',
      key: 'minValidLen',
      align: 'center',
    },
    {
      title: '最大合法号码长度',
      dataIndex: 'validLen',
      key: 'validLen',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'phoneType',
      label: '号码类型',
      renderType: 'select',
      isRequired: true,
      options: phoneTypeOpts,
      editOptinal: true,
    },
    {
      showOnAdd: true,
      name: 'countryCode',
      label: '国家码',
      isRequired: true,
      editOptinal: true,
      render: () => (
        <Select
          options={regionOptions.concat({
            label: '中国大陆_CN',
            value: 'CN',
          })}
          style={{ width: 200 }}
          placeholder="请选择"
          filterOption={(inputValue, option) => !!option?.label.includes(inputValue.toUpperCase())}
          showSearch
          allowClear
        />
      ),
    },
    {
      showOnAdd: true,
      name: 'nationCode',
      label: '国家区域ID',
      renderType: 'select',
      options: regionOptionsNationCode,
      isRequired: true,
      editOptinal: true,
    },
    {
      showOnAdd: true,
      name: 'version',
      label: '数据版本',
      isRequired: true,
      editOptinal: true,
    },
    {
      showOnAdd: true,
      name: 'prefix',
      label: '号段',
      isRequired: true,
      editOptinal: true,
    },
    {
      showOnAdd: true,
      name: 'carrier',
      label: '运营商',
      isRequired: true,
      editOptinal: true,
    },
    {
      showOnAdd: true,
      name: 'minValidLen',
      label: '最小合法号码长度',
      renderType: 'number',
      isRequired: true,
      editOptinal: true,
    },
    {
      showOnAdd: true,
      name: 'validLen',
      label: '最大合法号码长度',
      renderType: 'number',
      isRequired: true,
      editOptinal: true,
    },
  ];

  return (
    <PatternTable
      rowKey="id"
      getFn={async (vals) => {
        return await getNumberSegment({ ...vals });
      }}
      addFn={async (vals) => {
        return await addNumberSegment({ ...vals });
      }}
      editFn={async (__, vals) => {
        return await editNumberSegment({ ...vals });
      }}
      delFn={async (vals) => {
        return await delNumberSegment({ id: vals?.id });
      }}
      columns={columns}
      searchKeys={[
        {
          label: '国家码',
          name: 'countryCode',
          render: () => (
            <Select
              options={regionOptions.concat({
                label: '中国大陆_CN',
                value: 'CN',
              })}
              style={{ width: 200 }}
              placeholder="请选择"
              filterOption={(inputValue, option) =>
                !!option?.label.includes(inputValue.toUpperCase())
              }
              showSearch
              allowClear
            />
          ),
        },
        {
          label: '号段',
          name: 'prefix',
        },
        {
          label: '运营商',
          name: 'carrier',
        },
        {
          label: '来源',
          name: 'datasourceType',
          renderType: 'select',
          options: sourceTypeOpts,
        },
      ]}
      operateForm={operateForm}
      operType={0}
    />
  );
};

export default Manage;
