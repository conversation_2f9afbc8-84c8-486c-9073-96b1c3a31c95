import React, { useMemo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>lex, Table, message, Typography } from 'antd';
import _ from 'lodash';
import { compareGooglePhonePrefixOperator, addPhonePrefixOperator } from '@/services/numberSegment';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
const { Text } = Typography;

const CompareWithGoogle = (props: { onSuccess: () => void }) => {
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [addLoading, setAddLoading] = useState(false);
  const { onSuccess } = props;

  const {
    value: list = [[], []],
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const { data } = await compareGooglePhonePrefixOperator();
    const googleData = _.map(data, (el, i) => ({
      ...el.google_phone_prefix_carrier,
      uid: `id_${i}`,
    }));
    const tencentData = _.map(data, (el, i) => ({
      ...el.phone_prefix_operator,
      uid: `id_${i}`,
    }));
    return [tencentData, googleData];
  }, []);

  async function handleSync(data: any[]) {
    try {
      setAddLoading(true);
      const res = await addPhonePrefixOperator({
        params: _.map(data, (el) => ({ ..._.omit(el, ['uid']), datasource_type: 1 })),
      });
      setAddLoading(false);
      if (res.code === 0) {
        message.success('同步成功');
        setSelectedRows([]);
        setSelectedRowKeys([]);
        retry();
        onSuccess();
      }
    } catch (err) {
      setAddLoading(false);
    }
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: 'nationcode',
        dataIndex: 'nationcode',
        key: 'nationcode',
        valueType: 'select',
        width: 80,
        render: (text, row: any) => (
          <Text
            type={
              row.nationcode !== list[1].find((el) => el.uid === row.uid)?.nationcode
                ? 'danger'
                : undefined
            }
            ellipsis
          >
            {row.nationcode}
          </Text>
        ),
      },
      {
        title: '版本',
        dataIndex: 'version',
        key: 'version',
        width: 80,
        render: (text, row: any) => (
          <Text
            type={
              row.version !== list[1].find((el) => el.uid === row.uid)?.version
                ? 'danger'
                : undefined
            }
            ellipsis
          >
            {row.version}
          </Text>
        ),
      },
      {
        title: '号段',
        dataIndex: 'prefix',
        key: 'prefix',
        width: 120,
        render: (text, row: any) => (
          <Text
            type={
              row.prefix !== list[1].find((el) => el.uid === row.uid)?.prefix ? 'danger' : undefined
            }
            ellipsis
          >
            {row.prefix}
          </Text>
        ),
      },
      {
        title: '运营商',
        dataIndex: 'operator',
        key: 'operator',
        width: 280,
        render: (text, row: any) => <Text ellipsis>{row.operator}</Text>,
        render: (text, row: any) => (
          <Text
            type={
              row.operator !== list[1].find((el) => el.uid === row.uid)?.operator
                ? 'danger'
                : undefined
            }
            ellipsis
          >
            {row.operator}
          </Text>
        ),
      },
    ];
  }, [list]);

  const googleColumns = [
    ...columns,
    {
      title: '操作',
      key: 'operate',
      hideInSearch: true,
      fixed: 'right',
      width: 150,
      render: (text, row: any) => {
        return (
          <>
            <Button type="link" onClick={() => handleSync([row])}>
              同步至号码库
            </Button>
          </>
        );
      },
    },
  ];

  const tencentColumns = [
    ...columns,
    {
      title: '操作',
      key: 'operate',
      hideInSearch: true,
      fixed: 'right',
      width: 150,
      render: (text, row: any) => {
        return (
          <>
            <Button disabled type="link">
              删除
            </Button>
          </>
        );
      },
    },
  ];

  return (
    <Flex vertical={false} justify="space-between">
      <div>
        <h3 style={{ textAlign: 'center' }}>号码库</h3>
        <Button danger disabled>
          批量删除
        </Button>
        <Table
          columns={tencentColumns}
          dataSource={list[0]}
          rowKey="uid"
          loading={loading}
          pagination={false}
        />
      </div>
      <Divider type="vertical" />
      <div>
        <h3 style={{ textAlign: 'center' }}>谷歌库</h3>
        <Button type="primary" onClick={() => handleSync(selectedRows)} loading={addLoading}>
          批量同步至号码库
        </Button>
        <Table
          columns={googleColumns}
          dataSource={list[1]}
          rowKey="uid"
          loading={loading}
          pagination={false}
          rowSelection={{
            type: 'checkbox',
            selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRows(selectedRows);
              setSelectedRowKeys(selectedRowKeys);
            },
          }}
        />
      </div>
    </Flex>
  );
};
export default CompareWithGoogle;
