import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  addPhonePrefixOperator,
  getPhonePrefixOperator,
  editPhonePrefixOperator,
  deletePhonePrefixOperator,
} from '@/services/numberSegment';
import CompareWithGoogle from './component/compareWithGoogle';

type DataItem = {
  name: string;
  state: string;
};

const datasource = [
  { label: '人工录入', value: 0 },
  { label: 'google库同步', value: 1 },
];

const MncManage = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>();
  const [currentRow, setCurrentRow] = useState<any>({});
  const [type, setType] = useState<'create' | 'google' | 'edit'>('create');
  const [isModalOpen, setIsModalOpen] = useState(false);

  function handleEdit(row: any) {
    addFormRef.current?.setFieldsValue({
      ...row,
    });
    setInitialValues({
      ...row,
    });
    setOpen(true);
    setType('edit');
    setCurrentRow(row);
  }

  async function handleDelete(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除该号段吗?`,
        onOk: async () => {
          const res = await deletePhonePrefixOperator({
            id: row.id,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: 'nationcode',
        dataIndex: 'nationcode',
        key: 'nationcode',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          style: {
            width: 250,
          },
        },
      },
      {
        title: '号段',
        dataIndex: 'prefix',
        key: 'prefix',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          style: {
            width: 250,
          },
        },
      },
      {
        title: '运营商',
        dataIndex: 'operator',
        key: 'operator',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          style: {
            width: 250,
          },
        },
      },

      {
        title: '版本',
        dataIndex: 'version',
        key: 'version',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          style: {
            width: 250,
          },
        },
        hideInSearch: true,
      },
      {
        title: '创建时间',
        dataIndex: 'create_time',
        key: 'create_time',
        fieldProps: {
          style: {
            width: 250,
          },
        },
        hideInSearch: true,
      },
      {
        title: '更新时间',
        dataIndex: 'update_time',
        key: 'update_time',
        fieldProps: {
          style: {
            width: 250,
          },
        },
        hideInSearch: true,
      },
      {
        title: '来源',
        dataIndex: 'datasource_type',
        key: 'datasource_type',
        valueType: 'select',
        fieldProps: {
          options: datasource,
          style: {
            width: 250,
          },
        },
        render: (text, row: any) =>
          _.find(datasource, (v) => v.value === row.datasource_type)?.label,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleEdit(row)}>
                编辑
              </Button>
              <Button type="link" onClick={() => handleDelete(row)}>
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, []);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getPhonePrefixOperator({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'create' || type === 'google'
          ? await addPhonePrefixOperator({
              params: [
                {
                  ...vals,
                  datasource_type: type === 'google' ? 1 : 0,
                },
              ],
            })
          : await editPhonePrefixOperator({
              id: currentRow?.id,
              ...vals,
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const canEditKeys = ['version', 'prefix', 'operator'];
    return columns
      .filter((el) => ['nationcode', 'version', 'prefix', 'operator'].includes(el.key))
      .map((el) => {
        if (canEditKeys.includes(el.key) || type === 'create') {
          return el;
        }
        return {
          ...el,
          fieldProps: {
            ...el.fieldProps,
            disabled: true,
          },
        };
      });
  }, [columns, type]);

  useEffect(() => {
    const fields = formItems.map((el) => el.key);
    if (!open) {
      addFormRef.current?.setFieldsValue(_.zipObject(fields, Array(fields.length).fill('')));
    }
  }, [formItems, open]);

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  return (
    <PageContainer>
      <BetaSchemaForm<DataItem>
        title={type === 'create' ? '新增号段' : '编辑号段'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 4 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={600}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setOpen(true);
        }}
      >
        新增号段
      </Button>
      <Button
        style={{ marginLeft: 10 }}
        onClick={() => {
          setIsModalOpen(true);
        }}
      >
        对比谷歌运营商号段库
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
      <Modal
        title="Google运营商号段库对比"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
        width={1500}
        style={{ minWidth: 1500 }}
      >
        <CompareWithGoogle
          onSuccess={() => {
            actionRef.current?.reload();
          }}
        ></CompareWithGoogle>
      </Modal>
    </PageContainer>
  );
};
export default MncManage;
