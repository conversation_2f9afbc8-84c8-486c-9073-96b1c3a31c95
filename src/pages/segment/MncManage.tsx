import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';
import {
  addOperatorNetwork,
  getOperatorNetwork,
  editOperatorNetwork,
  deleteOperatorNetwork,
  getDiapatchMnc,
} from '@/services/numberSegment';
import { findLabel } from '@/utils/utils';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const MncManage = () => {
  const { regionOptionsMccCountryCode = [] } = useFetchCountryInfo();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const [mccMncInfo] = useMccMncInfo();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>();
  const [type, setType] = useState<'create' | 'edit' | 'bind'>('create');
  const [dispatchMncOptions, setDispatchMncOptions] = useState<any[]>([]);

  function handleEdit(row: any) {
    addFormRef.current?.setFieldsValue({
      ...row,
      country_code: `${row.country_code}_${row.mcc}`,
    });
    setInitialValues({
      ...row,
      country_code: `${row.country_code}_${row.mcc}`,
    });
    setOpen(true);
    setType('edit');
  }

  async function handleDelete(row: any) {
    try {
      Modal.confirm({
        title: '提示',
        content: `确定删除该mnc吗?`,
        onOk: async () => {
          const res = await deleteOperatorNetwork({
            mcc: row.mcc,
            country_code: row.country_code,
            dispatch_mnc: row.dispatch_mnc,
            mnc: row.mnc,
          });
          if (res.code === 0) {
            actionRef.current?.reload();
          }
        },
      });
    } catch (err) {}
  }

  const columns: any = useMemo(() => {
    return [
      {
        title: '国家',
        dataIndex: 'country_code',
        key: 'country_code',
        valueType: 'select',
        render: (text, row: any) =>
          findLabel(regionOptionsMccCountryCode, `${row.country_code}_${row.mcc}`),
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          options: regionOptionsMccCountryCode,
          style: {
            width: 250,
          },
          showSearch: true,
        },
      },
      {
        title: 'mcc',
        dataIndex: 'mcc',
        key: 'mcc',
        fieldProps: {
          style: {
            width: 250,
          },
        },
        hideInSearch: true,
      },
      {
        title: '主用mnc',
        dataIndex: 'dispatch_mnc',
        key: 'dispatch_mnc',
        fieldProps: {
          style: {
            width: 250,
          },
        },
      },
      {
        title: 'mnc',
        dataIndex: 'mnc',
        key: 'mnc',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          style: {
            width: 250,
          },
        },
        hideInSearch: true,
      },
      {
        title: 'carrier',
        dataIndex: 'carrier',
        key: 'carrier',
        fieldProps: {
          style: {
            width: 250,
          },
        },
        // hideInSearch: true,
      },
      {
        title: 'operator',
        dataIndex: 'operator',
        key: 'operator',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          style: {
            width: 250,
          },
        },
        hideInSearch: true,
      },
      {
        title: '运营商名称',
        dataIndex: 'operator_name',
        key: 'operator_name',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          style: {
            width: 250,
          },
        },
        // hideInSearch: true,
      },
      {
        title: '市占率(%)',
        dataIndex: 'market_share',
        key: 'market_share',
        valueType: 'digit',
        render: (text, row) => (row.dispatch_mnc === row.mnc ? row.market_share : '-'),
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
        fieldProps: {
          style: {
            width: 250,
          },
        },
        hideInSearch: true,
      },
      {
        title: '操作',
        key: 'operate',
        hideInSearch: true,
        fixed: 'right',
        render: (text, row: any) => {
          return (
            <>
              <Button type="link" onClick={() => handleEdit(row)}>
                编辑
              </Button>
              <Button type="link" onClick={() => handleDelete(row)}>
                删除
              </Button>
            </>
          );
        },
      },
    ];
  }, [mccMncInfo, regionOptionsMccCountryCode]);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getOperatorNetwork({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
      country_code: params.country_code?.split('_')[0],
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'create' || type === 'bind'
          ? await addOperatorNetwork({
              ...vals,
              country_code: vals.country_code.split('_')[0],
              mcc: vals.country_code.split('_')[1],
              dispatch_mnc:
                type === 'bind' ? JSON.parse(vals.dispatch_mnc)?.dispatch_mnc : vals.dispatch_mnc,
            })
          : await editOperatorNetwork({
              ...vals,
              country_code: vals.country_code.split('_')[0],
              mcc: vals.country_code.split('_')[1],
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const canEditKeys = ['carrier', 'operator', 'operator_name', 'market_share'];
    return [
      ...columns.filter((el) => !['mcc', 'operate', 'market_share'].includes(el.key)),
      {
        valueType: 'dependency',
        name: ['dispatch_mnc', 'mnc'],
        columns: ({ dispatch_mnc, mnc }: any) => {
          return dispatch_mnc === mnc
            ? [
                {
                  title: '市占率(%)',
                  dataIndex: 'market_share',
                  key: 'market_share',
                  valueType: 'digit',
                  formItemProps: {
                    rules: [
                      {
                        required: true,
                      },
                    ],
                  },
                  fieldProps: {
                    style: {
                      width: 250,
                    },
                  },
                  hideInSearch: true,
                },
              ]
            : [];
        },
      },
    ].map((el) => {
      if (canEditKeys.includes(el.key) || type === 'create') {
        return el;
      }
      return {
        ...el,
        fieldProps: {
          ...el.fieldProps,
          disabled: true,
        },
      };
    });
  }, [columns, type]);

  const bindFormItems = useMemo(() => {
    const disabledKeys = ['carrier', 'operator', 'operator_name'];
    return columns
      .filter((el) => !['mcc', 'operate', 'market_share'].includes(el.key))
      .map((el) => {
        if (disabledKeys.includes(el.key)) {
          return {
            ...el,
            fieldProps: {
              ...el.fieldProps,
              disabled: true,
            },
          };
        }
        if (el.key === 'dispatch_mnc') {
          return {
            ...el,
            valueType: 'select',
            fieldProps: {
              ...el.fieldProps,
              options: dispatchMncOptions,
            },
          };
        }
        return el;
      });
  }, [columns, dispatchMncOptions]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      country_code: '',
      mcc: '',
      dispatch_mnc: '',
      mnc: '',
      carrier: '',
      operator: '',
      operator_name: '',
      market_share: '',
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  async function getDispatchMncList(country_code) {
    const res = await getDiapatchMnc({ country_code });
    if (res.code === 0) {
      setDispatchMncOptions(
        res.data.map((el) => ({
          label: `${el.operator_name}(${el.dispatch_mnc})`,
          value: JSON.stringify(el),
        })),
      );
    }
  }

  return (
    <PageContainer>
      <BetaSchemaForm
        shouldUpdate={false}
        onValuesChange={(values) => {
          if (type !== 'bind') return;
          if (values.country_code) {
            addFormRef.current?.setFieldsValue({
              dispatch_mnc: undefined,
              operator: undefined,
              operator_name: undefined,
              carrier: undefined,
            });
            getDispatchMncList(values.country_code.split('_')[0]);
          }
          if (values.dispatch_mnc) {
            const dispatch_mnc = JSON.parse(values.dispatch_mnc);
            addFormRef.current?.setFieldsValue({
              operator: dispatch_mnc.operator,
              operator_name: dispatch_mnc.operator_name,
              carrier: dispatch_mnc.carrier,
            });
          }
        }}
        title={type === 'bind' ? '绑定主mnc' : type === 'create' ? '新增mnc' : '编辑mnc'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 4 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={type === 'bind' ? bindFormItems : formItems}
        width={600}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setOpen(true);
        }}
      >
        新增mnc
      </Button>
      <Button
        style={{ marginLeft: 10 }}
        onClick={() => {
          setType('bind');
          setOpen(true);
        }}
      >
        绑定主mnc
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey={(row) => `${row.country_code}_${row.mnc}`}
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};
export default MncManage;
