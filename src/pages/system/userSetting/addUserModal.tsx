import React, { useState, useEffect } from 'react';
import { Input, Form, Select, Modal, message } from 'antd';
import type { Dispatch } from 'umi';
import { connect } from 'umi';
import type { ConnectState } from '@/models/connect';
import AuthApiSelect from '@/components/AuthApiSelect';
// import AuthPageSelect from '@/components/AuthPageSelect';
import { queryAuthList } from '@/services/api';
import { addUser, addAuth, queryRoleList, editUserInfo } from '../../../services/api';

const { Option } = Select;

interface Props {
  reload: () => void;
  userInfo?: any;
  dispatch: Dispatch;
  apiList: any[];
  dialogRef: any;
}

const AddUser = (props: Props) => {
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [roleList, setRoleList] = useState<any[]>([]);
  const [form] = Form.useForm();
  const [authValue, setAuthValue] = useState<any[]>([]);
  // const [authPageValue, setAuthPageValue] = useState<any[]>([]);
  const [authList, setAuthList] = useState<any[]>([]);
  // const [authPageList, setAuthPageList] = useState<any[]>([]);
  const { userInfo, reload, dispatch, apiList, dialogRef } = props;

  useEffect(() => {
    getRoleList();
  }, []);
  useEffect(() => {
    initAuth();
  }, [userInfo?.staff_name]);
  useEffect(() => {
    dialogRef.current = {
      toggleModalVisible: () => {
        setIsModalVisible(true);
      },
    };
    initAuth();
    if (!isModalVisible) {
      form.resetFields();
    } else {
      if (dispatch) {
        dispatch({
          type: 'global/fetchApiList',
          payload: {
            page_size: 1000,
            page_index: 1,
          },
        });
      }
      if (userInfo?.staff_name) {
        getUserAuthList();
      }
    }
  }, [isModalVisible]);
  async function getRoleList() {
    const res = await queryRoleList({ page_index: 1, page_size: 1000 });
    setRoleList(res.data.list);
  }
  async function onSubmit(vals: any) {
    try {
      const params = { ...vals };
      let user_id = userInfo?.user_id;
      if (vals.roles?.length) {
        params.roles = vals.roles.join(',');
      } else {
        delete params.roles;
      }
      if (userInfo) {
        // 编辑角色信息
        if (params.roles) {
          await editUserInfo({
            user_id,
            ...params,
          });
        }
      } else {
        // 新增用户
        const res = await addUser({ ...params });
        user_id = res.data.user_id;
        if (res.code !== 0) return;
      }
      if (authValue.length) {
        // 编辑用户权限
        await addAuth({
          user_id,
          api_ids: authValue.join(','),
          // pages: authPageValue.join(','),
          appids: vals.appids,
          sdkappids: vals.sdkappids,
          type: 'user',
        });
      }
      setIsModalVisible(false);
      reload();
    } catch (e) {
      message.error(e);
    }
  }
  async function initAuth() {
    if (!userInfo) return;
    form.setFieldsValue({
      staff_name: userInfo.staff_name,
      roles: userInfo.roles ? userInfo.roles.split(',') : [],
    });
  }
  // 拉取用户所有权限列表
  async function getUserAuthList() {
    const res = await queryAuthList({
      user_id: userInfo.user_id,
      page_index: 1,
      page_size: 1000,
    });
    setAuthList(res.data.list);
    // setAuthPageList(res.data.page);
  }
  return (
    <Modal
      title={userInfo ? '新增配置' : '新增用户'}
      open={isModalVisible}
      onOk={() => form.submit()}
      onCancel={() => setIsModalVisible(false)}
    >
      <Form
        labelCol={{ span: 6 }}
        form={form}
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="staff_name" label="用户名" rules={[{ required: true }]}>
          <Input placeholder="请输入用户名" disabled={userInfo} style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="roles" label="角色">
          <Select placeholder="请选择角色" mode="multiple" allowClear style={{ width: 300 }}>
            {(roleList ?? []).map((el: any) => {
              return (
                <Option value={el.role} key={el.role}>
                  {el.role_name}
                </Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item name="appids" label="appid白名单">
          <Input placeholder="腾讯云appid白名单，用,隔开" style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="sdkappids" label="sdkappid白名单">
          <Input placeholder="腾讯云sdkappid白名单，用,隔开" style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="api_ids" label="自定义接口权限">
          <AuthApiSelect
            authList={authList}
            apiList={apiList}
            authValueChange={(val: any) => setAuthValue(val)}
          />
        </Form.Item>
        {/* <Form.Item name="api_ids" label="自定义页面权限">
          <AuthPageSelect
            authList={authPageList}
            // apiList={apiList}
            authValueChange={(val: any) => setAuthPageValue(val)}
          />
        </Form.Item> */}
      </Form>
    </Modal>
  );
};
export default connect(({ global }: ConnectState) => ({
  apiList: global.ApiList || [],
}))(AddUser);
