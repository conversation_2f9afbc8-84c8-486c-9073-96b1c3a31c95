import React, { useState, useEffect, Fragment, useRef, useCallback } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Input, Button, Table, Tag, Form } from 'antd';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { queryApiList, queryUserList } from '@/services/api';
import AddApiModal from './apiSetting/addApiModal';
import _ from 'lodash';
import { useSetState } from 'react-use';
import { isMobile } from '@/const/jadgeUserAgent';

// @ts-ignore
const ApiSetting = () => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [searchKey, setSearchKey] = useSetState<{
    api_name?: string;
    name?: string;
    route?: string;
    module_name?: string;
    page_size: number;
    page_index: number;
  }>({
    page_size: 10,
    page_index: 1,
  });
  const [operateType, setOperateType] = useState<number>(0);
  const moadlEl = useRef<{
    toggleModalVisible: (arg?: any) => void;
  }>({ toggleModalVisible: () => {} });
  const [apiList, setApiList] = useState<any[]>([]);
  const [apiCount, setApiCount] = useState<number>(0);
  const [userList, setUserLIst] = useState([]);

  const columns = [
    {
      title: '接口id',
      dataIndex: 'api_id',
      key: 'api_id',
      align: 'center',
    },
    {
      title: '接口中文名',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '模块名称',
      dataIndex: 'module_name',
      key: 'module_name',
      align: 'center',
    },
    {
      title: '接口名称',
      dataIndex: 'api_name',
      key: 'api_name',
      align: 'center',
    },
    {
      title: '路由',
      dataIndex: 'route',
      key: 'route',
      align: 'center',
    },
    {
      title: '审核人',
      dataIndex: 'auditors',
      key: 'auditors',
      align: 'center',
      width: '20%',
      render: (auditors: any) => (
        <div style={{ wordWrap: 'break-word', wordBreak: 'break-word', minWidth: '80px' }}>
          {auditors || '-'}
        </div>
      ),
    },
    {
      title: '审核白名单',
      dataIndex: 'white_users',
      key: 'white_users',
      render: (white_users: any) => (
        <div style={{ wordWrap: 'break-word', wordBreak: 'break-word', minWidth: '80px' }}>
          {white_users || '-'}
        </div>
      ),
      align: 'center',
    },
    {
      title: '是否记录日志',
      dataIndex: 'record_status',
      key: 'record_status',
      render: (record_status: any) =>
        record_status === 1 ? <Tag color="green">记录</Tag> : <Tag color="orange">不记录</Tag>,
      align: 'center',
    },
    {
      title: '是否紧急提醒',
      dataIndex: 'urgent',
      key: 'urgent',
      render: (urgent: any) =>
        urgent === 1 ? <Tag color="green">是</Tag> : <Tag color="orange">否</Tag>,
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      render: (remark: any) => remark || '-',
      align: 'center',
    },
    {
      title: '操作',
      render: (row: any) => {
        return (
          <Fragment>
            <Button
              type="link"
              onClick={() => {
                moadlEl?.current?.toggleModalVisible(row);
                setOperateType(0);
              }}
            >
              编辑
            </Button>
          </Fragment>
        );
      },
      align: 'center',
    },
  ];
  useEffect(() => {
    queryUserList({
      page_index: 1,
      page_size: 1000,
    }).then((res) => {
      const { list } = res.data;

      setUserLIst(list);
    });
  }, []);

  const getList = useCallback(async () => {
    try {
      setIsLoading(true);
      const params = _.pickBy(searchKey, (item) => item !== '' && !_.isNil(item));
      const res = await queryApiList({ ...params });
      setApiList(res.data.list);
      setApiCount(res.data.count);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  }, [searchKey]);

  useEffect(() => {
    getList();
  }, [getList]);

  return (
    <PageContainer title={false}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 20 }}>
        <Form
          labelCol={{ span: 6 }}
          form={form}
          layout="inline"
          labelAlign="right"
          onFinish={(vals) => {
            setSearchKey({ ...vals, page_index: 1 });
          }}
        >
          <Form.Item name="api_name">
            <Input style={{ width: 120 }} placeholder="输入接口名称" />
          </Form.Item>
          <Form.Item name="name">
            <Input style={{ width: 150 }} placeholder="接口中文名" />
          </Form.Item>
          <Form.Item name="route">
            <Input style={{ width: 160 }} placeholder="路由" />
          </Form.Item>
          <Form.Item name="module_name">
            <Input style={{ width: 160 }} placeholder="模块名称" />
          </Form.Item>
          <Form.Item>
            <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={isLoading}>
              查询
            </Button>
          </Form.Item>
        </Form>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            // @ts-ignore
            moadlEl?.current?.toggleModalVisible();
            setOperateType(1);
          }}
        >
          新增接口
        </Button>
      </div>
      <Table
        // @ts-ignore
        columns={columns}
        dataSource={apiList}
        rowKey={(row) => row.api_id}
        loading={isLoading}
        pagination={{
          current: searchKey.page_index,
          total: apiCount,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKey({ page_size }),
          onChange: (page_index) => {
            setSearchKey({ page_index });
          },
        }}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
      />
      <AddApiModal ref={moadlEl} getList={getList} operateType={operateType} userList={userList} />
    </PageContainer>
  );
};

export default ApiSetting;
