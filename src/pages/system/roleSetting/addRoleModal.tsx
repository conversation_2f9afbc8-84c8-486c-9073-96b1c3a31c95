import React, { useState, useEffect } from 'react';
import { Input, Form, Modal, message } from 'antd';
import type { Dispatch } from 'umi';
import { connect } from 'umi';
import type { ConnectState } from '@/models/connect';
import AuthApiSelect from '@/components/AuthApiSelect';
// import AuthPageSelect from '@/components/AuthPageSelect';
import { queryAuthList } from '@/services/api';
import { addRole, addAuth } from '../../../services/api';

interface Props {
  reload: () => void;
  roleInfo?: any;
  dispatch: Dispatch;
  apiList: any[];
  dialogRef: any;
}

const AddRoleModal = (props: Props) => {
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [authApiValue, setAuthApiValue] = useState<any[]>([]);
  // const [authPageValue, setAuthPageValue] = useState<any[]>([]);
  const [authApiList, setAuthApiList] = useState<any[]>([]);
  // const [authPageList, setAuthPageList] = useState<any[]>([]);
  const { roleInfo, reload, dispatch, apiList, dialogRef } = props;

  useEffect(() => {
    initAuth();
  }, [roleInfo?.role]);
  useEffect(() => {
    dialogRef.current = {
      toggleModalVisible: () => {
        setIsModalVisible(true);
      },
    };
    initAuth();
    if (!isModalVisible) {
      form.resetFields();
    } else {
      if (dispatch) {
        dispatch({
          type: 'global/fetchApiList',
          payload: {
            page_size: 1000,
            page_index: 1,
          },
        });
      }
      if (roleInfo?.role) {
        getRoleAuthList();
      }
    }
  }, [isModalVisible]);
  async function onSubmit(vals: any) {
    try {
      if (!roleInfo) {
        const res = await addRole({ ...vals });
        if (res.code !== 0) {
          return;
        }
      }
      if (authApiValue.length) {
        await addAuth({
          role: vals.role,
          api_ids: authApiValue.join(','),
          // pages: authPageValue.join(','),
          appids: vals.appids,
          sdkappids: vals.sdkappids,
          type: 'role',
        });
      }
      setIsModalVisible(false);
      reload();
    } catch (e) {
      message.error(e);
    }
  }
  async function initAuth() {
    if (!roleInfo) return;
    form.setFieldsValue({
      name: roleInfo.name,
      role: roleInfo.role,
    });
  }
  // 拉取角色所有权限列表
  async function getRoleAuthList() {
    const res = await queryAuthList({
      role: roleInfo.role,
      page_index: 1,
      page_size: 1000,
    });
    setAuthApiList(res.data.list);
    // setAuthPageList(res.data.page);
  }
  return (
    <Modal
      title={roleInfo ? '新增配置' : '新增角色'}
      open={isModalVisible}
      onOk={() => form.submit()}
      onCancel={() => setIsModalVisible(false)}
    >
      <Form
        labelCol={{ span: 6 }}
        form={form}
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="name" label="角色中文名" rules={[{ required: true }]}>
          <Input placeholder="请输入角色中文名" disabled={roleInfo?.name} style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="role" label="角色英文名" rules={[{ required: true }]}>
          <Input placeholder="角色英文名" disabled={roleInfo?.role} style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="appids" label="appid白名单">
          <Input placeholder="腾讯云appid白名单，用,隔开" style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="sdkappids" label="sdkappid白名单">
          <Input placeholder="腾讯云sdkappid白名单，用,隔开" style={{ width: 300 }} />
        </Form.Item>
        <Form.Item name="api_ids" label="选择接口权限">
          <AuthApiSelect
            authList={authApiList}
            apiList={apiList}
            authValueChange={(val: any) => setAuthApiValue(val)}
          />
        </Form.Item>
        {/* <Form.Item name="api_ids" label="选择页面权限">
          <AuthPageSelect
            authList={authPageList}
            // apiList={apiList}
            authValueChange={(val: any) => setAuthPageValue(val)}
          />
        </Form.Item> */}
      </Form>
    </Modal>
  );
};
export default connect(({ global }: ConnectState) => ({
  apiList: global.ApiList || [],
}))(AddRoleModal);
