import React, { useState, useEffect, useRef, Fragment } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Tag, Card, Space, message } from 'antd';
import { PlusOutlined, FormOutlined } from '@ant-design/icons';
import AddRoleModal from './addRoleModal';
import AuthEditModal from '@/components/AuthEditModal';
import { queryAuthList, editAuth } from '@/services/api';
import { useQuery } from '@/utils/react-use/useQuery';

const RoleAuthList: React.FC = () => {
  const { role, name } = useQuery();
  const [loading, setLoading] = useState<boolean>(false);
  const [count, setCount] = useState<number>(0);
  const [authlist, setAuthList] = useState<any[]>([]);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  // const [staffName, setStaffName] = useState<string>('')
  // const [roles, setRoles] = useState<string>('')
  const moadlEl = useRef<React.ReactNode>(null);
  const editEl = useRef<React.ReactNode>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);

  async function getRoleAuthList() {
    setLoading(true);
    const res: any = await queryAuthList({
      role,
      page_index: pageIndex,
      page_size: pageSize,
    });
    setCount(res.data.count);
    setAuthList(res.data.list);
    setLoading(false);
  }
  function onSelectChange(val: any) {
    setSelectedRowKeys(val);
  }
  async function onChangeAuthStatus(row: any) {
    await editAuth({
      conf_ids: `${row.conf_id}`,
      // user_id: Number(user_id),
      status: row.auth_status === 0 ? 1 : 0,
    });
    getRoleAuthList();
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: (val: any) => onSelectChange(val),
  };

  const columns: any = [
    {
      title: '配置id',
      dataIndex: 'conf_id',
      key: 'conf_id',
      align: 'center',
    },
    {
      title: '接口中文名',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '模块名称',
      dataIndex: 'module_name',
      key: 'module_name',
      align: 'center',
    },
    {
      title: '接口名称',
      dataIndex: 'api_name',
      key: 'api_name',
      align: 'center',
    },
    {
      title: '路由',
      dataIndex: 'route',
      key: 'route',
      align: 'center',
    },
    {
      title: 'appids',
      dataIndex: 'appids',
      key: 'appids',
      align: 'center',
    },
    {
      title: 'sdkappids',
      dataIndex: 'sdkappids',
      key: 'sdkappids',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'auth_status',
      key: 'auth_status',
      render: (auth_status: any) =>
        auth_status === 0 ? <Tag color="green">正常</Tag> : <Tag color="orange">禁用</Tag>,
      align: 'center',
    },
    {
      title: '操作',
      render: (row: any) => {
        return (
          <Fragment>
            {/* <Button type="link" onClick={() => onChangeUserStatus(row)}>{row.status === 0 ? '禁用' : '启用'}</Button> */}
            <Button type="link" onClick={() => onChangeAuthStatus(row)}>
              {row.auth_status === 0 ? '禁用' : '启用'}
            </Button>
            <Button
              type="link"
              onClick={() => {
                // @ts-ignore
                editEl?.current?.toggleModalVisible(row);
              }}
            >
              编辑
            </Button>
          </Fragment>
        );
      },
      align: 'center',
    },
  ];

  useEffect(() => {
    getRoleAuthList();
  }, [pageSize, pageIndex]);

  return (
    <PageContainer title={false}>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <span>
              <span style={{ color: '#999' }}>角色名：</span>
              {name}
            </span>
            {/* <span><span style={{color: '#999'}}>角色：</span>{name || '-'}</span> */}
          </Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              // @ts-ignore
              moadlEl?.current?.toggleModalVisible(0);
            }}
          >
            新增配置
          </Button>
        </div>
      </Card>
      <Button
        style={{ margin: '20px 0' }}
        type="primary"
        icon={<FormOutlined />}
        onClick={() => {
          if (!selectedRowKeys.length) {
            message.warning('请至少选择一项');
            return;
          }
          // @ts-ignore
          editEl?.current?.toggleModalVisible(selectedRowKeys);
        }}
      >
        批量编辑
      </Button>
      <Table
        columns={columns}
        dataSource={authlist}
        loading={loading}
        rowKey={(record) => record.conf_id}
        rowSelection={rowSelection}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPageSize(page_size),
          onChange: (page) => {
            setPageIndex(page);
          },
        }}
      />
      <AddRoleModal
        dialogRef={moadlEl}
        reload={() => {
          getRoleAuthList();
        }}
        roleInfo={{ role, name }}
      />
      <AuthEditModal dialogRef={editEl} reload={() => getRoleAuthList()} />
    </PageContainer>
  );
};

export default RoleAuthList;
