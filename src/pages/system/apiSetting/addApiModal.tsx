import React, { useState, useImperativeHandle, useEffect, useMemo } from 'react';
import { Input, Modal, Form, Radio, Select } from 'antd';
import { addApi, editApi } from '../../../services/api';
import _ from 'lodash';

interface Props {
  getList: () => void;
  operateType: number;
  userList: any;
}

const AddApiModal = React.forwardRef((props: Props, ref) => {
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [recordStatus] = useState<number>(1);
  const [apiId, setApiId] = useState<number>();

  useImperativeHandle(ref, () => ({
    toggleModalVisible: (row?: any) => {
      setIsModalVisible((prev) => !prev);
      if (row) {
        const params = {
          ...row,
          auditor_ids: (() => {
            return row.auditor_ids ? row.auditor_ids.split(',').map((el: any) => Number(el)) : [];
          })(),
          white_ids: (() => {
            return row.white_ids ? row.white_ids.split(',').map((el: any) => Number(el)) : [];
          })(),
        };
        form.setFieldsValue({ ...params });
        setApiId(row.api_id);
      }
    },
  }));
  useEffect(() => {
    if (!isModalVisible) {
      form.resetFields();
    }
  }, [isModalVisible]);
  async function onAddApi(vals: any) {
    const values = { ...vals };
    Object.keys(values).forEach((el: any) => {
      if (values[el] === '') delete values[el];
    });
    try {
      if (props.operateType === 0) {
        const params = {
          api_id: apiId,
          ...values,
          auditor_ids: values.auditor_ids?.length ? values.auditor_ids?.join(',') : '',
          white_ids: values.white_ids?.length ? values.white_ids?.join(',') : '',
        };
        await editApi({ ...params, all_site: undefined });
      } else {
        const params = {
          ...values,
          auditor_ids: values.auditor_ids?.length ? values.auditor_ids?.join(',') : '',
          white_ids: values.white_ids?.length ? values.white_ids?.join(',') : '',
        };
        !params.auditor_ids && delete params.auditor_ids;
        !params.white_ids && delete params.white_ids;
        await addApi({ ...params });
      }
    } catch (e) {
      // message.error(e.mess);
    }
    setIsModalVisible(false);
    props.getList();
  }

  const auditList = useMemo(() => {
    const result: any = [];
    const hasAuditroles = ['admin', 'auditors', 'developer', 'smsconfigDeveloper'];
    (props.userList ?? []).forEach((el: any) => {
      if (_.intersection(el.roles.split(','), hasAuditroles).length > 0) {
        result.push(el);
      }
    });
    return result;
  }, [props.userList]);

  return (
    <Modal
      title={props.operateType === 0 ? '编辑接口' : '新增接口'}
      open={isModalVisible}
      onOk={() => form.submit()}
      onCancel={() => setIsModalVisible(false)}
    >
      <Form
        labelCol={{ span: 6 }}
        form={form}
        onFinish={(vals) => onAddApi(vals)}
        initialValues={{ record_status: 1, urgent: 0, all_site: 1 }}
      >
        <Form.Item name="name" label="接口中文名" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item name="module_name" label="模块名称">
          <Input />
        </Form.Item>
        <Form.Item name="api_name" label="接口名称" rules={[{ required: true }]}>
          <Input disabled={props.operateType === 0} />
        </Form.Item>
        <Form.Item name="route" label="路由名称">
          <Input disabled={props.operateType === 0} />
        </Form.Item>
        <Form.Item name="auditor_ids" label="审核人">
          <Select
            options={(auditList ?? []).map((el: any) => ({
              value: el.user_id,
              label: el.staff_name,
            }))}
            placeholder="请选择"
            mode="multiple"
            allowClear
            style={{ width: 300 }}
            filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
          />
        </Form.Item>
        <Form.Item name="white_ids" label="审核人白名单">
          <Select
            options={(props.userList ?? []).map((el: any) => ({
              value: el.user_id,
              label: el.staff_name,
            }))}
            placeholder="请选择"
            mode="multiple"
            allowClear
            style={{ width: 300 }}
            filterOption={(val, opt: any) => opt.label.toLowerCase().includes(val.toLowerCase())}
          />
        </Form.Item>
        <Form.Item name="record_status" label="记录操作日志">
          <Radio.Group value={recordStatus}>
            <Radio value={1}>记录</Radio>
            <Radio value={0}>不记录</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item name="urgent" label="是否紧急提醒">
          <Radio.Group>
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item name="all_site" label="站点">
          <Radio.Group disabled={props.operateType === 0}>
            <Radio value={1}>所有站点</Radio>
            <Radio value={0}>仅本站点</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item name="remark" label="备注">
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
});

export default AddApiModal;
