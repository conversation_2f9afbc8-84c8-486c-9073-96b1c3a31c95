import React, { useState, useEffect, Fragment, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Input, Button, Table, Tag, Popconfirm } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { history } from 'umi';
import { queryRoleList, editRoleInfo } from '../../services/api';
import AddRoleModal from './roleSetting/addRoleModal';
import { isMobile } from '@/const/jadgeUserAgent';

const { Search } = Input;

const RoleSetting: React.FC<{}> = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [count, setCount] = useState<number>(0);
  const [list, setList] = useState<any[]>([]);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchKey, setSearchKey] = useState<string>('');
  const moadlEl = useRef<React.ReactNode>(null);

  const columns = [
    {
      title: '角色中文名',
      dataIndex: 'role_name',
      key: 'role_name',
      align: 'center',
    },
    {
      title: '角色英文名',
      dataIndex: 'role',
      key: 'role',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: any) =>
        status === 0 ? <Tag color="green">正常</Tag> : <Tag color="orange">禁用</Tag>,
      align: 'center',
    },
    {
      title: '权限范围',
      render: (row: any) => (
        <Button
          type="link"
          onClick={() => {
            history.push(`/system/roleSetting-authList?role=${row.role}&name=${row.role_name}`);
          }}
        >
          编辑
        </Button>
      ),
      align: 'center',
    },
    {
      title: '创建日期',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
    },
    {
      title: '更新日期',
      dataIndex: 'updated_at',
      key: 'updated_at',
      align: 'center',
    },
    {
      title: '操作',
      render: (row: any) => {
        const operateDesc = row.status === 0 ? '禁用' : '启用';
        return (
          <Popconfirm
            title={`确定${operateDesc}该角色吗`}
            onConfirm={() => onChangeRoleStatus(row)}
            okText="确认"
            cancelText="取消"
          >
            <Button type="link" style={{ color: row.status === 0 ? '#d48806' : '#389e0d' }}>
              {operateDesc}
            </Button>
          </Popconfirm>
        );
      },
      align: 'center',
    },
  ];
  useEffect(() => {
    getList();
  }, [searchKey, pageIndex, pageSize]);
  async function getList() {
    setIsLoading(true);
    const params = {
      name: searchKey,
      page_size: pageSize,
      page_index: pageIndex,
    };
    // @ts-ignore
    if (!params.role) {
      delete params.role;
    }
    // @ts-ignore
    if (!params.name) {
      delete params.name;
    }
    const res = await queryRoleList({ ...params });
    setCount(res.data?.count);
    setList(res.data?.list);
    setIsLoading(false);
  }
  async function onChangeRoleStatus(row: any) {
    setIsLoading(true);
    await editRoleInfo({
      role: row.role,
      status: row.status === 0 ? 1 : 0,
    });
    getList();
    setIsLoading(false);
  }
  return (
    <PageContainer title={false}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 20 }}>
        <Search
          placeholder="输入角色名称"
          loading={isLoading}
          enterButton
          onSearch={(val) => setSearchKey(val)}
          style={{ width: 400 }}
        />
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            // @ts-ignore
            moadlEl?.current?.toggleModalVisible();
          }}
        >
          新增角色
        </Button>
      </div>
      <Table
        // @ts-ignore
        columns={columns}
        dataSource={list}
        rowKey={(record) => record.role}
        loading={isLoading}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPageSize(page_size),
          onChange: (page) => {
            setPageIndex(page);
          },
        }}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
      />
      <AddRoleModal dialogRef={moadlEl} reload={getList} />
    </PageContainer>
  );
};

export default RoleSetting;
