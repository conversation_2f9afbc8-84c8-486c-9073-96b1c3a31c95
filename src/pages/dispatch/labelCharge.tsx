import React from 'react';
import {
  deleteChargeLabel,
  editChargeLabel,
  getChargeLabel,
  addChargeLabel,
  getTypeStr,
} from '@/services/thrdAPI';
import PatternTable from '@/pages/component/PatternLayout';
import { Button } from 'antd';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { AddTypeStrDialog } from './component/AddTypeStrDialog';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import _ from 'lodash';

const columns: any = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
    align: 'center',
  },
  {
    title: 'qappid',
    dataIndex: 'qappid',
    key: 'qappid',
    align: 'center',
  },
  {
    title: '业务名称',
    dataIndex: 'billing_type_str',
    key: 'billing_type_str',
    align: 'center',
  },
  {
    title: '账号',
    dataIndex: 'account',
    key: 'account',
    align: 'center',
  },
  {
    title: '账号类型',
    dataIndex: 'type',
    key: 'type',
    align: 'center',
  },
  {
    title: '创建人',
    dataIndex: 'staffname',
    key: 'staffname',
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'created_time',
    key: 'created_time',
    align: 'center',
  },
];

const LabelCharge = () => {
  const dialogRef = useDialogRef();
  const { value: state, retry } = useAsyncRetryFunc(async () => {
    const res = await getTypeStr();
    return (res?.data || []).map((v: { billing_type_str: string }) => ({
      value: v.billing_type_str,
    }));
  }, []);

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'qappid',
      label: 'qappid',
      disabled: true,
      isRequired: false,
    },
    {
      showOnAdd: true,
      name: 'billing_type_str',
      label: '业务名称',
      isRequired: true,
      disabled: false,
      renderType: 'select',
      options: state,
      placeholder: '请选择',
    },
    {
      showOnAdd: true,
      name: 'account',
      label: '账号',
      isRequired: false,
      disabled: true,
    },
    {
      showOnAdd: true,
      name: 'type',
      label: '账号类型',
      disabled: true,
      isRequired: false,
      options: [{ value: 'sdkappid' }, { value: 'uin' }],
      renderType: 'select',
    },
  ];

  async function doEdit(vals: any) {
    return await editChargeLabel(_.pick(vals, ['id', 'billing_type_str']));
  }
  async function doAdd(vals: any) {
    return await addChargeLabel({ ...vals });
  }

  async function doDel(vals: any) {
    return await deleteChargeLabel({ id: vals.id });
  }
  async function getList(vals?: any) {
    return await getChargeLabel({ ...vals });
  }

  return (
    <>
      <PatternTable
        rowKey="id"
        upRender={() => (
          <Button
            type="primary"
            style={{ marginBottom: 10 }}
            onClick={() => {
              dialogRef.current.open();
            }}
          >
            添加业务名称
          </Button>
        )}
        getFn={getList}
        addFn={doAdd}
        editFn={doEdit}
        delFn={doDel}
        columns={columns}
        searchKeys={[
          {
            label: 'account',
            name: '账号',
          },
          {
            label: 'qappid',
            name: 'qappid',
          },
          {
            label: '业务名称',
            name: 'billing_type_str',
            renderType: 'select',
            options: state,
          },
          {
            label: '客户名称',
            name: 'customer_name',
          },
        ]}
        operateForm={operateForm}
        operType={1}
      />
      <AddTypeStrDialog dialogRef={dialogRef} onSuccess={retry} />
    </>
  );
};

export default LabelCharge;
