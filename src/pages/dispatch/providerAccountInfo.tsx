import React from 'react';
import PatternTable from '@/pages/component/PatternLayout';
import { addProviderAccount, deleteProviderAccount, getProviderAccount } from '@/services/provider';
import { accountTypes } from '@/const/const';
import _ from 'lodash';

const billTypes = [
  { value: 1, label: 'uid' },
  { value: 2, label: '签名' },
  { value: 3, label: 'uid与签名' },
  { value: 4, label: '供应商账号' },
];

const bizTypes = [
  { value: 0, label: '普通' },
  { value: 1, label: '金融游戏教育营销' },
  { value: 2, label: '信用卡' },
  { value: 3, label: '二类电商' },
  { value: 4, label: '金融' },
  { value: 5, label: '游戏' },
  { value: 6, label: '教育' },
];

const ProviderAccountInfo = () => {
  const columns: any = [
    {
      dataIndex: 'id',
      title: 'ID',
      key: 'id',
      align: 'center',
    },
    {
      dataIndex: 'provider_str',
      title: '账号',
      key: 'provider_str',
      align: 'center',
    },
    {
      dataIndex: 'alias',
      title: '简称',
      key: 'alias',
      align: 'center',
    },
    {
      dataIndex: 'name',
      title: '全称',
      key: 'name',
      align: 'center',
    },
    {
      dataIndex: 'check_bill_type',
      title: '对账方式',
      key: 'check_bill_type',
      align: 'center',
      render: (check_bill_type: number) =>
        _.find(billTypes, (v) => v.value === check_bill_type)?.label,
    },
    {
      dataIndex: 'provider',
      title: '账号描述',
      key: 'provider',
      align: 'center',
    },
    {
      dataIndex: 'account_type',
      title: '账号类型',
      key: 'account_type',
      align: 'center',
      render: (account_type: number) =>
        _.find(accountTypes, (v) => v.value === account_type)?.label,
    },
    {
      dataIndex: 'biz_type',
      title: '业务用途',
      key: 'biz_type',
      align: 'center',
      render: (normal_sms_module: number) =>
        _.find(bizTypes, (v) => v.value === normal_sms_module)?.label,
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'provider_id',
      label: '账号ID',
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'provider_str',
      label: '账号',
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'check_bill_type',
      label: '对账方式',
      isRequired: true,
      renderType: 'select',
      options: billTypes,
    },
    {
      showOnAdd: true,
      name: 'provider',
      label: '账号描述',
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'account_type',
      label: '账号类型',
      isRequired: true,
      renderType: 'select',
      options: accountTypes,
    },
    {
      showOnAdd: true,
      name: 'biz_type',
      label: '业务用途',
      isRequired: true,
      renderType: 'select',
      options: bizTypes,
    },
  ];

  async function doAdd(vals: any) {
    return await addProviderAccount({ ...vals });
  }

  async function doDel(vals: any) {
    return await deleteProviderAccount({ account_id: vals.id });
  }

  async function getList(vals?: any) {
    return await getProviderAccount({ ...vals });
  }

  return (
    <PatternTable
      rowKey="appid"
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        { label: '简称', name: 'alias' },
        { label: '全称', name: 'name' },
        { label: '账号', name: 'provider_str' },
      ]}
      operateForm={operateForm}
      operType={2}
    />
  );
};

export default ProviderAccountInfo;
