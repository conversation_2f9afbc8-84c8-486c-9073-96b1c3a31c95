import React, { useRef, useState } from 'react';
import { Button, Select, message, Modal } from 'antd';
import { ActionType, PageContainer, ProTable } from '@ant-design/pro-components';
import { findLabel } from '@/utils/utils';
import { isMobile } from '@/const/jadgeUserAgent';
import _ from 'lodash';
import { changeStatusProviderAccount, getProviderAccountList } from '@/services/thrdAPI';
import { enabledOptions, operator, allProvince } from './component/const';
import AddScheduleDialog from './component/addScheduleDialog';
import EditProviderAccountInfo from './component/editProviderAccountInfo';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { getExportData, saveCSV } from '../global-components/saveCsv';

const signTypeRenderMap = [
  {
    label: '自定义',
    value: 0,
  },
  {
    label: '报备',
    value: 1,
  },
];

const ProviderAccountList = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const editDialogRef = useDialogRef();
  const dispatchDialogRef = useDialogRef();
  const [exportLoading, setExportLoading] = useState(false);
  async function changeEnabled({ company_id, account }: any, enabled: number) {
    const map: { [key: string]: string } = {
      '0': '恢复接入',
      '2': '放弃接入',
    };
    Modal.confirm({
      title: '提示',
      content: `确定${map[enabled.toString()]}吗?`,
      onOk: async () => {
        const { code } = await changeStatusProviderAccount({
          company_id,
          account,
          enabled,
        });
        if (code === 0) {
          message.success('操作成功');
          actionRef.current?.reload();
        }
      },
    });
  }

  const columns: any = [
    {
      title: '供应商名称',
      // dataIndex: 'company_name',
      key: 'company_name',
      render: (row: any) => {
        return `${row.company_name}(${row.company_id})`;
      },
    },
    {
      title: '账号名称',
      dataIndex: 'account_name',
      key: 'account_name',
    },
    {
      title: '账号',
      dataIndex: 'account',
      key: 'account',
    },
    {
      title: '业务类型',
      dataIndex: 'business_type',
      key: 'business_type',
    },
    {
      title: '密码',
      dataIndex: 'password',
      key: 'password',
      hideInSearch: true,
    },
    {
      title: '接入IP',
      dataIndex: 'ip',
      key: 'ip',
      hideInSearch: true,
    },
    {
      title: '接入端口',
      dataIndex: 'port',
      key: 'port',
      hideInSearch: true,
    },
    {
      title: '支持省份',
      dataIndex: 'province',
      key: 'province',
      formItemProps: {
        label: '省份',
      },
      renderFormItem: () => <Select placeholder="请选择省份"></Select>,
      fieldProps: {
        options: allProvince,
        allowClear: true,
        showSearch: true,
      },
    },
    {
      title: '支持运营商',
      dataIndex: 'operator',
      key: 'operator',
      formItemProps: {
        label: '运营商',
      },
      renderFormItem: () => <Select placeholder="请选择运营商"></Select>,
      fieldProps: {
        options: operator,
        allowClear: true,
      },
    },
    {
      title: '接入码',
      dataIndex: 'access_code',
      key: 'access_code',
      hideInSearch: true,
    },
    {
      title: '服务ID',
      dataIndex: 'service_id',
      key: 'service_id',
      hideInSearch: true,
    },
    {
      title: '预留扩展位',
      dataIndex: 'extend_len',
      key: 'extend_len',
      hideInSearch: true,
    },
    {
      title: '移动tps',
      dataIndex: 'cmcc_tps',
      key: 'cmcc_tps',
      hideInSearch: true,
    },
    {
      title: '联通tps',
      dataIndex: 'cucc_tps',
      key: 'cucc_tps',
      hideInSearch: true,
    },
    {
      title: '电信tps',
      dataIndex: 'ctcc_tps',
      key: 'ctcc_tps',
      hideInSearch: true,
    },
    {
      title: '签名类型',
      dataIndex: 'sign_type',
      key: 'sign_type',
      hideInSearch: true,
      render: (val: string) => findLabel(signTypeRenderMap, val),
    },
    {
      title: '连接数',
      dataIndex: 'connection_num',
      key: 'connection_num',
      hideInSearch: true,
    },
    {
      title: '频率限制',
      dataIndex: 'freq_limit',
      key: 'freq_limit',
      hideInSearch: true,
    },
    {
      title: '是否关键字',
      dataIndex: 'is_keyword',
      key: 'is_keyword',
      hideInSearch: true,
      render: (val: number) => (val ? '是' : '否'),
    },
    {
      title: '是否黑名单',
      dataIndex: 'is_black_list',
      key: 'is_black_list',
      hideInSearch: true,
      render: (val: number) => (val ? '是' : '否'),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
    },
    {
      title: '提交人',
      dataIndex: 'submit_rtx',
      key: 'submit_rtx',
      hideInSearch: true,
    },
    {
      title: '提交时间',
      dataIndex: 'submit_time',
      key: 'submit_time',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'create_at',
      key: 'create_at',
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'update_at',
      key: 'update_at',
      hideInSearch: true,
    },
    {
      title: '对接状态',
      dataIndex: 'enabled',
      key: 'enabled',
      renderFormItem: () => <Select></Select>,
      fieldProps: {
        options: enabledOptions,
        allowClear: true,
      },
      render: (val: string) => findLabel(enabledOptions, val),
      // fixed: 'right',
      // width: 80,
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      width: 130,
      fixed: 'right',
      render: (row: any) => (
        <>
          <Button
            type="link"
            onClick={() => {
              editDialogRef.current?.open({
                row,
                onSuccess: () => {
                  actionRef.current?.reload();
                },
              });
            }}
          >
            编辑
          </Button>
          {row.enabled === 0 && (
            <Button type="link" onClick={() => changeEnabled(row, 2)}>
              放弃接入
            </Button>
          )}
          {row.enabled === 2 && (
            <Button type="link" onClick={() => changeEnabled(row, 0)}>
              恢复接入
            </Button>
          )}
        </>
      ),
      hideInSearch: true,
    },
  ];

  const requestFn = async (params: any) => {
    const { data } = await getProviderAccountList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_index: params.current,
      page_size: params.pageSize,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  };

  async function handleExport() {
    try {
      setExportLoading(true);
      const params = formRef.current?.getFieldsValue();
      const res = await getProviderAccountList({
        ..._.omit(
          _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
          ['pageSize', 'current'],
        ),
        page_index: 1,
        page_size: 1000,
      });
      const { head, data } = getExportData(columns, res.data.list ?? []);
      saveCSV(`通道信息${Date.now()}`, head, data, {
        params,
        route: 'dispatch/csms-schedule',
      })
        .then(() => {
          message.success('导出成功');
        })
        .catch(() => {
          message.error('导出失败');
        });
      setExportLoading(false);
    } catch (error) {
      console.log(error);
      setExportLoading(false);
    }
  }

  return (
    <PageContainer>
      <Button
        onClick={() => {
          dispatchDialogRef.current?.open({
            onSuccess: () => {
              actionRef.current?.reload();
            },
          });
        }}
        type="primary"
      >
        新增通道调度
      </Button>
      <Button style={{ marginLeft: 10 }} onClick={handleExport} loading={exportLoading}>
        导出
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapseRender: false,
          collapsed: false,
          span: 5,
        }}
        scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 2000 }}
        request={requestFn}
        form={{
          initialValues: {
            enabled: 0,
          },
        }}
        options={false}
      />
      <AddScheduleDialog dialogRef={dispatchDialogRef}></AddScheduleDialog>
      <EditProviderAccountInfo dialogRef={editDialogRef}></EditProviderAccountInfo>
    </PageContainer>
  );
};
export default ProviderAccountList;
