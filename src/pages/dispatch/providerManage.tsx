import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { addProviderInfo, editProviderInfo, getProviderInfo } from '@/services/provider';
import { Button } from 'antd';
import { ActionType, BetaSchemaForm, PageContainer, ProTable } from '@ant-design/pro-components';
import _ from 'lodash';

const validateEmails = (rule, value) => {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  const emails = value.split(';');
  for (const email of emails) {
    const trimmedEmail = email.trim();
    if (!emailRegex.test(trimmedEmail)) {
      return Promise.reject(`请输入正确的邮箱，多个邮箱英文分号分隔`);
    }
  }
  return Promise.resolve();
};

const ProviderAccountInfo = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<any>();
  const addFormRef = useRef<any>();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<any>();
  const [type, setType] = useState<'create' | 'edit'>('create');

  function handleEdit(row: any) {
    addFormRef.current?.setFieldsValue({
      ...row,
    });
    setInitialValues({
      ...row,
    });
    setOpen(true);
    setType('edit');
  }

  const columns: any = useMemo(() => {
    return [
      {
        dataIndex: 'id',
        title: 'id',
        key: 'id',
        align: 'center',
        hideInSearch: true,
        // 只读
        proFieldProps: {
          mode: 'read',
        },
      },
      {
        dataIndex: 'alias',
        title: '简称',
        key: 'alias',
        align: 'center',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        dataIndex: 'alias_pinyin',
        title: '拼音简称',
        key: 'alias_pinyin',
        align: 'center',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        dataIndex: 'name',
        title: '全称',
        key: 'name',
        align: 'center',
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        dataIndex: 'report_email',
        title: '报备邮箱',
        key: 'report_email',
        width: 200,
        align: 'center',
        hideInSearch: true,
        formItemProps: {
          rules: [
            {
              required: true,
            },
            {
              validator: validateEmails,
            },
          ],
        },
        fieldProps: {
          placeholder: '多个邮箱英文分号隔开',
        },
      },
      {
        dataIndex: 'email_file_secret',
        title: '邮箱附件密钥',
        key: 'email_file_secret',
        align: 'center',
        hideInSearch: true,
        formItemProps: {
          rules: [
            {
              required: true,
            },
          ],
        },
      },
      {
        dataIndex: 'created_time',
        title: '创建时间',
        key: 'created_time',
        align: 'center',
        hideInSearch: true,
      },
      {
        dataIndex: 'modified_time',
        title: '编辑时间',
        key: 'modified_time',
        hideInSearch: true,
        align: 'center',
      },
      {
        // dataIndex: 'operate',
        title: '操作',
        key: 'operate',
        align: 'center',
        hideInSearch: true,
        render: (text, record: any) => (
          <Button type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
        ),
      },
    ];
  }, []);

  const requestFn = useCallback(async (params: any) => {
    const { data } = await getProviderInfo({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      page_size: params.pageSize,
      page_index: params.current,
    });
    return {
      data: data.list ?? [],
      success: true,
      total: data.count,
    };
  }, []);

  async function onFinish(vals) {
    try {
      const res =
        type === 'create'
          ? await addProviderInfo({
              ...vals,
            })
          : await editProviderInfo({
              ...vals,
            });
      if (res.code === 0) {
        actionRef.current?.reload();
        return true;
      }
      return false;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  const formItems = useMemo(() => {
    const fields = ['alias', 'name', 'alias_pinyin', 'report_email', 'email_file_secret'];
    const keys = type === 'create' ? fields : fields.concat(['id']);
    return columns.filter((el) => keys.includes(el.key));
  }, [columns, type]);

  function reset() {
    addFormRef.current?.setFieldsValue({
      name: '',
      alias: '',
      alias_pinyin: '',
      report_email: '',
      email_file_secret: '',
    });
  }

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  return (
    <PageContainer>
      <BetaSchemaForm
        title={type === 'create' ? '新增' : '编辑'}
        formRef={addFormRef}
        open={open}
        onOpenChange={setOpen}
        layoutType="ModalForm"
        layout="horizontal"
        labelCol={{ span: 5 }}
        onFinish={onFinish}
        modalProps={{ maskClosable: false }}
        columns={formItems}
        width={550}
        initialValues={{ ...initialValues }}
      />
      <Button
        type="primary"
        onClick={() => {
          setType('create');
          setOpen(true);
        }}
      >
        新增
      </Button>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        columns={columns}
        rowKey="id"
        pagination={{
          defaultPageSize: 0,
          showSizeChanger: true,
        }}
        search={{
          collapsed: false,
          collapseRender: () => null,
        }}
        request={requestFn}
        options={false}
      />
    </PageContainer>
  );
};

export default ProviderAccountInfo;
