import React, { useMemo } from 'react';
import { Form, Select, Typography, message, Input, Switch, Row, Col, Button, Modal } from 'antd';

import _ from 'lodash';
import { getProviderAccountList, dispatchProviderAccount } from '@/services/thrdAPI';
import {
  allProvince,
  dispatchTypeOptions,
  envOptions,
  protocolOptions,
  signTagOptions,
  signTypeOptions,
} from './const';
import ScrollLoadMore from '@/components/ScrollLoadMore';
import SelectWithAddItem from '@/components/SelectWithAddItem';
import { useDialog } from '@/utils/react-use/useDialog';
const { Text } = Typography;

const AddScheduleDialog = (props: { dialogRef: DialogRef }) => {
  const [form] = Form.useForm();
  const [accountList, setAccountList] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState(false);
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    onSuccess?: () => void;
  }>(dialogRef);
  const { onSuccess } = defaultVal || {};

  function close() {
    form.resetFields();
    setShowState(false);
    setAccountList([]);
  }

  async function onFinish(values: any) {
    try {
      if (!values.general_account && !values.market_account) {
        message.error('请选择通道');
        return;
      }
      if (!values.cmcc_province && !values.cucc_province && !values.ctcc_province) {
        message.error('至少选择一个可用省份');
        return;
      }
      setLoading(true);
      const { code } = await dispatchProviderAccount({
        ...values,
        company_id: +values.company_id,
        is_direct: values.is_direct ? 1 : 0,
        is_transport: values.is_transport ? 1 : 0,
        is_group: values.is_group ? 1 : 0,
        cmcc_province: values.cmcc_province ?? '',
        cucc_province: values.cucc_province ?? '',
        ctcc_province: values.ctcc_province ?? '',
      });
      if (code === 0 || code === 2001) {
        setShowState(false);
        close();
        onSuccess?.();
      }
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }

  async function getAccountList(company_id: string) {
    const params = {
      page_index: 1,
      page_size: 100,
      company_id,
      enabled: 0,
    };
    const res = await getProviderAccountList({
      ..._.pickBy(params, (v) => !_.isNil(v) && v !== ''),
    });
    setAccountList(res?.data?.list);
  }

  const accountOptions = useMemo(() => {
    return accountList.map((el) => ({
      value: `${el.account}`,
      label: `${el.account_name}(${el.account})`,
    }));
  }, [accountList]);

  const province = useMemo(() => {
    const province: { [key: string]: any } = {};
    accountList.forEach((item) => {
      if (item.operator === '三网') {
        province[item.account] = {
          operator: '移动,联通,电信',
          province: item.province,
        };
      } else {
        province[item.account] = {
          operator: item.operator,
          province: item.province,
        };
      }
    });
    return province;
  }, [accountList]);

  return (
    <Modal width={1000} open={visible} footer={null} onCancel={close} destroyOnClose>
      <Form
        layout="horizontal"
        title="新增调度"
        labelAlign="right"
        style={{ maxWidth: 960 }}
        labelCol={{ span: 2 }}
        form={form}
        onFinish={(values) => onFinish(values)}
        initialValues={{
          protocol: 'cmpp',
          cmcc_province: '全国',
          cucc_province: '全国',
          ctcc_province: '全国',
        }}
      >
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.market_account !== curValues.market_account ||
            prevValues.general_account !== curValues.general_account
          }
        >
          {({ getFieldValue }) => {
            let accountNameOptions: { [key: string]: any } = {};
            const marketAccount = getFieldValue('market_account');
            const generalAccount = getFieldValue('general_account');
            const isSameOperator =
              marketAccount && generalAccount
                ? province[marketAccount]?.operator === province[generalAccount]?.operator
                : true;
            const isSameProvince =
              marketAccount && generalAccount
                ? province[marketAccount]?.province === province[generalAccount]?.province
                : true;
            if (marketAccount || generalAccount) {
              const support = generalAccount ? province[generalAccount] : province[marketAccount];
              const defaultProvince =
                _.find(allProvince, (item) => item.value === support.province)?.value ?? '';
              form.setFieldsValue({
                cmcc_province: support.operator.includes('移动') ? defaultProvince : '',
                cucc_province: support.operator.includes('联通') ? defaultProvince : '',
                ctcc_province: support.operator.includes('电信') ? defaultProvince : '',
              });
              const generalItem = accountList?.find((item) => item.account === generalAccount);
              const marketItem = accountList?.find((item) => item.account === marketAccount);
              accountNameOptions = {
                [generalAccount]: [generalItem?.account_name, generalItem?.business_type],
                [marketAccount]: [marketItem?.account_name, marketItem?.business_type],
              };
            } else {
              form.setFieldsValue({
                cmcc_province: '全国',
                cucc_province: '全国',
                ctcc_province: '全国',
              });
            }
            return (
              <>
                <Form.Item label="供应商" name="company_id" rules={[{ required: true }]}>
                  <ScrollLoadMore
                    style={{ width: 400 }}
                    onChange={(value) => {
                      form.setFieldsValue({
                        supplier: value,
                        general_account: undefined,
                        market_account: undefined,
                        market_client_name: undefined,
                        general_client_name: undefined,
                        cmcc_province: undefined,
                        cucc_province: undefined,
                        ctcc_province: undefined,
                      });
                      getAccountList(value);
                    }}
                    loadFn={async (params) => {
                      const pageParams = {
                        page_index: params.page_index,
                        page_size: params.page_size,
                        enabled: 0,
                        company_name: params.search_key,
                      };
                      const res = await getProviderAccountList({
                        ..._.pickBy(pageParams, (v) => !_.isNil(v) && v !== ''),
                      });
                      const options = _.uniqBy(res?.data?.list, 'company_id')?.map(
                        (item: Record<string, any>) => {
                          return {
                            value: `${item.company_id}`,
                            label: `${item.company_name}(${item.company_id})`,
                          };
                        },
                      );
                      return {
                        options,
                        total: res.data.count,
                      };
                    }}
                  />
                </Form.Item>
                <Form.Item label="通道" required>
                  <Form.Item
                    name="general_account"
                    style={{ display: 'inline-block', marginBottom: 0 }}
                  >
                    <Select
                      options={accountOptions.filter((item) => item.value !== marketAccount)}
                      style={{ width: 400 }}
                      placeholder="搜索选择行业通道"
                      showSearch
                      allowClear
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                    ></Select>
                  </Form.Item>
                  <Form.Item
                    name="market_account"
                    style={{ display: 'inline-block', marginBottom: 0, marginLeft: 10 }}
                  >
                    <Select
                      options={accountOptions.filter((item) => item.value !== generalAccount)}
                      style={{ width: 400 }}
                      placeholder="搜索选择营销通道"
                      showSearch
                      allowClear
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                    ></Select>
                  </Form.Item>
                  <Row>
                    {isSameOperator}
                    <span style={{ marginLeft: 10 }}>
                      {!isSameOperator && <Text type="danger">两个通道支持的运营商不一致</Text>}
                      {!isSameProvince && <Text type="danger">两个通道支持的省份不一致</Text>}
                    </span>
                  </Row>
                </Form.Item>
                <Form.Item label="客户名称">
                  <Form.Item
                    style={{ display: 'inline-block', marginBottom: 0 }}
                    name="general_client_name"
                  >
                    <SelectWithAddItem
                      options={accountNameOptions?.[generalAccount] ?? []}
                      placeholder="业务类型/客户名称"
                      style={{ width: 400 }}
                      disabled={!generalAccount}
                    ></SelectWithAddItem>
                  </Form.Item>
                  <Form.Item
                    style={{ display: 'inline-block', marginBottom: 0, marginLeft: 10 }}
                    name="market_client_name"
                  >
                    <SelectWithAddItem
                      options={accountNameOptions?.[marketAccount] ?? []}
                      placeholder="业务类型/客户名称"
                      style={{ width: 400 }}
                      disabled={!marketAccount}
                    ></SelectWithAddItem>
                  </Form.Item>
                </Form.Item>
                <Form.Item label="账号说明">
                  <Form.Item
                    noStyle
                    style={{ display: 'inline-block', marginBottom: 0 }}
                    name="general_remark"
                  >
                    <Input
                      placeholder="请输入"
                      style={{ width: 400 }}
                      disabled={!generalAccount}
                    ></Input>
                  </Form.Item>
                  <Form.Item
                    style={{ display: 'inline-block', marginBottom: 0, marginLeft: 10 }}
                    name="market_remark"
                  >
                    <Input
                      placeholder="请输入"
                      style={{ width: 400 }}
                      disabled={!marketAccount}
                    ></Input>
                  </Form.Item>
                </Form.Item>
                <Form.Item name="sign_tag" label="签名标记" rules={[{ required: true }]}>
                  <SelectWithAddItem
                    options={signTagOptions.map((item) => item.value)}
                    placeholder="业务类型/客户名称"
                    style={{ width: 400 }}
                  ></SelectWithAddItem>
                </Form.Item>
                <Form.Item name="sign_type" label="是否固签" rules={[{ required: true }]}>
                  <Select
                    options={signTypeOptions}
                    showSearch
                    allowClear
                    style={{ width: 400 }}
                  ></Select>
                </Form.Item>
                <Form.Item name="dispatch_type" label="调度类型" rules={[{ required: true }]}>
                  <Select
                    options={dispatchTypeOptions}
                    showSearch
                    allowClear
                    style={{ width: 400 }}
                  ></Select>
                </Form.Item>
                <Form.Item name="is_direct" label="是否直连">
                  <Switch></Switch>
                </Form.Item>
                <Form.Item name="is_transport" label="是否透传">
                  <Switch />
                </Form.Item>
                <Form.Item name="is_group" label="是否通道组">
                  <Switch />
                </Form.Item>
                <Form.Item name="protocol" label="协议" rules={[{ required: true }]}>
                  <Select options={protocolOptions} allowClear style={{ width: 400 }}></Select>
                </Form.Item>
                <Form.Item name="env" label="环境" rules={[{ required: true }]}>
                  <Select
                    options={envOptions}
                    style={{ width: 400 }}
                    placeholder="请选择环境"
                    allowClear
                  ></Select>
                </Form.Item>
                <Row>
                  <Col span={8}>
                    <Form.Item name="cmcc_province" label="移动可用省份" labelCol={{ span: 8 }}>
                      <Select
                        options={allProvince}
                        style={{ width: 190 }}
                        placeholder="请选择"
                        allowClear
                      ></Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="cucc_province" label="联通可用省份" labelCol={{ span: 8 }}>
                      <Select
                        options={allProvince}
                        style={{ width: 190 }}
                        placeholder="请选择"
                        allowClear
                      ></Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="ctcc_province" label="电信可用省份" labelCol={{ span: 8 }}>
                      <Select
                        options={allProvince}
                        style={{ width: 190 }}
                        placeholder="请选择"
                        allowClear
                      ></Select>
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item style={{ textAlign: 'center' }}>
                  <Button loading={loading} type="primary" htmlType="submit">
                    提交
                  </Button>
                  <Button style={{ marginLeft: 20 }} onClick={() => form.resetFields()}>
                    重置
                  </Button>
                </Form.Item>
              </>
            );
          }}
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default AddScheduleDialog;
