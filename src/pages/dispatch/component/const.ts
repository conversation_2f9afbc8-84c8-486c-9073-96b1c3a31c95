export const yesOrNO = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
];

export const signTagOptions = [
  { value: '固签' },
  { value: '自定义' },
  { value: '自签' },
  { value: '报备' },
];

export const signTypeOptions = [
  { label: '自定义签名', value: 0 },
  { label: '固定签名', value: 1 },
  { label: '无扩展透传', value: 3 },
  { label: '供应商自签2位', value: 4 },
  { label: '供应商自签3位', value: 5 },
  { label: '供应商自签4位', value: 6 },
];

export const dispatchTypeOptions = [
  { label: '精准营销', value: 99 },
  { label: '精准营销2', value: 101 },
  { label: '物联短信', value: 102 },
  { label: '普通正规', value: 666 },
];

export const protocolOptions = [
  { value: 'cmpp' },
  { value: 'smgp' },
  { value: 'sgip' },
  { value: 'http' },
];

export const envOptions = [
  { label: '正规正式', value: '1' },
  { label: '正规测试', value: '2' },
  { label: '金融正式', value: '3' },
  { label: '金融测试', value: '4' },
  { label: '受限连接', value: '6' },
  { label: '新正规正式', value: '7' },
  { label: '新金融正式', value: '8' },
  { label: '新受限连接', value: '9' },
  { label: '活动专用', value: '10' },
  { label: '物联正式', value: '11' },
];

export const enabledOptions = [
  { value: 0, label: '待接入' },
  { value: 1, label: '已接入' },
  { value: 2, label: '放弃接入' },
];

export const operator = [
  { value: '移动', abbr: 'cmcc' },
  { value: '联通', abbr: 'ctcc' },
  { value: '电信', abbr: 'cucc' },
];

export const allProvince = [
  { value: '全国' },
  { value: '山东' },
  { value: '江苏' },
  { value: '上海' },
  { value: '浙江' },
  { value: '安徽' },
  { value: '福建' },
  { value: '江西' },
  { value: '广东' },
  { value: '广西' },
  { value: '海南' },
  { value: '河南' },
  { value: '湖南' },
  { value: '湖北' },
  { value: '北京' },
  { value: '天津' },
  { value: '河北' },
  { value: '山西' },
  { value: '内蒙古' },
  { value: '宁夏' },
  { value: '青海' },
  { value: '陕西' },
  { value: '甘肃' },
  { value: '新疆' },
  { value: '四川' },
  { value: '贵州' },
  { value: '云南' },
  { value: '重庆' },
  { value: '西藏' },
  { value: '辽宁' },
  { value: '吉林' },
  { value: '黑龙江' },
];
