import React, { useEffect, useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, Input } from 'antd';
import { addTypeStr } from '@/services/thrdAPI';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export const AddTypeStrDialog = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [form] = Form.useForm();
  const [isConfirmLoading, setConfirmLoading] = useState(false);
  const [visible, setShowState] = useDialog(dialogRef);

  const handlerSubmit = async (vals: any) => {
    setConfirmLoading(true);
    try {
      const res = await addTypeStr({ ...vals });
      setConfirmLoading(false);
      setShowState(false);
      if (res?.code === 0) {
        onSuccess();
      }
    } catch (error) {
      setConfirmLoading(false);
    }
  };

  useEffect(() => {
    !visible && form.setFieldsValue({});
  }, [form, visible]);

  return (
    <>
      <Modal
        open={visible}
        confirmLoading={isConfirmLoading}
        onOk={() => form.submit()}
        onCancel={() => {
          setShowState(false);
          form.resetFields();
        }}
      >
        <Form
          form={form}
          labelAlign="right"
          onFinish={(vals) => handlerSubmit(vals)}
          style={{ maxHeight: 500, overflow: 'auto' }}
        >
          <Form.Item
            name="billing_type_str"
            key="billing_type_str"
            label="业务名称"
            rules={[{ required: true }]}
          >
            <Input style={{ width: 200 }} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
