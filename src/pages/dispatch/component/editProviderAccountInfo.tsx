import React, { useEffect } from 'react';
import { Form, Select, message, Input, Switch, Button, Flex, Modal } from 'antd';

import { editProviderAccount } from '@/services/thrdAPI';
import { allProvince, operator, signTypeOptions } from './const';
import { DialogRef, useDialog } from '@/utils/react-use/useDialog';
import _ from 'lodash';

const EditProviderAccountInfo = (props: { dialogRef: DialogRef }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    row: Record<string, any>;
    onSuccess?: () => void;
  }>(dialogRef);
  const { row, onSuccess } = defaultVal;

  useEffect(() => {
    visible && form.setFieldsValue(row);
  }, [visible, row, form]);

  function close() {
    form.resetFields();
    setShowState(false);
  }

  async function onFinish(values: any) {
    try {
      setLoading(true);
      const { code } = await editProviderAccount({
        ..._.pickBy(values, (v) => !_.isNil(v) && v !== ''),
        company_id: row.company_id,
      });
      if (code === 0) {
        message.success('编辑成功');
        setShowState(false);
        close();
        onSuccess?.();
      }
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  }

  return (
    <Modal width={1000} open={visible} footer={null} onCancel={close} destroyOnClose>
      <Form
        layout="horizontal"
        title="新增调度"
        style={{ maxWidth: 960 }}
        form={form}
        onFinish={(values) => onFinish(values)}
      >
        <Flex wrap="wrap" gap="small">
          <Form.Item name="company_name" label="供应商">
            <Input style={{ width: 190 }} disabled></Input>
          </Form.Item>
          <Form.Item name="account" label="账号">
            <Input style={{ width: 190 }} disabled></Input>
          </Form.Item>
          <Form.Item name="account_name" label="账号名称">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="password" label="密码">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="business_type" label="业务类型">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="ip" label="接入IP">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="port" label="端口">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="province" label="省份">
            <Select options={allProvince} showSearch style={{ width: 190 }}></Select>
          </Form.Item>
          <Form.Item name="operator" label="运营商">
            <Select options={operator} showSearch style={{ width: 190 }}></Select>
          </Form.Item>
          <Form.Item name="access_code" label="接入码">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="service_id" label="服务ID">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="extend_len" label="预留扩展位">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="cmcc_tps" label="移动tps">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="cucc_tps" label="联通tps">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="ctcc_tps" label="电信tps">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="sign_type" label="签名类型">
            <Select options={signTypeOptions} showSearch style={{ width: 190 }}></Select>
          </Form.Item>
          <Form.Item name="connection_num" label="连接数">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <Form.Item name="freq_limit" label="账号频率限制">
            <Input style={{ width: 190 }}></Input>
          </Form.Item>
          <br />
          <Form.Item name="is_keyword" label="是否关键字">
            <Switch></Switch>
          </Form.Item>
          <Form.Item name="is_black_list" label="是否黑名单">
            <Switch></Switch>
          </Form.Item>
        </Flex>

        <Form.Item style={{ textAlign: 'center' }}>
          <Button loading={loading} type="primary" htmlType="submit">
            提交
          </Button>
          <Button style={{ marginLeft: 20 }} onClick={() => form.resetFields()}>
            重置
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default EditProviderAccountInfo;
