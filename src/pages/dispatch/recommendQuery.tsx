import React from 'react';
import { queryRecommend } from '@/services/thrdAPI';
import PatternTable from '@/pages/component/PatternLayout';
import CopyText from '@/utils/CopyText';
import { InputNumber, Select, Form, Tag, Input } from 'antd';

enum FlagType {
  '无变化',
  '新增报价',
  '价格上涨',
  '价格下跌',
}

function getColor(flag: number) {
  switch (flag) {
    case 0:
      return 'default';
    case 1:
      return 'warning';
    case 2:
      return 'error';
    case 3:
      return 'success';
    default:
      return 'default';
  }
}

const RecommendQuery = () => {
  const columns: any = [
    {
      title: '调度ID',
      dataIndex: 'scheduler_id',
      key: 'scheduler_id',
      align: 'center',
    },
    {
      title: '供应商账号',
      dataIndex: 'account',
      key: 'account',
      align: 'center',
    },
    {
      title: '供应商名称',
      dataIndex: 'company_name',
      key: 'company_name',
      align: 'center',
    },
    {
      title: '业务类型',
      dataIndex: 'billing_type_str',
      key: 'billing_type_str',
      align: 'center',
    },
    {
      title: '调度名称',
      dataIndex: 'scheduler_name',
      key: 'scheduler_name',
      align: 'center',
      width: 150,
      render: (content: string) => <CopyText text={content} width={150} />,
    },
    {
      title: '协议类型',
      dataIndex: 'protocol',
      key: 'protocol',
      align: 'center',
    },
    {
      title: '通道标识',
      dataIndex: 'provider_str',
      key: 'provider_str',
      align: 'center',
      width: 150,
      render: (content: string) => <CopyText text={content} width={150} />,
    },
    {
      title: '运营商',
      dataIndex: 'operator',
      key: 'operator',
      align: 'center',
    },
    {
      title: '省份',
      dataIndex: 'province',
      key: 'province',
      align: 'center',
    },
    {
      title: '短信类型',
      dataIndex: 'sms_type',
      key: 'sms_type',
      align: 'center',
      render: (type: number) => {
        return <span>{type === 1 ? '营销' : '普通'}</span>;
      },
    },
    {
      title: '预留扩展位',
      dataIndex: 'extend_len',
      key: 'extend_len',
      align: 'center',
    },
    {
      title: '通道速率',
      dataIndex: 'channel_qps',
      key: 'channel_qps',
      align: 'center',
    },
    {
      title: '调度配置状态',
      dataIndex: 'scheduler_status',
      key: 'scheduler_status',
      align: 'center',
      render: (status: number) =>
        status === 0 ? <Tag color="green">正常</Tag> : <Tag color="orange">禁用</Tag>,
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      align: 'center',
    },
    {
      title: '价格较昨日',
      dataIndex: 'change_flag',
      key: 'change_flag',
      align: 'center',
      render: (flag: number) => <Tag color={getColor(flag)}>{FlagType[flag]}</Tag>,
    },
    {
      title: '价格生效时间',
      dataIndex: 'price_expire_ftime',
      key: 'price_expire_ftime',
      align: 'center',
    },
    {
      title: '价格失效时间',
      dataIndex: 'price_expire_ttime',
      key: 'price_expire_ttime',
      align: 'center',
    },
  ];

  async function getList(vals?: any) {
    return await queryRecommend({ ...vals });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      hideShowAdd={true}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'company_name',
          placeholder: '供应商名称',
        },
        {
          label: '',
          name: 'account',
        },
        {
          label: '',
          name: 'billing_type_str',
          placeholder: '业务类型',
        },
        {
          label: '',
          name: 'scheduler_id',
          render: () => <Input style={{ width: 120 }} placeholder="调度ID" />,
        },
        {
          label: '',
          name: 'scheduler_name',
          placeholder: '调度名称',
        },
        {
          label: '',
          name: 'sms_type',
          placeholder: '短信类型',
          render: () => {
            return (
              <Select style={{ width: 100 }} placeholder="短信类型">
                <Select.Option value={''}>全部</Select.Option>
                <Select.Option value={0}>普通</Select.Option>
                <Select.Option value={1}>营销</Select.Option>
              </Select>
            );
          },
        },
        {
          label: '',
          name: 'provider_str',
          placeholder: '通道标识',
        },
        {
          label: '',
          name: 'operator',
          placeholder: '运营商',
        },
        {
          label: '',
          name: 'province',
          placeholder: '省份',
        },
        {
          label: '',
          name: 'extend_len',
          placeholder: '扩展位',
        },
        {
          label: '',
          name: 'scheduler_status',
          placeholder: '调度配置状态',
          render: () => {
            return (
              <Select style={{ width: 150 }} placeholder="调度配置状态">
                <Select.Option value={''}>全部</Select.Option>
                <Select.Option value={0}>正常</Select.Option>
                <Select.Option value={1}>禁用</Select.Option>
              </Select>
            );
          },
        },
        {
          label: '价格',
          placeholder: 'min',
          render: () => {
            return (
              <div style={{ display: 'flex' }}>
                <Form.Item name="min_price">
                  <InputNumber placeholder="min" controls={false} />
                </Form.Item>
                <div style={{ margin: '3px 16px 0 0 ' }}>{'--'}</div>
                <Form.Item name="max_price">
                  <InputNumber placeholder="max" controls={false} />
                </Form.Item>
              </div>
            );
          },
        },
      ]}
      operateForm={[]}
      operType={3}
    />
  );
};

export default RecommendQuery;
