import React, { useState, useEffect, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Input, Button, Table, Modal, Popover, Divider, message } from 'antd';
import { EditOutlined, ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { getLabelGroup, setLabelGroup, delLabelGroup } from '../../services/api';
import ExpandedTableLabelGroup from './componment/ExpandedTableLabelGroup';
import CreateGroup from './componment/CreateGroup';

const { confirm } = Modal;

const LabelGroupList: React.FC<{}> = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [count, setCount] = useState<number>(0);
  const [list, setList] = useState<any[]>([]);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [visibleNameEdit, setVisibleNameEdit] = useState('');
  const [labelName, setLabelName] = useState('');
  const modalEl = useRef(null);

  const columns = [
    {
      title: '标签组id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '标签组名',
      align: 'center',
      render: (row: any) => (
        <>
          {row.name}
          <Popover
            placement="bottom"
            visible={row.id === visibleNameEdit}
            onOpenChange={(val) => {
              setLabelName(row.name);
              val ? setVisibleNameEdit(row.id) : setVisibleNameEdit('');
            }}
            content={
              <>
                <Input
                  value={labelName}
                  onChange={(e) => {
                    setLabelName(e.target.value);
                  }}
                />
                <Divider />
                <Button type="primary" onClick={() => editLabelInfo(row)}>
                  确定
                </Button>
                <Button onClick={() => setVisibleNameEdit('')}>取消</Button>
              </>
            }
            trigger="click"
          >
            <EditOutlined />
          </Popover>
        </>
      ),
    },
    {
      title: '标签数',
      align: 'center',
      render: (row: any) => row.label_ids.split(',').length,
    },
    {
      title: '绑定数',
      dataIndex: 'bind_sum',
      key: 'bind_sum',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
    },
    {
      title: '操作',
      render: (row: any) => (
        <>
          <Button type="link" onClick={() => onDelGroup(row)}>
            删除
          </Button>
          <Button type="link" onClick={() => modalEl?.current?.toggleModalVisible(false, row)}>
            编辑
          </Button>
        </>
      ),
      align: 'center',
    },
  ];

  useEffect(() => {
    getList();
  }, [pageIndex, pageSize]);

  async function getList() {
    setList([]);
    setIsLoading(true);
    const params = {
      // staff_name: searchKey,
      page_size: pageSize,
      page_index: pageIndex,
    };

    const res = await getLabelGroup({ ...params });
    setCount(res.data?.count);
    setList(res.data?.list);
    setIsLoading(false);
  }

  async function onDelGroup(row: any) {
    setIsLoading(true);
    confirm({
      title: '确认删除当前所选标签组？',
      icon: <ExclamationCircleOutlined />,
      content: '删除后，该标签组下的所有内容将会被清空，且无法恢复。',
      async onOk() {
        try {
          const res = await delLabelGroup({
            id: row.id,
          });
          if (res.code === 0) {
            message.success('删除成功');
            getList();
            // @ts-ignore
            // modalEl?.current?.toggleModalVisible();
          }
        } catch (err) {
          console.log(err);
        }
        // console.log('OK');
      },
      onCancel() {
        console.log('Cancel');
      },
    });
    setIsLoading(false);
  }

  async function editLabelInfo(row: any) {
    setIsLoading(true);
    try {
      await setLabelGroup({
        id: row.id,
        name: labelName.trim(),
      });
      setVisibleNameEdit('');
      message.success('编辑成功');
      setIsLoading(false);
      getList();
    } catch (err) {
      setIsLoading(false);
      message.error('编辑失败');
    }
  }

  return (
    <PageContainer title={false}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 20 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            // @ts-ignore
            modalEl?.current?.toggleModalVisible(true);
          }}
        >
          新增标签组
        </Button>
      </div>
      <Table
        size="small"
        // @ts-ignore
        columns={columns}
        dataSource={list}
        rowKey={(record) => record.id}
        loading={isLoading}
        expandable={{
          expandedRowRender: (row: any) => <ExpandedTableLabelGroup currentRow={row} />,
        }}
        expandedRowClassName={() => 'expanded-table'}
        // onExpand={onExpandedChange}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPageSize(page_size),
          onChange: (page) => {
            setPageIndex(page);
          },
        }}
      />
      <CreateGroup dialogRef={modalEl} reload={getList} />
    </PageContainer>
  );
};

export default LabelGroupList;
