import React, { useState, useEffect, useRef } from 'react';
import { useList } from 'react-use';
import { PageContainer } from '@ant-design/pro-layout';
import { Input, Button, Table, Modal, Popover, Divider, message, Select } from 'antd';
import { EditOutlined, ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons';
import { getLabelInfo, setLabelInfo, delLabel, getLabelBindSum } from '../../services/api';
import CreateLabel from './componment/CreateLabel';
import ExpandedTableLabel from './componment/ExpandedTableLabel';

const { confirm } = Modal;
const { Option } = Select;

const TagListCustomer: React.FC<{}> = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [count, setCount] = useState<number>(0);
  const [list, { set, updateAt }] = useList<any[]>([]);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [type, setType] = useState(1);

  const [pageSize, setPageSize] = useState<number>(10);
  const [visibleFirstTagEdit, setVisibleFirstTagEdit] = useState('');
  const [labelName, setLabelName] = useState('');
  const modalEl = useRef(null);

  const columns = [
    {
      title: '一级标签',
      // dataIndex: 'staff_name',
      // key: 'staff_name',
      align: 'center',
      render: (row: any) => (
        <>
          {row.name}
          <Popover
            placement="bottom"
            visible={row.id === visibleFirstTagEdit}
            onOpenChange={(val) => {
              setLabelName(row.name);
              val ? setVisibleFirstTagEdit(row.id) : setVisibleFirstTagEdit('');
            }}
            content={
              <>
                <Input
                  value={labelName}
                  onChange={(e) => {
                    setLabelName(e.target.value);
                  }}
                />
                <Divider />
                <Button type="primary" onClick={() => editLabelInfo(row)}>
                  确定
                </Button>
                <Button onClick={() => setVisibleFirstTagEdit('')}>取消</Button>
              </>
            }
            trigger="click"
          >
            <EditOutlined />
          </Popover>
        </>
      ),
    },
    {
      title: '二级标签数',
      dataIndex: 'son_label_sum',
      key: 'son_label_sum',
    },
    {
      title: '绑定数',
      align: 'center',
      render: (row: any) => {
        return <Button type="text">{row.bind_sum}</Button>;
      },
    },
    {
      title: '操作',
      render: (row: any) => (
        <>
          <Button type="link" onClick={() => onDelTag(row)}>
            删除
          </Button>
        </>
      ),
      align: 'center',
    },
  ];

  const expandedRowRender = (row: any) => <ExpandedTableLabel currentRow={row} />;

  useEffect(() => {
    getList(1, true);
  }, [pageIndex, pageSize, type]);

  async function getList(level: number, init?: boolean) {
    setIsLoading(true);
    const params = {
      // staff_name: searchKey,
      page_size: level === 1 ? pageSize : 1000,
      page_index: pageIndex,
      level,
      status: 0,
      type,
    };

    const res = await getLabelInfo({ ...params });
    if (init) {
      res.data?.list?.forEach((el: any) => getBindSum(res.data?.list, el.id));
    }
    setCount(res.data?.count);
    set(res.data?.list);
    setIsLoading(false);
  }

  async function onDelTag(row: any) {
    setIsLoading(true);
    // await deleteUser({
    //   user_id: row.user_id,
    // });
    // getList();
    confirm({
      title: '确认删除当前所选一级标签？',
      icon: <ExclamationCircleOutlined />,
      content: '删除后，该一级标签下的所有内容将会被清空，且无法恢复。',
      async onOk() {
        try {
          const res = await delLabel({
            label_id: row.id,
          });
          if (res.code === 0) {
            message.success('删除成功');
            getList(1);
            // @ts-ignore
            // modalEl?.current?.toggleModalVisible();
          }
        } catch (err) {
          console.log(err);
        }
        // console.log('OK');
      },
      onCancel() {
        console.log('Cancel');
      },
    });
    setIsLoading(false);
  }

  async function editLabelInfo(row: any) {
    setIsLoading(true);
    try {
      await setLabelInfo({
        id: row.id,
        name: labelName.trim(),
      });
      setVisibleFirstTagEdit('');
      message.success('编辑成功');
      setIsLoading(false);
      getList(1);
    } catch (err) {
      setIsLoading(false);
      message.error('编辑失败');
    }
  }

  async function getBindSum(list: any, id: any) {
    try {
      const res = await getLabelBindSum({ label_id: id });
      const index = list.findIndex((el: any) => {
        return el.id === id;
      });
      const sumBind = res?.data?.reduce(
        (pre: any, cur: any) => {
          if (type === 0 || cur.ObjType === (type === 1 ? 'uin' : 'sdkappid')) {
            pre.Sum += cur.Sum;
          }
          return pre;
        },
        { Sum: 0 },
      );
      updateAt(index, { ...list[index], bind_sum: sumBind.Sum || 0 });
      return res.data[0]?.name;
    } catch (err) {
      console.log(err);
    }
  }

  function onSelectChange(val: any) {
    setType(val);
  }

  return (
    <PageContainer title={false}>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 20 }}>
        <Select defaultValue={1} style={{ width: 120, marginRight: 10 }} onChange={onSelectChange}>
          <Option value={0}>通用标签</Option>
          <Option value={1}>客户标签</Option>
          <Option value={2}>资源标签</Option>
        </Select>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => {
            // @ts-ignore
            modalEl?.current?.toggleModalVisible();
          }}
        >
          新建标签
        </Button>
      </div>
      <Table
        size="small"
        // @ts-ignore
        columns={columns}
        dataSource={list}
        rowKey={(record: any) => record.id}
        loading={isLoading}
        expandable={{ expandedRowRender }}
        expandedRowClassName={() => 'expanded-table'}
        // onExpand={onExpandedChange}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPageSize(page_size),
          onChange: (page) => {
            setPageIndex(page);
          },
        }}
      />
      <CreateLabel
        dialogRef={modalEl}
        type={type}
        reload={() => {
          set([]);
          getList(1);
        }}
      />
    </PageContainer>
  );
};

export default TagListCustomer;
