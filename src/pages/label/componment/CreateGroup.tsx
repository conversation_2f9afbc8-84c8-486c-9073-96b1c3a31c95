/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useRef } from 'react';
import { Button, Form, Input, message, Drawer } from 'antd';

import { addLabelGroup, setLabelGroup, getLabelGroup, getLabelInfo } from '../../../services/api';
import LabelTree from './LabelTree';

interface Props {
  dialogRef: any;
  reload: any;
  // isCreate: false;
}

const CreateGroup = (props: Props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { dialogRef, reload } = props;
  const [isCreate, setIsCreate] = useState<number>(1);
  const [currentRow, setCurrentRow] = useState<any>({});
  const [labelGroupList, setLabelGroupList] = useState([]);
  const [labelList, setLabelList] = useState([]);
  const [form] = Form.useForm();
  const labelRef = useRef(null);

  useEffect(() => {
    dialogRef.current = {
      toggleModalVisible: (flag: any, row?: any) => {
        setCurrentRow(row);
        setIsCreate(flag);
        isModalVisible ? setIsModalVisible(false) : setIsModalVisible(true);
      },
    };
    if (isModalVisible) {
      getLabelGroupList();
      getLabelList();
    } else {
      form.resetFields(['name']);
    }
  }, [isModalVisible, dialogRef, currentRow]);

  useEffect(() => {
    if (!isCreate && isModalVisible) {
      form.setFieldsValue({ name: currentRow?.name });
      const label_ids = currentRow?.label_ids?.length
        ? currentRow?.label_ids?.split(',')?.map((el: any) => Number(el))
        : [];
      labelRef?.current?.setSelectedLabel(label_ids);
    }
  }, [currentRow, form, isCreate, isModalVisible]);

  async function getLabelList() {
    const res1 = await getLabelInfo({
      page_size: 1000,
      page_index: 1,
      level: 1,
      type: 1,
      status: 0,
    });
    const res2 = await getLabelInfo({
      page_size: 1000,
      page_index: 1,
      level: 2,
      type: 1,
      status: 0,
    });
    const firstLabelList: any = res1.data?.list || [];
    const secondLabelList: any = res2.data?.list || [];
    // @ts-ignore
    setLabelList([...firstLabelList, ...secondLabelList]);
  }

  async function getLabelGroupList() {
    const res = await getLabelGroup({ pageSize: 1000 });
    setLabelGroupList(res.data?.list || []);
  }

  async function onFinish(vals: any) {
    if (!labelRef?.current?.selectedLabel.length) {
      return message.error('请选择标签');
    }
    const data = {
      name: vals.name,
      // @ts-ignore
      label_ids: labelRef?.current?.selectedLabel?.join(','),
    };
    if (isCreate) {
      await addLabelGroup({ ...data });
      message.success('创建成功');
      reload();
      close();
    } else {
      await setLabelGroup({ ...data, id: currentRow.id });
      message.success('编辑成功');
      reload();
      close();
    }
  }

  function close() {
    setIsModalVisible(false);
    setCurrentRow([]);
    form.resetFields();
  }

  return (
    <Drawer
      title={isCreate ? '创建标签组' : '编辑标签组'}
      placement="right"
      onClose={close}
      width={500}
      // style={{ width: 600 }}
      // size="large"
      open={isModalVisible}
    >
      <Form
        name="edit"
        form={form}
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 16 }}
        initialValues={{ name: currentRow?.name }}
        onFinish={onFinish}
        // onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        <Form.Item name="name" label="标签组名称" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        {isModalVisible && (
          <LabelTree
            labelRef={labelRef}
            labelType={0}
            labelGroupList={labelGroupList}
            labelList={labelList}
          />
        )}
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit" rootStyle={{ marginRight: 10 }}>
            保存
          </Button>
          <Button
            onClick={() => {
              close();
            }}
          >
            取消
          </Button>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default CreateGroup;
