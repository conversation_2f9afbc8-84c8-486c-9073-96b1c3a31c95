import React, { useState, useEffect } from 'react';
import { Input, Button, Modal, Divider, Select, Form, Space, Typography, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

import { getLabelInfo, addLabel } from '../../../services/api';
import { useForm } from 'antd/es/form/Form';

const { Option } = Select;

interface Props {
  dialogRef: any;
  reload: any;
  type: number;
}

const CreateLabel = (props: Props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { dialogRef, reload, type } = props;
  const [firstLabelOption, setFirstLabelOption] = useState([]);
  const [firstLabelList, setFirstLabelList] = useState<any[]>([]);
  const [secondLabel, setSecondLabel] = useState('');
  const [firstLabe, setFirstLabel] = useState('');
  const [form] = useForm();

  useEffect(() => {
    dialogRef.current = {
      toggleModalVisible: () => {
        isModalVisible ? setIsModalVisible(false) : setIsModalVisible(true);
      },
    };
    getList(1);
  }, [dialogRef, isModalVisible]);

  useEffect(() => {
    formatFirstLabelOption();
  }, [firstLabelList]);

  async function getList(level: number) {
    const params = {
      // staff_name: searchKey,
      page_size: 1000,
      page_index: 1,
      level,
      type,
      status: 0,
    };
    const res = await getLabelInfo({ ...params });
    setFirstLabelList(res.data?.list || []);
    // formatFirstLabelOption();
  }

  async function formatFirstLabelOption() {
    const arr: any = firstLabelList?.map((el: any) => ({
      value: el.id,
      text: el.name,
    }));
    setFirstLabelOption(arr);
  }

  async function onSubmit(vals: any) {
    if (!vals.secondLabelVal) {
      setIsModalVisible(false);
      return;
    }
    try {
      const res = await addLabel({
        level: 2,
        type,
        name: secondLabel.trim(),
        parent_id: vals.firstLabelVal,
        first_parent_id: vals.firstLabelVal,
      });
      close();
      if (res.code === 0) {
        message.success('新建二级标签成功');
        reload();
      }
    } catch (err) {
      console.log(err);
    }
  }

  async function addFirstLabel(e: any) {
    e.preventDefault();
    const data = {
      name: firstLabe.trim(),
      level: 1,
      type,
    };
    try {
      const res = await addLabel({ ...data });
      if (res.code === 0) {
        message.success('新建一级标签成功');
        setFirstLabel('');
        getList(1);
        reload();
      }
    } catch (err) {
      console.log(err);
    }
  }

  function close() {
    form.resetFields();
    setIsModalVisible(false);
  }

  return (
    <Modal
      title="新建标签"
      open={isModalVisible}
      onCancel={() => {
        close();
      }}
      footer={null}
    >
      <Form
        name="edit"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 16 }}
        initialValues={{ remember: true }}
        onFinish={(vals) => onSubmit(vals)}
        form={form}
        // onFinish={onFinish}
        // onFinishFailed={onFinishFailed}
        autoComplete="off"
      >
        <Form.Item
          label="一级标签"
          name="firstLabelVal"
          rules={[{ required: true, message: '请选择一级标签' }]}
        >
          <Select
            allowClear
            style={{ width: '100%' }}
            placeholder="请选择一级标签内容"
            showSearch
            filterOption={(input, option) =>
              option?.children?.toLowerCase()?.indexOf(input.toLowerCase()) >= 0
            }
            dropdownRender={(menu) => (
              <>
                {menu}
                <Divider style={{ margin: '8px 0' }} />
                <Space align="center" style={{ padding: '0 8px 4px' }}>
                  <Input
                    placeholder="请输入标签名"
                    value={firstLabe}
                    onChange={(e) => setFirstLabel(e.target.value)}
                  />
                  <Typography.Link onClick={addFirstLabel} style={{ whiteSpace: 'nowrap' }}>
                    <PlusOutlined /> 添加选项
                  </Typography.Link>
                </Space>
              </>
            )}
          >
            {firstLabelOption.map((el: any) => (
              <Option key={el.value} value={el.value}>
                {el.text}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label="二级标签" name="secondLabelVal">
          <Input
            value={secondLabel}
            onChange={(e) => setSecondLabel(e.target.value)}
            placeholder="请输入二级标签内容"
          />
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button type="primary" htmlType="submit" style={{ marginRight: 10 }}>
            保存
          </Button>
          <Button
            onClick={() => {
              close();
            }}
          >
            取消
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateLabel;
