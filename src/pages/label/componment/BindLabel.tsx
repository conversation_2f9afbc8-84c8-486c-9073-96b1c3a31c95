/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useRef } from 'react';
import {
  Button,
  Select,
  Form,
  Input,
  Radio,
  Tag,
  message,
  Table,
  Drawer,
  Typography,
  Spin,
} from 'antd';
import { LoadingOutlined } from '@ant-design/icons';

import {
  getLabelInfo,
  setLabelBind,
  getLabelBind,
  setLabelUnbind,
  getLabelGroup,
} from '../../../services/api';
import LabelTree from './LabelTree';

import _ from 'lodash';
import { useAsyncFn } from 'react-use';

const { Option, OptGroup } = Select;
const { Text } = Typography;

interface Props {
  dialogRef: any;
  objType: any;
  reload: any;
  // isCreate: false;
}
let originCurrentLabel: any = [];

const m: any = new Map([
  ['uin', 'uin'],
  ['scheduler', 'scheduler_id'],
  ['sign', 'sign'],
  ['sdkappid', 'sdkappid'],
]);

const BindLabel = (props: Props) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [unbindLoading, setUnbindLoading] = useState('');
  const { dialogRef, reload } = props;
  const [objType, setObjType] = useState(props.objType);
  const [labelType, setLabelType] = useState(0);
  const [isCreate, setIsCreate] = useState<number>(1);
  const [currentRow, setCurrentRow] = useState<any>({});
  const [currentLabel, setCurrentLabel] = useState<any>([]);
  const [form] = Form.useForm();
  const [labelList, setLabelList] = useState([]);
  const [labelGroupList, setLabelGroupList] = useState([]);
  const labelRef = useRef(null);
  const [saveLoading, setSaveLoading] = useState(false);
  const [baseInfo, setBaseInfo] = useState([
    {
      text: '公司名',
      value: 'client_name',
    },
    {
      text: 'uin',
      value: 'uin',
    },
  ]);

  useEffect(() => {
    dialogRef.current = {
      toggleModalVisible: (flag: any, row?: any) => {
        setCurrentRow(row);
        setIsCreate(flag);
        isModalVisible ? setIsModalVisible(false) : setIsModalVisible(true);
      },
    };
    form.setFieldsValue({ obj_type: props.objType });
    setObjType(props.objType);
    if (isModalVisible) {
      getLabelList();
      getLabelGroupList();
      !isCreate && queryBindInfo();
    }
  }, [dialogRef, form, isCreate, isModalVisible, props.objType]);

  useEffect(() => {
    switch (props.objType) {
      case 'scheduler':
        setBaseInfo([
          {
            text: '通道id',
            value: 'scheduler_id',
          },
        ]);
        break;
      case 'sign':
        setBaseInfo([
          {
            text: '签名',
            value: 'sign',
          },
        ]);
        break;
      case 'sdkappid':
        setBaseInfo([
          {
            text: 'sdkappid',
            value: 'sdkappid',
          },
        ]);
        break;
      default:
        setBaseInfo([
          {
            text: '公司名',
            value: 'client_name',
          },
          {
            text: 'uin',
            value: 'uin',
          },
        ]);
    }
  }, [props.objType]);

  const [bindInfoStatus, queryBindInfo] = useAsyncFn(
    async () => {
      if (!isModalVisible) return;
      const res = await getLabelBind({
        obj_type: props.objType === 'uin' ? 'uin' : 'sdkappid',
        obj_ids: currentRow[m.get(props.objType)].toString(),
      });
      setCurrentLabel(res.data.list || []);
      originCurrentLabel = [...(res.data.list || [])];
      return res.data.list || [];
    },
    [isModalVisible],
    {
      value: [],
      loading: false,
      error: null,
    },
  );

  async function getLabelList() {
    const res1 = await getLabelInfo({
      page_size: 1000,
      page_index: 1,
      level: 1,
      type: objType === 'uin' ? 1 : 2,
      status: 0,
    });
    const res2 = await getLabelInfo({
      page_size: 1000,
      page_index: 1,
      level: 2,
      type: objType === 'uin' ? 1 : 2,
      status: 0,
    });
    const firstLabelList: any = res1.data?.list || [];
    const secondLabelList: any = res2.data?.list || [];
    // @ts-ignore
    setLabelList([...firstLabelList, ...secondLabelList]);
  }

  async function getLabelGroupList() {
    const res = await getLabelGroup({ pageSize: 1000 });
    setLabelGroupList(res.data?.list);
  }

  function onSelectChange(val: any) {
    setObjType(val);
  }

  async function onFinish(vals: any) {
    if (!isCreate && objType === 'uin') {
      unbindLabel(vals);
      return;
    }
    // @ts-ignore
    labelRef?.current?.selectedLabel.length ? bindLabel(vals) : reset();
  }

  async function bindLabel(vals: any) {
    setSaveLoading(true);
    const data = {
      obj_type: objType === 'uin' ? 'uin' : 'sdkappid',
      obj_ids: isCreate ? vals.obj_ids : currentRow[m.get(objType)].toString(),
      // @ts-ignore
      label_ids: labelRef?.current?.selectedLabel.join(','),
      label_type: objType === 'uin' ? 0 : labelType,
      is_black: objType === 'uin' ? 0 : vals.is_black,
    };
    try {
      const res = await setLabelBind({ ...data });
      if (res.code === 0) {
        message.success('操作成功');
        // @ts-ignore
        labelRef.current.setSelectedLabel([]);
        queryBindInfo();
        setUnbindLoading('');
      }
      setSaveLoading(false);
    } catch (err) {
      console.log(err);
      setSaveLoading(false);
    }
  }

  async function unbindLabel(vals?: any) {
    let deletedTag: any = [];
    if (objType === 'uin') {
      deletedTag = _.differenceBy(originCurrentLabel, currentLabel, 'label_id');
    } else {
      deletedTag = [vals];
      setUnbindLoading(vals.label_id);
    }
    if (deletedTag.length) {
      try {
        const res = await setLabelUnbind({
          label_ids: deletedTag.map((el: any) => el.label_id).join(','),
          obj_ids: currentRow[m.get(objType)].toString(),
          obj_type: objType === 'uin' ? 'uin' : 'sdkappid',
          label_type: objType === 'uin' ? 0 : deletedTag[0].label_type,
          is_black: objType === 'uin' ? 0 : deletedTag[0].is_black,
        });
        if (res.code === 0) {
          if (objType === 'uin') {
            message.success('操作成功');
            reload();
            // @ts-ignore
            labelRef?.current?.selectedLabel.length ? bindLabel(vals) : reset();
          } else {
            message.success('解绑成功');
            queryBindInfo();
          }
        }
        setUnbindLoading('');
      } catch (err) {
        console.log(err);
      }
    } else {
      // @ts-ignore
      labelRef?.current?.selectedLabel.length ? bindLabel(vals) : reset();
    }
  }

  function reset() {
    // setIsModalVisible(false);
    setCurrentLabel([]);
    setUnbindLoading('');
    form.resetFields();
    // reload();
  }

  function closeTag(e: any, tag: any) {
    // e.preventDefault();
    const arr = [...currentLabel];
    const index = arr.findIndex((el: any) => el.label_id === tag.label_id);
    if (index !== -1) {
      arr.splice(index, 1);
    }
    setCurrentLabel(arr);
  }

  function getLabelName(row: any) {
    const item: any =
      row.label_type === 0
        ? labelList.find((el: any) => el.id === row.label_id)
        : labelGroupList.find((el: any) => el.id === row.label_id);
    return item?.name;
  }

  function currentLabelRender() {
    if (bindInfoStatus.loading && objType === 'uin') {
      return <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />;
    }
    if (objType === 'uin') {
      return currentLabel?.length
        ? currentLabel?.map((el: any) => (
            <Tag
              key={el.label_id}
              style={{ marginBottom: 5 }}
              closable
              onClose={(e: any) => closeTag(e, el)}
            >
              {getLabelName(el)}
            </Tag>
          ))
        : '暂无数据';
    }
    return (
      <Table
        loading={bindInfoStatus.loading}
        dataSource={currentLabel}
        rowKey={(record) => record.id}
        columns={[
          {
            title: '标签id',
            dataIndex: 'label_id',
            key: 'label_id',
            align: 'center',
          },
          {
            title: '标签名',
            align: 'center',
            render: (row) => getLabelName(row),
          },
          {
            title: '黑标签',
            dataIndex: 'is_black',
            key: 'is_black',
            align: 'center',
            render: (key) => {
              return key === 1 ? '是' : '否';
            },
          },
          {
            title: '标签类型',
            dataIndex: 'label_type',
            key: 'label_type',
            align: 'center',
            render: (key) => {
              return key === 1 ? '标签组' : '单标签';
            },
          },
          {
            title: '操作',
            align: 'center',
            render: (row) => (
              <Button
                type="link"
                onClick={() => unbindLabel(row)}
                loading={unbindLoading === row.label_id}
              >
                解绑
              </Button>
            ),
          },
        ]}
      />
    );
  }

  return (
    <Drawer
      title={isCreate ? '打标签' : '管理标签'}
      placement="right"
      onClose={() => {
        setIsModalVisible(false);
      }}
      width={600}
      open={isModalVisible}
    >
      <Form
        name="edit"
        form={form}
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 20 }}
        initialValues={{ obj_type: props.objType }}
        onFinish={onFinish}
        autoComplete="off"
      >
        {!isCreate ? (
          <>
            {baseInfo.map((el: any) => (
              <Form.Item label={el.text} key={el.value}>
                {currentRow[el.value]}
              </Form.Item>
            ))}
          </>
        ) : null}
        {isCreate ? (
          <Form.Item label="绑定对象" name="obj_type">
            <Select rootStyle={{ width: 120 }} onChange={onSelectChange}>
              <Option value="uin">uin</Option>
              <OptGroup label="资源标签">
                <Option value="scheduler">通道</Option>
                <Option value="sign">签名</Option>
                <Option value="sdkappid">应用id</Option>
              </OptGroup>
            </Select>
          </Form.Item>
        ) : null}
        {isCreate ? (
          <Form.Item
            name="obj_ids"
            label={m.get(objType)}
            extra="支持多个，英文逗号分隔"
            rules={[{ required: true }]}
          >
            <Input />
          </Form.Item>
        ) : null}
        {!isCreate ? <Form.Item label="当前标签">{currentLabelRender()}</Form.Item> : null}
        {objType !== 'uin' ? (
          <Form.Item label="标签类型">
            <Radio.Group value={labelType} onChange={(e: event) => setLabelType(e.target.value)}>
              <Radio value={0}>单标签</Radio>
              <Radio value={1}>标签组</Radio>
            </Radio.Group>
          </Form.Item>
        ) : null}
        {isModalVisible && (
          <LabelTree
            labelRef={labelRef}
            objType={objType}
            labelType={labelType}
            labelList={labelList}
            labelGroupList={labelGroupList}
          />
        )}
        {objType !== 'uin' ? (
          <Form.Item name="is_black" label="是否黑标签" rootStyle={{ alignItems: 'baseline' }}>
            <Radio.Group defaultValue={0}>
              <Radio value={1}>是</Radio>
              <Radio value={0}>否</Radio>
            </Radio.Group>
            <p>
              <Text type="secondary" rootStyle={{ fontSize: 12 }}>
                注意：标签绑定后再修改此字段，需要先解绑再重新绑定
              </Text>
            </p>
          </Form.Item>
        ) : null}
        <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
          <Button
            loading={saveLoading}
            type="primary"
            htmlType="submit"
            rootStyle={{ marginRight: 10 }}
          >
            保存
          </Button>
          <Button
            onClick={() => {
              setIsModalVisible(false);
            }}
          >
            取消
          </Button>
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default BindLabel;
