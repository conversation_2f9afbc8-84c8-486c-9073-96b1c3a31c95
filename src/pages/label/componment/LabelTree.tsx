/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { Form, TreeSelect } from 'antd';

import { getLabelGroup } from '../../../services/api';

interface Props {
  objType?: string;
  labelRef: any;
  labelType?: any;
  labelList?: any[];
  labelGroupList?: any[];
}

const LabelSelect = (props: Props) => {
  const { labelRef, labelType, labelList, labelGroupList } = props;
  const [selectedLabel, setSelectedLabel] = useState([]);
  const [treeData, setTreeData] = useState([]);
  const [temp, setTemp] = useState([]);

  useEffect(() => {
    labelRef.current = {
      selectedLabel,
      setSelectedLabel: (label_ids: any) => {
        setTemp(label_ids);
        // temp = label_ids;
      },
    };
  }, [selectedLabel, labelRef, labelType]);

  useEffect(() => {
    setTreeData([]);
    if (labelType === 0) {
      getLabelList();
    } else {
      getLabelGroupTree();
    }
  }, [labelType, labelList, labelGroupList]);

  useEffect(() => {
    if (!treeData?.length) return;
    setSelectedLabel(temp);
  }, [temp, treeData]);

  async function getLabelList() {
    const firstLabelList: any = [];
    const secondLabelList: any = [];

    labelList?.forEach((el) => {
      if (el.level === 1) {
        firstLabelList.push(el);
      } else if (el.level === 2) {
        secondLabelList.push(el);
      }
    });
    // @ts-ignore
    // setLabelList([...firstLabelList, ...secondLabelList]);
    const firstTree = firstLabelList.map((el: any) => ({
      title: el.name,
      key: el.id,
      value: el.id,
      children: [],
      label_id: el.id,
    }));
    const secondTree = secondLabelList.map((el: any) => ({
      title: el.name,
      key: el.id,
      value: el.id,
      parent_id: el.parent_id,
      label_id: el.id,
    }));
    const tree = firstTree.map((el: any) => {
      // el.children === undefined && (el.children = []);
      const arr: any = [];
      secondTree.forEach((el2: any) => {
        if (el2.parent_id === el.key) {
          arr.push(el2);
        }
      });
      if (arr.length) {
        el.children = arr;
      }
      return el;
    });
    setTreeData(tree);
    setSelectedLabel(temp);
  }

  async function getLabelGroupTree() {
    await getLabelGroup({ pageSize: 1000 });
    const tree: any = labelGroupList?.map((el: any) => {
      return {
        title: el.name,
        key: el.id,
        value: el.id,
      };
    });
    setTreeData(tree);
  }

  return (
    <Form.Item label="新增标签">
      <span style={{ fontSize: 0 }}>{selectedLabel}</span>
      <TreeSelect
        // defaultExpandAll
        value={selectedLabel}
        treeCheckable
        showCheckedStrategy={TreeSelect.SHOW_CHILD}
        treeData={treeData}
        maxTagCount={2}
        allowClear
        style={{ width: 300 }}
        loadData={labelType === 0 ? getLabelList : getLabelGroupTree}
        onChange={(val) => {
          setSelectedLabel(val);
        }}
      />
    </Form.Item>
  );
};

export default LabelSelect;
