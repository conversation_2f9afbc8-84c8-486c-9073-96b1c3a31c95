import React, { useState, useEffect } from 'react';
import { Input, Button, Table, Modal, Popover, Divider, message } from 'antd';
import { EditOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { getLabelInfo, setLabelInfo, delLabel } from '../../../services/api';

const { confirm } = Modal;
interface Props {
  currentRow: any;
}

const ExpandedTableLabel = (props: Props) => {
  const { currentRow } = props;
  const [sonLoading, setSonLoading] = useState(false);
  const [sonList, setSonList] = useState([]);
  const [visibleSecondTagEdit, setVisibleSecondTagEdit] = useState('');
  const [labelName, setLabelName] = useState('');

  const expandedColumns:any[] = [
    {
      title: '二级标签',
      align: 'center',
      render: (row: any) => (
        <>
          {row.name}
          <Popover
            placement="bottom"
            visible={row.id === visibleSecondTagEdit}
            onOpenChange={(val) => {
              setLabelName(row.name);
              val ? setVisibleSecondTagEdit(row.id) : setVisibleSecondTagEdit('');
            }}
            content={
              <>
                <Input
                  value={labelName}
                  onChange={(e) => {
                    setLabelName(e.target.value);
                  }}
                />
                <Divider />
                <Button type="primary" onClick={() => editLabelInfo(row)}>
                  确定
                </Button>
                <Button onClick={() => setVisibleSecondTagEdit('')}>取消</Button>
              </>
            }
            trigger="click"
          >
            <EditOutlined />
          </Popover>
        </>
      ),
    },
    {
      title: '绑定数',
      dataIndex: 'bind_sum',
      key: 'bind_sum',
      align: 'center',
    },
    {
      title: '操作',
      render: (row: any) => (
        <>
          <Button
            type="link"
            onClick={() => {
              onDelTag(row);
            }}
          >
            删除
          </Button>
        </>
      ),
      align: 'center',
    },
  ];

  useEffect(() => {
    getList(2);
  }, []);

  async function getList(level: number) {
    setSonLoading(true);
    const params = {
      // staff_name: searchKey,
      page_size: 1000,
      page_index: 1,
      parent_id: currentRow.id,
      level,
      status: 0,
      type: currentRow.type,
    };

    const res = await getLabelInfo({ ...params });
    // setCount(res.data?.count);
    setSonList(res.data?.list || []);
    setSonLoading(false);
  }

  async function onDelTag(row: any) {
    setSonLoading(true);
    confirm({
      title: '确认删除当前所选二级标签？',
      icon: <ExclamationCircleOutlined />,
      content: '删除后，该二级标签下的所有内容将会被清空，且无法恢复。',
      async onOk() {
        try {
          const res = await delLabel({
            label_id: row.id,
            // status: 1,
          });
          if (res.code === 0) {
            message.success('删除成功');
            getList(2);
            // @ts-ignore
            // modalEl?.current?.toggleModalVisible();
          }
        } catch (err) {
          console.log(err);
        }
        // console.log('OK');
      },
      onCancel() {
        console.log('Cancel');
      },
    });
    setSonLoading(false);
  }

  async function editLabelInfo(row: any) {
    setSonLoading(true);
    try {
      await setLabelInfo({
        id: row.id,
        name: labelName.trim(),
      });
      setVisibleSecondTagEdit('');
      message.success('编辑成功');
      setSonLoading(false);
      getList(2);
    } catch (err) {
      setSonLoading(false);
      message.error('编辑失败');
    }
  }

  return (
    <Table
      size="small"
      rowKey={(record: any) => record.id}
      columns={expandedColumns}
      dataSource={sonList}
      loading={sonLoading}
      pagination={false}
    />
  );
};

export default ExpandedTableLabel;
