import React, { useState, useCallback } from 'react';
import { Table } from 'antd';

interface Props {
  currentRow: any;
}

const ExpandedTableLabelGroup = (props: Props) => {
  const { currentRow } = props;
  const [sonList] = useState(currentRow.label_infos);

  const labelTypeMap = [
    { value: '0', text: '通用' },
    { value: '1', text: '客户标签' },
    { value: '2', text: '资源标签' },
  ];

  const getLabelTypeMap = useCallback((val) => {
    return labelTypeMap.find((el) => el.value === val.toString())?.text;
  }, []);

  const expandedColumns = [
    {
      title: '标签id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '标签名',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      align: 'center',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (val: number) => getLabelTypeMap(val),
    },
  ];

  return (
    <Table
      size="small"
      rowKey={(record) => record.id}
      columns={expandedColumns}
      dataSource={sonList}
      pagination={false}
    />
  );
};

export default ExpandedTableLabelGroup;
