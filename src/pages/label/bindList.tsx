import React, { useState, useEffect, useRef } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Input, Button, Table, Select } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import BindLabel from './componment/BindLabel';
import {
  getCustomerList,
  getSchedulerList,
  getLabelSignList,
  getLabelBind,
} from '../../services/api';

const { Option, OptGroup } = Select;
const { Search } = Input;

const TagList: React.FC<{}> = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [count, setCount] = useState<number>(0);
  const [list, setList] = useState<any[]>([]);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchKey, setSearchKey] = useState<string>('uin');
  const [searchValue, setSearchValue] = useState<string>('');
  const [objType, setObjType] = useState('uin');
  const [columns, setColumns] = useState([]);
  // const [visibleSecondTagEdit, setVisibleSecondTagEdit] = useState('');
  const bindLabelRef = useRef(null);

  const clientColumsConfig = [
    {
      text: '公司名',
      value: 'client_name',
    },
    {
      text: 'uin',
      value: 'uin',
    },
    {
      text: 'qappid',
      value: 'qappid',
    },
    {
      text: '客户类型',
      value: 'account_type',
    },
    {
      text: '行业',
      value: 'industry',
    },
  ];

  const schedulerColumnsConfig = [
    {
      text: '通道id',
      value: 'scheduler_id',
    },
    {
      text: '通道名',
      value: 'scheduler_name',
    },
  ];

  const signColumnsConfig = [
    {
      text: '签名',
      value: 'sign',
    },
  ];

  const sdkappidColumnsConfig = [
    {
      text: 'sdkappid',
      value: 'obj_id',
    },
  ];

  const m: any = new Map([
    ['uin', 'uin'],
    ['scheduler', 'scheduler_id'],
    ['sign', 'sign'],
    ['sdkappid', 'sdkappid'],
  ]);

  useEffect(() => {
    getList();
  }, [searchValue, pageIndex, pageSize, objType]);

  useEffect(() => {
    // const type = 'uin'
    let config: any = [];
    switch (objType) {
      case 'uin':
        config = clientColumsConfig;
        break;
      case 'scheduler':
        config = schedulerColumnsConfig;
        break;
      case 'sign':
        config = signColumnsConfig;
        break;
      case 'sdkappid':
        config = sdkappidColumnsConfig;
        break;
      default:
        config = clientColumsConfig;
    }
    const columnsitem = config.map((el: any) => ({
      title: el.text,
      dataIndex: el.value.toString(),
      key: el.value.toString(),
      align: 'center',
    }));
    columnsitem.push({
      title: '操作',
      // dataIndex: 'staff_name',
      // key: 'staff_name',
      align: 'center',
      render: (row: any) => (
        <>
          {row.name}
          <Button type="link" onClick={() => onMangeTag(row)}>
            管理
          </Button>
        </>
      ),
    });
    setColumns(columnsitem);
  }, [objType]);

  async function getList() {
    setIsLoading(true);
    const params = {
      page_size: pageSize,
      page_index: pageIndex,
    };
    searchValue && (params[searchKey] = searchValue.trim());
    let res: any = {};
    switch (objType) {
      case 'uin':
        res = await getCustomerList({ ...params });
        break;
      case 'scheduler':
        res = await getSchedulerList({ ...params });
        break;
      case 'sign':
        res = await getLabelSignList({ ...params });
        break;
      case 'sdkappid':
        if (searchValue) {
          res = await getLabelBind({
            obj_type: 'sdkappid',
            obj_ids: searchValue.toString(),
          });
        }
        break;
      default:
        res = await getCustomerList({ ...params });
        break;
    }
    setCount(res.data?.count);
    setList(
      objType === 'sdkappid'
        ? res.data?.list.map((el: any) => ({ ...el, sdkappid: el.obj_id.toString() })).slice(0, 1)
        : res.data?.list,
    );
    setIsLoading(false);
  }

  function onSelectChange(val: any) {
    setObjType(val);
  }

  function onMangeTag(row: any) {
    // @ts-ignore
    bindLabelRef?.current?.toggleModalVisible(0, row);
  }

  function onBindLabel() {
    // @ts-ignore
    bindLabelRef?.current?.toggleModalVisible(1);
  }

  return (
    <PageContainer title={false}>
      <div style={{ marginBottom: 20, display: 'flex', justifyContent: 'space-between' }}>
        <div style={{ display: 'flex' }}>
          <Select
            defaultValue="uin"
            style={{ width: 120, marginRight: 10 }}
            onChange={onSelectChange}
          >
            <Option value="uin">客户标签</Option>
            <OptGroup label="资源标签">
              <Option value="scheduler">通道</Option>
              <Option value="sign">签名</Option>
              <Option value="sdkappid">应用id</Option>
            </OptGroup>
          </Select>
          <Input.Group compact>
            {objType === 'uin' && (
              <Select
                value={searchKey}
                onChange={(val: any) => setSearchKey(val)}
                style={{ width: 100 }}
              >
                <Option value="uin">uin</Option>
                <Option value="appid">appid</Option>
                <Option value="client_name">客户名称</Option>
              </Select>
            )}
            <Search
              loading={isLoading}
              enterButton
              placeholder={`请输入${objType === 'uin' ? searchKey : m.get(objType)}`}
              style={{ width: objType === 'uin' ? '64%' : '100%', marginRight: 10 }}
              // value={searchValue}
              onSearch={(val: any) => setSearchValue(val)}
            />
          </Input.Group>
        </div>
        <Button type="primary" icon={<PlusOutlined />} onClick={onBindLabel}>
          打标签
        </Button>
      </div>
      <Table
        size="small"
        // @ts-ignore
        columns={columns}
        dataSource={list}
        rowKey={(record, index) =>
          record.uin + index || record.scheduler_id + index || record.sign + index
        }
        loading={isLoading}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPageSize(page_size),
          onChange: (page) => {
            setPageIndex(page);
          },
        }}
      />
      {/* <TagManage dialogRef={editRef}></TagManage> */}
      <BindLabel dialogRef={bindLabelRef} objType={objType} reload={getList} />
      {/* <AddUserModal dialogRef={moadlEl} reload={getList} /> */}
    </PageContainer>
  );
};

export default TagList;
