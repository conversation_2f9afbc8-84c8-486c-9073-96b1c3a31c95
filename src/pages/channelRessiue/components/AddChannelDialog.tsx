import ChannelSelect from '@/pages/channel/commonComponent/ChannelSelect';
import MncSelect from '@/pages/channel/commonComponent/MncSelect';
import { useDialog } from '@/utils/react-use/useDialog';
import { Form, InputNumber, message, Modal, Select } from 'antd';
import _ from 'lodash';
import React, { useEffect } from 'react';

export const AddChannelDialog = (props: { dialogRef: any; onAdd: (val: any[]) => void }) => {
  const { dialogRef, onAdd } = props;
  const [form] = Form.useForm();
  const [visible, setShowState, defaultVal] = useDialog<{ mnc: string; row: any }>(dialogRef);
  const { mnc, row } = defaultVal;
  const [selectedChannel, setSelectedChannel] = React.useState<any[]>([]);

  const onClose = () => {
    setShowState(false);
    form.resetFields();
  };

  const handleOk = () => {
    form.submit();
    const tacticId = form.getFieldValue('tactic_id');
    const providerId = form.getFieldValue('channel_id');
    if (!tacticId && !providerId) {
      message.error('请选择标品或通道');
      return;
    }
    const values = form.getFieldsValue();
    console.log(selectedChannel);
    const tactic =
      _.flatMap(values.tactic_id, (el: number) =>
        values.mnc.map((mnc: string) => ({
          channel_id: el,
          channel_type: 2,
          channel_name: selectedChannel.find((item) => item.value === el)?.label,
          mcc: row.mcc,
          mnc: mnc.split('_')?.[1],
          weight: values.weight,
          key: `${mnc.split('_')?.[1]}_${el}`,
        })),
      ) ?? [];
    const provider =
      _.flatMap(values.channel_id, (el: number) =>
        values.mnc.map((mnc: string) => ({
          channel_id: el,
          channel_name: selectedChannel.find((item) => item.value === el)?.label,
          channel_type: 0,
          mcc: row.mcc,
          mnc: mnc.split('_')?.[1],
          weight: values.weight,
          key: `${mnc.split('_')?.[1]}_${el}`,
        })),
      ) ?? [];
    onAdd([...tactic, ...provider]);
    onClose();
  };

  useEffect(() => {
    form.setFieldsValue({ mnc });
  }, [form, mnc]);

  return (
    <Modal title="添加通道" open={visible} onOk={handleOk} onCancel={onClose} destroyOnClose>
      <Form form={form} labelCol={{ span: 4 }} initialValues={{ channel_type: 0, weight: 10 }}>
        <Form.Item label="通道类型" name="channel_type" required>
          <Select
            style={{ width: 200 }}
            options={[
              { label: '标品', value: 2 },
              { label: '单通道', value: 0 },
            ]}
          ></Select>
        </Form.Item>
        <Form.Item label="运营商" name="mnc" required>
          <MncSelect
            mode={true ? 'multiple' : undefined}
            initialValues={{ mcc: row?.mcc, mnc: row?.category === 1 ? [row?.mnc] : undefined }}
            onChange={(mnc: any) => form.setFieldsValue({ mnc })}
            value={form.getFieldValue('mnc')}
          />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.channel_type !== curValues.channel_type
          }
        >
          {({ getFieldValue }) => {
            const channelType = getFieldValue('channel_type');
            return channelType === 0 ? (
              <Form.Item label="单通道" name="channel_id" required>
                <ChannelSelect
                  value={form.getFieldValue('channel_id')}
                  onChange={(val, context) => {
                    form.setFieldsValue({ channel_id: val });
                    setSelectedChannel(context);
                  }}
                  mode="multiple"
                  type={0}
                  pageSize={200}

                  // placeholder={FlagType[typeValue]}
                />
              </Form.Item>
            ) : (
              <Form.Item label="标品" name="tactic_id" required>
                <ChannelSelect
                  value={form.getFieldValue('tactic_id')}
                  onChange={(val, context) => {
                    form.setFieldsValue({ tactic_id: val });
                    setSelectedChannel(context);
                  }}
                  mode="multiple"
                  type={2}
                  pageSize={200}
                  loadFnParams={{ 2: { country_codes: [row?.country_code] } }}
                  // placeholder={FlagType[typeValue]}
                />
              </Form.Item>
            );
          }}
        </Form.Item>

        <Form.Item label="权重" name="weight" rules={[{ required: true }]}>
          <InputNumber controls={true} style={{ width: 150 }} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
