import { Form, InputNumber, Select, Switch } from 'antd';
import React, { useEffect } from 'react';

export const enableFormFieldsKey = [
  'status_reissue_enable',
  'reissue_status_array',
  'status_reissue_percent',
  'status_reissue_time',
];

export default function SupplyStatusEnable(props: {
  form: any;
  initialValues: any;
  ressiueStatus: Selection[];
}) {
  const { form, initialValues, ressiueStatus } = props;

  useEffect(() => {
    const formValues = enableFormFieldsKey.reduce((obj: any, key) => {
      return { ...obj, [key]: initialValues?.[key] };
    }, {});
    form.setFieldsValue(formValues);
  }, [form, initialValues]);

  return (
    <Form layout="inline" form={form} initialValues={initialValues}>
      <Form.Item name="status_reissue_enable" label="启用补发状态">
        <Switch />
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) =>
          prevValues.status_reissue_enable !== curValues.status_reissue_enable
        }
      >
        {({ getFieldValue }) => {
          const disabled = !getFieldValue('status_reissue_enable');
          const rules = disabled ? undefined : [{ required: true }];
          return (
            <>
              <Form.Item name="reissue_status_array" label="补发状态选择" rules={rules}>
                <Select
                  mode="multiple"
                  options={ressiueStatus}
                  style={{ width: 200 }}
                  disabled={disabled}
                  optionFilterProp="label"
                  popupMatchSelectWidth={false}
                />
              </Form.Item>
              <Form.Item name="status_reissue_time" label="补发有效时间" rules={rules}>
                <InputNumber
                  controls={true}
                  style={{ width: 150 }}
                  disabled={disabled}
                  addonAfter="秒"
                ></InputNumber>
              </Form.Item>
              <Form.Item name="status_reissue_percent" label="补发比例" rules={rules}>
                <InputNumber
                  controls={true}
                  style={{ width: 150 }}
                  disabled={disabled}
                  addonAfter="%"
                ></InputNumber>
              </Form.Item>
            </>
          );
        }}
      </Form.Item>
    </Form>
  );
}
