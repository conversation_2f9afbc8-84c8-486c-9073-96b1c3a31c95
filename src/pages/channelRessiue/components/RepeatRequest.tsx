import { Form, InputNumber, Select, Switch } from 'antd';
import React, { useEffect } from 'react';

export const requestFormFieldsKey = [
  'repeat_request_enable',
  'repeat_request_time',
  'repeat_request_start_times',
  // 'repeat_request_percent',
  'request_status_array',
];

export default function RepeatRequest(props: {
  form: any;
  initialValues: any;
  ressiueStatus: Selection[];
}) {
  const { form, initialValues, ressiueStatus } = props;
  useEffect(() => {
    const formValues = requestFormFieldsKey.reduce((obj: any, key) => {
      return { ...obj, [key]: initialValues?.[key] };
    }, {});
    form.setFieldsValue(formValues);
  }, [form, initialValues]);
  return (
    <Form layout="inline" form={form}>
      <Form.Item name="repeat_request_enable" label="重复请求配置">
        <Switch />
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) =>
          prevValues.repeat_request_enable !== curValues.repeat_request_enable
        }
      >
        {({ getFieldValue }) => {
          const disabled = !getFieldValue('repeat_request_enable');
          const rules = disabled ? undefined : [{ required: true }];
          return (
            <>
              <Form.Item name="request_status_array" label="补发状态选择">
                <Select
                  mode="multiple"
                  options={ressiueStatus}
                  style={{ width: 200 }}
                  disabled={disabled}
                  optionFilterProp="label"
                  popupMatchSelectWidth={false}
                />
              </Form.Item>
              <Form.Item name="repeat_request_time" label="请求时间间隔" rules={rules}>
                <InputNumber
                  controls={true}
                  style={{ width: 150 }}
                  disabled={disabled}
                  addonAfter="秒"
                ></InputNumber>
              </Form.Item>
              <Form.Item name="repeat_request_start_times" label="触发请求轮次" rules={rules}>
                <InputNumber
                  controls={true}
                  style={{ width: 150 }}
                  disabled={disabled}
                  addonAfter="次"
                ></InputNumber>
              </Form.Item>
              {/* <Form.Item name="repeat_request_percent" label="补发比例" rules={rules}>
                <InputNumber
                  controls={true}
                  style={{ width: 150 }}
                  disabled={disabled}
                  addonAfter="%"
                ></InputNumber>
              </Form.Item> */}
            </>
          );
        }}
      </Form.Item>
    </Form>
  );
}
