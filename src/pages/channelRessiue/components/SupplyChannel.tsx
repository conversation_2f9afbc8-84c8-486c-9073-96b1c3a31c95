import { DeleteOutlined, ExclamationCircleTwoTone, PlusCircleOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { Button, Form, Typography } from 'antd';
import _ from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { AddChannelDialog } from './AddChannelDialog';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { getRessiueChannel } from '@/services/ressiue';
import { history } from 'umi';
import { ChannelInfo } from '../type';
import { getOperatorName } from '@/utils/utils';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import { useAsyncFn } from 'react-use';

const { Text } = Typography;

function flattenChannel(dataSource: any[]) {
  return dataSource
    .filter((el) => el.children?.length)
    .reduce((acc, cur) => {
      return [...acc, ...cur.children];
    }, []);
}

export function groupByMnc(_data: any) {
  return _data.reduce((acc: any, cur: any) => {
    const parentKey = cur.mnc;
    const childKey = `${cur.mnc}_${cur.channel_id}`;
    const index = acc.findIndex((el: any) => el.mnc === cur.mnc);
    if (index === -1) {
      return [
        ...acc,
        {
          mnc: cur.mnc,
          mcc: cur.mcc,
          key: parentKey,
          children: [{ ...cur, key: childKey }],
        },
      ];
    }
    acc[index].children.push({ ...cur, key: childKey });
    return acc;
  }, []);
}

export default function SupplyConfig(props: { channelRef: any; smsType: string | number }) {
  const { channelRef, smsType: _smsType } = props;
  const { row } = history?.location?.state || {};

  const dialogRef = useDialogRef();
  const isTactic = history.location?.pathname.includes('/resources/operator-tactic/ressiue');
  const type = isTactic ? 'product' : history?.location?.pathname.split('/').at(-2) || '';
  const idKey = isTactic ? 'tactic_id' : type;
  const [mccMncInfo] = useMccMncInfo();

  const [dataSource, setDataSource] = useState<any[]>(() => []);
  // 解绑，绑定，编辑
  // const [params, setParams] = useState<any>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const editableKeys = dataSource?.reduce((acc, cur) => {
    return _.flatten([
      ...acc,
      cur.children?.map((item: any) => `${item?.mnc}_${item?.channel_id}`),
    ]);
  }, []);
  const [initialList, setInitialList] = useState<any[]>([]);

  const [, fetchChannel] = useAsyncFn(async () => {
    const res: any = await getRessiueChannel({
      id: row[idKey],
      type,
      mcc: row.mcc,
      sms_type: _smsType,
    });
    const uniqKeyData =
      res.data?.map((el: any) => ({ ...el, key: `${el.mnc}_${el.channel_id}` })) ?? [];
    const data: any[] = groupByMnc(
      uniqKeyData?.map((el: any) => {
        if (el.channel_type === 0) {
          return {
            ...el,
            channel_name: `${el.account_name}_${el.supplier_name}(${el.channel_id})`,
          };
        }
        return el;
      }) ?? [],
    );
    setInitialList(uniqKeyData);
    setDataSource(data);
    return res.data ?? [];
  }, [_smsType, idKey, row, type]);

  const columns: ProColumns[] = useMemo(() => {
    return [
      {
        title: '运营商',
        key: 'mnc',
        render: (_, row: ChannelInfo) => {
          return row.channel_id ? '' : getOperatorName(mccMncInfo, row.mnc, { mcc: row.mcc });
        },
        width: 300,
        editable: false,
      },
      {
        title: '通道信息',
        key: 'resources',
        render: (v, _row: ChannelInfo) => {
          const resources = JSON.parse(row.resources ?? '{}');
          const flattenResources = _.flatten(
            _.map(resources, (v, k) => {
              return v.accounts;
            }).filter((v) => v),
          );
          const isRepeat = _.some(
            flattenResources,
            (v) => v.mcc === _row?.mcc && v.mnc === _row?.mnc && v.account_id === _row?.channel_id,
          );
          return (
            <>
              {_row?.channel_name ? `${_row?.channel_name}` : null}
              {isRepeat ? (
                <p>
                  <ExclamationCircleTwoTone twoToneColor="#ff4d4f" />
                  <Text style={{ fontSize: 12 }} type="danger">
                    与主通道重复
                  </Text>
                </p>
              ) : null}
            </>
          );
        },
        editable: false,
      },
      {
        title: '权重',
        key: 'weight',
        valueType: 'digit',
        dataIndex: 'weight',
        fieldProps: {
          min: 0,
          max: 100,
        },
        width: 200,
      },
      {
        title: '操作',
        valueType: 'option',
        width: 180,
        align: 'center',
        render: (_, row) => {
          return row.channel_id ? (
            <Button>解绑</Button>
          ) : (
            <Button
              type="link"
              onClick={() => {
                dialogRef.current?.open({
                  mnc: [`${row.mcc}_${row.mnc}`],
                  row,
                });
              }}
            >
              添加通道
            </Button>
          );
        },
      },
    ];
  }, [dialogRef, mccMncInfo, row.resources]);

  // function reload() {
  //   setDataSource(initialList);
  //   setSelectedRowKeys([]);
  // }

  useEffect(() => {
    // channelRef.current = {};
    channelRef.current = { retry: fetchChannel };
  }, [channelRef, fetchChannel]);

  useEffect(() => {
    fetchChannel();
  }, [fetchChannel]);

  function batchUnBind() {
    setDataSource((prev) =>
      prev
        .map((el) => ({
          ...el,
          children: el.children.filter(
            (item: any) => !selectedRowKeys.includes(`${item?.mnc}_${item?.channel_id}`),
          ),
        }))
        .filter((el) => el?.children?.length),
    );
    setSelectedRowKeys([]);
  }

  function onAdd(val: any) {
    const data = groupByMnc(val);
    const mergedArr = _.reduce(
      data,
      (acc, current) => {
        const existingItem = _.find(acc, { mnc: current.mnc });
        if (existingItem) {
          existingItem.children = _.uniqBy(
            _.concat(existingItem.children, current.children),
            'key',
          );
        } else {
          acc.push(current);
        }
        return acc;
      },
      dataSource,
    ).map((el) => ({
      ...el,
      children: el.children?.filter((el: any) => el),
    }));
    setDataSource([...mergedArr]);
  }

  const _params = useMemo(() => {
    const data: any[] = flattenChannel(dataSource);
    const deleteItems = _.differenceBy(initialList, data, 'key');
    const editItems = _.differenceWith(data, initialList, (i1, i2) => {
      return i1.key === i2.key && i1.weight === i2.weight;
    });
    const addItems = _.differenceBy(data, initialList, 'key');
    return {
      edit: _.differenceBy(editItems, addItems, 'key').map((el) => ({
        id: el.channel_id,
        weight: el.weight,
        mnc: el.mnc,
        sms_type: _smsType,
      })),
      delete: deleteItems.map((el) => ({ id: el.channel_id, mnc: el.mnc, sms_type: _smsType })),
      add: addItems.map((el) => ({
        channel_id: el.channel_id,
        channel_type: el.channel_type,
        sms_type: _smsType,
        weight: el.weight,
        mnc: el.mnc,
      })),
    };
  }, [dataSource, initialList, _smsType]);

  useEffect(() => {
    channelRef.current = {
      ...channelRef.current,
      _params,
      dataSource,
      allChannel: flattenChannel(dataSource),
    };
  }, [_params, channelRef, dataSource]);

  const expandedRowKeys = useMemo(() => {
    return dataSource
      .filter((el) => el.children?.length)
      ?.reduce((acc, cur) => {
        return [...acc, cur.key, ...cur.children?.map((item: any) => item?.key)];
      }, []);
  }, [dataSource]);

  return (
    <>
      <Form layout="inline">
        <Form.Item label="补发通道" required>
          <Button
            onClick={() => {
              dialogRef.current?.open({
                mnc: [],
                row,
              });
            }}
            style={{ marginLeft: 25 }}
            type="primary"
          >
            <PlusCircleOutlined />
            添加通道
          </Button>
          <Button onClick={batchUnBind} style={{ marginLeft: 10 }}>
            <DeleteOutlined />
            批量解绑
          </Button>
          {/* <Button onClick={reload} style={{ marginLeft: 10 }}>
            <RedoOutlined />
            回退至初始状态
          </Button> */}
          <EditableProTable<any>
            style={{ width: 1200, marginTop: 20 }}
            columns={columns}
            rowKey="key"
            value={dataSource.filter((el) => el.children?.length)}
            onChange={setDataSource}
            recordCreatorProps={false}
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys,
              alwaysShowAlert: false,
              checkStrictly: false,
              onChange: (_selectedRowKeys: React.Key[]) => {
                setSelectedRowKeys(_selectedRowKeys);
              },
            }}
            editable={{
              type: 'multiple',
              editableKeys,
              deleteText: '解绑',
              actionRender: (row, config, defaultDoms) => {
                return [defaultDoms.delete];
              },
              onValuesChange: (record, recordList) => {
                setDataSource(recordList);
              },
            }}
            expandable={{
              defaultExpandAllRows: true,
              expandedRowKeys,
            }}
          />
        </Form.Item>
      </Form>
      <AddChannelDialog
        dialogRef={dialogRef}
        onAdd={(val: ChannelInfo[] = []) => {
          onAdd(val);
        }}
      ></AddChannelDialog>
    </>
  );
}
