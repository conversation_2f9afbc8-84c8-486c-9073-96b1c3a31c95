import { Form, Input, InputNumber, Select, Switch } from 'antd';
import React, { useEffect } from 'react';

const durationTypeOptions = [
  {
    label: '按停留时长',
    value: 0,
  },
  {
    label: '按未变成该状态时长',
    value: 1,
  },
];

export const keepFormFieldsKey = [
  'status_keep_enable',
  'status_keep_time',
  'status_keep_percent',
  'keep_status_array',
];

export default function SupplyStatusStay(props: {
  form: any;
  initialValues: any;
  ressiueStatus: Selection[];
}) {
  const { form, initialValues, ressiueStatus } = props;

  useEffect(() => {
    const formValues = keepFormFieldsKey.reduce((obj: any, key) => {
      return { ...obj, [key]: initialValues?.[key] };
    }, {});
    form.setFieldsValue(formValues);
  }, [form, initialValues]);

  return (
    <Form layout="inline" form={form}>
      <Form.Item name="status_keep_enable" label="补发状态停留">
        <Switch />
      </Form.Item>
      <Form.Item
        noStyle
        shouldUpdate={(prevValues, curValues) =>
          prevValues.status_keep_enable !== curValues.status_keep_enable
        }
      >
        {({ getFieldValue }) => {
          const disabled = !getFieldValue('status_keep_enable');
          const rules = disabled ? undefined : [{ required: true }];
          return (
            <>
              <Form.Item name="status_keep_time" label="停留时长" rules={rules}>
                <InputNumber
                  controls={true}
                  style={{ width: 200 }}
                  disabled={disabled}
                  addonAfter="秒"
                ></InputNumber>
              </Form.Item>
              <Form.Item name="keep_status_array" label="补发状态选择" rules={rules}>
                <Select
                  mode="multiple"
                  options={ressiueStatus}
                  style={{ width: 200 }}
                  disabled={disabled}
                  optionFilterProp="label"
                  popupMatchSelectWidth={false}
                />
              </Form.Item>
              <Form.Item name="status_keep_percent" label="补发比例" rules={rules}>
                <InputNumber
                  controls={true}
                  style={{ width: 150 }}
                  disabled={disabled}
                  addonAfter="%"
                ></InputNumber>
              </Form.Item>
            </>
          );
        }}
      </Form.Item>
    </Form>
  );
}
