import React, { useMemo, useRef } from 'react';
import EnableSupplyStatus from './components/SupplyStatusEnable';
import RepeatRequest from './components/RepeatRequest';
import { Button, Divider, Form, message, Modal } from 'antd';
import SupplyConfig from './components/SupplyChannel';
import SupplyStatusStay from './components/SupplyStatusStay';
import { history } from 'umi';
import _, { isArray } from 'lodash';
import { findLabel } from '@/utils/utils';
import { smsType } from '@/const/const';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import {
  deleteRessiueConfiguration,
  getRessiueConfiguration,
  getRessiueStatus,
  updateRessiueConfiguration,
} from '@/services/ressiue';
import { useAsyncFn } from 'react-use';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

export default function RessiueConfig(Props: { _smsType: number }) {
  const { _smsType } = Props;
  const isTactic = history.location?.pathname.includes('/resources/operator-tactic/ressiue');
  const type = isTactic ? 'product' : history?.location?.pathname.split('/').at(-2) || '';
  const idKey = isTactic ? 'tactic_id' : type;
  const { row } = history?.location?.state || {};
  const [formEnable] = Form.useForm();
  const [formStay] = Form.useForm();
  const [formRepeat] = Form.useForm();
  const channelRef = useRef<any>();
  const { regionOptions } = useFetchCountryInfo();

  const { value: configuration, retry: getConfiguration } = useAsyncRetryFunc(async () => {
    const res = await getRessiueConfiguration({
      id: row[idKey],
      type,
      mcc: row.mcc,
      mnc: '000',
      sms_type: _smsType,
    });
    return _.mapValues(res.data, (v) => {
      return isArray(v) ? v.filter((a) => a) : v;
    });
  }, [_smsType, idKey, row, type]);

  const { value: ressiueStatus } = useAsyncRetryFunc(async () => {
    const res = await getRessiueStatus();
    return res.data ?? [];
  }, []);

  const ressiueStatusOptions = useMemo(() => {
    return (
      ressiueStatus?.map((el) => ({
        label: `${el.standard_code}(${el.standard_code_desc_cn})`,
        value: el.standard_code,
      })) || []
    );
  }, [ressiueStatus]);

  const [state, updateConfig] = useAsyncFn(
    async (params) => {
      const res = await updateRessiueConfiguration(params);
      if (res.code === 0) {
        getConfiguration();
        channelRef.current?.retry();
        message.success('操作成功');
      }
      return res;
    },
    [getConfiguration],
  );

  async function handleSubmit() {
    // const weightCollection = channelRef.current?.dataSource.reduce((acc, cur) => {
    //   return {
    //     ...acc,
    //     [getOperatorName(mccMncInfo, cur.mnc, { mcc: cur.mcc })]: cur.children?.reduce(
    //       (acc: number, cur: any) => (acc += cur.weight),
    //       0,
    //     ),
    //   };
    // }, {});
    // const n = _.keys(_.pickBy(weightCollection, (v, k) => v !== 100));
    // if (n.length) {
    //   message.error({
    //     content: `运营商【${n.join(',')}】的通道权重之和必须为100`,
    //   });
    //   return;
    // }
    const values1 = await formEnable.validateFields();
    const values2 = await formStay.validateFields();
    const values3 = await formRepeat.validateFields();
    if (
      (values1.status_reissue_enable || values2.status_keep_enable) &&
      channelRef.current?.allChannel.length === 0
    ) {
      message.error('至少配置一个通道');
      return;
    }
    // if (
    //   !values1.status_reissue_enable &&
    //   !values2.status_keep_enable &&
    //   !values3.repeat_request_enable
    // ) {
    //   message.error('至少配置一种场景');
    //   return;
    // }
    const params = {
      id: row[idKey],
      type,
      mcc: row.mcc,
      mnc: '000',
      sms_type: _smsType.toString(),
      config: [
        {
          rule: 'enableReissueStatus',
          status: values1.reissue_status_array?.join(',') || undefined,
          ..._.omit(values1, ['reissue_status_array']),
          status_reissue_enable: values1.status_reissue_enable ? 1 : 0,
        },
        {
          rule: 'reissueStatusStays',
          status: values2.keep_status_array?.join(',') || undefined,
          ..._.omit(values2, ['keep_status_array']),
          status_keep_enable: values2.status_keep_enable ? 1 : 0,
        },
        {
          rule: 'requestConfiguration',
          status: values3.request_status_array?.join(',') || undefined,
          ..._.omit(values3, ['request_status_array']),
          repeat_request_enable: values3.repeat_request_enable ? 1 : 0,
        },
      ],
      channel: channelRef.current?._params,
    };

    updateConfig(params);
  }

  function goBack() {
    if (isTactic) {
      const isFromStrategy = history.location?.state?.from === 'strategy';
      history.push(
        isFromStrategy ? '/channel/strategy/list' : '/resources/operator-tactic',
        history.location.state,
      );
      return;
    }
    history.push(`/channel/${type}?from=1`, history.location.state);
  }

  function handleDelete() {
    Modal.confirm({
      title: '删除确认',
      content: '确定删除该补发策略吗？删除后无法恢复，请确认。',
      onOk: async () => {
        const res = await deleteRessiueConfiguration({
          id: row[idKey],
          mcc: row.mcc,
          mnc: '000',
          sms_type: _smsType.toString(),
          type,
        });
        if (res.code === 0) {
          getConfiguration();
          channelRef.current?.retry();
          message.success('删除补发成功');
        }
      },
    });
  }

  return (
    <>
      <Button onClick={() => goBack()} style={{ marginBottom: '20px' }}>
        返回上一页
      </Button>
      <Form layout="inline">
        <Form.Item name="country_code" label="国家">
          {findLabel(regionOptions ?? [], row.country_code)}
        </Form.Item>
        <Form.Item name="type" label={isTactic ? '标品' : type}>
          {isTactic ? row.name : row[type]}
        </Form.Item>
        {/* {isTactic ? (
          <Form.Item name="mnc" label="运营商">
            {getOperatorName(mccMncInfo, row.mnc, {
              mcc: row.mcc,
            })}
          </Form.Item>
        ) : (
          <Form.Item name="country_code" label="国家/地区">
            {row.country_name}
          </Form.Item>
        )} */}
        <Form.Item name="sms_type" label="短信类型">
          {findLabel(smsType, _smsType)}
        </Form.Item>
      </Form>
      <h3 style={{ marginTop: '20px' }}>补发场景</h3>
      <EnableSupplyStatus
        form={formEnable}
        initialValues={configuration}
        ressiueStatus={ressiueStatusOptions}
      ></EnableSupplyStatus>
      <Divider></Divider>
      <SupplyStatusStay
        form={formStay}
        initialValues={configuration}
        ressiueStatus={ressiueStatusOptions}
      ></SupplyStatusStay>
      <Divider></Divider>
      <RepeatRequest
        form={formRepeat}
        initialValues={configuration}
        ressiueStatus={ressiueStatusOptions}
      ></RepeatRequest>
      <Divider></Divider>
      <SupplyConfig channelRef={channelRef} smsType={_smsType}></SupplyConfig>
      <div style={{ textAlign: 'center', marginTop: '50px' }}>
        {type === 'product' ? null : (
          <Button onClick={() => handleDelete()} style={{ marginRight: '50px' }}>
            删除补发
          </Button>
        )}
        <Button type="primary" onClick={() => handleSubmit()} loading={state.loading}>
          提交
        </Button>
      </div>
    </>
  );
}
