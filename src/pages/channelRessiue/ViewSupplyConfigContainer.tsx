import React from 'react';
import { Button, Tabs } from 'antd';
import type { TabsProps } from 'antd';
import { history } from 'umi';
import { PageContainer } from '@ant-design/pro-layout';
import ViewSupplyConfig from './ViewSupplyConfig';

const onChange = (key: string) => {
  console.log(key);
};

const items: TabsProps['items'] = [
  {
    key: '1',
    label: 'OTP',
    children: <ViewSupplyConfig _smsType={1}></ViewSupplyConfig>,
  },
  {
    key: '2',
    label: '通知',
    children: <ViewSupplyConfig _smsType={2}></ViewSupplyConfig>,
  },
  {
    key: '4',
    label: '营销',
    children: <ViewSupplyConfig _smsType={4}></ViewSupplyConfig>,
  },
];

const ViewSupplyConfigContainer: React.FC = () => {
  const isTactic = history.location?.pathname.includes('/resources/operator-tactic/ressiue');
  const { row } = history?.location?.state || {};
  const type = isTactic ? 'product' : history?.location?.pathname.split('/').at(-2) || '';

  function goBack() {
    if (isTactic) {
      history.push('/resources/operator-tactic', history.location.state);
      return;
    }
    history.push(`/channel/${type}?from=1`, history.location.state);
  }

  return (
    <PageContainer>
      <Button onClick={() => goBack()} style={{ marginBottom: '20px' }}>
        返回上一页
      </Button>
      {isTactic ? (
        <ViewSupplyConfig _smsType={row.sms_type}></ViewSupplyConfig>
      ) : (
        <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
      )}
    </PageContainer>
  );
};

export default ViewSupplyConfigContainer;
