export type ChannelInfo = {
  channel_type: 0 | 1;
  channel_id: number;
  channel_name: string;
  supplier_name: string;
  account_name: string;
  weight: number;
  mnc: string;
  mcc: string;
};

export type statusType = {
  status: string;
  status_set_id: string;
  status_name: string;
};

export type ConfigurationInfo = {
  keep_status_array: statusType[];
  reissue_status_array: statusType[];
  repeat_request_enable: number;
  // repeat_request_percent: number;
  repeat_request_start_times: number;
  repeat_request_time: number;
  request_status_array: statusType[];
  status_keep_enable: number;
  status_keep_percent: number;
  status_keep_time: number;
  status_reissue_enable: number;
  status_reissue_percent: number;
  status_reissue_time: number;
};

export type ruleTypes = 'requestConfiguration' | 'reissueStatusStays' | 'enableReissueStatus';
