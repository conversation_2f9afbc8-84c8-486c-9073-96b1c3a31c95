import React, { useState } from 'react';
import { Tabs } from 'antd';
import type { TabsProps } from 'antd';
import RessiueConfig from './Ressiue';
import { history } from 'umi';
import { PageContainer } from '@ant-design/pro-layout';

const RessiueContainer: React.FC = () => {
  const [activeKey, setActiveKey] = useState<string>('1');
  const isTactic = history.location?.pathname.includes('/resources/operator-tactic/ressiue');
  const { row } = history?.location?.state || {};

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'OTP',
      children: <RessiueConfig _smsType={1} key={1}></RessiueConfig>,
    },
    {
      key: '2',
      label: '通知',
      children: <RessiueConfig _smsType={2} key={2}></RessiueConfig>,
    },
    {
      key: '4',
      label: '营销',
      children: <RessiueConfig _smsType={4} key={4}></RessiueConfig>,
    },
  ];

  const onChange = (key: string) => {
    setActiveKey(key);
  };

  return (
    <PageContainer>
      {isTactic ? (
        <RessiueConfig _smsType={row.sms_type}></RessiueConfig>
      ) : (
        <Tabs
          activeKey={activeKey}
          defaultActiveKey="1"
          items={items}
          onChange={onChange}
          destroyInactiveTabPane={true}
        />
      )}
    </PageContainer>
  );
};

export default RessiueContainer;
