import { ressiueRule } from '@/pages/channelRessiue/components/const';
import { requestFormFieldsKey } from '@/pages/channelRessiue/components/RepeatRequest';
import { enableFormFieldsKey } from '@/pages/channelRessiue/components/SupplyStatusEnable';
import { keepFormFieldsKey } from '@/pages/channelRessiue/components/SupplyStatusStay';
import { ruleTypes } from '@/pages/channelRessiue/type';
import { getRessiueChannel, getRessiueConfiguration } from '@/services/ressiue';
import { findLabel } from '@/utils/utils';
import { Button, Form, List, Modal, Spin, Table, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import _ from 'lodash';
import React, { useEffect, useMemo } from 'react';
import { useAsyncFn } from 'react-use';
import { history } from '@umijs/max';
import useMccMncInfo from '@/components/Hooks/UseFetchMccMncInfo';
import OperatorTacticInfo from '../resources/OperatorTacticInfo';
import { smsType } from '@/const/const';
import useFetchCountryInfo from '@/components/Hooks/UseFetchCountryInfo';

const { Text } = Typography;

interface SupplyConfig {
  enable: number;
  rule: number;
  array: string[];
  percent: number;
  repeat_request_start_times: number;
  status_keep_time: number;
  status_reissue_time: number;
  repeat_request_time: number;
}

function genarateKeys(obj: any, keys: string[], rule: ruleTypes): SupplyConfig {
  return _.entries(_.pick(obj, keys)).reduce((acc, [key, value]) => {
    const _key = key.split('_').at(-1) as string;
    const isModifyKey = ['percent', 'enable', 'array'].includes(_key);
    return { ...acc, [isModifyKey ? _key : key]: value, rule };
  }, {}) as SupplyConfig;
}

export default function ViewSupplyConfig(props: {
  _smsType: number;
  showChannel?: boolean;
  from?: 'dialog' | 'route';
  row?: any;
}) {
  const { _smsType, showChannel = true, row: _row, from = 'route' } = props;

  const row = from === 'route' ? history?.location?.state?.row : _row;
  const isTactic = !!row.tactic_id;
  const type = isTactic ? 'product' : history?.location?.pathname.split('/').at(-2) || '';
  const idKey = isTactic ? 'tactic_id' : type;
  const [mccMncInfo] = useMccMncInfo();
  const { regionOptions } = useFetchCountryInfo();

  console.log('type', type);

  const [state, fetchConfig] = useAsyncFn(async () => {
    const res = await getRessiueConfiguration({
      id: row[idKey],
      type,
      mcc: row.mcc,
      mnc: '000',
      sms_type: _smsType,
    });
    return res.data;
  }, [_smsType, idKey, row, type]);

  const [channel, fetchChannel] = useAsyncFn(async () => {
    const res: any = await getRessiueChannel({
      id: row[idKey],
      type,
      mcc: row.mcc,
      sms_type: _smsType,
    });
    return res.data ?? [];
  }, [_smsType, idKey, row, type]);

  const dataSource = useMemo(() => {
    const enableConfig = genarateKeys(state.value, enableFormFieldsKey, 'enableReissueStatus');
    const requestConfig = genarateKeys(state.value, requestFormFieldsKey, 'requestConfiguration');
    const keepConfig = genarateKeys(state.value, keepFormFieldsKey, 'reissueStatusStays');

    return [enableConfig, requestConfig, keepConfig].filter((el: SupplyConfig) => el.enable === 1);
  }, [state.value]);

  useEffect(() => {
    fetchConfig();
    fetchChannel();
  }, [fetchChannel, fetchConfig]);

  const columns: ColumnsType<any> = [
    {
      title: (
        <div>
          <span>补发通道</span>
          <div>(账号ID-供应商简称 通道名-当前成本-实时CR-近24小时下发总量-通道占比)</div>
          <div style={{ color: '#aaa' }}>(cr为null表示无足够来自腾讯系内自用流量来监控CR)</div>
        </div>
      ),
      key: 'reissue_resources',
      width: 600,
      render: () => {
        const _data = _.groupBy(channel.value, 'mnc');
        const data = _.map(_data, (v, k) => {
          return { mnc: k, accounts: v };
        });
        const listRender = (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <List style={{ width: 680 }}>
              {data?.map((item: any) => {
                console.log(item);
                const name =
                  mccMncInfo.find(
                    (el) =>
                      el.mcc.toString() === row.mcc?.toString() &&
                      item.mnc?.toString() === el.mnc.toString(),
                  )?.operator_name ?? '--';
                return (
                  <List.Item key={item.mnc}>
                    <p style={{ width: 150, marginBottom: 0 }}>{`${name}`}</p>
                    <p style={{ width: 450, marginBottom: 0 }}>
                      {item.accounts?.length
                        ? item.accounts.map((account: any) => {
                            const isTactic = account?.channel_type.toString() === '2';
                            return (
                              // eslint-disable-next-line react/jsx-key
                              <div>
                                {isTactic ? (
                                  <Text>{`${account.channel_id} - ${
                                    account.channel_name
                                  } - ${Number(account.price).toFixed(5)} - ${
                                    account.cr === null ? 'null' : `${account.cr}%`
                                  } - ${account.total} - ${account.weight}`}</Text>
                                ) : (
                                  <Text>{`${account.channel_id} - ${
                                    account.supplier_abbr_name ?? account.supplier_name
                                  } ${account.channel_name} - ${Number(account.price).toFixed(
                                    5,
                                  )} - ${account.cr === null ? 'null' : `${account.cr}%`} - ${
                                    account.total
                                  } - ${account.weight}`}</Text>
                                )}
                                {isTactic && (
                                  <Button
                                    type="link"
                                    onClick={() =>
                                      Modal.info({
                                        title: '标品详情',
                                        width: '60%',
                                        content: (
                                          <OperatorTacticInfo
                                            isSimple={true}
                                            tacticId={account.channel_id}
                                          />
                                        ),
                                      })
                                    }
                                  >
                                    查看标品
                                  </Button>
                                )}
                              </div>
                            );
                          })
                        : '--'}
                    </p>
                  </List.Item>
                );
              })}
            </List>
          </div>
        );
        return listRender;
      },
      onCell: (_, index) => {
        if (index === 0) {
          return { rowSpan: 3 };
        }
        // These two are merged into above cell
        if (index === 1) {
          return { rowSpan: 0 };
        }
        if (index === 2) {
          return { colSpan: 0 };
        }

        return {};
      },
    },
    {
      title: '补发场景',
      // dataIndex: 'rule',
      key: 'rule',
      render: (row: SupplyConfig) => findLabel(ressiueRule, row.rule),
    },
    {
      title: '补发状态',
      // dataIndex: 'status',
      key: 'array',
      render: (row: SupplyConfig) =>
        row.array?.map((el: string, idx: number) => <div key={idx}>{el}</div>),
    },
    {
      title: '补发有效时间',
      key: 'status_reissue_time',
      render: (row: SupplyConfig) =>
        !_.isNil(row.status_reissue_time) ? `${row.status_reissue_time} 秒` : '-',
    },
    {
      title: '补发状态停留',
      key: 'status_keep_time',
      render: (row: SupplyConfig) =>
        !_.isNil(row.status_keep_time) ? `${row.status_keep_time} 秒` : '-',
    },
    {
      title: '请求时间间隔',
      key: 'repeat_request_time',
      render: (row: SupplyConfig) =>
        !_.isNil(row.repeat_request_time) ? `${row.repeat_request_time} 秒` : '-',
    },
    {
      title: '触发补发请求轮次',
      key: 'repeat_request_start_times',
      render: (row: SupplyConfig) =>
        !_.isNil(row.repeat_request_start_times) ? `${row.repeat_request_start_times} 次` : '-',
    },
    {
      title: '补发比例',
      key: 'percent',
      render: (row: SupplyConfig) => (!_.isNil(row.percent) ? `${row.percent} %` : '-'),
    },
    {
      title: '操作',
      key: 'operate',
      render: () => (
        <Button
          type="link"
          onClick={() => {
            history.push(`/channel/${type}/ressiue`, history.location.state);
          }}
        >
          编辑
        </Button>
      ),
      onCell: (_, index) => {
        if (index === 0) {
          return { rowSpan: 3 };
        }
        // These two are merged into above cell
        if (index === 1) {
          return { rowSpan: 0 };
        }
        if (index === 2) {
          return { colSpan: 0 };
        }

        return {};
      },
    },
  ];

  const simpleColumns = columns.filter(
    (el) => el.key !== 'reissue_resources' && el.key !== 'operate',
  );

  return (
    <>
      <Form layout="inline">
        <Form.Item name="country_code" label="国家">
          {findLabel(regionOptions ?? [], row.country_code)}
        </Form.Item>
        <Form.Item name="type" label={isTactic ? '标品' : type}>
          {isTactic ? row.name : row[type]}
        </Form.Item>
        {/* {isTactic ? (
          <Form.Item name="mnc" label="运营商">
            {getOperatorName(mccMncInfo, row.mnc, {
              mcc: row.mcc,
            })}
          </Form.Item>
        ) : (
          <Form.Item name="country_code" label="国家/地区">
            {row.country_name}
          </Form.Item>
        )} */}
        <Form.Item name="sms_type" label="短信类型">
          {findLabel(smsType, _smsType)}
        </Form.Item>
      </Form>
      {state.loading ? (
        <Spin />
      ) : (
        <Table dataSource={dataSource} columns={!showChannel ? simpleColumns : columns}></Table>
      )}
    </>
  );
}
