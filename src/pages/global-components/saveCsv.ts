import { dumpwarning } from '@/services/dumpWarning';
import _ from 'lodash';

function isMSbrowser() {
  const { userAgent } = window.navigator;
  return userAgent.indexOf('Edge') !== -1 || userAgent.indexOf('Trident') !== -1;
}

function format(data: any) {
  return String(data)
    .replace(/"/g, '""')
    .replace(/(^[\s\S]*$)/, '"$1"');
}

export function saveCSV(
  title: string,
  head: string[],
  data: any[],
  { route, params }: { route: string; params: Record<string, any> },
) {
  const wordSeparator = ',';
  const lineSeparator = '\n';

  const reTitle = `${title}.csv`;
  const headBOM = '\ufeff';
  const headStr = head ? head.map((item) => format(item)).join(wordSeparator) + lineSeparator : '';
  const dataStr = data
    ? data
        .map((row) => row.map((item: any) => format(item)).join(wordSeparator))
        .join(lineSeparator)
    : '';
  const blob = new Blob([headBOM + headStr + dataStr], {
    type: 'text/plain;charset=utf-8',
  });

  return isMSbrowser()
    ? new Promise((resolve) => {
        // Edge、IE11
        (window.navigator as any).msSaveBlob(blob, reTitle);
        resolve(undefined);
      })
    : new Promise((resolve) => {
        // Chrome、Firefox

        const a = document.createElement('a');
        a.href = window.URL.createObjectURL(blob);
        a.download = reTitle;
        a.click();
        dumpwarning({ route, params });
        resolve(undefined);
      });
}

export function getExportData(boxes: any[], list: any[]) {
  const cBoxes = _.filter(boxes, (box) => box.key !== 'operation');
  const head = _.map(cBoxes, (box) => box.title);
  const keys = _.map(cBoxes, (box) => {
    return {
      key: box.key,
      dataIndex: box.dataIndex,
      render: box.exportRender ?? box.render,
    };
  });
  const data = _.map(list, (item) => {
    return _.map(keys, (key) => {
      const info = key?.render ? key.render(key.dataIndex ? item[key.key] : item) : item[key.key];
      return info || '';
    });
  });

  return {
    head,
    data,
  };
}

// 公共函数：处理行数据并返回可导出的文本和数据
export const processRowsData = (rows: any[], columns: any[]) => {
  // 过滤出可见的列，排除隐藏列和操作列
  const visibleColumns = columns.filter(
    (column) => !column.hideInTable && column.key !== 'operation',
  );
  // 生成表头文本
  const headerText = visibleColumns.map((column) => column.title);
  // 处理每一行数据
  const exportData = rows.map((row) =>
    visibleColumns.map(({ dataIndex, render }) => {
      // 获取单元格的值
      const renderValue = render ? render(row, row[dataIndex]) : row[dataIndex];
      const cellValue =
        typeof renderValue === 'string' || typeof renderValue === 'number'
          ? renderValue
          : row[dataIndex];
      // 返回有效的字符串或数字值，若无则返回 '-'
      return typeof cellValue === 'string' || typeof cellValue === 'number' ? cellValue : '-';
    }),
  );

  return { headerText, exportData };
};
