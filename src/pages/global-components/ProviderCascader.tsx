import { getProviderAccountList } from '@/services/channel';
import { Cascader, CascaderProps } from 'antd';
import React, { useEffect, FC } from 'react';
import { useAsyncFn } from 'react-use';
import _ from 'lodash';
import { getAccountList } from '@/services/smppAccount';

enum SelectType {
  SMS = 0,
  MMS = 1,
}

interface ProviderData {
  supplier_id: number;
  supplier_name: string;
  provider_id: number;
  provider_name: string;
  account_id: number;
  account_name: string;
  status: number;
}

const ProviderCascader: FC<CascaderProps & { type?: SelectType; levelValueIsName?: boolean[] }> = ({
  type = SelectType.SMS,
  levelValueIsName = [],
  ...props
}) => {
  const [level1 = false, level2 = false] = levelValueIsName;
  const [options, getList] = useAsyncFn(async () => {
    const fetchData =
      type === SelectType.SMS
        ? getProviderAccountList({ page_index: 1, page_size: 10000, status: 7 })
        : getAccountList({ page_index: 1, page_size: 10000 });

    const res = await fetchData;
    const data: ProviderData[] = res?.data?.list ?? [];

    const filterList =
      type === SelectType.SMS ? data : data.filter((v) => [2, 4, 5, 7].includes(v.status));

    return _.chain(filterList)
      .groupBy('supplier_id')
      .map((data, supplier_id) => {
        const supplierName = data[0].supplier_name;
        const isSMS = type === SelectType.SMS;

        return {
          value: level1 ? supplierName : supplier_id,
          label: `${supplierName} (${supplier_id})`,
          children: data.map((row) => {
            const accountName = isSMS ? row.provider_name : row.account_name;
            const accountId = isSMS ? row.provider_id : row.account_id;

            return {
              value: level2 ? accountName : accountId,
              label: `${accountName} (${accountId})`,
            };
          }),
        };
      })
      .value();
  }, [type, level1, level2]);

  const accountProps: any = {
    changeOnSelect: type === SelectType.SMS,
    showCheckedStrategy: type === SelectType.MMS ? Cascader.SHOW_CHILD : undefined,
    multiple: type === SelectType.MMS,
  };

  useEffect(() => {
    getList();
  }, [getList]);

  return (
    <Cascader
      maxTagCount={5}
      placeholder="请选择供应商"
      options={options.value ?? []}
      showSearch
      {...accountProps}
      {...props}
    />
  );
};

export default ProviderCascader;
