import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button, Checkbox, message } from 'antd';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { CheckboxValueType } from 'antd/es/checkbox/Group';
import _ from 'lodash';

interface DialogProps {
  current: string[];
  dialogRef: DialogRef;
  options: { value: string; label: string }[];
  boxes: string[];
  onSuccess: (params: string[]) => void;
}

export const CustomColsDialog = (props: DialogProps) => {
  const { dialogRef, current, boxes, options, onSuccess } = props;
  const [visible, setShowState] = useDialog(dialogRef);
  const [cols, setCols] = useState<CheckboxValueType[]>(current);
  const [indeterminate, setIndeterminate] = useState(current.length < boxes.length);
  const [checkAll, setCheckAll] = useState(_.isEqual(current, boxes));
  console.log(checkAll);

  const onChange = (list: CheckboxValueType[]) => {
    console.log(boxes, list);
    setCols(list);
    setIndeterminate(!!list.length && list.length < boxes.length);
    setCheckAll(list.length === boxes.length);
  };

  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    setCols(e.target.checked ? boxes : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };

  useEffect(() => {
    setCols(current);
  }, [current, visible]);

  const handlerSubmit = (): any => {
    if (!cols.length) return message.error('至少勾选一项');
    setShowState(false);
    onSuccess(boxes.filter((box) => cols.includes(box)));
  };

  return (
    <>
      <Modal
        open={visible}
        title="列自定义"
        onCancel={() => setShowState(false)}
        footer={[
          <Button type="primary" onClick={handlerSubmit} key="sure">
            确定
          </Button>,
          <Button
            key="cancel"
            onClick={() => {
              setShowState(false);
            }}
          >
            取消
          </Button>,
        ]}
      >
        <section>
          <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
            全选
          </Checkbox>
          <Checkbox.Group value={cols} onChange={onChange} options={options} />
        </section>
      </Modal>
    </>
  );
};
