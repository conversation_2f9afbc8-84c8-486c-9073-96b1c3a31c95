import React, { useRef, useEffect } from 'react';
import { Tooltip } from 'antd';

const SelectWrapper = ({ onMouseEnter, onMouseLeave, ...rest }) => {
  const ref = useRef();

  useEffect(() => {
    const span = ref.current;
    span.addEventListener('mouseenter', onMouseEnter);
    span.addEventListener('mouseleave', onMouseLeave);

    return () => {
      span.removeEventListener('mouseenter', onMouseEnter);
      span.removeEventListener('mouseleave', onMouseLeave);
    };
  }, [onMouseEnter, onMouseLeave]);

  return (
    <div
      ref={ref}
      style={{
        display: 'inline-block',
      }}
      {...rest}
    />
  );
};

const SelectTooltip = ({ children, ...rest }) => {
  return (
    <Tooltip {...rest} mouseEnterDelay={0}>
      <SelectWrapper>{children}</SelectWrapper>
    </Tooltip>
  );
};

export default SelectTooltip;
