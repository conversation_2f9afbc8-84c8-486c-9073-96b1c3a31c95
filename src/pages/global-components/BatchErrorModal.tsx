import React, { useEffect, useMemo, useState } from 'react';
import { Button, Modal, Table, Typography } from 'antd';
import _ from 'lodash';
import { ExclamationCircleOutlined } from '@ant-design/icons';

const { Paragraph } = Typography;

type ErrorType = {
  code: number;
  msg: any;
  param: Record<string, any>;
}[];

export const errorsObj = {
  visible: false,
  setVisible: (_visible: boolean, _errors: ErrorType) => {},
};

export const BatchErrorModal = () => {
  const [visible, setShowState] = useState(false);
  const [errors, setErrors] = useState<ErrorType>([]);

  useEffect(() => {
    errorsObj.setVisible = (visible, errors) => {
      setShowState(visible);
      setErrors(errors);
    };
  }, [setShowState]);

  useEffect(() => {
    errorsObj.visible = visible;
  }, [visible]);

  function close() {
    setShowState(false);
  }

  const list = useMemo(() => {
    return _.map(errors, (v) => {
      return {
        msg: typeof v.msg === 'string' ? v.msg : JSON.stringify(v.msg),
        param: v.param,
      };
    });
  }, [errors]);

  return (
    <Modal
      open={visible}
      title={
        <p style={{ color: 'red' }}>
          <ExclamationCircleOutlined /> 批量请求出错
        </p>
      }
      width="50%"
      onCancel={close}
      maskClosable={false}
      footer={
        <Button
          onClick={() => {
            close();
          }}
        >
          关闭
        </Button>
      }
      zIndex={10000}
    >
      <Table
        dataSource={list}
        size="small"
        columns={[
          {
            title: 'msg',
            dataIndex: 'msg',
            key: 'msg',
            align: 'center',
            render: (msg) => {
              return typeof msg === 'string' ? msg : JSON.stringify(msg);
            },
          },
          {
            title: '参数',
            dataIndex: 'param',
            key: 'param',
            align: 'center',
            width: '40%',
            render: (param) => {
              const _params = JSON.stringify(param);
              return (
                <Paragraph
                  copyable={{ text: _params }}
                  ellipsis={{ rows: 2, expandable: true, symbol: 'more' }}
                  style={{ wordBreak: 'break-all', width: 320 }}
                >
                  {_params}
                </Paragraph>
              );
            },
          },
        ]}
        scroll={{ x: 'max-content' }}
        pagination={false}
      />
    </Modal>
  );
};
