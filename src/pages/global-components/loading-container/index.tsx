import React from 'react';
import style from './loading.module.less';
import classNames from 'classnames';
import { Spin } from 'antd';

interface Props extends React.HTMLAttributes<HTMLElement> {
  loading?: boolean;
  fullWhite?: boolean;
  children: React.ReactNode | React.ReactFragment | React.ReactPortal | boolean | null | undefined;
}
export const LoadingContainer = (props: Props) => {
  const { loading, className, fullWhite } = props;
  const _loading = !!loading;

  return (
    <div
      className={classNames([
        style.container,
        className,
        {
          [style.isLoading]: _loading,
          [style.fullWhite]: fullWhite,
        },
      ])}
    >
      <div className={style.modal}>
        <Spin className={style.tip} tip="Loading" />
      </div>
      {props.children}
    </div>
  );
};
