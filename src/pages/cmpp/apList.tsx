import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Modal, Form, Input, Radio, message } from 'antd';
import { queryAP, modifyAccessPoint, createAccessPoint } from '@/services/api';
import _ from 'lodash';

const ApList = () => {
  let timerId: any;
  const [list, setList] = useState<any[]>([]);
  const [count, setCount] = useState<number>(0);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [form2] = Form.useForm();
  const [modifyList, setModifyList] = useState({
    id: 0,
    name: '',
    status: 0,
    vip: '',
    domain: '',
    type: 0,
    point_type: 0,
  });

  const columns: any = [
    {
      title: '流水id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '接入点名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '接入点域名',
      dataIndex: 'domain',
      key: 'domain',
      align: 'center',
    },
    {
      title: '接入点ip',
      dataIndex: 'vip',
      key: 'vip',
      align: 'center',
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      align: 'center',
    },
    {
      title: '接入点类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (type: number) => {
        return <span>{type === 0 ? '签名子码' : '应用子码'}</span>;
      },
    },
    {
      title: '接入点状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status: number) => {
        return <span>{status === 0 ? '正常' : '禁用'}</span>;
      },
    },
    {
      title: '接入方式',
      dataIndex: 'point_type',
      key: 'point_type',
      align: 'center',
      render: (point_type: number) => {
        return <span>{point_type === 0 ? 'ip接入' : '域名接入'}</span>;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'create_at',
      key: 'create_at',
      align: 'center',
    },
    {
      title: '操作',
      render: (row: any) => {
        return (
          <a
            onClick={() => {
              showModal(row);
            }}
          >
            编辑
          </a>
        );
      },
      align: 'center',
    },
  ];

  useEffect(() => {
    getList();
  }, [pageIndex, pageSize]);

  useEffect(() => {
    !isModalVisible && form.resetFields();
    return () => {
      form.resetFields();
    };
  }, [isModalVisible]);

  useEffect(() => {
    !isAddModalVisible && form2.resetFields();
    return () => {
      form2.resetFields();
    };
  }, [isAddModalVisible]);

  useEffect(() => {
    if (isLoading) {
      return;
    }
    timerId && clearTimeout(timerId);
    timerId = window.setTimeout(() => {
      getList();
    }, 60 * 1000);
    return () => clearTimeout(timerId);
  }, [isLoading]);

  function showModal(row: any) {
    setModifyList({
      ...modifyList,
      ...row,
    });
    setIsModalVisible(true);
  }

  async function getList() {
    setLoading(true);
    const res = await queryAP({
      page_index: pageIndex,
      page_size: pageSize,
    });
    setCount(res.data?.count);
    setList(res.data?.list);
    setLoading(false);
  }

  async function onSubmit(val: any) {
    const params = _.pickBy(val, (item) => item !== undefined && item !== '');
    const res = await modifyAccessPoint(params);
    setIsModalVisible(false);
    if (res.code === 0) {
      message.success('操作成功');
      getList();
    }
  }
  async function onAddSubmit(val: any) {
    const params = _.pickBy(val, (item) => item !== undefined && item !== '');
    const res = await createAccessPoint(params);
    setIsAddModalVisible(false);
    if (res.code === 0) {
      message.success('操作成功');
      getList();
    }
  }

  return (
    <>
      <PageContainer title="接入点列表">
        <Button
          type="primary"
          onClick={() => {
            setIsAddModalVisible(true);
          }}
        >
          新增接入点
        </Button>
        <Table
          columns={columns}
          dataSource={list}
          rowKey="id"
          loading={isLoading}
          pagination={{
            defaultCurrent: 1,
            total: count,
            showSizeChanger: true,
            onShowSizeChange: (current, page_size) => setPageSize(page_size),
            onChange: (page) => {
              setPageIndex(page);
            },
          }}
          style={{ marginTop: 20 }}
        />
      </PageContainer>
      <Modal
        open={isModalVisible}
        onOk={() => {
          form.submit();
        }}
        onCancel={() => {
          setIsModalVisible(false);
        }}
      >
        <Form
          labelCol={{ span: 6 }}
          form={form}
          labelAlign="right"
          initialValues={{ ...modifyList }}
          onFinish={(vals) => onSubmit(vals)}
        >
          <Form.Item name="id" label="流水id">
            <Input bordered={false} disabled style={{ width: 300, color: '#666' }} />
          </Form.Item>
          <Form.Item name="name" label="接入点名称">
            <Input placeholder="接入点名称" style={{ width: 300, color: '#666' }} />
          </Form.Item>
          <Form.Item name="status" label="接入点状态">
            <Radio.Group>
              <Radio value={0}>正常</Radio>
              <Radio value={1}>禁用</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="vip" label="接入点ip">
            <Input placeholder="接入点ip" style={{ width: 300, color: '#666' }} />
          </Form.Item>
          <Form.Item name="domain" label="接入点域名">
            <Input placeholder="接入点域名" style={{ width: 300, color: '#666' }} />
          </Form.Item>
          <Form.Item name="type" label="接入点类型">
            <Radio.Group>
              <Radio value={0}>签名子码</Radio>
              <Radio value={1}>应用子码</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="point_type" label="接入方式">
            <Radio.Group>
              <Radio value={0}>ip接入</Radio>
              <Radio value={1}>域名接入</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
      <Modal
        open={isAddModalVisible}
        onOk={() => {
          form2.submit();
        }}
        onCancel={() => {
          setIsAddModalVisible(false);
        }}
      >
        <Form
          labelCol={{ span: 6 }}
          form={form2}
          labelAlign="right"
          onFinish={(vals) => onAddSubmit(vals)}
        >
          <Form.Item name="name" label="接入点名称" rules={[{ required: true }]}>
            <Input placeholder="接入点名称" style={{ width: 300, color: '#666' }} />
          </Form.Item>
          <Form.Item name="vip" label="接入点ip">
            <Input placeholder="接入点ip" style={{ width: 300, color: '#666' }} />
          </Form.Item>
          <Form.Item name="domain" label="接入点域名">
            <Input placeholder="接入点域名" style={{ width: 300, color: '#666' }} />
          </Form.Item>
          <Form.Item name="type" label="接入点类型" rules={[{ required: true }]}>
            <Radio.Group>
              <Radio value={0}>签名子码</Radio>
              <Radio value={1}>应用子码</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="point_type" label="接入方式" rules={[{ required: true }]}>
            <Radio.Group>
              <Radio value={0}>ip接入</Radio>
              <Radio value={1}>域名接入</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ApList;
