import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import CmppForm from './component/cmppForm';
import type { ConnectState } from '@/models/connect';
import { connect } from 'umi';
import type { Dispatch } from 'umi';

interface Props {
  cmppInfo?: any;
  dispatch: Dispatch;
}

const AddCmppAccount = (props: Props) => {
  const { cmppInfo, dispatch } = props;
  const [list, setList] = useState();
  useEffect(() => {
    setList(cmppInfo);
    return () => {
      dispatch({
        type: 'audit/editCmppForm',
        payload: {
          cmppInfo: {
            customer_name: '',
            customer_email: '',
            sdkappid: '',
            sms_type: 0,
            sp_type: 0,
            business_type: 0,
            account_attribute: [],
            use_description: '',
            remark: '',
            customer_type: 0,
          },
        },
      });
    };
  }, [cmppInfo]);
  return (
    <PageContainer title="CMPP新开账户">
      <CmppForm list={list} isCreate={0} />
    </PageContainer>
  );
};

export default connect(({ audit }: ConnectState) => ({
  cmppInfo: audit.cmppInfo,
}))(AddCmppAccount);
