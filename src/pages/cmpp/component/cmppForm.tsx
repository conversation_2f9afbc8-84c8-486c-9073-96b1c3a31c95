import React, { useState, useEffect } from 'react';
import { Button, Form, Input, Select, Row, Col, Checkbox, Radio, Space, message } from 'antd';
import { createCmppAcount, queryAP, modifyCmppAcount } from '@/services/api';
import { connect } from 'umi';
import type { ConnectState } from '@/models/connect';
import _ from 'lodash';

// isCreate 0-新增  1-编辑
export interface AccountProps {
  list: { customer_type?: number };
  isCreate?: number;
  onSuccess?: any;
  onCancel?: any;
  releaseLink?: string;
  isReset?: boolean;
}

const CmppForm: React.FC<AccountProps> = ({
  list,
  isCreate,
  onSuccess,
  onCancel,
  releaseLink,
  isReset,
}) => {
  const [form] = Form.useForm();
  const [accessPoints, setAccessPoints] = useState<any>([]);
  const [domainPoints, setDomainPoints] = useState<any>([]);
  const [customType, setCustomType] = useState(list?.customer_type || 0);

  useEffect(() => {
    queryAccessPoints();
    queryDomianAp();
    return () => {
      form.resetFields();
    };
  }, [list]);

  useEffect(() => {
    isReset && form.resetFields();
  }, [isReset]);

  const accountAttrOptions = [
    { label: '电信', value: 1 },
    { label: '移动', value: 2 },
    { label: '联通', value: 4 },
  ];
  const onTypeChange = function (e: any) {
    setCustomType(e.target.value);
    form.setFields([
      { name: 'master_vip', value: '' },
      { name: 'slaver_vip', value: '' },
      { name: 'domain', value: '' },
    ]);
  };

  async function queryAccessPoints() {
    const params = {
      page_size: 1000,
      page_index: 1,
      status: 0,
      point_type: 0,
    };
    const res = await queryAP(params);
    const aplist: { key: number; name: string; value: string; type: number }[] = _.map(
      res?.data?.list,
      (val) => {
        return {
          key: val.id,
          name: val.name,
          value: val.id,
          type: val.type,
        };
      },
    );
    setAccessPoints(aplist);
  }

  async function queryDomianAp() {
    const params = {
      page_size: 1000,
      page_index: 1,
      status: 0,
      point_type: 1,
    };
    const res = await queryAP(params);
    const aplist: { key: number; name: string; value: string; type: number }[] = _.map(
      res?.data?.list,
      (val) => {
        return {
          key: val.id,
          name: val.name,
          value: val.id,
          type: val.type,
        };
      },
    );
    setDomainPoints(aplist);
  }

  async function onSubmit(vals: any) {
    let params = _.cloneDeep(vals);
    const oldParams = _.pick(list, [
      'customer_name',
      'customer_email',
      'sdkappid',
      'sms_type',
      'sp_type',
      'business_type',
      'account_attribute',
      'use_description',
      'remark',
      'customer_type',
      'domain',
      'master_vip',
      'slaver_vip',
      'sp_id',
    ]);
    if (isCreate === 1 && _.isEqual(params, oldParams)) {
      message.error('编辑后数据与原数据一致');
      return;
    }
    params = _.pickBy(params, (val) => val !== '');
    params.account_attribute = _.sum(vals.account_attribute);
    params.sdkappid = Number(params.sdkappid);
    let res;
    switch (isCreate) {
      case 0:
        res = await createCmppAcount(params);
        break;
      case 1:
        delete params.sdkappid;
        res = await modifyCmppAcount(params);
        break;
      case 2:
      default:
        break;
    }
    if (res?.code === 0) {
      message.success('提交成功');
      form.resetFields();
    }
    onSuccess?.();
  }

  function validateEmail(value: string, callback: any) {
    if (!value) {
      callback();
      return;
    }
    const exp = /^[.a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    const emailArr = value.split(',');
    const isValid = _.every(emailArr, (email: string) => {
      return exp.test(email);
    });
    isValid ? callback() : callback('邮箱格式不正确，请重新输入');
  }

  return (
    <>
      <Form
        labelCol={{ span: 8 }}
        form={form}
        labelAlign="right"
        initialValues={{ ...list }}
        onFinish={(vals) => onSubmit(vals)}
      >
        {isCreate === 1 && (
          <Form.Item name="sp_id" label="cmpp账号id" rules={[{ required: true }]} hidden>
            <Input bordered={false} style={{ width: 200, color: '#666' }} />
          </Form.Item>
        )}
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item name="customer_name" label="用户名称" rules={[{ required: true }]}>
              <Input style={{ width: 200 }} placeholder="xx科技公司" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="customer_email"
              label="接受账号邮箱"
              rules={[
                { required: true },
                {
                  validator: (rule, value, callback) => {
                    validateEmail(value, callback);
                  },
                },
              ]}
            >
              <Input style={{ width: 200 }} placeholder="支持多个邮箱，用逗号分隔" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="sdkappid" label="短信应用" rules={[{ required: true }]}>
              <Input disabled={isCreate === 1} style={{ width: 200 }} placeholder="140xxxxxxxx" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item name="sms_type" label="短信类型" rules={[{ required: true }]}>
              <Select style={{ width: 200 }}>
                <Select.Option value={0}>行业</Select.Option>
                <Select.Option value={1}>营销</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="sp_type" label="账号格式" rules={[{ required: true }]}>
              <Select style={{ width: 200 }}>
                <Select.Option value={0}>标准格式</Select.Option>
                <Select.Option value={1}>完全发海外且国家码前面不带+号</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="business_type" label="业务类型" rules={[{ required: true }]}>
              <Select style={{ width: 200 }}>
                <Select.Option value={0}>常规业务</Select.Option>
                <Select.Option value={1}>三大类业务</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item label="账号属性" name="account_attribute" rules={[{ required: true }]}>
              <Checkbox.Group options={accountAttrOptions} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="use_description" label="用途描述">
              <Input placeholder="请填写用途，如：物流通知/会员营销/信用卡营销" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item name="remark" label="备注">
              <Input placeholder="请填写场景说明，如：客户要求500qps，投诉率较高" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item name="customer_type" label="客户类型" rules={[{ required: true }]}>
              <Radio.Group onChange={onTypeChange}>
                <Radio value={0}>签名子码</Radio>
                <Radio value={1}>应用子码</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item name="master_vip" label="主接入点" rules={[{ required: true }]}>
              <Select style={{ width: 200 }} placeholder="请选择主接入点">
                {(accessPoints ?? []).map((el: any) => {
                  if (el.type === customType) {
                    return (
                      <Select.Option value={el.value} key={el.key}>
                        {el.name}
                      </Select.Option>
                    );
                  }
                  return '';
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="slaver_vip" label="备接入点" rules={[{ required: true }]}>
              <Select style={{ width: 200 }} placeholder="请选择备接入点">
                {(accessPoints ?? []).map((el: any) => {
                  if (el.type === customType) {
                    return (
                      <Select.Option value={el.value} key={el.key} label={el.name}>
                        {el.name}
                      </Select.Option>
                    );
                  }
                  return '';
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="domain" label="域名接入点" rules={[{ required: customType === 0 }]}>
              <Select style={{ width: 200 }} placeholder="请选择域名接入点">
                {(domainPoints ?? []).map((el: any) => {
                  if (el.type === customType) {
                    return (
                      <Select.Option value={el.value} key={el.key} label={el.name}>
                        {el.name}
                      </Select.Option>
                    );
                  }
                  return '';
                })}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        {releaseLink && (
          <Row>
            <Col>
              请前往
              <a href={releaseLink} target="_blank" rel="noreferrer">
                七彩石
              </a>
              进行发布操作
            </Col>
          </Row>
        )}
        {isCreate === 0 ? (
          <Space style={{ marginLeft: 100 }}>
            <Button htmlType="submit" type="primary">
              提交审核
            </Button>
          </Space>
        ) : (
          <div className="ant-modal-footer">
            <Button onClick={onCancel}>取消</Button>
            <Button htmlType="submit" type="primary">
              确定
            </Button>
          </div>
        )}
      </Form>
    </>
  );
};

export default connect(({ audit }: ConnectState) => ({
  isReset: audit.isReset || false,
}))(CmppForm);
