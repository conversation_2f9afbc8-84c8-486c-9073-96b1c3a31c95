import React, { useState } from 'react';
import { Button, Row, Col, Input, Form, message } from 'antd';
import { sendEmail } from '@/services/api';
import _ from 'lodash';

// isCreate 0-新增  1-编辑   2-查看
export interface AccountProps {
  checkList: {
    status?: number;
    sp_id: number;
    sign?: string;
  }[];
  onCancel?: any;
  refresh: any;
}

const EmailForm: React.FC<AccountProps> = ({ checkList, onCancel, refresh }) => {
  const [form] = Form.useForm();
  const [list, setList] = useState(checkList);

  function handleChange(val: string, sp_id: number) {
    setList((prev) => {
      return prev.map((perItem) => {
        if (perItem.sp_id === sp_id) {
          return {
            sp_id: perItem.sp_id,
            sign: val,
          };
        }
        return perItem;
      });
    });
  }

  async function onSubmit() {
    const isFull = _.every(list, (item) => item.sign);
    if (!isFull) {
      message.error('sign不能为空');
      return;
    }
    const res = await sendEmail({ params: list });
    if (res?.code === 0) {
      message.success('提交成功');
    } else if (res?.code === 30006) {
      const msg = _.reduce(
        res?.data,
        (prev, item) => {
          return `${prev + item.sp_id + item.msg}。`;
        },
        '',
      );
      message.error(msg);
    } else {
      message.error(res?.msg);
    }
    refresh();
  }

  return (
    <>
      <Form labelCol={{ span: 4 }} form={form} labelAlign="right" onFinish={() => onSubmit()}>
        {checkList.map((item) => (
          <Row gutter={24} key={item.sp_id}>
            <Col span={12}>
              <Form.Item label="sp_id">
                <Input
                  bordered={false}
                  disabled
                  style={{ width: 200, color: '#666' }}
                  value={item.sp_id}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="sign">
                <Input
                  style={{ width: 200 }}
                  value={item.sign}
                  onChange={(e) => {
                    handleChange(e.target.value, item.sp_id);
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        ))}
        <div className="ant-modal-footer">
          <Button onClick={onCancel}>取消</Button>
          <Button htmlType="submit" type="primary">
            确定
          </Button>
        </div>
      </Form>
    </>
  );
};

export default EmailForm;
