import React, { useState, useEffect } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Table, Space, Modal, message, Button, Descriptions, Form, Select, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { releaseAccount, queryCmppList, queryAP } from '@/services/api';
import _ from 'lodash';
import CmppForm from './component/cmppForm';
import EmailForm from './component/emailForm';

let allAp: Record<string, any> = {};

const CmppList = () => {
  let timerId: any;
  const [list, setList] = useState([]);
  const [form] = Form.useForm();
  const [count, setCount] = useState<number>(0);
  const [checkAccount, setCheckAccount] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [emailList, setEmailList] = useState<
    {
      status: number;
      sp_id: number;
      sign?: '';
    }[]
  >([]);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isSendModalShow, setIsSendModalShow] = useState(false);
  const [isViewShow, setIsViewShow] = useState(false);
  const [formData, setFormData] = useState<{
    customer_name: string;
    customer_email: string;
    sdkappid: string;
    sms_type: number;
    sp_type: number;
    sp_id: number;
    business_type: number;
    account_attribute: string[];
    use_description: string;
    remark: string;
    customer_type: number;
    master_vip: string;
    slaver_vip: string;
    domain: string;
  }>({
    customer_name: '',
    customer_email: '',
    sdkappid: '',
    sms_type: 0,
    sp_type: 0,
    sp_id: 0,
    business_type: 0,
    account_attribute: [],
    use_description: '',
    remark: '',
    customer_type: 0,
    master_vip: '',
    slaver_vip: '',
    domain: '',
  });
  const [initForm, setInitForm] = useState<{
    customer_name: string;
    sdkappid: string;
    sms_type: any;
    sp_ids: string;
    status: any;
  }>({
    customer_name: '',
    sdkappid: '',
    sms_type: '',
    sp_ids: '',
    status: 100,
  });
  const statusOptions = [
    { value: 100, label: '未完成', key: 100 },
    { value: 0, label: '待测试', key: 0 },
    { value: 1, label: '测试中', key: 1 },
    { value: 2, label: '测试通过', key: 2 },
    { value: 3, label: '测试未通过', key: 3 },
    { value: 4, label: '待发布', key: 4 },
    { value: 5, label: '发布中', key: 5 },
    { value: 6, label: '发布成功', key: 6 },
    { value: 7, label: '发布失败', key: 7 },
    { value: 8, label: '邮件已推送', key: 8 },
    { value: '', label: '全部状态', key: 101 },
  ];

  const columns: any = [
    {
      title: 'cmpp账号id',
      dataIndex: 'sp_id',
      key: 'sp_id',
      align: 'center',
    },
    {
      title: '用户名称',
      dataIndex: 'customer_name',
      key: 'customer_name',
      align: 'center',
    },
    {
      title: '应用id',
      dataIndex: 'sdkappid',
      key: 'sdkappid',
      align: 'center',
    },
    {
      title: '接收账号邮箱',
      dataIndex: 'customer_email',
      key: 'customer_email',
      align: 'center',
      width: '30%',
      render: (customer_email: string) => (
        <div style={{ wordWrap: 'break-word', wordBreak: 'break-word' }}>{customer_email}</div>
      ),
    },
    {
      title: '业务类型',
      dataIndex: 'business_type',
      key: 'business_type',
      align: 'center',
      render: (buType: number) => {
        return <span>{buType === 0 ? '常规业务' : '三大类业务'}</span>;
      },
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status: number) => {
        const mapText: any = new Map([
          [0, '待测试'],
          [1, '测试中'],
          [2, '测试通过'],
          [3, '测试未通过'],
          [4, '待发布'],
          [5, '发布中'],
          [6, '发布成功'],
          [7, '发布失败'],
          [8, '邮件已推送'],
        ]);
        return <span>{mapText.get(status)}</span>;
      },
    },
    {
      title: '操作',
      render: (row: any) => {
        if (row.status === 3 || row.status === 7) {
          return (
            <a
              onClick={() => {
                doEdit(row);
              }}
            >
              编辑
            </a>
          );
        }
        if (row.status === 2) {
          return (
            <Space>
              <a
                onClick={() => {
                  singleRelease(row);
                }}
              >
                发布现网
              </a>
              <a
                onClick={() => {
                  doView(row);
                }}
              >
                查看
              </a>
            </Space>
          );
        }
        if (row.status === 6) {
          return (
            <Space>
              <a
                onClick={() => {
                  singleSend(row);
                }}
              >
                推送邮件
              </a>
              <a
                onClick={() => {
                  doView(row);
                }}
              >
                查看
              </a>
            </Space>
          );
        }
        return (
          <a
            onClick={() => {
              doView(row);
            }}
          >
            查看
          </a>
        );
      },
      align: 'center',
    },
  ];

  useEffect(() => {
    getList({ ...initForm });
  }, [pageIndex, pageSize]);

  useEffect(() => {
    if (isLoading) {
      return;
    }
    timerId && clearTimeout(timerId);
    timerId = window.setTimeout(() => {
      getList({ ...initForm });
    }, 10 * 1000);
    return () => clearTimeout(timerId);
  }, [isLoading]);

  useEffect(() => {
    getAccessPoints();
  }, []);

  async function getAccessPoints() {
    const initApList: Record<string, any> = {};
    const params = {
      page_size: 1000,
      page_index: 1,
    };
    const res = await queryAP(params);
    _.forEach(res?.data?.list, (point) => {
      initApList[point.id] = point.name;
    });
    allAp = _.cloneDeep(initApList);
  }

  async function getList(params = {}) {
    params = _.pickBy(params, (item) => item !== '');
    setLoading(true);
    const res = await queryCmppList({
      ...params,
      page_index: pageIndex,
      page_size: pageSize,
    });
    setList(res?.data?.list || []);
    setCount(res?.data?.count);
    setLoading(false);
  }

  async function release() {
    if (!selectedRows.length) return;
    const canRelease = _.every(selectedRows, (item) => item.status === 2);
    if (!canRelease) {
      message.error('勾选项存在不可发布现网状态的条目');
      return;
    }
    const res = await releaseAccount({ sp_ids: checkAccount.join(',') });
    if (res?.code === 0) {
      message.success('提交成功');
    }
    onRefresh();
  }
  async function singleRelease(row: any) {
    const res = await releaseAccount({ sp_ids: row.sp_id.toString() });
    if (res?.code === 0) {
      message.success('提交成功');
    }
    onRefresh();
  }

  async function send() {
    if (!selectedRows.length) return;
    const canSend = _.every(selectedRows, (item) => item.status === 6);
    if (!canSend) {
      message.error('勾选项存在不可推送邮件状态的条目');
      return;
    }
    setEmailList(selectedRows);
    setIsSendModalShow(true);
  }
  async function singleSend(row: any) {
    setEmailList([row]);
    setIsSendModalShow(true);
  }

  function onRefresh() {
    getList({ ...initForm });
    setCheckAccount([]);
    setSelectedRows([]);
  }

  function doView(row: any) {
    // 1移动/2联通/4电信 电信+移动：5'
    const mapText: any = new Map([
      [1, ['电信']],
      [2, ['移动']],
      [4, ['联通']],
      [3, ['电信+移动']],
      [5, ['电信+联通']],
      [6, ['移动+联通']],
      [7, ['移动+联通+电信']],
    ]);
    const accountAttrs = mapText.get(row.account_attribute);
    setFormData(
      Object.assign(
        { ...row },
        {
          account_attribute: accountAttrs,
          master_vip: allAp[row.master_vip_id],
          slaver_vip: allAp[row.slaver_vip_id],
          domain: allAp[row.domain_id],
        },
      ),
    );
    setIsViewShow(true);
  }

  function doEdit(row: any) {
    const mapText: any = new Map([
      [1, [1]],
      [2, [2]],
      [4, [4]],
      [3, [1, 2]],
      [5, [1, 4]],
      [6, [2, 4]],
      [7, [1, 2, 4]],
    ]);
    const accountAttrs = mapText.get(row.account_attribute);
    setFormData(
      Object.assign(
        { ...row },
        {
          account_attribute: accountAttrs,
          master_vip: row.master_vip_id,
          slaver_vip: row.slaver_vip_id,
          domain: row.domain_id,
        },
      ),
    );
    setIsModalVisible(true);
  }

  function onSubmit(vals: any) {
    getList({ ...vals });
    setInitForm({ ...vals });
  }

  const rowSelection = {
    selectedRowKeys: checkAccount,
    onChange: (selectedRowKeys: React.Key[], selectedRow: any) => {
      setCheckAccount(selectedRowKeys);
      setSelectedRows(selectedRow);
    },
  };

  return (
    <>
      <PageContainer title="CMPP列表">
        <Form
          labelCol={{ span: 6 }}
          form={form}
          initialValues={{ ...initForm }}
          layout="inline"
          labelAlign="right"
          onFinish={(vals) => onSubmit(vals)}
        >
          <Form.Item name="sp_ids">
            <Input placeholder="cmpp账号id，支持批量以英文逗号分隔" style={{ width: 280 }} />
          </Form.Item>
          <Form.Item name="customer_name">
            <Input placeholder="客户名称" style={{ width: 250 }} />
          </Form.Item>
          <Form.Item name="sms_type">
            <Select style={{ width: 200 }}>
              <Select.Option value={''}>所有短信类型</Select.Option>
              <Select.Option value={0}>行业</Select.Option>
              <Select.Option value={1}>营销</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="sdkappid">
            <Input placeholder="应用id" style={{ width: 250 }} />
          </Form.Item>
          <Form.Item name="status">
            <Select style={{ width: 280 }}>
              {(statusOptions ?? []).map((el: any) => {
                return (
                  <Select.Option value={el.value} key={el.key}>
                    {el.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item>
            <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
              查询
            </Button>
          </Form.Item>
        </Form>
        <Space style={{ marginTop: 15 }}>
          <Button
            type="primary"
            onClick={() => {
              release();
            }}
          >
            批量发布现网
          </Button>
          <Button
            type="primary"
            onClick={() => {
              send();
            }}
          >
            批量推送邮件
          </Button>
        </Space>
        <Table
          rowKey="sp_id"
          rowSelection={{
            type: 'checkbox',
            ...rowSelection,
          }}
          dataSource={list}
          columns={columns}
          loading={isLoading}
          pagination={{
            defaultCurrent: 1,
            total: count,
            showSizeChanger: true,
            onShowSizeChange: (current, page_size) => setPageSize(page_size),
            onChange: (page) => {
              setPageIndex(page);
            },
          }}
          style={{ marginTop: 20 }}
        />
      </PageContainer>
      <Modal
        width={1100}
        footer={null}
        open={isViewShow}
        onCancel={() => {
          setIsViewShow(false);
        }}
      >
        <Descriptions>
          <Descriptions.Item label="用户名称">{formData.customer_name}</Descriptions.Item>
          <Descriptions.Item
            label="接受账号邮箱"
            style={{ wordWrap: 'break-word', wordBreak: 'break-word' }}
          >
            {formData.customer_email}
          </Descriptions.Item>
          <Descriptions.Item label="短信应用">{formData.sdkappid}</Descriptions.Item>
          <Descriptions.Item label="短信类型">
            {formData.sms_type === 0 ? '行业' : '营销'}
          </Descriptions.Item>
          <Descriptions.Item label="账号格式">
            {formData.sp_type === 0 ? '标准格式' : '完全发海外且国家码前面不带+号'}
          </Descriptions.Item>
          <Descriptions.Item label="业务类型">
            {formData.business_type === 0 ? '常规业务' : '三大类业务'}
          </Descriptions.Item>
          <Descriptions.Item label="账号属性">{formData.account_attribute}</Descriptions.Item>
          <Descriptions.Item label="用途描述">{formData.use_description}</Descriptions.Item>
          <Descriptions.Item label="备注">{formData.remark}</Descriptions.Item>
          <Descriptions.Item label="客户类型">
            {formData.customer_type === 0 ? '签名子码' : '应用子码'}
          </Descriptions.Item>
          <Descriptions.Item label="主接入点">{formData.master_vip}</Descriptions.Item>
          <Descriptions.Item label="备接入点">{formData.slaver_vip}</Descriptions.Item>
          <Descriptions.Item label="域名接入点">{formData.domain}</Descriptions.Item>
        </Descriptions>
      </Modal>
      <Modal
        width={1100}
        open={isModalVisible}
        destroyOnClose
        onCancel={() => {
          setIsModalVisible(false);
        }}
        footer={null}
      >
        <CmppForm
          list={formData}
          isCreate={1}
          onSuccess={() => {
            setIsModalVisible(false);
            getList({ ...initForm });
          }}
          onCancel={() => {
            setIsModalVisible(false);
          }}
        />
      </Modal>
      <Modal
        width={600}
        open={isSendModalShow}
        destroyOnClose
        onCancel={() => {
          setIsSendModalShow(false);
        }}
        footer={null}
      >
        <EmailForm
          checkList={emailList}
          onCancel={() => {
            setIsSendModalShow(false);
          }}
          refresh={() => {
            setIsSendModalShow(false);
            onRefresh();
          }}
        />
      </Modal>
    </>
  );
};

export default CmppList;
