import React from 'react';
import { getAllowReport, addAllowReport, editAllowReport } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const AllowReport = () => {
  const columns: any = [
    {
      title: 'qappid',
      dataIndex: 'qappid',
      key: 'qappid',
      align: 'center',
    },
    {
      title: '购买以后是否上报',
      dataIndex: 'buy_package_report',
      key: 'buy_package_report',
      align: 'center',
      render: (buy_package_report: number) => {
        return <span> {buy_package_report === 1 ? '是' : '否'}</span>;
      },
    },
    {
      title: '是否首次发送短信',
      dataIndex: 'first_send',
      key: 'first_send',
      align: 'center',
      render: (first_send: number) => {
        return <span> {first_send === 1 ? '是' : '否'}</span>;
      },
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'qappid',
      label: 'qappid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: false,
      name: 'buy_package_report',
      label: '购买以后是否上报',
      disabled: false,
      isRequired: true,
      renderType: 'select',
      options: [
        { value: 1, label: '是' },
        { value: 0, label: '否' },
      ],
    },
    {
      showOnAdd: false,
      name: 'first_send',
      label: '是否首次发送短信',
      disabled: false,
      isRequired: true,
      renderType: 'select',
      options: [
        { value: 1, label: '是' },
        { value: 0, label: '否' },
      ],
    },
  ];

  async function doEdit(vals: any) {
    vals.appid = vals.qappid;
    delete vals.qappid;
    return await editAllowReport({ ...vals });
  }
  async function doAdd(vals: any) {
    vals.appid = vals.qappid;
    return await addAllowReport({ ...vals });
  }
  async function getList(vals?: any) {
    return await getAllowReport({ ...vals });
  }

  return (
    <PatternTable
      rowKey="qappid"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'appid',
        },
      ]}
      operateForm={operateForm}
      operType={0}
    />
  );
};

export default AllowReport;
