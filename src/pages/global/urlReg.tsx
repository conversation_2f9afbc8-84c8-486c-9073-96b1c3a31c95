import React from 'react';
import { getUrlReg, addUrlReg, delUrlReg, editUrlReg } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';
import { Tag } from 'antd';

const UrlReg = () => {
  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: '正则式',
      dataIndex: 'url_regex_text',
      key: 'url_regex_text',
      align: 'center',
    },
    {
      title: '描述',
      dataIndex: 'url_regex_desc',
      key: 'url_regex_desc',
      align: 'center',
    },
    {
      title: '观察模式',
      dataIndex: 'is_observe',
      key: 'is_observe',
      align: 'center',
      render: (value: number) =>
        value === 1 ? <Tag color="green">启用</Tag> : <Tag color="red">关闭</Tag>,
    },
  ];

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'url_regex_text',
      label: '正则式',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'url_regex_desc',
      label: '描述',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'is_observe',
      label: '观察模式',
      disabled: false,
      isRequired: true,
      renderType: 'select',
      options: [
        { label: '开启', value: 1 },
        { label: '关闭', value: 0 },
      ],
    },
  ];

  async function doDel(vals: any) {
    return await delUrlReg({ id: vals.id });
  }
  async function doEdit(vals: any) {
    return await editUrlReg({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addUrlReg({ ...vals });
  }
  async function getList(vals?: any) {
    return await getUrlReg({ ...vals });
  }

  return (
    <PatternTable
      modalTipRender={() => <h4>网址正则式 （最多只能添加30个正则）</h4>}
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      columns={columns}
      searchKeys={[]}
      operateForm={operateForm}
      operType={1}
    />
  );
};

export default UrlReg;
