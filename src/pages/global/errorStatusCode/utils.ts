import * as XLSX from 'xlsx';

export interface ExcelData {
  [key: string]: any; // 每个 sheet 的数据结构
}

export interface SheetConfig {
  sheetName: string; // sheet 名称
  sheetKey: string; // 表单字段映射
  keyRow: number; // 指定哪一行作为 keys（从 0 开始）
  formKeyMap: Record<string, string | string[]>; // 表单字段映射
}
export const getSheetConfigs = () =>
  [
    {
      sheetName: ['Message State List', 'SMPP WA Message Stage'],
      sheetKey: 'message_state_mapping',
      keyRow: 2,
      formKeyMap: {
        State: 'code',
        Description: 'code_desc',
        'Temporary[Y/N]': 'temporary',
        'Retry[Y/N]': 'retry',
      },
    },
    {
      sheetName: 'Error Code List',
      sheetKey: 'error_code_mapping',
      keyRow: 4,
      formKeyMap: {
        'Error Code': 'code',
        Description: 'code_desc',
        'Temporary[Y/N]': 'temporary',
        'Retry[Y/N]': 'retry',
      },
    },
    {
      sheetName: 'Status Code',
      sheetKey: 'submit_status_mapping',
      keyRow: 4,
      formKeyMap: {
        'Error Code': 'code',
        Description: 'code_desc',
        'Temporary[Y/N]': 'temporary',
        'Retry[Y/N]': 'retry',
      },
    },
    // {
    //   sheetName: 'SMPP WA Message Stage',
    //   sheetKey: 'standard_code_mapping',
    //   keyRow: 2,
    //   formKeyMap: {
    //     State: ['standard_code', 'external_code'],
    //     Description: [
    //       'standard_code_desc_cn',
    //       'standard_code_desc_en',
    //       'external_code_desc_cn',
    //       'external_code_desc_en',
    //     ],
    //     'Retry[Y/N]': 'is_need_retry',
    //   },
    // },
  ] as SheetConfig[];

const processSheet = (
  keyRow: number,
  formKeyMap: Record<string, string | string[]>,
  jsonData: any[], // 读取的 JSON 数据
): ExcelData[] => {
  const keys = jsonData[keyRow]; // 根据配置的行作为keys
  const values = jsonData.slice(keyRow + 1); // 其余行作为values

  // 过滤掉空行并构建结果
  return values
    .filter((row: any) => row.some((cell: any) => cell !== null && cell !== '')) // 只保留非空行
    .map((row: any) => {
      const item: ExcelData = {};
      keys.forEach((key: any, index: number) => {
        item[key] = row[index]; // 将每一行的值与对应的key组合
      });

      // 根据 formKeyMap 进行字段映射
      const mappedItem: ExcelData = {};
      Object.keys(item).forEach((key) => {
        if (!formKeyMap[key]) {
          return;
        }
        const mappedValue = item[key];
        if (Array.isArray(formKeyMap[key])) {
          formKeyMap[key].forEach((k) => {
            mappedItem[k] = String(mappedValue);
          });
        } else {
          mappedItem[formKeyMap[key]] = String(mappedValue);
        }
      });
      return mappedItem;
    });
};

export function readExcelFileToJson(
  file: File,
  configs: SheetConfig[],
): Promise<Record<string, ExcelData[]>> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    // 读取文件并将所有sheet转为json
    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        const workbook = XLSX.read(data, { type: 'array' });
        const formData: Record<string, ExcelData[]> = {};

        // 遍历配置的sheet
        configs.forEach(({ sheetName, sheetKey, keyRow, formKeyMap }) => {
          if (Array.isArray(sheetName)) {
            sheetName.forEach((name) => {
              if (workbook.SheetNames.includes(name)) {
                const worksheet = workbook.Sheets[name];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                if (formData[sheetKey]) {
                  formData[sheetKey].push(...processSheet(keyRow, formKeyMap, jsonData));
                } else {
                  formData[sheetKey] = processSheet(keyRow, formKeyMap, jsonData);
                }
              }
            });
          } else {
            if (workbook.SheetNames.includes(sheetName)) {
              const worksheet = workbook.Sheets[sheetName];
              const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
              formData[sheetKey] = processSheet(keyRow, formKeyMap, jsonData);
            }
          }
        });

        resolve(formData);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
}
