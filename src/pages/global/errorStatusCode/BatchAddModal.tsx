import React, { useEffect, useState } from 'react';
import { Modal, Form, Upload, Button, Tabs, message, Alert } from 'antd';
import { getSheetConfigs, readExcelFileToJson } from './utils';
import ProviderCascader from '@/pages/global-components/ProviderCascader';
import { useSetState } from 'react-use';
import { addErrorCodeList } from '@/services/errorStatusCode';
import { EditableProTable, ProColumns } from '@ant-design/pro-components';
import { omit } from 'lodash';

interface BatchAddModalProps {
  visible: boolean;
  handleClose: () => void;
  refreshAllTables: () => void;
}

const tableTitleMap = {
  code: '错误码',
  code_desc: '错误码描述',
  temporary: '是否临时',
  retry: '是否重试',
  is_need_retry: '是否需要重试',
  standard_code: '标准错误码',
  standard_code_desc_cn: '标准错误码中文描述',
  standard_code_desc_en: '标准错误码英文描述',
  external_code: '外部错误码',
  external_code_desc_cn: '外部错误码中文描述',
  external_code_desc_en: '外部错误码英文描述',
} as const;
const BatchAddModal = ({ visible, handleClose, refreshAllTables }: BatchAddModalProps) => {
  const [loading, setLoading] = useState(false);
  const [state, setState] = useSetState<{
    fileResult: Record<string, any[]> | null;
    supplier_id_account_id: { supplier_id: string; account_id: string } | null;
    isAddMode: boolean;
    failedResults: Record<string, any[]> | null; // 失败的结果
  }>({
    fileResult: null,
    supplier_id_account_id: null,
    isAddMode: true,
    failedResults: null,
  });
  const [currentPageMap, setCurrentPageMap] = useState<Record<string, number>>({}); // 每个 Tab 的当前页
  const [pageSize] = useState(7); // 默认每页显示7条数据

  // 关闭时重置状态
  useEffect(() => {
    if (visible === false) {
      setCurrentPageMap({});
      setState({
        fileResult: null,
        supplier_id_account_id: null,
        failedResults: null,
        isAddMode: true,
      });
    }
  }, [visible]);

  const initializeCurrentPage = (result: Record<string, any[]>) => {
    const initialValue: Record<string, number> = {};
    setCurrentPageMap(
      Object.keys(result).reduce((acc, key) => {
        acc[key] = 1;
        return acc;
      }, initialValue),
    );
  };

  const handleUploadChange = async (file: File) => {
    try {
      const result = await readExcelFileToJson(file, getSheetConfigs());
      setState({ fileResult: result }); // 保存解析结果
      initializeCurrentPage(result);
    } catch (error) {
      message.error('文件解析失败，请检查文件格式');
    }
  };

  const handleResponse = (res: any) => {
    if (res.code === 0) {
      const count = Object.keys(res.data).reduce((acc, key) => {
        return acc + (res.data[key]?.length ?? 0);
      }, 0);
      if (count > 0) {
        const { account_id, supplier_id, ...restData } = res.data;
        const failedKeys = Object.keys(restData).filter((key) => restData[key].length > 0);
        for (const key of failedKeys) {
          restData[key] = restData[key].map((item: any, index: number) => ({
            ...item,
            // 添加自定义索引
            custom_index: `${key}-${index}`,
          }));
        }
        setState({
          failedResults: restData,
          isAddMode: false,
        }); // 切换到查看失败结果模式
        message.error(`添加失败: ${failedKeys.join(', ')}`);
      } else {
        message.success('添加成功');
        handleClose(); // 关闭模态框
      }
    } else if (res.code === 2) {
      message.error('添加失败');
    }
    refreshAllTables();
  };

  const handleSubmit = async () => {
    setLoading(true);
    const finalData = state.isAddMode ? state.fileResult : state.failedResults;
    const deleteCustomIndex = (data: Record<string, any[]>) => {
      return Object.keys(data).reduce((acc: Record<string, any[]>, key) => {
        acc[key] = data[key].map((item) => {
          return omit(item, ['custom_index', 'index']);
        });
        return acc;
      }, {});
    };
    const transformData = {
      ...state.supplier_id_account_id,
      ...deleteCustomIndex(finalData ?? {}),
    };
    try {
      const res = await addErrorCodeList(transformData);
      handleResponse(res);
    } catch (error) {
      message.error('提交失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const renderEditableTable = (data: any[], sheetKey: string, canEdit: boolean = true) => {
    if (!data || data.length === 0) return null;

    // 动态生成列
    const columns: ProColumns<any>[] = Object.keys(data[0]).map((key) => {
      const column: ProColumns<any> = {
        title: tableTitleMap[key as keyof typeof tableTitleMap] || key, // 使用原始的 key 作为标题
        dataIndex: key,
        key,
        editable: () => canEdit,
      };
      if (key === 'custom_index' || key === 'index') {
        column.hideInTable = true;
      }
      // 设置 valueType 为 select 的列
      if (key === 'temporary' || key === 'retry' || key === 'is_need_retry') {
        column.valueType = 'select';
        column.valueEnum = {
          Y: { text: '是' },
          N: { text: '否' },
        };
      }

      return column;
    });
    const finalColumns = [
      ...columns,
      canEdit
        ? {
            title: '操作',
            valueType: 'option',
            width: 200,
            render: (_: any, record: any, __: any, action: any) => (
              <a
                key="editable"
                onClick={() => {
                  action?.startEditable?.(record.custom_index);
                }}
              >
                编辑
              </a>
            ),
          }
        : undefined,
    ].filter((column) => column !== undefined);

    return (
      <EditableProTable
        key={data.length}
        rowKey="custom_index"
        maxLength={5}
        recordCreatorProps={false}
        columns={finalColumns as ProColumns<any>[]}
        value={data}
        editable={{
          type: 'single',
          // editableKeys: editableKeys[sheetKey] ?? [],
          actionRender: (row, config, defaultDoms) => {
            return [defaultDoms.save, defaultDoms.cancel];
          },
          onValuesChange: (record, recordList) => {
            // console.log(record, recordList);
          },
          onSave: async (key, record) => {
            const updatedData = (data ?? []).map((item) => {
              if (item.custom_index === key) {
                return { ...record }; // 直接使用新的 record 数据
              }
              return item;
            });
            setState({
              failedResults: {
                ...(state.failedResults ?? {}),
                [sheetKey]: updatedData,
              },
            });
          },
        }}
        pagination={{
          total: data.length,
          pageSize,
          current: currentPageMap[sheetKey],
          onChange: (page) => setCurrentPageMap((prev) => ({ ...prev, [sheetKey]: page })),
        }}
      />
    );
  };

  // Tab 中文名映射
  const tabLabels: Record<string, string> = {
    error_code_mapping: '错误码列表',
    message_state_mapping: '回执状态列表',
    submit_status_mapping: '提交状态列表',
    standard_code_mapping: '标准错误码列表',
  };

  const disabled = !state.supplier_id_account_id || !state.fileResult;

  return (
    <Modal
      title="批量添加"
      open={visible}
      onCancel={handleClose}
      footer={
        <Button disabled={disabled} loading={loading} type="primary" onClick={handleSubmit}>
          提交
        </Button>
      }
      destroyOnClose
      width={'80vw'}
    >
      <Alert
        message="添加失败一般都是因为错误码重复了,双击单选供应商账号"
        type="info"
        showIcon
        style={{ marginBottom: 10 }}
      />
      <Form>
        <Form.Item
          label="供应商/账号"
          name="supplier_id_account_id"
          rules={[{ required: true, message: '请选择供应商/账号' }]}
        >
          <ProviderCascader
            type={0}
            style={{ maxWidth: 500 }}
            disabled={!state.isAddMode}
            onChange={(value) => {
              setState({
                supplier_id_account_id: {
                  supplier_id: value?.[0]?.toString(),
                  account_id: value?.[1]?.toString(),
                },
              });
            }}
            placeholder="请选择供应商账号"
          />
        </Form.Item>
        <Form.Item
          style={{ display: state.isAddMode ? 'block' : 'none' }}
          label="上传文件"
          name="file"
          rules={[{ required: true, message: '请上传文件' }]}
        >
          <Upload
            beforeUpload={(file) => {
              console.log('beforeUpload', file);
              handleUploadChange(file);
              return false;
            }}
            accept=".xlsx,.xls"
            maxCount={1}
            onChange={({ file }) => {
              if (file.status === 'removed') {
                setState({ fileResult: null });
              }
            }}
          >
            <Button>点击上传</Button>
          </Upload>
        </Form.Item>
      </Form>
      {state.isAddMode && state.fileResult && (
        <Tabs defaultActiveKey="1">
          {Object.keys(state.fileResult).map((sheetKey) => (
            <Tabs.TabPane tab={tabLabels[sheetKey] || sheetKey} key={sheetKey}>
              {renderEditableTable(state?.fileResult?.[sheetKey] ?? [], sheetKey, false)}
            </Tabs.TabPane>
          ))}
        </Tabs>
      )}
      {!state.isAddMode && state.failedResults && (
        <>
          <span style={{ marginBottom: 10, color: 'red' }}>添加失败, 请检查以下数据:</span>
          <Tabs defaultActiveKey="1">
            {Object.keys(state.failedResults).map((sheetKey) => (
              <Tabs.TabPane tab={tabLabels[sheetKey] || sheetKey} key={sheetKey}>
                {renderEditableTable(state?.failedResults?.[sheetKey] ?? [], sheetKey, true)}
              </Tabs.TabPane>
            ))}
          </Tabs>
        </>
      )}
    </Modal>
  );
};

export default BatchAddModal;
