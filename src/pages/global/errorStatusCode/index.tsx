import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Select, Tooltip, message } from 'antd';
import { BetaSchemaForm, PageContainer, ProColumns } from '@ant-design/pro-components';
import CommonErrorCodeTable, { CommonErrorCode } from './CommonErrorCodeTable';
import { useAsyncRetry } from 'react-use';
import { addErrorCodeList, getStandardCodeList } from '@/services/errorStatusCode';
import StandardCodeTable, { StandardCode } from './StandardCodeTable';
import { QuestionCircleOutlined } from '@ant-design/icons';
import BatchAddModal from './BatchAddModal';
import ProviderCascader from '@/pages/global-components/ProviderCascader';

export enum ErrorStatusCodeType {
  SUBMIT_STATUS = 1,
  MESSAGE_STATE = 2,
  ERROR_CODE = 3,
  STANDARD_CODE = 4,
}

const ErrorStatusCodeTypeMap = {
  [ErrorStatusCodeType.SUBMIT_STATUS]: {
    label: '提交状态错误码',
    formKey: 'submit_status_mapping',
  },
  [ErrorStatusCodeType.MESSAGE_STATE]: {
    label: '回执状态错误码',
    formKey: 'message_state_mapping',
  },
  [ErrorStatusCodeType.ERROR_CODE]: {
    label: '回执子错误码',
    formKey: 'error_code_mapping',
  },
  [ErrorStatusCodeType.STANDARD_CODE]: {
    label: '标准错误码',
    formKey: 'standard_code_mapping',
  },
};

const ErrorCodeManager = () => {
  const [visible, setVisible] = useState(false);
  const formRef = useRef<any>();
  const standardCodeDescMapRef = useRef<any>();
  const [currentType, setCurrentType] = useState(ErrorStatusCodeType.SUBMIT_STATUS);
  const tablesRef = useRef<any>({
    [ErrorStatusCodeType.SUBMIT_STATUS]: null,
    [ErrorStatusCodeType.MESSAGE_STATE]: null,
    [ErrorStatusCodeType.ERROR_CODE]: null,
    [ErrorStatusCodeType.STANDARD_CODE]: null,
  });

  const { value: standardCodeListOptions, retry } = useAsyncRetry(async () => {
    const res = await getStandardCodeList();
    standardCodeDescMapRef.current = res?.data?.reduce((acc: any, item: any) => {
      acc[item.standard_code] = {
        standard_code_desc_cn: item.standard_code_desc_cn,
        standard_code_desc_en: item.standard_code_desc_en,
      };
      return acc;
    }, {});
    return (
      res?.data?.map(({ standard_code, standard_code_desc_cn }: any) => ({
        label: `${standard_code} - ${standard_code_desc_cn} `,
        value: standard_code,
      })) ?? []
    );
  });

  const handleOpen = () => {
    setVisible(true);
  };

  const handleClose = () => {
    setVisible(false);
  };
  // 刷新所有table
  const refreshAllTables = () => {
    Object.values(tablesRef.current).forEach((table: any) => {
      table?.reload?.();
    });
  };

  const handleSubmit = async (values: any) => {
    const { supplier_id, account_id, ...rest } = values;

    const res = await addErrorCodeList({
      supplier_id,
      account_id,
      [ErrorStatusCodeTypeMap[currentType].formKey]: [
        {
          ...rest,
        },
      ],
    });
    if (res.code === 0) {
      message.success('新增成功');
      tablesRef.current[currentType].reload();
      formRef.current.resetFields();
      return true;
    }
    message.error(res.message);
    return false;
  };

  const commonErrorCodeColumns: ProColumns<CommonErrorCode>[] = [
    { title: 'ID', dataIndex: 'id', hideInSearch: true },
    {
      title: '供应商',
      dataIndex: 'supplier_id',
      hideInForm: true,
      hideInSearch: true,
      renderText: (_: string, record: CommonErrorCode) =>
        `${record.supplier_name} - ${record.supplier_id}`,
    },
    {
      title: '账号',
      dataIndex: 'account_id',
      hideInForm: true,
      hideInSearch: true,
      renderText: (_: string, record: CommonErrorCode) =>
        record.account_id === '*' ? '*' : `${record.account_name} - ${record.account_id}`,
    },
    {
      title: '供应商/账号',
      dataIndex: 'supplier_id_account_id',

      hideInTable: true,
      renderFormItem: () => <ProviderCascader type={0} placeholder="请选择供应商账号" />,
      // @ts-ignore
      transform: (value: string) => {
        return {
          supplier_id: value[0]?.toString(),
          account_id: value[1]?.toString(),
        };
      },
    },
    { title: '错误码', dataIndex: 'code' },
    { title: '错误码描述', dataIndex: 'code_desc', hideInSearch: true },
    {
      title: '标准错误码(中文描述)',
      dataIndex: 'standard_code',
      render: (_, record) => {
        const standardCodeDesc = standardCodeDescMapRef.current[record.standard_code];
        const cnDesc =
          standardCodeDesc?.standard_code_desc_cn?.length > 10
            ? `${standardCodeDesc?.standard_code_desc_cn.slice(0, 10)}...`
            : standardCodeDesc?.standard_code_desc_cn;
        return (
          <Tooltip
            title={
              <div>
                <p>
                  中文描述:
                  {standardCodeDesc?.standard_code_desc_cn}
                </p>
                <p>
                  英文描述:
                  {standardCodeDesc?.standard_code_desc_en}
                </p>
              </div>
            }
          >
            {/* 一行显示不下，显示省略号 */}
            {record.standard_code} - {cnDesc}
            <QuestionCircleOutlined style={{ marginLeft: 8 }} />
          </Tooltip>
        );
      },
      renderFormItem: () => <Select allowClear options={standardCodeListOptions} showSearch />,
    },
    {
      title: '是否临时错误码',
      dataIndex: 'temporary',
      hideInSearch: true,
      valueEnum: { Y: '是', N: '否' },
    },
    {
      title: '是否重试',
      dataIndex: 'retry',
      hideInSearch: true,
      valueEnum: { Y: '是', N: '否' },
    },
  ];

  const standardCodeColumns: ProColumns<StandardCode>[] = [
    { title: 'ID', dataIndex: 'id', hideInSearch: true },
    { title: '标准错误码', dataIndex: 'standard_code' },
    { title: '中文错误码描述', dataIndex: 'standard_code_desc_cn', hideInSearch: true },
    { title: '英文错误码描述', dataIndex: 'standard_code_desc_en', hideInSearch: true },
    { title: '外部错误码', dataIndex: 'external_code', hideInSearch: true },
    { title: '外部中文错误码描述', dataIndex: 'external_code_desc_cn', hideInSearch: true },
    { title: '外部英文错误码描述', dataIndex: 'external_code_desc_en', hideInSearch: true },
    {
      title: '是否重试',
      dataIndex: 'retry',
      hideInSearch: true,
      // @ts-ignore
      transform: (value: string) => ({
        is_need_retry: value,
      }),
      valueEnum: { Y: '是', N: '否' },
    },
  ];

  const addCommonErrorCodeColumns = commonErrorCodeColumns.filter(
    (item) => !['id', 'standard_code'].includes(item.dataIndex as string),
  ) as any;
  const addStandardCodeColumns = standardCodeColumns.filter(
    (item) => !['id'].includes(item.dataIndex as string),
  ) as any;

  return (
    <PageContainer>
      <BetaSchemaForm
        width={500}
        formRef={formRef}
        title={`新增${ErrorStatusCodeTypeMap[currentType].label}`}
        layoutType="ModalForm"
        onOpenChange={handleClose}
        trigger={
          <Button type="primary" style={{ marginRight: 16 }}>
            新增当前错误码
          </Button>
        }
        onFinish={handleSubmit}
        columns={
          currentType === ErrorStatusCodeType.STANDARD_CODE
            ? addStandardCodeColumns
            : addCommonErrorCodeColumns
        }
      />
      <Button type="primary" onClick={handleOpen}>
        批量新增
      </Button>
      <BatchAddModal
        visible={visible}
        handleClose={handleClose}
        refreshAllTables={refreshAllTables}
      />
      <Tabs
        onChange={(key) => {
          setCurrentType(Number(key) as ErrorStatusCodeType);
          retry();
        }}
        activeKey={String(currentType)}
      >
        <Tabs.TabPane
          tab={ErrorStatusCodeTypeMap[ErrorStatusCodeType.SUBMIT_STATUS].label}
          key={ErrorStatusCodeType.SUBMIT_STATUS}
        >
          <CommonErrorCodeTable
            ref={(ref) => (tablesRef.current[ErrorStatusCodeType.SUBMIT_STATUS] = ref)}
            map_type={ErrorStatusCodeType.SUBMIT_STATUS}
            standardCodeListOptions={standardCodeListOptions}
            columns={commonErrorCodeColumns}
          />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={ErrorStatusCodeTypeMap[ErrorStatusCodeType.MESSAGE_STATE].label}
          key={ErrorStatusCodeType.MESSAGE_STATE}
        >
          <CommonErrorCodeTable
            ref={(ref) => (tablesRef.current[ErrorStatusCodeType.MESSAGE_STATE] = ref)}
            map_type={ErrorStatusCodeType.MESSAGE_STATE}
            standardCodeListOptions={standardCodeListOptions}
            columns={commonErrorCodeColumns}
          />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={ErrorStatusCodeTypeMap[ErrorStatusCodeType.ERROR_CODE].label}
          key={ErrorStatusCodeType.ERROR_CODE}
        >
          <CommonErrorCodeTable
            ref={(ref) => (tablesRef.current[ErrorStatusCodeType.ERROR_CODE] = ref)}
            map_type={ErrorStatusCodeType.ERROR_CODE}
            standardCodeListOptions={standardCodeListOptions}
            columns={commonErrorCodeColumns}
          />
        </Tabs.TabPane>
        <Tabs.TabPane
          tab={ErrorStatusCodeTypeMap[ErrorStatusCodeType.STANDARD_CODE].label}
          key={ErrorStatusCodeType.STANDARD_CODE}
        >
          <StandardCodeTable
            ref={(ref) => (tablesRef.current[ErrorStatusCodeType.STANDARD_CODE] = ref)}
            columns={standardCodeColumns}
            standardCodeListOptions={standardCodeListOptions}
          />
        </Tabs.TabPane>
      </Tabs>
    </PageContainer>
  );
};

export default ErrorCodeManager;
