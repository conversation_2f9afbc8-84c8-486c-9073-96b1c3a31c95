import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import ProTable, { ProColumns } from '@ant-design/pro-table';
import {
  deleteErrorCodeList,
  getErrorCodeList,
  updateErrorCodeList,
} from '@/services/errorStatusCode';
import _ from 'lodash';
import { Button, message, Modal, Popconfirm } from 'antd';
import { BetaSchemaForm } from '@ant-design/pro-components';

export interface CommonErrorCode {
  id: number;
  supplier_id: string;
  supplier_name: string;
  account_id: string;
  account_name: string;
  code: string;
  code_desc: string;
  standard_code: string;
  temporary?: string;
  retry?: string;
}

interface CommonErrorCodeTableProps {
  standardCodeListOptions: { label: string; value: string }[];
  map_type: number;
  columns: ProColumns<CommonErrorCode>[];
}

const CommonErrorCodeTable = forwardRef(({ columns, map_type }: CommonErrorCodeTableProps, ref) => {
  const tableRef = useRef<any>();
  const [visible, setVisible] = useState(false);
  const [batchEditVisible, setBatchEditVisible] = useState(false);
  const [selectedRecords, setSelectedRecords] = useState<CommonErrorCode[]>([]);
  const formRef = useRef<any>();
  const [currentRecord, setCurrentRecord] = useState<CommonErrorCode | null>(null);

  useImperativeHandle(ref, () => ({
    reload: () => {
      tableRef.current.reload();
    },
  }));

  const handleCommonErrorCodeEdit = async (record: CommonErrorCode) => {
    setVisible(true);
    const editRecord = {
      ...record,
      supplier_id_account_id:
        record.account_id === '*' ? [record.supplier_id] : [record.supplier_id, record.account_id],
    };
    if (formRef.current) {
      formRef.current.setFieldsValue(editRecord);
    }
    setCurrentRecord(editRecord);
  };

  const handleCommonErrorCodeSubmit = async (record: Omit<CommonErrorCode, 'id'>) => {
    const finallyRecord = {
      id: currentRecord?.id,
      ...record,
      account_id: record.account_id ? record.account_id : '*',
    };
    console.log('finallyRecord', finallyRecord);
    const res = await updateErrorCodeList({
      map_type,
      update_data: [
        {
          ..._.omit(finallyRecord, ['supplier_id_account_id']),
        },
      ],
    });
    if (res.code === 0) {
      message.success('更新成功');
      tableRef.current.reload();
      return true;
    }
    return false;
  };

  const handleCommonErrorCodeDelete = (record: CommonErrorCode) => {
    return deleteErrorCodeList({ ids: [record.id], map_type })
      .then(() => {
        message.success('删除成功');
        tableRef.current.reload();
      })
      .catch((err) => {
        message.error(err.message);
      });
  };
  const handleCommonErrorCodeBatchDelete = (rows: CommonErrorCode[]) => {
    return deleteErrorCodeList({ ids: rows.map((item) => item.id), map_type }).then(() => {
      message.success('删除成功');
      tableRef.current.reload();
    });
  };

  const request = async (params: any) => {
    const res = await getErrorCodeList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      map_type,
      page_index: params.current,
      page_size: params.pageSize,
    });
    return {
      data: res?.data?.list ?? [],
      success: true,
      total: res?.data?.count ?? 0,
    };
  };

  const transformColumns = columns.map((item) => {
    if (item.dataIndex === 'supplier_id_account_id') {
      return {
        ...item,
        transform: (value: string) => {
          return {
            supplier_id: [value[0]?.toString()],
            account_id: [value[1]?.toString()],
          };
        },
      };
    }
    return item;
  });
  const showBatchEditDialog = () => {
    setBatchEditVisible(true);
  };
  const handleCommonErrorCodeBatchSubmit = async (value: {
    standard_code: string;
  }): Promise<boolean | void> => {
    return new Promise<boolean>((resolve) => {
      Modal.confirm({
        title: `确定批量更新吗选中的${selectedRecords.length}条错误码配置吗？`,
        onOk: async () => {
          const res = await updateErrorCodeList({
            map_type,
            update_data: selectedRecords.map((item) => ({
              id: item.id,
              standard_code: value.standard_code,
            })),
          });
          if (res.code === 0) {
            message.success('更新成功');
            setSelectedRecords([]);
            tableRef.current.reload();
            resolve(true);
          } else {
            message.error(res.message);
            resolve(false);
          }
        },
        onCancel: () => {
          resolve(false);
        },
      });
    });
  };
  const disabledBatchDelete = selectedRecords.length === 0;
  return (
    <>
      <BetaSchemaForm
        formRef={formRef}
        open={visible}
        title="编辑错误码"
        layoutType="ModalForm"
        width={500}
        onOpenChange={setVisible}
        columns={(columns.filter((item) => item.dataIndex !== 'id') as any) ?? []}
        onFinish={handleCommonErrorCodeSubmit}
        initialValues={currentRecord || undefined}
      />
      <BetaSchemaForm
        open={batchEditVisible}
        title="批量编辑"
        layoutType="ModalForm"
        width={500}
        modalProps={{ destroyOnClose: true }}
        onOpenChange={setBatchEditVisible}
        columns={columns.filter((item) => item.dataIndex === 'standard_code') as any}
        onFinish={handleCommonErrorCodeBatchSubmit}
        initialValues={currentRecord || undefined}
      />
      <ProTable
        form={{
          labelAlign: 'left',
          labelWidth: 'auto',
        }}
        search={{
          optionRender: (searchConfig, props, dom) => {
            return [
              ...dom,
              <Popconfirm
                key="batchDelete"
                title="确定批量删除所选的错误码吗？"
                onConfirm={() => handleCommonErrorCodeBatchDelete(selectedRecords)}
              >
                <Button key="batchDelete" disabled={disabledBatchDelete}>
                  批量删除
                </Button>
              </Popconfirm>,
              <Button key="batchEdit" disabled={disabledBatchDelete} onClick={showBatchEditDialog}>
                批量编辑
              </Button>,
            ];
          },
        }}
        rowKey="id"
        rowSelection={{
          selectedRowKeys: selectedRecords.map((item) => item.id),
          onChange: (_, records) => setSelectedRecords(records),
        }}
        pagination={{
          onChange: () => {
            setSelectedRecords([]);
          },
        }}
        options={false}
        actionRef={tableRef}
        columns={transformColumns.concat([
          {
            title: '操作',
            dataIndex: 'action',
            hideInSearch: true,
            render: (_, record) => (
              <>
                <Button onClick={() => handleCommonErrorCodeEdit(record)} type="link">
                  编辑
                </Button>
                <Popconfirm
                  title="确定删除吗？"
                  onConfirm={() => handleCommonErrorCodeDelete(record)}
                >
                  <Button danger type="link">
                    删除
                  </Button>
                </Popconfirm>
              </>
            ),
          },
        ])}
        request={request}
      />
    </>
  );
});

export default CommonErrorCodeTable;
