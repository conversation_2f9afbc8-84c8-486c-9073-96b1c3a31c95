import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import ProTable, { ProColumns } from '@ant-design/pro-table';
import {
  deleteErrorCodeList,
  getErrorCodeList,
  updateErrorCodeList,
} from '@/services/errorStatusCode';
import _ from 'lodash';
import { Button, message, Popconfirm } from 'antd';
import { BetaSchemaForm } from '@ant-design/pro-components';
import { ErrorStatusCodeType } from './index';
export interface StandardCode {
  id: number;
  standard_code: string;
  standard_code_desc: string;
  external_code: string;
  external_code_desc: string;
}

interface StandardCodeTableProps {
  columns: ProColumns<StandardCode>[];
  standardCodeListOptions: { label: string; value: string }[];
}

const StandardCodeTable = forwardRef(({ columns: _columns }: StandardCodeTableProps, ref) => {
  const map_type = ErrorStatusCodeType.STANDARD_CODE;
  const tableRef = useRef<any>();
  const formRef = useRef<any>();
  const [visible, setVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<StandardCode | undefined>(undefined);

  const handleStandardCodeEdit = async (record: StandardCode) => {
    setCurrentRecord(record);
    setVisible(true);
    if (formRef.current) {
      formRef.current.setFieldsValue(record);
    }
  };

  const handleDelete = (record: StandardCode) => {
    return deleteErrorCodeList({ ids: [record.id], map_type })
      .then(() => {
        message.success('删除成功');
        tableRef.current.reload();
      })
      .catch((err) => {
        message.error(err.message);
      });
  };

  const columns: ProColumns<StandardCode>[] = [
    ..._columns,
    {
      title: '操作',
      dataIndex: 'action',
      hideInSearch: true,
      render: (_, record) => (
        <>
          <Button onClick={() => handleStandardCodeEdit(record)} type="link">
            编辑
          </Button>
          <Popconfirm title="确定删除吗？" onConfirm={() => handleDelete(record)}>
            <Button danger type="link">
              删除
            </Button>
          </Popconfirm>
        </>
      ),
    },
  ];

  useImperativeHandle(ref, () => ({
    reload: () => {
      tableRef.current.reload();
    },
  }));

  const request = async (params: any) => {
    const res = await getErrorCodeList({
      ..._.omit(
        _.pickBy(params, (v) => !_.isNil(v) && v !== ''),
        ['pageSize', 'current'],
      ),
      map_type,
      page_index: params.current,
      page_size: params.pageSize,
    });
    return {
      data: res?.data?.list ?? [],
      success: true,
      total: res?.data?.count ?? 0,
    };
  };
  const handleStandardCodeSubmit = async (record: Omit<StandardCode, 'id'>) => {
    const finallyRecord = {
      standard_id: currentRecord?.id,
      ...record,
    };
    console.log('finallyRecord', finallyRecord);
    const res = await updateErrorCodeList({
      map_type,
      update_data: [finallyRecord],
    });
    if (res.code === 0) {
      message.success('更新成功');
      tableRef.current.reload();
      return true;
    }
    return false;
  };
  return (
    <>
      <BetaSchemaForm
        formRef={formRef}
        open={visible}
        title="编辑错误码"
        layoutType="ModalForm"
        width={500}
        onOpenChange={setVisible}
        columns={
          (columns.filter(
            (item) => item.dataIndex !== 'id' && item.dataIndex !== 'action',
          ) as any) ?? []
        }
        onFinish={handleStandardCodeSubmit}
        initialValues={currentRecord || undefined}
      />
      <ProTable
        form={{
          labelAlign: 'left',
          labelWidth: 'auto',
          span: 4,
        }}
        options={false}
        actionRef={tableRef}
        columns={columns}
        request={request}
      />
    </>
  );
});

export default StandardCodeTable;
