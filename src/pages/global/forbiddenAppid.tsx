import React, { useMemo } from 'react';
import { getForbiddenAppid, delForbiddenAppid } from '@/services/scdAPI';
import { Button, Col, Form, Input, message, Modal, Row, Table } from 'antd';
import _ from 'lodash';
import dayjs from 'dayjs';
import { PageContainer } from '@ant-design/pro-layout';
import { SearchOutlined } from '@ant-design/icons';
import { AddForbiddenAppid } from './components/AddForbiddenAppid';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { useSetState } from 'react-use';

export const options = [
  { value: 1, label: '发送违规短信' },
  { value: 2, label: '疑似违规团伙' },
  { value: 3, label: '多次创建违规模板' },
  { value: 0, label: '其他' },
];

const ForbiddenAppid = () => {
  const [form] = Form.useForm();
  const [searchKeys, setSearchKeys] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const keys = _.pickBy(searchKeys, (v: number | string) => v !== '');
    const res = await getForbiddenAppid({ ...keys });
    return res?.data || {};
  }, [searchKeys]);

  const dialogRef = useDialogRef();

  const packageList = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const _handlerSubmit = async (formValue: { appid: string }) => {
    setSearchKeys({
      ...formValue,
      page_index: 1,
    });
  };

  async function doDel(row: any) {
    Modal.confirm({
      title: '确认删除？',
      content: `确认删除appid： ${row.appid}?`,
      onOk: async (close) => {
        try {
          const res = await delForbiddenAppid({ appid: row.appid, type: row.type });
          close();
          if (res.code === 0) {
            message.success('删除成功');
            retry();
          }
        } catch (error) {}
      },
    });
  }

  return (
    <PageContainer>
      <Row justify="space-between">
        <Col>
          <Button
            type="primary"
            onClick={() => {
              dialogRef.current.open({
                onSuccess: retry,
                initValues: { forbidden_endtime: dayjs().add(3, 'days') },
              });
            }}
          >
            添加
          </Button>
        </Col>
        <Col>
          <Form
            form={form}
            layout="inline"
            labelAlign="right"
            onFinish={(vals) => _handlerSubmit(vals)}
            style={{ maxHeight: 500, overflow: 'auto' }}
          >
            <Form.Item name="appid" label="">
              <Input placeholder="appid" style={{ width: 200 }} />
            </Form.Item>
            <Form.Item>
              <Button htmlType="submit" type="primary" loading={loading} icon={<SearchOutlined />}>
                查询
              </Button>
            </Form.Item>
          </Form>
        </Col>
      </Row>
      <div>
        这里添加腾讯云appid会开启该腾讯云appid下面所有应用的ip白名单校验,同时控制台禁止创建应用和禁止展示已有应用
      </div>
      <Table
        dataSource={packageList}
        columns={[
          {
            title: 'appid',
            dataIndex: 'appid',
            key: 'appid',
            align: 'center',
          },
          {
            title: 'id_card',
            dataIndex: 'icard_code',
            key: 'icard_code',
            align: 'center',
          },
          {
            title: '封禁类型',
            dataIndex: 'forbidden_type',
            key: 'forbidden_type',
            align: 'center',
            render: (type: number) => _.find(options, (v) => v.value === type)?.label,
          },
          {
            title: '封禁结束时间',
            dataIndex: 'forbidden_endtime',
            key: 'forbidden_endtime',
            align: 'center',
          },
          {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
            align: 'center',
          },
          {
            title: '操作',
            render: (row: any) => {
              return (
                <a
                  onClick={() => {
                    doDel(row);
                  }}
                >
                  删除
                </a>
              );
            },
          },
        ]}
        loading={loading}
        rowKey="appid"
        pagination={{
          current: searchKeys.page_index,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
        style={{ marginTop: 20 }}
      />
      <AddForbiddenAppid dialogRef={dialogRef} onSuccess={retry} />
    </PageContainer>
  );
};

export default ForbiddenAppid;
