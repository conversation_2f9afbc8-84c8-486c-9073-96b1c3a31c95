import React from 'react';
import PatternTable from '@/pages/component/PatternLayout';
import { addDebitList, deleteDebitList, getDebitList } from '@/services/debitByQappid';
import _ from 'lodash';
import { Input } from 'antd';

const DebitList = () => {
  const columns: any = [
    {
      title: 'qappid',
      dataIndex: 'qappid',
      key: 'qappid',
      align: 'center',
    },
    {
      title: 'uin',
      dataIndex: 'uin',
      key: 'uin',
      align: 'center',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      align: 'center',
    },
    {
      title: '创建人',
      dataIndex: 'staffname',
      key: 'staffname',
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'qappids',
      label: 'qappids',
      isRequired: true,
      render: () => <Input.TextArea placeholder="换行输入多个" autoSize style={{ width: 200 }} />,
    },
    {
      showOnAdd: true,
      name: 'remark',
      label: '备注',
      isRequired: true,
      render: () => <Input.TextArea autoSize style={{ width: 200 }} />,
    },
  ];

  async function doDel(vals: any) {
    return await deleteDebitList({ qappid: vals.qappid });
  }
  async function doAdd(vals: any) {
    return await addDebitList({
      qappids: _.map(_.compact(vals.qappids.split('\n')), _.trim),
      remark: vals.remark,
    });
  }
  async function getList(vals?: any) {
    return await getDebitList({ ...vals });
  }

  return (
    <PatternTable
      rowKey={(record: { qappid: number; uin: number }) => `${record.qappid}_${record.uin}`}
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        {
          label: 'qappid',
          name: 'qappid',
          renderType: 'number',
        },
        {
          label: 'uin',
          name: 'uin',
          renderType: 'number',
        },
      ]}
      operateForm={operateForm}
      operType={2}
    />
  );
};

export default DebitList;
