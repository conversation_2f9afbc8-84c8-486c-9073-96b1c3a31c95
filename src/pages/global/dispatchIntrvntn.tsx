import React, { useState, useEffect } from 'react';
import { getDispatchIntrvntn, setDispatchIntrvntn } from '@/services/scdAPI';
import { Checkbox, Button } from 'antd';
import { PageContainer, PageLoading } from '@ant-design/pro-layout';
import { useSetState } from 'react-use';
import _ from 'lodash';
import { isChina } from '@/const/const';

const DispatchIntrvntn = () => {
  const [isLoading, setLoading] = useState<boolean>(false);
  const [dispathInfo, setDispatchIno] = useSetState<{
    id: number;
    receiptIntervention: number;
    timeoutIntervention: number;
    qualityControl: number;
    reissueSetting?: number;
  }>({
    id: 0,
    receiptIntervention: 0,
    timeoutIntervention: 0,
    qualityControl: 0,
    reissueSetting: 0,
  });

  useEffect(() => {
    getList();
  }, []);

  async function doEdit() {
    let params = _.cloneDeep(dispathInfo);
    if (!isChina) {
      params = _.omit(params, ['reissueSetting']);
    }
    const res = await setDispatchIntrvntn({
      ...params,
    });
    if (res?.code === 0) {
      getList();
    }
  }

  async function getList() {
    setLoading(true);
    const res = await getDispatchIntrvntn();
    setDispatchIno(res?.data ?? {});
    setLoading(false);
  }

  return (
    <PageContainer>
      {isLoading ? (
        <PageLoading />
      ) : (
        <div style={{ lineHeight: '30px' }}>
          <div>
            <Checkbox
              checked={dispathInfo.receiptIntervention === 1}
              onChange={(e) => {
                setDispatchIno({ receiptIntervention: e.target.checked ? 1 : 0 });
              }}
            >
              开启回执率干预
            </Checkbox>
          </div>
          <div>
            <Checkbox
              checked={dispathInfo.timeoutIntervention === 1}
              onChange={(e) => {
                setDispatchIno({ timeoutIntervention: e.target.checked ? 1 : 0 });
              }}
            >
              开启超时率干预
            </Checkbox>
          </div>
          <div>
            <Checkbox
              checked={dispathInfo.qualityControl === 1}
              onChange={(e) => {
                setDispatchIno({ qualityControl: e.target.checked ? 1 : 0 });
              }}
            >
              开启质量分析调度控制
            </Checkbox>
          </div>
          {isChina && (
            <div>
              <Checkbox
                checked={dispathInfo.reissueSetting === 1}
                onChange={(e) => {
                  setDispatchIno({ reissueSetting: e.target.checked ? 1 : 0 });
                }}
              >
                开启补发功能
              </Checkbox>
            </div>
          )}
          <div>
            <Button type="primary" size="small" onClick={doEdit}>
              设置
            </Button>
          </div>
        </div>
      )}
    </PageContainer>
  );
};

export default DispatchIntrvntn;
