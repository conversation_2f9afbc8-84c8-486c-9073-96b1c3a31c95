import React, { useState, useEffect } from 'react';
import { getForbidProv, addForbidProv, delForbidProv } from '@/services/scdAPI';
import { getGloablProviders } from '@/services/api';
import PatternTable from '@/pages/component/PatternLayout';
import _ from 'lodash';

const columns: any = [
  {
    title: 'provider_name',
    dataIndex: 'provider_name',
    key: 'provider_name',
    align: 'center',
  },
];
const ForbidProvider = () => {
  const [providers, setProviders] = useState<any>([]);

  const operateForm = [
    {
      showOnAdd: true,
      name: 'provider_id',
      label: 'provider',
      disabled: false,
      isRequired: true,
      renderType: 'select',
      options: providers,
    },
  ];

  const iot_provider = [
    { label: '新联物联卡北京', value: '1' },
    { label: '新联物联卡广州', value: '2' },
  ];

  const searchKeys = [
    {
      label: '',
      name: 'provider_id',
      renderType: 'select',
      options: providers,
    },
  ];

  useEffect(() => {
    getGloablProviders().then((res) => {
      const providersList = (res?.data ?? []).map(
        (item: { provider_id: number; provider_name: string }) => {
          return {
            value: item.provider_id,
            label: item.provider_name,
          };
        },
      );
      setProviders(providersList);
    });
  }, []);

  async function doDel(type: string, vals: any) {
    return await delForbidProv({ provider_id: vals.provider_id, type });
  }
  async function doAdd(type: string, vals: any) {
    return await addForbidProv({ provider_id: vals.provider_id, type });
  }

  return (
    <>
      <PatternTable
        title="短信服务"
        columns={columns}
        rowKey="provider_id"
        getFn={async (vals: object) => {
          return await getForbidProv({ ...vals, type: '1' });
        }}
        addFn={async (vals: object) => {
          return await doAdd('1', vals);
        }}
        delFn={async (vals: object) => {
          return await doDel('1', vals);
        }}
        searchKeys={searchKeys}
        operateForm={operateForm}
        operType={2}
      />
      <hr />
      <PatternTable
        breadcrumbRender={false}
        title="物联卡服务"
        rowKey="provider_id"
        getFn={async (vals: object) => {
          return await getForbidProv({ ...vals, type: '2' });
        }}
        addFn={async (vals: object) => {
          return await doAdd('2', vals);
        }}
        delFn={async (vals: object) => {
          return await doDel('2', vals);
        }}
        columns={_.cloneDeep(columns)}
        searchKeys={[
          {
            label: '',
            name: 'provider_id',
            renderType: 'select',
            options: iot_provider,
          },
        ]}
        operateForm={[
          {
            showOnAdd: true,
            name: 'provider_id',
            label: 'provider',
            disabled: false,
            isRequired: true,
            renderType: 'select',
            options: iot_provider,
          },
        ]}
        operType={2}
      />
    </>
  );
};

export default ForbidProvider;
