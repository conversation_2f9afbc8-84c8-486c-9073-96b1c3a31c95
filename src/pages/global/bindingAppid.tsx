import React, { useState, useEffect } from 'react';
import { getGloablProviders } from '@/services/api';
import {
  getAvailableProvider,
  getBindingProvider,
  addBindingProvider,
  delBindingProvider,
  editBindingProvider,
} from '@/services/thrdAPI';
import PatternTable from '@/pages/component/PatternLayout';

function handleSuccess(res: any) {
  const providersList = (res?.data ?? []).map(
    (item: { provider_id: number; provider_name: string }) => {
      return {
        value: item.provider_id,
        label: item.provider_name,
      };
    },
  );
  return providersList;
}

const BindingAppid = () => {
  const [providers, setProviders] = useState([]);
  const [unAssignProviders, setUnProviders] = useState([]);

  useEffect(() => {
    getAvailableProvider().then((res) => {
      const result = handleSuccess(res);
      setUnProviders(result);
    });
    getGloablProviders().then((res) => {
      const result = handleSuccess(res);
      setProviders(result);
    });
  }, []);

  const columns: any = [
    {
      title: 'provider_id',
      dataIndex: 'provider_id',
      key: 'provider_id',
      align: 'center',
    },
    {
      title: 'provider_name',
      dataIndex: 'provider_name',
      key: 'provider_name',
      align: 'center',
    },
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: '添加时间',
      dataIndex: 'created_time',
      key: 'created_time',
      align: 'center',
    },
    {
      title: '添加人',
      dataIndex: 'created_rtx',
      key: 'created_rtx',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'provider_id',
      label: 'provider',
      disabled: true,
      isRequired: true,
      renderType: 'select',
      options: unAssignProviders,
    },
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: false,
      isRequired: true,
    },
  ];

  async function doEdit(vals: any) {
    return await editBindingProvider({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addBindingProvider({ ...vals });
  }

  async function doDel(vals: any) {
    return await delBindingProvider({ appid: vals.appid, provider_id: vals.provider_id });
  }
  async function getList(vals?: any) {
    return await getBindingProvider({ ...vals });
  }

  return (
    <PatternTable
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        {
          label: 'appid',
          name: 'appid',
          renderType: 'number',
        },
        {
          label: 'provider_id',
          name: 'provider_id',
          renderType: 'select',
          options: providers,
          placeholder: '请选择供应商',
        },
      ]}
      operateForm={operateForm}
      operType={1}
    />
  );
};

export default BindingAppid;
