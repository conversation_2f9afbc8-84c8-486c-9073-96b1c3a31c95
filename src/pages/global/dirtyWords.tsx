import React from 'react';
import { getDirtyWords, addDirtyWord } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const columns: any = [
  {
    title: 'id',
    dataIndex: 'id',
    key: 'id',
    align: 'center',
  },
  {
    title: '脏字',
    dataIndex: 'dirty_word',
    key: 'dirty_word',
    align: 'center',
  },
];

const operateForm = [
  {
    showOnAdd: true,
    name: 'dirty_word',
    label: '脏字',
    disabled: false,
    isRequired: true,
  },
];

export default function DirtyWords() {
  async function doAdd(vals: any) {
    return await addDirtyWord({ ...vals });
  }
  async function getList(vals: any) {
    return await getDirtyWords({ ...vals });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'dirty_word',
          placeholder: '脏字',
        },
      ]}
      operateForm={operateForm}
      operType={3}
    />
  );
}
