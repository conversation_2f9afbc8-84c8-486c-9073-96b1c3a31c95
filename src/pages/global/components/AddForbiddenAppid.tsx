import React, { useState } from 'react';
import type { DialogRef } from '@/utils/react-use/useDialog';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, Input, message, DatePicker, Select } from 'antd';
import { addForbiddenAppid } from '@/services/scdAPI';
import dayjs from 'dayjs';
import _ from 'lodash';
import { options } from '../forbiddenAppid';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export const AddForbiddenAppid = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [form] = Form.useForm();
  const [visible, setShowState, defaultVal] = useDialog<{
    smsType: string;
    initValues: any;
  }>(dialogRef);
  const { initValues } = defaultVal;
  const [isLoading, setLoading] = useState<boolean>(false);

  const _handlerSubmit = async (vals: any) => {
    try {
      setLoading(true);
      const res = await addForbiddenAppid(
        _.pickBy(
          {
            ...vals,
            forbidden_endtime: vals?.forbidden_endtime
              ? dayjs(vals.forbidden_endtime).format('YYYY-MM-DD HH:mm:ss')
              : '',
          },
          (v) => v !== '',
        ),
      );
      if (res?.code === 0) {
        message.success('添加成功');
        onSuccess();
      }
      setShowState(false);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setShowState(false);
    }
  };

  return (
    <Modal
      open={visible}
      confirmLoading={isLoading}
      onOk={() => form.submit()}
      onCancel={() => {
        setShowState(false);
        form.resetFields();
      }}
    >
      <Form
        form={form}
        labelAlign="right"
        initialValues={initValues}
        onFinish={(vals) => _handlerSubmit(vals)}
        style={{ maxHeight: 500, overflow: 'auto' }}
      >
        {(fields) => {
          return (
            <>
              <Form.Item name="appid" label="appid" rules={[{ required: true }]}>
                <Input placeholder="请输入" style={{ width: 200 }} />
              </Form.Item>
              <Form.Item name="forbidden_type" label="封禁类型" rules={[{ required: true }]}>
                <Select options={options} placeholder="请输入" style={{ width: 200 }} />
              </Form.Item>
              {fields?.forbidden_type === 3 && (
                <Form.Item name="forbidden_endtime" label="封禁结束时间">
                  <DatePicker
                    placeholder="请选择时间"
                    style={{ width: 200 }}
                    format="YYYY-MM-DD HH:mm:ss"
                  />
                </Form.Item>
              )}
              <Form.Item name="remark" label="备注" rules={[{ required: true }]}>
                <Input.TextArea placeholder="请输入" style={{ width: 200 }} />
              </Form.Item>
            </>
          );
        }}
      </Form>
    </Modal>
  );
};
