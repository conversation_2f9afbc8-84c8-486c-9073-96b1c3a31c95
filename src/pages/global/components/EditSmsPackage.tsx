import type { DialogRef } from '@/utils/react-use/useDialog';
import React, { useEffect, useState } from 'react';
import { useDialog } from '@/utils/react-use/useDialog';
import { Modal, Form, Input, message, DatePicker, Tooltip, InputNumber } from 'antd';
import { editAbroadPackage, editSmsPackage } from '@/services/scdAPI';
import dayjs from 'dayjs';
import _ from 'lodash';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export const EditSmsPackage = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [form] = Form.useForm();
  const [visible, setShowState, defaultVal] = useDialog<{
    smsType: string;
    initValues: any;
  }>(dialogRef);
  const { smsType, initValues } = defaultVal;
  const [isConfirmLoading, setConfirmLoading] = useState<boolean>(false);
  const [changedFields, setChangedField] = useState<string[]>(['package_ids']);

  const _handlerSubmit = async (_vals: any) => {
    const vals = _.pickBy(_vals, (v, index) => !_.isNil(v) && changedFields.includes(index));
    if (changedFields.length === 1 && changedFields[0] === 'package_ids') {
      message.info('与原始数据一致');
      return;
    }
    try {
      setConfirmLoading(true);
      let res;
      if (vals?.to_time) {
        vals.to_time = dayjs(vals?.to_time).format('YYYY-MM-DD HH:mm:ss');
      }
      if (smsType === 'abroad') {
        res = await editAbroadPackage({ ...vals });
      } else {
        res = await editSmsPackage({ ...vals });
      }
      if (res?.code === 0) {
        message.success('编辑成功');
        onSuccess();
      }
      setShowState(false);
      setConfirmLoading(false);
    } catch (error) {
      setConfirmLoading(false);
      setShowState(false);
    }
  };

  useEffect(() => {
    if (!visible) {
      form.resetFields();
      setChangedField(['package_ids']);
      return;
    }
    form.setFieldsValue({
      ...initValues,
      to_time: initValues?.to_time ? dayjs(initValues?.to_time) : undefined,
    });
  }, [form, initValues, visible]);

  return (
    <Modal
      open={visible}
      confirmLoading={isConfirmLoading}
      onOk={() => form.submit()}
      onCancel={() => {
        setShowState(false);
        form.resetFields();
      }}
    >
      <Form
        form={form}
        labelAlign="right"
        onFinish={(vals) => _handlerSubmit(vals)}
        style={{ maxHeight: 500, overflow: 'auto' }}
        onFieldsChange={(changedFields) => {
          if (changedFields?.[0]?.name?.[0]) {
            setChangedField((prev: string[]) => {
              const n: string[] = prev.concat(changedFields?.[0]?.name?.[0]);
              return [...new Set(n)];
            });
          }
        }}
      >
        <Tooltip title={`${initValues?.package_ids}`}>
          <Form.Item name="package_ids" label="套餐包ID" rules={[{ required: true }]}>
            <Input placeholder="请输入" style={{ width: 200 }} disabled />
          </Form.Item>
        </Tooltip>
        <Form.Item
          name="appid"
          label="appid"
          extra="套餐包要更换拥有者就将此appid换成新账号的腾讯云appid"
        >
          <InputNumber
            placeholder="请输入"
            style={{ width: 200 }}
            disabled={initValues?.type === 1}
          />
        </Form.Item>
        <Form.Item name="amount" label="amount">
          <InputNumber
            placeholder="请输入"
            style={{ width: 200 }}
            disabled={initValues?.type === 1}
          />
        </Form.Item>
        <Form.Item name="to_time" label="to_time">
          <DatePicker
            placeholder="请选择时间"
            style={{ width: 200 }}
            format="YYYY-MM-DD HH:mm:ss"
            showTime
          />
        </Form.Item>
        <Form.Item name="pkg_name" label="pkg_name">
          <Input placeholder="请输入" style={{ width: 200 }} disabled={initValues?.type === 1} />
        </Form.Item>
      </Form>
    </Modal>
  );
};
