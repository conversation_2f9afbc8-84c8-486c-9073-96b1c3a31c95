import React, { useState, useEffect } from 'react';
import { Select } from 'antd';
import { getDefaultWeight, addDefaultWeight, editDefaultWeight } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';
import { priorityStatus } from '../sign/const';
import { getGloablProviders } from '@/services/api';

enum FlagType {
  '默认',
  '新客户',
}

const typeOptions = [
  {
    value: '',
    label: '全部',
  },
  {
    value: 0,
    label: '默认',
  },
  {
    value: 1,
    label: '新客户',
  },
];

const DefaultWeight = () => {
  const [providers, setProviders] = useState<any>([]);

  const columns: any = [
    {
      title: 'provider',
      dataIndex: 'provider_name',
      key: 'provider_name',
      align: 'center',
    },
    {
      title: '分组',
      dataIndex: 'group_id',
      key: 'group_id',
      align: 'center',
      render: (group_id: number) => {
        return <span>{FlagType[group_id]}</span>;
      },
    },
    {
      title: '单发普通',
      dataIndex: 'single',
      key: 'single',
      align: 'center',
    },
    {
      title: '单发营销',
      dataIndex: 'single_b',
      key: 'single_b',
      align: 'center',
    },
    {
      title: '群发2普通',
      dataIndex: 'multi2',
      key: 'multi2',
      align: 'center',
    },
    {
      title: '群发2营销',
      dataIndex: 'multi2_b',
      key: 'multi2_b',
      align: 'center',
    },
    {
      title: '优先级',
      dataIndex: 'hot_backup',
      key: 'hot_backup',
      align: 'center',
      render: (hot_backup: number) => {
        return (
          // eslint-disable-next-line no-nested-ternary
          <span>{hot_backup === 0 ? '0级(低)' : hot_backup === 1 ? '1级(中)' : '2级(高)'}</span>
        );
      },
    },
  ];

  useEffect(() => {
    getGloablProviders().then((res) => {
      let providersList = (res?.data ?? []).map(
        (item: { provider_id: number; provider_name: string }) => {
          return {
            value: item.provider_id,
            label: item.provider_name,
          };
        },
      );
      providersList = providersList.filter((item: { label: string; value: number }) =>
        /自定义|报备/.test(item.label),
      );
      setProviders(providersList);
    });
  }, []);

  const operateForm = [
    {
      showOnAdd: true,
      name: 'provider_id',
      label: 'provider',
      disabled: true,
      isRequired: true,
      renderType: 'select',
      options: providers,
    },
    {
      showOnAdd: true,
      name: 'group_id',
      label: '分组',
      disabled: false,
      isRequired: true,
      renderType: 'select',
      options: typeOptions.slice(1),
      plceholder: '请选择分组',
    },
    {
      showOnAdd: true,
      name: 'single',
      label: '单发普通',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'single_b',
      label: '单发营销',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'multi2',
      label: '群发2普通',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'multi2_b',
      label: '群发2营销',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'hot_backup',
      label: '优先级',
      disabled: false,
      isRequired: true,
      render: () => {
        return (
          <Select style={{ width: 120 }}>
            {priorityStatus.map((item) => {
              return (
                <Select.Option value={item.value} key={item.value}>
                  {item.text}
                </Select.Option>
              );
            })}
          </Select>
        );
      },
    },
  ];

  async function doEdit(vals: any) {
    return await editDefaultWeight({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addDefaultWeight({ ...vals });
  }
  async function getList(vals?: any) {
    return await getDefaultWeight({ ...vals });
  }

  return (
    <PatternTable
      modalTipRender={() => (
        <div style={{ color: 'red', marginBottom: '15px' }}>
          添加的供应商必须是自定义签名通道（配置接口权重时需要检查接口能力）
        </div>
      )}
      title={'全局默认调度供应商权重信息'}
      rowKey="provider_id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'provider_id',
          renderType: 'select',
          options: providers,
          placeholder: 'provider',
        },
        {
          label: '',
          name: 'group_id',
          renderType: 'select',
          options: typeOptions,
          placeholder: '分组',
        },
      ]}
      operateForm={operateForm}
      operType={0}
    />
  );
};

export default DefaultWeight;
