import React from 'react';
import PatternTable from '@/pages/component/PatternLayout';
import {
  addPackageConfig,
  delPackageConfig,
  getPackageConfig,
  updatePackageConfig,
} from '@/services/packageConfig';

enum FlagType {
  '按发送成功抵扣',
  '按送达成功抵扣',
}

const typeOpts = [
  { value: 0, label: '按发送成功抵扣' },
  { value: 1, label: '按送达成功抵扣' },
];

const columns: any = [
  {
    title: 'qappid',
    dataIndex: 'qappid',
    key: 'qappid',
    align: 'center',
  },
  {
    title: '抵扣类型',
    dataIndex: 'type',
    key: 'type',
    align: 'center',
    render: (type: number) => FlagType[type],
  },
  {
    title: '操作人名称',
    dataIndex: 'rtx',
    key: 'rtx',
    align: 'center',
  },
  {
    title: '创建日期',
    dataIndex: 'created_at',
    key: 'created_at',
    align: 'center',
  },
  {
    title: '生效日期',
    dataIndex: 'started_at',
    key: 'started_at',
    align: 'center',
  },
  {
    title: '上次修改日期',
    dataIndex: 'updated_at',
    key: 'updated_at',
    align: 'center',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    align: 'center',
  },
];

const operateForm = [
  {
    showOnAdd: true,
    name: 'qappid',
    label: 'qappid',
    disabled: true,
    isRequired: true,
  },
  {
    showOnAdd: true,
    name: 'type',
    label: '抵扣类型',
    isRequired: true,
    renderType: 'select',
    options: typeOpts,
  },
  {
    showOnAdd: true,
    name: 'remark',
    label: '备注',
    placeholder: '备注',
  },
];

const PackageConfig = () => {
  async function doEdit(_: any, newVals: any) {
    return await updatePackageConfig({ ...newVals });
  }
  async function doAdd(vals: any) {
    return await addPackageConfig({ ...vals });
  }
  async function getList(vals?: any) {
    return await getPackageConfig({ ...vals });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={async (row) => {
        return await delPackageConfig({ qappid: row?.qappid });
      }}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'qappid',
        },
        {
          label: '',
          name: 'executor',
          placeholder: '操作人名称',
        },
        {
          label: '',
          name: 'type',
          placeholder: '请选择抵扣类型',
          renderType: 'select',
          options: typeOpts,
        },
      ]}
      operateForm={operateForm}
      operType={1}
      initialValues={{ type: 1 }}
    />
  );
};

export default PackageConfig;
