import React from 'react';
import { getSendTask, addSendTask, delSendTask } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const SendTaskAudit = () => {
  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (type: number) => {
        return <span> {type === 0 ? '免审核' : '强制审核'}</span>;
      },
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'type',
      label: '类型',
      disabled: true,
      isRequired: true,
      renderType: 'select',
      options: [
        { value: 0, label: '免审核' },
        { value: 1, label: '强制审核' },
      ],
    },
  ];

  async function doDel(vals: any) {
    return await delSendTask({ appid: vals.appid });
  }
  async function doAdd(vals: any) {
    return await addSendTask({ ...vals });
  }
  async function getList(vals?: any) {
    return await getSendTask({ ...vals });
  }

  return (
    <PatternTable
      rowKey="appid"
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'appid',
        },
      ]}
      operateForm={operateForm}
      initialValues={{ type: 1 }}
      operType={2}
    />
  );
};

export default SendTaskAudit;
