import React from 'react';
import { getIgnoreLog, addIgnoreLog, delIgnoreLog } from '@/services/thrdAPI';
import { Input } from 'antd';
import PatternTable from '@/pages/component/PatternLayout';
import _ from 'lodash';

const { TextArea } = Input;

const IgnoreLog = () => {
  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'app_name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appids',
      label: 'appid',
      disabled: false,
      isRequired: true,
      render: () => (
        <TextArea
          rows={4}
          style={{ width: 350 }}
          placeholder="请输入应用id，可使用空格或换行分隔多个"
        />
      ),
      appendRules: [
        {
          validator: (rule: any, value: string, callback: () => {}) => {
            validateApps(value, callback);
          },
        },
      ],
    },
  ];

  function validateApps(value: string, callback: any) {
    if (!value) {
      callback();
      return;
    }
    const appArr = value
      .trim()
      .replace(/[\s\t\n]+/g, ',')
      .split(',');
    const isValid = _.every(appArr, (app: string) => {
      return app && /^[0-9]*$/.test(app);
    });
    isValid ? callback() : callback('应用id格式不正确，请重新输入');
  }

  async function doAdd(vals: any) {
    return await addIgnoreLog({
      appids: vals.appids
        .trim()
        .replace(/[\s\t\n]+/g, ',')
        .split(','),
    });
  }

  async function doDel(vals: any) {
    return await delIgnoreLog({ appids: [vals.appid] });
  }
  async function getList(vals?: any) {
    return await getIgnoreLog({ ...vals });
  }

  return (
    <PatternTable
      getFn={getList}
      addFn={doAdd}
      delFn={doDel}
      columns={columns}
      searchKeys={[]}
      operateForm={operateForm}
      operType={2}
    />
  );
};

export default IgnoreLog;
