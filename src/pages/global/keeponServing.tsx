import React from 'react';
import { getKeeponServing, addKeeponServing, delKeeponServing } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const KeeponServing = () => {
  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '时间',
      dataIndex: 'modify_time',
      key: 'modify_time',
      align: 'center',
    },
    {
      title: '添加人',
      dataIndex: 'rtx',
      key: 'rtx',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: false,
      isRequired: true,
    },
  ];

  async function doDel(vals: any) {
    return await delKeeponServing({ appid: vals.appid });
  }
  async function doAdd(vals: any) {
    return await addKeeponServing({ ...vals });
  }
  async function getList(vals?: any) {
    return await getKeeponServing({ ...vals });
  }

  return (
    <PatternTable
      rowKey="appid"
      getFn={getList}
      addFn={doAdd}
      upRender={() => {
        return (
          <>
            <p>添加进去腾讯云appid的同时会让已停服的业务恢复服务，并且会阻止计费侧再次的停服请求</p>
            <p>{'删除腾讯云appid只是取消"阻止计费侧停服请求"的功能'}</p>
          </>
        );
      }}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'appid',
        },
      ]}
      operateForm={operateForm}
      operType={2}
    />
  );
};

export default KeeponServing;
