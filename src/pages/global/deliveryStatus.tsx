import React from 'react';
import { getDeliveryStatus, addDeliveryStatus, editDeliveryStatus } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';
import _ from 'lodash';
enum ErrType {
  '发送成功',
  '责任方为用户',
  '责任方为厂商',
  '责任方为运营商',
  '责任方不明' = 99,
}

const options = [
  { label: '发送成功', value: 0 },
  { label: '责任方为用户', value: 1 },
  { label: '责任方为厂商', value: 2 },
  { label: '责任方为运营商', value: 3 },
  { label: '责任方不明', value: 99 },
];

export const stateTypes = [
  { label: '送达成功', value: 0 },
  { label: '运营商内部错误', value: 1 },
  { label: '异常号码', value: 2 },
  { label: '通道拦截', value: 3 },
  { label: '携号转网', value: 4 },
  { label: '黑名单', value: 5 },
  { label: '未分类', value: 100 },
];

const DeliveryStatus = () => {
  const columns: any = [
    {
      title: 'code',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
    },
    {
      title: 'msg',
      dataIndex: 'msg',
      key: 'msg',
      align: 'center',
    },
    {
      title: 'type',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
    },
    {
      title: 'solution',
      dataIndex: 'solution',
      key: 'solution',
      align: 'center',
    },
    {
      title: '状态码类型',
      dataIndex: 'state_type',
      key: 'state_type',
      align: 'center',
      render: (state_type: number) => _.find(stateTypes, (v) => v.value === state_type)?.label,
    },
    {
      title: '错误归属类型',
      dataIndex: 'err_type',
      key: 'err_type',
      align: 'center',
      render: (err_type: number) => {
        return <span>{ErrType[err_type]}</span>;
      },
    },
  ];

  const operateForm = [
    {
      showOnAdd: true,
      name: 'code',
      label: 'code',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'msg',
      label: 'msg',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'type',
      label: 'type',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'solution',
      label: 'solution',
      disabled: false,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'state_type',
      label: '状态码类型',
      disabled: false,
      isRequired: true,
      options: stateTypes,
      renderType: 'select',
    },
    {
      showOnAdd: true,
      name: 'err_type',
      label: '错误归属类',
      disabled: false,
      isRequired: true,
      options,
      renderType: 'select',
    },
  ];

  async function doEdit(vals: any) {
    return await editDeliveryStatus({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addDeliveryStatus({ ...vals });
  }
  async function getList(vals?: any) {
    return await getDeliveryStatus({ ...vals });
  }

  return (
    <PatternTable
      rowKey="code"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: '',
          name: 'code',
        },
        {
          label: '',
          name: 'state_type',
          placeholder: '请选择',
          renderType: 'select',
          options: stateTypes,
        },
      ]}
      operateForm={operateForm}
      operType={0}
    />
  );
};

export default DeliveryStatus;
