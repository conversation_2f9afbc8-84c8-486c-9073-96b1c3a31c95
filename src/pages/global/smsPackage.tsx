import React, { useMemo, useState } from 'react';
import { Form, Input, message, Space, Table, Tag, Select, Button, Modal } from 'antd';
import { useSetState } from 'react-use';
import useAsyncRetryFunc from '@/utils/react-use/useAsyncFunc';
import {
  delAbroadPackage,
  delSmsPackage,
  getAbroadPackage,
  getSmsPackage,
} from '@/services/scdAPI';
import { EditSmsPackage } from './components/EditSmsPackage';
import { useDialogRef } from '@/utils/react-use/useDialog';
import { SearchOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import _ from 'lodash';

export type FormType = {
  package_id?: string;
  qappid?: string;
  sdkappid?: string;
  tranid?: string;
  type?: string;
  deleted?: string;
};

const SmsPackage = () => {
  const smsType = location.pathname === '/global/abroadPackage' ? 'abroad' : '';
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [searchKeys, setSearchKeys] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    let res;
    const keys = _.pickBy(searchKeys, (v: number | string) => v !== '');
    if (smsType === 'abroad') {
      res = await getAbroadPackage({ ...keys });
    } else {
      res = await getSmsPackage({ ...keys });
    }
    return res?.data || {};
  }, [searchKeys, smsType]);
  const dialogRef = useDialogRef();

  const packageList = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const _handlerSubmit = async (formValue: FormType) => {
    setSearchKeys({
      ...formValue,
      page_index: 1,
    });
  };

  async function deletePackage(package_ids: (string | number)[]) {
    Modal.confirm({
      title: '确认删除？',
      content: `确认删除${package_ids?.join(',')}?`,
      onOk: async (close) => {
        try {
          let res;
          if (smsType === 'abroad') {
            res = await delAbroadPackage({ package_ids });
          } else {
            res = await delSmsPackage({ package_ids });
          }
          close();
          if (res.code === 0) {
            message.success('删除成功');
            retry();
          }
        } catch (error) {}
      },
    });
  }

  return (
    <PageContainer>
      <Space style={{ marginBottom: 10 }}>
        <Button
          type="primary"
          disabled={!selectedRowKeys.length}
          onClick={() => {
            dialogRef.current.open({
              onSuccess: retry,
              smsType,
              initValues: { package_ids: selectedRowKeys },
            });
          }}
        >
          批量编辑
        </Button>
        <Button
          type="primary"
          disabled={!selectedRowKeys.length}
          onClick={() => {
            deletePackage(selectedRowKeys);
          }}
        >
          批量删除
        </Button>
      </Space>
      <div>
        <Form
          form={form}
          className="sender-search-form"
          layout="inline"
          labelAlign="right"
          onFinish={(vals) => _handlerSubmit(vals)}
          style={{ maxHeight: 500, overflow: 'auto' }}
        >
          <Form.Item name="package_id" label="套餐包ID">
            <Input placeholder="请输入" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="qappid" label="腾讯云appid">
            <Input placeholder="请输入" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="sdkappid" label="sdkappid">
            <Input placeholder="请输入" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="tranid" label="订单ID">
            <Input placeholder="请输入" style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name="type" label="类型">
            <Select
              style={{ width: 150 }}
              placeholder="请选择"
              options={[
                { value: 0, label: '赠送' },
                { value: 1, label: '购买' },
              ]}
              showSearch
              allowClear
            />
          </Form.Item>
          <Form.Item name="deleted" label="状态">
            <Select
              style={{ width: 150 }}
              placeholder="请选择"
              options={[
                { value: 0, label: '存在' },
                { value: 1, label: '已删除' },
              ]}
              showSearch
              allowClear
            />
          </Form.Item>
          <Form.Item>
            <Button htmlType="submit" type="primary" loading={loading} icon={<SearchOutlined />}>
              查询
            </Button>
          </Form.Item>
        </Form>
      </div>
      <Table
        dataSource={packageList}
        columns={[
          {
            title: '套餐包id',
            dataIndex: 'package_id',
            key: 'package_id',
            align: 'center',
          },
          {
            title: 'appid',
            dataIndex: 'appid',
            key: 'appid',
            align: 'center',
          },
          {
            title: '创建时间',
            dataIndex: 'create_time',
            key: 'create_time',
            align: 'center',
          },
          {
            title: '开始时间',
            dataIndex: 'from_time',
            key: 'from_time',
            align: 'center',
          },
          {
            title: '结束时间',
            dataIndex: 'to_time',
            key: 'to_time',
            align: 'center',
          },
          {
            title: '价格（元）',
            dataIndex: 'realpay',
            key: 'realpay',
            align: 'center',
          },
          {
            title: '类型',
            dataIndex: 'type',
            key: 'type',
            align: 'center',
            render: (type: number) => <span>{type === 1 ? '购买' : '赠送'}</span>,
          },
          {
            title: '名称',
            dataIndex: 'pkg_name',
            key: 'pkg_name',
            align: 'center',
          },
          {
            title: '订单号',
            dataIndex: 'tranid',
            key: 'tranid',
            align: 'center',
          },
          {
            title: '包大小',
            dataIndex: 'amount',
            key: 'amount',
            align: 'center',
          },
          {
            title: '使用量',
            dataIndex: 'used',
            key: 'used',
            align: 'center',
          },
          {
            title: '操作',
            render: (row) => {
              if (row.deleted) {
                return <Tag>已删除</Tag>;
              }
              return (
                <Space>
                  <a
                    onClick={() => {
                      dialogRef.current.open({
                        onSuccess: retry,
                        smsType,
                        initValues: { ...row, package_ids: [row.package_id] },
                      });
                    }}
                  >
                    编辑
                  </a>
                  <a
                    onClick={() => {
                      deletePackage([row.package_id]);
                    }}
                  >
                    删除
                  </a>
                </Space>
              );
            },
          },
        ]}
        loading={loading}
        rowKey="package_id"
        pagination={{
          current: searchKeys.page_index,
          total,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setSearchKeys({ page_size }),
          onChange: (page) => {
            setSearchKeys({ page_index: page });
          },
        }}
        rowSelection={{
          type: 'checkbox',
          onChange: (selectedRowKeys: React.Key[]) => {
            setSelectedRowKeys(selectedRowKeys);
          },
          getCheckboxProps: (record) => ({
            disabled: record.deleted,
          }),
        }}
        style={{ marginTop: 20 }}
      />
      <EditSmsPackage dialogRef={dialogRef} onSuccess={retry} />
    </PageContainer>
  );
};
export default SmsPackage;
