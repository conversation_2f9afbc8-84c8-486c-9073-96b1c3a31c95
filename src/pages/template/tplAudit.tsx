import React, { useRef } from 'react';
import { findAuditTpl, editTplStatus } from '@/services/scdAPI';
import { InputNumber, message } from 'antd';
import { PageContainer } from '@ant-design/pro-layout';
import type { ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import _ from 'lodash';

const TplAudit = () => {
  const ref = useRef<ActionType>();

  async function editStatus(data: any) {
    const res = await editTplStatus({ ...data });
    if (res?.code === 0) {
      message.success('编辑成功');
      ref?.current?.reload();
    }
  }

  return (
    <PageContainer>
      <ProTable
        onRequestError={() => {
          message.error('查询失败');
        }}
        actionRef={ref}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          optionRender: (row, config, defaultDom) => {
            return [defaultDom[1]];
          },
          collapseRender: false,
        }}
        options={false}
        columns={[
          {
            title: '申请单ID',
            key: 'id',
            dataIndex: 'id',
            align: 'center',
            search: false,
            editable: false,
          },
          {
            title: '模板ID',
            key: 'template_id',
            align: 'center',
            dataIndex: 'template_id',
            editable: false,
            renderFormItem: () => (
              <InputNumber style={{ width: 180 }} placeholder="输入模板ID开始查询" />
            ),
          },
          {
            title: '模板内容',
            key: 'template',
            align: 'center',
            dataIndex: 'template',
            search: false,
            editable: false,
          },
          {
            title: '申请时间',
            key: 'apply_time',
            align: 'center',
            dataIndex: 'apply_time',
            search: false,
            editable: false,
          },
          {
            title: '审批时间',
            key: 'reply_time',
            align: 'center',
            dataIndex: 'reply_time',
            search: false,
            editable: false,
          },
          {
            title: '状态',
            key: 'status',
            align: 'center',
            valueType: 'select',
            dataIndex: 'status',
            search: false,
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
            fieldProps: {
              options: [
                {
                  label: '待审核',
                  value: 0,
                },
                {
                  label: '已通过',
                  value: 1,
                },
                {
                  label: '已拒绝',
                  value: 2,
                },
              ],
            },
          },
          {
            title: '操作',
            valueType: 'option',
            align: 'center',
            render: (text, record, _, action) => [
              <a
                key="editable"
                onClick={() => {
                  action?.startEditable?.(record.id);
                }}
              >
                编辑
              </a>,
            ],
          },
        ]}
        style={{ padding: 0 }}
        request={async (params: { template_id?: string }) => {
          if (!params.template_id) return { data: [], success: true };
          return await findAuditTpl({ template_id: params.template_id });
        }}
        editable={{
          type: 'single',
          actionRender: (row, config, defaultDom) => [defaultDom.save, defaultDom.cancel],
          onSave: async (rowKey, data) => {
            await editStatus({
              ..._.pick(data, ['id', 'template_id', 'appid']),
              operate: data.status,
            });
          },
        }}
        pagination={false}
      />
    </PageContainer>
  );
};
export default TplAudit;
