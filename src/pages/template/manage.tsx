import React from 'react';
import PatternTable from '@/pages/component/PatternLayout';
import { addTplList, editTplList, getTplList } from '@/services/scdAPI';
import { DatePicker, Input } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import { findLabelWithType } from '@/utils/utils';
import { BaseType } from 'antd/es/typography/Base';

const { RangePicker } = DatePicker;
const statusOptions: { value: any; label: string; type: BaseType }[] = [
  { label: '待审核', value: 1, type: 'warning' },
  { label: '已通过', value: 0, type: 'success' },
  { label: '已拒绝', value: 2, type: 'danger' },
  { label: '已删除', value: 3, type: 'secondary' },
];

interface ParamsType {
  pageIndex: number;
  pageSize: number;
  [key: string]: any;
}

const typeOpts = [
  {
    value: 0,
    label: '普通短信',
  },
  {
    value: 1,
    label: '营销短信',
  },
];
const { TextArea } = Input;

const TplManage = () => {
  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'IP地址',
      dataIndex: 'client_ip',
      key: 'client_ip',
      align: 'center',
    },
    {
      title: 'type',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (type: number) => {
        return <span>{type === 0 ? '普通短信' : '营销短信'}</span>;
      },
    },
    {
      title: '频率规则',
      dataIndex: 'frq_rule',
      key: 'frq_rule',
      align: 'center',
    },
    {
      title: '单手机频率规则',
      width: 100,
      dataIndex: 'frq_mobile_rule',
      key: 'frq_mobile_rule',
      align: 'center',
    },
    {
      title: '模版',
      dataIndex: 'template',
      key: 'template',
      align: 'center',
      width: '400px',
    },
    {
      title: 'status',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status: number) => findLabelWithType(statusOptions, status),
    },
  ];

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'type',
      label: 'type',
      isRequired: true,
      disabled: false,
      renderType: 'select',
      options: typeOpts,
    },
    {
      showOnAdd: false,
      name: 'frq_rule',
      label: '频率规则',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: false,
      name: 'frq_mobile_rule',
      label: '单手机频率规则',
      disabled: false,
      isRequired: true,
      renderType: 'number',
    },
    {
      showOnAdd: true,
      name: 'template',
      label: '模板',
      disabled: false,
      isRequired: true,
      render: () => <TextArea placeholder="模板" />,
    },
  ];

  async function doEdit(vals: any) {
    return await editTplList({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addTplList({ ...vals });
  }
  async function getList(vals?: Partial<ParamsType>) {
    const params: any = _.cloneDeep(vals);
    if (vals?.time) {
      params.from_time = dayjs(params.time[0]).format('YYYY-MM-DD HH:mm:ss');
      params.to_time = dayjs(params.time[1]).format('YYYY-MM-DD HH:mm:ss');
    }
    return await getTplList({ ..._.omit(params, 'time') });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      columns={columns}
      searchKeys={[
        {
          label: 'appid',
          name: 'appid',
        },
        {
          label: 'template',
          name: 'template',
        },
        {
          label: 'regexp',
          name: 'regexp',
        },
        {
          label: 'IP地址',
          name: 'ip',
        },
        {
          label: '',
          name: 'time',
          render: () => (
            <RangePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              placeholder={['默认查询最近一个月数据', '结束时间']}
              presets={[
                { label: 'Today', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
                {
                  label: 'Yesterday',
                  value: [
                    dayjs().subtract(1, 'days').startOf('day'),
                    dayjs().subtract(1, 'days').endOf('day'),
                  ],
                },
                {
                  label: 'Week',
                  value: [dayjs().subtract(7, 'days').startOf('day'), dayjs().endOf('day')],
                },
                {
                  label: 'Month',
                  value: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')],
                },
              ]}
            />
          ),
        },
      ]}
      operateForm={operateForm}
      operType={0}
      initialValues={{
        type: 0,
      }}
      searchInitials={{ time: [dayjs().subtract(1, 'month').startOf('day'), dayjs().endOf('day')] }}
    />
  );
};

export default TplManage;
