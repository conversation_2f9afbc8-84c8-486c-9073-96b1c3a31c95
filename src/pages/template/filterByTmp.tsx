import React from 'react';
import { Input } from 'antd';
import { getFilterList, editFilterList, addFilterList, delFilterList } from '@/services/scdAPI';
import PatternTable from '@/pages/component/PatternLayout';

const { TextArea } = Input;

const FilterByTmp = () => {
  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'app_name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: '模板',
      dataIndex: 'template',
      key: 'template',
      align: 'center',
    },
  ];

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'template',
      label: '模板',
      disabled: false,
      isRequired: true,
      render: () => <TextArea placeholder="模板" />,
    },
  ];

  async function doEdit(vals: any) {
    return await editFilterList({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addFilterList({ ...vals });
  }
  async function doDel(vals: any) {
    return await delFilterList({ id: vals.id, appid: vals.appid });
  }
  async function getList(vals: any) {
    return await getFilterList({ ...vals });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      columns={columns}
      searchKeys={[
        {
          label: 'appid',
          name: 'appid',
        },
      ]}
      operateForm={operateForm}
      operType={1}
    />
  );
};

export default FilterByTmp;
