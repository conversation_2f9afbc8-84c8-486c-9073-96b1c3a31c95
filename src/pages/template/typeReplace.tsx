import React from 'react';
import { getReplaceList, editReplaceList, addReplaceList, delReplaceList } from '@/services/scdAPI';
import { Input } from 'antd';
import PatternTable from '@/pages/component/PatternLayout';

const { TextArea } = Input;
const typeOpts = [
  {
    value: 0,
    label: '通知类',
  },
  {
    value: 1,
    label: '营销类',
  },
];

const TypeReplace = () => {
  const columns: any = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
    },
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: 'app_name',
      dataIndex: 'app_name',
      key: 'app_name',
      align: 'center',
    },
    {
      title: '模板',
      dataIndex: 'template',
      key: 'template',
      align: 'center',
    },
    {
      title: '替换到类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      render: (type: number) => {
        return <span>{type === 0 ? '通知类' : '营销类'}</span>;
      },
    },
  ];

  const operateForm = [
    {
      showOnAdd: false,
      name: 'id',
      label: 'id',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'appid',
      label: 'appid',
      disabled: true,
      isRequired: true,
    },
    {
      showOnAdd: true,
      name: 'type',
      label: 'type',
      isRequired: true,
      disabled: false,
      renderType: 'select',
      options: typeOpts,
    },
    {
      showOnAdd: true,
      name: 'template',
      label: '模板',
      disabled: false,
      isRequired: true,
      render: () => <TextArea placeholder="模板" />,
    },
  ];

  async function doEdit(vals: any) {
    return await editReplaceList({ ...vals });
  }
  async function doAdd(vals: any) {
    return await addReplaceList({ ...vals });
  }
  async function doDel(vals: any) {
    return await delReplaceList({ id: vals.id, appid: vals.appid });
  }
  async function getList(vals: any) {
    return await getReplaceList({ ...vals });
  }

  return (
    <PatternTable
      rowKey="id"
      getFn={getList}
      addFn={doAdd}
      editFn={doEdit}
      delFn={doDel}
      initialValues={{ type: 0 }}
      columns={columns}
      searchKeys={[
        {
          label: 'appid',
          name: 'appid',
        },
      ]}
      operateForm={operateForm}
      operType={1}
    />
  );
};

export default TypeReplace;
