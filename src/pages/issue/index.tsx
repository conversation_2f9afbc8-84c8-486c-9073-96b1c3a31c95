import React from 'react';
import { But<PERSON>, Modal, Popover, Select, Table, Typography } from 'antd';
import { ProTable } from '@ant-design/pro-components';
import { getIssueList } from '@/services/issue';
import { findLabel } from '@/utils/utils';
import { isMobile } from '@/const/jadgeUserAgent';
import _ from 'lodash';
const { Text } = Typography;

interface ReplyTxt {
  time: string;
  content: string;
  provider_name: string;
  provider_id: number;
}

const statusOptions = [
  { value: 0, label: '待提交' },
  { value: 1, label: '已提交' },
  { value: 2, label: '供应商已回复' },
];

const malfunctionTypeOptions = [
  { value: 0, label: '其他原因' },
  { value: 1, label: '号码异常原因失败' },
  { value: 2, label: '携号转网已处理' },
  { value: 3, label: '运营商黑名单已协调解除' },
  { value: 4, label: '短信正常下发，请用户侧排查' },
  { value: 5, label: '营销内容行业账号不发' },
  { value: 6, label: '签名未报备/未提供实名资料/签名未实名完成' },
  { value: 999, label: '-' },
];

const subMalfunctionTypeOptions = [
  { value: 0, label: '其他签名未报备原因' },
  { value: 1, label: '实名资质未更新' },
  { value: 2, label: '签名实名中' },
  { value: 999, label: '-' },
];

function openReplyDetail(reply_txt: ReplyTxt[]) {
  return Modal.info({
    title: '回复历史',
    width: '50%',
    content: (
      <Table
        rowKey={(row, i) => `${i}`}
        pagination={false}
        columns={[
          { title: '供应商', dataIndex: 'provider_name', width: 200 },
          { title: '时间', dataIndex: 'time', width: 200 },
          { title: '内容', dataIndex: 'content' },
        ]}
        dataSource={reply_txt}
      ></Table>
    ),
  });
}

const IssueManage = () => {
  const columns: any = [
    {
      title: '工单号',
      dataIndex: 'issue_number',
      key: 'issue_number',
      align: 'center',
      width: 150,
      render: (val: string) => {
        return <Text>{val}</Text>;
      },
    },
    {
      title: '子单号',
      dataIndex: 'sub_issue_number',
      key: 'sub_issue_number',
      align: 'center',
      width: 150,
      render: (val: string) => {
        return <Text>{val}</Text>;
      },
    },
    {
      title: 'sid',
      dataIndex: 'sid',
      key: 'sid',
      align: 'center',
      hideInSearch: true,
      width: 150,
      render: (val: string) => {
        return <Text>{val}</Text>;
      },
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      key: 'mobile',
      align: 'center',
    },
    {
      title: '发送时间',
      dataIndex: 'send_time',
      key: 'send_time',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '下发内容',
      dataIndex: 'sms_content',
      key: 'sms_content',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '发送状态',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '工单创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '供应商',
      dataIndex: 'provider',
      key: 'provider',
      align: 'center',
    },
    {
      title: '供应商回复时间',
      dataIndex: 'reply_time',
      key: 'reply_time',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '供应商回复内容',
      dataIndex: 'reply_content',
      key: 'reply_content',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '回复历史',
      dataIndex: 'reply_txt',
      key: 'reply_txt',
      align: 'center',
      hideInSearch: true,
      render: (reply_txt: ReplyTxt[]) => {
        return (
          <Button type="link" onClick={() => openReplyDetail(reply_txt)}>
            回复历史
          </Button>
        );
      },
    },
    {
      title: '排障分类',
      dataIndex: 'malfunction_type',
      key: 'malfunction_type',
      align: 'center',
      renderFormItem: () => <Select></Select>,
      fieldProps: {
        options: malfunctionTypeOptions,
        allowClear: true,
      },
      render: (malfunction_type: number) => findLabel(malfunctionTypeOptions, malfunction_type),
    },
    {
      title: '子分类',
      dataIndex: 'malfunction_sub_type',
      key: 'malfunction_sub_type',
      align: 'center',
      renderFormItem: () => <Select></Select>,
      fieldProps: {
        options: subMalfunctionTypeOptions,
        allowClear: true,
      },
      hideInSearch: true,
      render: (malfunction_sub_type: number) =>
        findLabel(subMalfunctionTypeOptions, malfunction_sub_type),
    },
    {
      title: '提交人',
      dataIndex: 'staffname',
      key: 'staffname',
      align: 'center',
    },
    {
      title: '工单状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      hideInSearch: true,
      render: (status: number) => findLabel(statusOptions, status),
    },
  ];

  return (
    <ProTable
      columns={columns}
      rowKey={(r, i) => `${i}`}
      pagination={{
        defaultPageSize: 0,
        showSizeChanger: true,
      }}
      search={{
        collapseRender: false,
        collapsed: false,
      }}
      scroll={isMobile() ? { x: 'max-content' } : { y: 700, x: 1300 }}
      request={async (params: { pageSize: number; current: number }) => {
        const { data } = await getIssueList({
          ..._.omit(params, ['pageSize', 'current']),
          page_index: params.current,
          page_size: params.pageSize,
        });
        return {
          data: data.list ?? [],
          success: true,
          total: data.count,
        };
      }}
    />
  );
};
export default IssueManage;
