import React, { useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, DatePicker } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { queryVmsSend } from '@/services/api';

const { RangePicker } = DatePicker;

const VmsSendQuery = () => {
  const [form] = Form.useForm();
  const [list, setList] = useState<any[]>([]);
  const [isLoading, setLoading] = useState<boolean>(false);

  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '语音提醒callid',
      dataIndex: 'callid',
      key: 'callid',
      align: 'center',
    },
    {
      title: 'result',
      dataIndex: 'result',
      key: 'result',
      align: 'center',
    },
    {
      title: '发送时间',
      dataIndex: 'sendtime',
      key: 'sendtime',
      align: 'center',
    },
    {
      title: 'state',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
    },
    {
      title: '接听时间',
      dataIndex: 'accepttime',
      key: 'accepttime',
      align: 'center',
    },
    {
      title: '开始时间',
      dataIndex: 'starttime',
      key: 'starttime',
      align: 'center',
    },
    {
      title: '结束时间',
      dataIndex: 'endtime',
      key: 'endtime',
      align: 'center',
    },
    {
      title: '主叫号码',
      dataIndex: 'displaynum',
      key: 'displaynum',
      align: 'center',
    },
    {
      title: '计费时长',
      dataIndex: 'fee',
      key: 'fee',
      align: 'center',
    },
    {
      title: '发送结果',
      dataIndex: 'result_text',
      key: 'result_text',
      align: 'center',
    },
    {
      title: '接听状态',
      dataIndex: 'call_state',
      key: 'call_state',
      align: 'center',
    },
  ];

  async function getList(params: any) {
    setLoading(true);
    const res = await queryVmsSend({
      phone: params.phone,
      sdkappid: params.sdkappid,
      from: dayjs(params.time[0]).format('YYYY-M-D H:m:s'),
      to: dayjs(params.time[1]).format('YYYY-M-D H:m:s'),
    });
    setList(res.data);
    setLoading(false);
  }

  async function onSubmit(vals: any) {
    !vals.sdkappid && delete vals.sdkappid;
    getList({ ...vals });
  }

  return (
    <PageContainer title="下行语音查询">
      <Form
        labelCol={{ span: 6 }}
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="phone" rules={[{ required: true, message: '请输入号码' }]}>
          <Input placeholder="号码" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item name="sdkappid">
          <Input placeholder="sdkappid" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item name="time" rules={[{ required: true, message: '请选择时间' }]}>
          <RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={isLoading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table columns={columns} dataSource={list} loading={isLoading} style={{ marginTop: 20 }} />
    </PageContainer>
  );
};

export default VmsSendQuery;
