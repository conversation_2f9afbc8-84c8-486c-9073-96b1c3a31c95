import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { queryVmsNumber } from '@/services/api';
import { isMobile } from '@/const/jadgeUserAgent';

const AuditList = () => {
  const [form] = Form.useForm();
  const [list, setList] = useState<any[]>([]);
  const [count, setCount] = useState<number>(0);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const columns: any = [
    {
      title: 'sdkappid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '号码',
      dataIndex: 'display_number',
      key: 'display_number',
      align: 'center',
    },
    {
      title: 'provider_id',
      dataIndex: 'provider_id',
      key: 'provider_id',
      align: 'center',
    },
    {
      title: '购买时间',
      dataIndex: 'begin_time',
      key: 'begin_time',
      align: 'center',
    },
    {
      title: '过期时间',
      dataIndex: 'end_time',
      key: 'end_time',
      align: 'center',
    },
    {
      title: '自动续费标识',
      dataIndex: 'auto_renew',
      key: 'auto_renew',
      align: 'center',
    },
    {
      title: '资源id',
      dataIndex: 'res_id',
      key: 'res_id',
      align: 'center',
    },
    {
      title: '省份',
      dataIndex: 'provinces',
      key: 'provinces',
      align: 'center',
    },
    {
      title: '城市',
      dataIndex: 'city',
      key: 'city',
      align: 'center',
    },
    {
      title: '运营商',
      dataIndex: 'operator',
      key: 'operator',
      align: 'center',
    },
    {
      title: '状态标识',
      dataIndex: 'res_status',
      key: 'res_status',
      align: 'center',
    },
    {
      title: '状态映射',
      dataIndex: 'res_status_text',
      key: 'res_status_text',
      align: 'center',
    },
    {
      title: '是否自动续费',
      dataIndex: 'auto_renew_text',
      key: 'auto_renew_text',
      align: 'center',
    },
  ];

  useEffect(() => {
    getList({});
  }, [pageIndex, pageSize]);
  async function getList(params: any) {
    setLoading(true);
    const res = await queryVmsNumber({
      ...params,
      page_index: pageIndex,
      page_size: pageSize,
    });
    setCount(res.data.count);
    setList(res.data.list);
    setLoading(false);
  }

  async function onSubmit(vals: any) {
    !vals.sdkappid && delete vals.sdkappid;
    !vals.number && delete vals.number;
    getList({ ...vals });
  }

  return (
    <PageContainer title="语音号码查询">
      <Form
        labelCol={{ span: 6 }}
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSubmit(vals)}
      >
        <Form.Item name="number">
          <Input placeholder="号码" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item name="sdkappid">
          <Input placeholder="sdkappid" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={isLoading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={list}
        rowKey={(record) => record.display_number}
        loading={isLoading}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPageSize(page_size),
          onChange: (page) => {
            setPageIndex(page);
          },
        }}
        style={{ marginTop: 20 }}
        scroll={isMobile() ? { x: 'max-content' } : undefined}
      />
    </PageContainer>
  );
};

export default AuditList;
