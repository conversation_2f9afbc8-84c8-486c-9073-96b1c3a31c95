import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import { Button, Table, Form, Input, DatePicker, message } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { queryVmsNewSend } from '@/services/api';

const { RangePicker } = DatePicker;

const VmsNewSendQuery = () => {
  const [form] = Form.useForm();
  const [list, setList] = useState<any[]>([]);
  const [count, setCount] = useState<number>(0);
  const [pageIndex, setPageIndex] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [isLoading, setLoading] = useState<boolean>(false);
  const [formConfig, setFormConfig] = useState<any>({});

  const columns: any = [
    {
      title: 'appid',
      dataIndex: 'appid',
      key: 'appid',
      align: 'center',
    },
    {
      title: '语音提醒callid',
      dataIndex: 'callid',
      key: 'callid',
      width: 100,
      align: 'center',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
    },
    {
      title: '国家码',
      dataIndex: 'nationcode',
      key: 'nationcode',
      align: 'center',
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      key: 'mobile',
      align: 'center',
    },
    {
      title: '发送时间',
      dataIndex: 'call_time',
      key: 'call_time',
      align: 'center',
    },
    {
      title: 'result',
      dataIndex: 'result',
      key: 'result',
      align: 'center',
    },
    {
      title: '发送结果',
      dataIndex: 'result_text',
      key: 'result_text',
      align: 'center',
    },
    {
      title: 'state',
      dataIndex: 'call_result',
      key: 'call_result',
      align: 'center',
    },
    {
      title: '接听状态',
      dataIndex: 'call_result_text',
      key: 'call_result_text',
      align: 'center',
    },
    {
      title: '主叫号码',
      dataIndex: 'display_number',
      key: 'display_number',
      align: 'center',
    },
    {
      title: '开始拨打时间',
      dataIndex: 'start_call_time',
      key: 'start_call_time',
      align: 'center',
    },
    {
      title: '接听时间',
      dataIndex: 'accept_time',
      key: 'accept_time',
      align: 'center',
    },
    {
      title: '结束时间',
      dataIndex: 'end_call_time',
      key: 'end_call_time',
      align: 'center',
    },
    {
      title: '计费时长',
      dataIndex: 'fee',
      key: 'fee',
      align: 'center',
    },
    {
      title: '按键',
      dataIndex: 'keypress',
      key: 'keypress',
      align: 'center',
    },
  ];

  useEffect(() => {
    if (!formConfig.time) return;
    getList({ ...formConfig });
  }, [pageIndex, pageSize]);

  // const [list, getList] = useAsyncFn(async () => {

  // }, [...])

  // list = {
  //   isLoading,
  //   value:
  // }

  async function getList(params: any) {
    setLoading(true);
    const res = await queryVmsNewSend({
      phone: params.phone,
      sdkappid: params.sdkappid,
      from: dayjs(params.time[0]).format('YYYY-M-D H:m:s'),
      to: dayjs(params.time[1]).format('YYYY-M-D H:m:s'),
      page_size: pageSize,
      page_index: pageIndex,
    });
    setList(res.data.list || []);
    setCount(res.data.count);
    setLoading(false);
  }

  function onSubmit(vals: any) {
    !vals.phone && delete vals.phone;
    !vals.sdkappid && delete vals.sdkappid;
    setFormConfig({ ...vals });
    if (!vals.phone && !vals.sdkappid) {
      return message.error('请输入号码或者sdkappid进行检索');
    }
    return getList({ ...vals });
  }

  return (
    <PageContainer title="下行新语音查询">
      <Form
        labelCol={{ span: 6 }}
        form={form}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => {
          onSubmit(vals);
        }}
      >
        <Form.Item name="phone">
          <Input placeholder="号码" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item name="sdkappid">
          <Input placeholder="sdkappid" style={{ width: 250 }} />
        </Form.Item>
        <Form.Item name="time" rules={[{ required: true, message: '请选择时间' }]}>
          <RangePicker showTime format="YYYY-MM-DD HH:mm:ss" />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary" loading={isLoading}>
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        rowKey={(record, index) => `${index}-${record.appid}`}
        dataSource={list}
        loading={isLoading}
        style={{ marginTop: 20 }}
        pagination={{
          defaultCurrent: 1,
          total: count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPageSize(page_size),
          onChange: (page) => {
            setPageIndex(page);
          },
        }}
      />
    </PageContainer>
  );
};

export default VmsNewSendQuery;
