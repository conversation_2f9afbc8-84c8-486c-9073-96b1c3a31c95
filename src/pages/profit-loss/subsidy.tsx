import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-layout';
import {
  Select,
  Form,
  Button,
  Table,
  Upload,
  message,
  Input,
  DatePicker,
  Space,
  Popconfirm,
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import * as XLSX from 'xlsx';
import { useAsyncFn, useSetState } from 'react-use';
import { useDialogRef } from '@/utils/react-use/useDialog';

import { ImportResultDialog } from '@/components/ImportResultDialog';
import dayjs from 'dayjs';
import {
  addProfitLossSubsidy,
  getProfitLossSubsidyList,
  deleteProfitLossSubsidy,
} from '@/services/profitLoss';
import { smsType, site } from './const';

const ProfitLossSubsidy = () => {
  const [form] = Form.useForm();
  const importResultRef = useDialogRef();
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [pagination, setPagination] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [searchKeys, setSearchKeys] = useState<any>({});

  const columns: any = [
    {
      title: '账单结算时间',
      dataIndex: 'push_time',
      key: 'push_time',
      align: 'center',
    },
    {
      title: '客户uin',
      dataIndex: 'uin',
      key: 'uin',
      align: 'center',
    },
    {
      title: '客户名称',
      dataIndex: 'client_name',
      key: 'client_name',
      align: 'center',
    },
    {
      title: '短信类型',
      dataIndex: 'sms_type',
      key: 'sms_type',
      align: 'center',
      render: (val: number) => smsType.find((el) => el.value.toString() === val?.toString())?.label,
    },
    {
      title: '站点',
      dataIndex: 'node',
      key: 'node',
      align: 'center',
      render: (val: number) => site.find((el) => el.value.toString() === val?.toString())?.label,
    },
    {
      title: '补贴金额',
      dataIndex: 'amount',
      key: 'amount',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      render: (row: any) => {
        return (
          <Popconfirm
            title="确认删除此条数据吗？"
            onConfirm={() => handleDelete(row.conf_id)}
            okText="Yes"
            cancelText="No"
          >
            <Button type="link">删除</Button>
          </Popconfirm>
        );
      },
    },
  ];

  const [state, getList] = useAsyncFn(async () => {
    const res = await getProfitLossSubsidyList({
      ...pagination,
      ...searchKeys,
      push_time: searchKeys.push_time
        ? dayjs(searchKeys.push_time).add(1, 'month').startOf('month').format('YYYYMMDD')
        : undefined,
    });
    return res.data;
  }, [pagination, searchKeys]);

  const onSearch = (vals: any) => {
    Object.keys(vals).forEach((k) => {
      if (!vals[k]) delete vals[k];
    });
    setSearchKeys({
      ...vals,
    });
  };

  const handleData = (data: any[]) => {
    const a = data.map((el: any) => ({
      uin: Number(el.uin),
      push_time: Number(el.push_time),
      client_name: String(el.client_name),
      sms_type: smsType.find((t) => el.sms_type === t.label)?.value.toString(),
      node: site.find((t) => el.node === t.label)?.value.toString(),
      amount: String(el.amount),
    }));
    return a;
  };

  const onChange = (info: any) => {
    const render = new FileReader();
    render.readAsArrayBuffer(info.file.originFileObj);
    render.addEventListener('load', (e) => {
      const ab = e.target?.result;
      const wb = XLSX.read(ab, { type: 'array' });
      const wsname = wb.SheetNames[0];
      const ws = wb.Sheets[wsname];
      const data: any = XLSX.utils.sheet_to_json(ws, {
        header: columns.map((el: any) => el.key).slice(0, -1),
        defval: '',
        dateNF: 'string',
        blankrows: false,
        range: 1,
      });
      if (!data?.length) {
        message.error('导入数据为空');
        return false;
      }
      importResultRef.current.open({ list: handleData(data) });
      return true;
    });
    return false;
  };

  const onSubmit = async (vals: any) => {
    return await addProfitLossSubsidy({ ...vals });
  };

  const onSuccess = () => {
    getList();
  };

  const rowSelection = {
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
      setSelectedRowKeys(selectedRowKeys);
    },
  };

  async function handleDelete(conf_id?: number) {
    try {
      const res = await deleteProfitLossSubsidy({
        conf_ids: conf_id ? conf_id.toString() : selectedRowKeys.join(','),
      });
      if (res.code === 0) {
        message.success('删除成功');
      }
      getList();
    } catch {
      message.error('删除失败');
    }
  }

  useEffect(() => {
    getList();
  }, [pagination, searchKeys]);

  return (
    <PageContainer>
      <Space style={{ marginBottom: 20 }}>
        <Upload
          onChange={onChange}
          showUploadList={false}
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        >
          <Button type="primary" ghost>
            导入数据
          </Button>
        </Upload>
        <Popconfirm
          title="确认删除选中的数据吗？"
          placement="bottom"
          onConfirm={() => handleDelete()}
          okText="Yes"
          cancelText="No"
        >
          <Button danger disabled={!selectedRowKeys.length}>
            批量删除
          </Button>
        </Popconfirm>
      </Space>
      <ImportResultDialog
        dialogRef={importResultRef}
        columns={columns.slice(0, -1)}
        onSubmit={onSubmit}
        onSuccess={onSuccess}
      />

      <Form
        className="sender-search-form"
        labelCol={{ span: 6 }}
        form={form}
        // initialValues={{ ...formData }}
        layout="inline"
        labelAlign="right"
        onFinish={(vals) => onSearch(vals)}
      >
        <Form.Item name="push_time">
          <DatePicker picker="month" placeholder="账单结算时间" />
        </Form.Item>
        <Form.Item name="uin">
          <Input placeholder="客户uin" />
        </Form.Item>
        <Form.Item name="client_name">
          <Input placeholder="客户名称" />
        </Form.Item>
        <Form.Item name="sms_type">
          <Select
            showSearch
            allowClear
            placeholder="短信类型"
            options={smsType}
            style={{ width: 120 }}
          />
        </Form.Item>
        <Form.Item name="node">
          <Select showSearch allowClear placeholder="站点" options={site} style={{ width: 120 }} />
        </Form.Item>
        <Form.Item>
          <Button icon={<SearchOutlined />} htmlType="submit" type="primary">
            查询
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={state.value?.list}
        loading={state.loading}
        rowKey="conf_id"
        pagination={{
          defaultCurrent: 1,
          total: state.value?.count,
          showSizeChanger: true,
          onShowSizeChange: (current, page_size) => setPagination({ page_size }),
          onChange: (page) => {
            setPagination({ page_index: page });
          },
        }}
        rowSelection={{
          type: 'checkbox',
          ...rowSelection,
        }}
      />
    </PageContainer>
  );
};

export default ProfitLossSubsidy;
