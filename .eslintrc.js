module.exports = {
  env: {
    browser: true,
    es6: true,
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 6,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  rules: {
    // typescript 写 react 项目，不需要给 Class 的属性加描述
    '@typescript-eslint/explicit-member-accessibility': 'off',
    '@typescript-eslint/no-misused-promises': 'off',
    'no-unused-vars': 'off',
    'no-underscore-dangle': 'off',
    'react/display-name': 'off',
    'arrow-body-style': 'off',
    '@typescript-eslint/naming-convention': 'off',
    '@typescript-eslint/no-shadow': 'off',
    'prettier/prettier': [
      'warn',
      {},
      {
        usePrettierrc: true,
      },
    ],
    // typescript 写 react 项目，不需要
    'react/prop-types': 'off',
    'react-hooks/rules-of-hooks': 'error',
    'react-hooks/exhaustive-deps': [
      'warn',
      {
        additionalHooks: '(useAsyncRetryFunc|useGlobalAppDetails|useAsyncFn)',
      },
    ],
    'no-nested-ternary': 'off',
  },
  globals: {
    window: true,
    document: true,
    localStorage: true,
    FormData: true,
    FileReader: true,
    Blob: true,
    navigator: true,
    Headers: true,
    Request: true,
    fetch: true,
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: true,
    page: true,
    REACT_APP_ENV: true,
  },
  extends: [
    require.resolve('@umijs/lint/dist/config/eslint'),
    // 'react-app',
    'plugin:react/recommended',
    'plugin:prettier/recommended',
    'plugin:react-hooks/recommended',
    '@tencent/eslint-config-tencent',
    '@tencent/eslint-config-tencent/ts',
    // '@tencent/eslint-config-tencent/prettier',
    'prettier',
  ],
  ignorePatterns: ['slot_modules', 'tctui-test'],
  plugins: ['react', '@typescript-eslint', 'prettier'],
};
