master:
  push:
    - docker:
        image: node:10
      stages:
        - name: 依赖安装
          script: npm install --registry=http://r.tnpm.oa.com
        - name: 编译
          script: npm run build
        - name: 创建织云包
          type: zhiyun:pkg
          options:
            product: CSIG-IMWEB
            name: sms_config_web
            dist: /dist
        - name: 织云包升级 # 4. 织云包自动升级
          type: zhiyun:update # 内置脚本
          options:
            product: CSIG-IMWEB
            name: sms_config_web
            # ips:
            #   - *************