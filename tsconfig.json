{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "importHelpers": true, "jsx": "preserve", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "skipLibCheck": true, "experimentalDecorators": true, "strict": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "paths": {"@/*": ["./src/*"], "@@/*": ["./src/.umi/*"], "@@test/*": ["./src/.umi-test/*"]}}, "include": ["src/**/*", "./**/*.d.ts", "./**/*.ts", "./**/*.tsx", ".eslintrc.js", ".stylelintrc.js", ".prettierrc.js", "plop-template/simple/prompt.js", "plopfile.js"]}